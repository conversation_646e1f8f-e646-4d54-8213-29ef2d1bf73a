<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台验证码测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .captcha-display { margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        button { 
            padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; 
            border: none; border-radius: 4px; cursor: pointer; font-size: 14px;
        }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .timestamp { color: #666; font-size: 12px; margin-top: 10px; }
        img { max-width: 100%; border: 1px solid #ddd; border-radius: 4px; }
        .tech-info { background: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 13px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 管理后台验证码测试</h1>
        <p>测试管理后台是否能正确显示彩色验证码，绕过所有可能的缓存。</p>
        
        <div class="test-section">
            <h3>🎯 测试选项</h3>
            <button onclick="testCaptcha(false)">🔄 普通测试</button>
            <button onclick="testCaptcha(true)">💥 强制无缓存测试</button>
            <button onclick="clearCache()" class="danger">🧹 清除浏览器缓存</button>
            <button onclick="location.reload(true)">🔄 强制刷新页面</button>
        </div>
        
        <div class="test-section">
            <h3>🖼️ 验证码显示</h3>
            <div id="captcha-display" class="captcha-display">
                <p>点击测试按钮开始...</p>
            </div>
        </div>
        
        <div id="status"></div>
        
        <div class="tech-info">
            <h4>🔧 技术信息</h4>
            <p>用户代理: <code id="user-agent"></code></p>
            <p>页面加载时间: <span id="load-time"></span></p>
            <p>缓存策略: 强制无缓存 + 时间戳 + 随机参数</p>
        </div>
        
        <div class="timestamp" id="timestamp"></div>
    </div>

    <script>
        // 显示技术信息
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('load-time').textContent = new Date().toLocaleString('zh-CN');
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        function updateTimestamp() {
            document.getElementById('timestamp').textContent = 
                '最后更新: ' + new Date().toLocaleString('zh-CN');
        }

        async function clearCache() {
            showStatus('🧹 正在清除缓存...', 'info');
            
            try {
                // 清除各种缓存
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (let registration of registrations) {
                        await registration.unregister();
                    }
                }
                
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                }
                
                // 清除本地存储
                localStorage.clear();
                sessionStorage.clear();
                
                showStatus('✅ 缓存清除完成，建议手动按 Ctrl+Shift+R 强制刷新', 'success');
            } catch (error) {
                showStatus(`❌ 缓存清除失败: ${error.message}`, 'error');
            }
        }

        async function testCaptcha(forceNoCache = false) {
            const captchaDisplay = document.getElementById('captcha-display');
            const testType = forceNoCache ? '强制无缓存' : '普通';
            
            captchaDisplay.innerHTML = `<p>🔄 正在获取验证码 (${testType}测试)...</p>`;
            showStatus(`🚀 开始${testType}测试`, 'info');
            
            try {
                // 构建请求URL，添加多重防缓存参数
                let url = 'http://localhost:8181/api/captcha/get';
                if (forceNoCache) {
                    const timestamp = Date.now();
                    const random = Math.random().toString(36).substring(7);
                    url += `?_t=${timestamp}&_r=${random}&_nc=${Math.floor(Math.random() * 1000000)}`;
                }
                
                const headers = {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                };
                
                if (forceNoCache) {
                    // 添加所有可能的防缓存头
                    headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0';
                    headers['Pragma'] = 'no-cache';
                    headers['Expires'] = '0';
                    headers['If-None-Match'] = '';
                    headers['If-Modified-Since'] = '';
                }
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers,
                    cache: forceNoCache ? 'no-store' : 'default',
                    mode: 'cors'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data) {
                    const { originalImageBase64, jigsawImageBase64, y, token } = data.data;
                    
                    captchaDisplay.innerHTML = `
                        <div style="margin: 15px 0;">
                            <h4>🖼️ 背景图片 (${testType}测试)：</h4>
                            <img src="data:image/png;base64,${originalImageBase64}" 
                                 style="border: 2px solid #007bff; max-width: 100%; background: #f0f0f0;" 
                                 alt="验证码背景"/>
                            <p style="font-size: 12px; color: #666; margin: 5px 0;">
                                检查背景是否为<strong>天蓝色渐变</strong>，带有<strong>白色网格线</strong>
                            </p>
                        </div>
                        <div style="margin: 15px 0;">
                            <h4>🧩 拼图滑块：</h4>
                            <img src="data:image/png;base64,${jigsawImageBase64}" 
                                 style="border: 2px solid #28a745; background: #f8f9fa;" 
                                 alt="验证码滑块"/>
                            <p style="font-size: 12px; color: #666; margin: 5px 0;">
                                检查滑块是否有<strong>柔和的渐变填充</strong>和<strong>深灰色边框</strong>
                            </p>
                        </div>
                        <div style="margin: 15px 0; padding: 10px; background: #e9ecef; border-radius: 4px; font-size: 12px;">
                            <p><strong>📍 技术参数:</strong></p>
                            <p>Y坐标: ${y}</p>
                            <p>Token: ${token.substring(0, 30)}...</p>
                            <p>图片大小: ${originalImageBase64.length} 字符 (Base64)</p>
                        </div>
                    `;
                    
                    const colorStatus = forceNoCache ? 
                        '🎨 如果看到天蓝色渐变背景和柔和的滑块颜色，说明彩色验证码已生效！' :
                        '🎨 如果还是灰色，请点击"强制无缓存测试"';
                    
                    showStatus(`✅ ${testType}测试成功！${colorStatus}`, 'success');
                    updateTimestamp();
                } else {
                    throw new Error('API返回数据格式错误');
                }
                
            } catch (error) {
                console.error('获取验证码失败:', error);
                captchaDisplay.innerHTML = `<p style="color: red;">❌ ${testType}测试失败: ${error.message}</p>`;
                showStatus(`❌ ${testType}测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动进行普通测试
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('🔄 页面加载完成，开始自动测试...', 'info');
            setTimeout(() => testCaptcha(false), 1000);
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                testCaptcha(true);
            }
        });
    </script>
</body>
</html>