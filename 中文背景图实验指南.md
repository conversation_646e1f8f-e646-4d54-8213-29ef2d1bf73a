# 🀄 中文字符背景图验证码实验指南

## 📋 实验目标

测试使用彩色中文字符作为验证码背景图时，拼图滑块的自动生成效果。

## 🔬 实验理论

**核心假设**：当使用中文字符背景图时，系统将：
1. 从中文字符图像中随机提取47×47像素区域
2. 该区域将包含部分中文字符的内容
3. 形成具有中文字符特色的拼图块
4. 与背景图的缺口完美匹配

## 📁 实验步骤

### 第1步：保存背景图
将你的中文字符背景图保存到：
```
/Volumes/acasis/Assessment/backend/src/main/resources/images/bg_chinese.png
```

### 第2步：确认代码更新
代码已更新，将 `bg_chinese.png` 加入随机选择列表：
```java
String[] backgroundFiles = {"bg1.png", "bg2.png", "bg3.png", "bg4.png", "bg5.png", 
                          "bg6.png", "bg7.png", "bg8.png", "bg9.png", "bg_chinese.png"};
```

### 第3步：重新编译服务
```bash
cd /Volumes/acasis/Assessment/backend
./mvnw clean package -DskipTests
```

### 第4步：重启服务
```bash
./scripts/dev-start-m4.sh
```

### 第5步：进行实验测试
访问实验页面：`chinese-captcha-experiment.html`

## 🔍 预期实验结果

### 成功指标：
- ✅ 验证码背景显示为你的中文字符图像
- ✅ 拼图块包含中文字符的部分内容
- ✅ 拼图块颜色与背景图对应位置匹配
- ✅ 缺口与拼图块大小和位置完美对应

### 实验观察要点：
1. **字符识别度**：拼图块中的中文字符是否清晰可辨？
2. **颜色匹配度**：拼图块颜色是否与背景图该位置一致？
3. **视觉效果**：相比普通背景图，是否更具特色？
4. **用户体验**：是否会影响验证码的可用性？

## 📊 实验数据记录

### 测试记录表格：
| 测试次数 | 背景图选中 | 拼图位置(x,y) | 包含字符 | 视觉效果评分 |
|---------|-----------|--------------|----------|-------------|
| 1       | bg_chinese.png | (?,?) | ? | ?/10 |
| 2       | ? | (?,?) | ? | ?/10 |
| 3       | ? | (?,?) | ? | ?/10 |

### 后端日志关键信息：
```
INFO c.a.service.SimpleCaptchaService - ✅ 成功加载AjCaptcha背景图片: bg_chinese.png
INFO c.a.service.SimpleCaptchaService - 验证码生成成功，token: xxx, 滑块位置: (x, y)
```

## 🎯 实验成功标准

1. **技术成功**：
   - 图片正确加载
   - 拼图块成功生成
   - 验证机制正常工作

2. **视觉成功**：
   - 中文字符清晰可见
   - 色彩搭配和谐
   - 整体效果美观

3. **用户体验成功**：
   - 不影响识别难度
   - 增加视觉趣味性
   - 保持专业性

## 🚀 实验扩展

### 可能的改进方向：
1. **多套中文背景**：准备不同风格的中文字符图
2. **字体优化**：选择更适合拼图的字体和大小
3. **对比度调整**：确保最佳的视觉识别效果
4. **主题背景**：根据应用场景制作相关主题的中文背景

### 其他实验想法：
- 🎨 艺术字背景图实验
- 📝 书法字体背景图实验  
- 🌈 渐变色中文字符实验
- 🎭 传统文化元素背景实验

## 📱 测试链接

- **实验页面**：`chinese-captcha-experiment.html`
- **管理后台**：http://localhost:5274/login
- **移动端**：http://localhost:5273/#/pages/login/index

---

**实验开始时间**：_____________  
**实验完成时间**：_____________  
**实验结论**：_________________