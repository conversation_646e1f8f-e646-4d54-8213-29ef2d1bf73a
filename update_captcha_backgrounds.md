# 更换验证码背景图指南

## 📁 目录位置
```
/Volumes/acasis/Assessment/backend/src/main/resources/images/
```

## 🎨 方法1：直接替换（最简单）
```bash
# 替换现有的9张背景图
cp your_image1.png backend/src/main/resources/images/bg1.png
cp your_image2.png backend/src/main/resources/images/bg2.png
# ... 继续替换其他图片
```

## 🎨 方法2：添加新图片
1. 添加图片到目录：
```bash
cp your_new_image.png backend/src/main/resources/images/bg10.png
```

2. 修改代码文件：
`backend/src/main/java/com/assessment/service/SimpleCaptchaService.java`

找到第175-176行：
```java
String[] backgroundFiles = {"bg1.png", "bg2.png", "bg3.png", "bg4.png", "bg5.png", 
                          "bg6.png", "bg7.png", "bg8.png", "bg9.png"};
```

修改为：
```java
String[] backgroundFiles = {"bg1.png", "bg2.png", "bg3.png", "bg4.png", "bg5.png", 
                          "bg6.png", "bg7.png", "bg8.png", "bg9.png", "bg10.png", "your_new_bg.png"};
```

## 🔄 应用更改
```bash
# 重新编译并启动
cd backend
./mvnw clean package -DskipTests
./scripts/dev-start-m4.sh
```

## 📏 图片要求
- **格式**: PNG (推荐)、JPG、JPEG
- **尺寸**: 310×155像素 或 等比例
- **质量**: 高清晰度，色彩丰富
- **内容**: 避免过于复杂的图案，保证拼图块识别度

## 🎯 最佳实践
1. 使用多样化的背景（风景、建筑、抽象图案等）
2. 确保图片对比度适中，不影响拼图块识别
3. 定期更换背景图提高安全性
4. 保留备份以便回滚

## ✅ 验证更改
访问登录页面查看新的验证码效果：
- 管理后台：http://localhost:5274/login
- 移动端：http://localhost:5273/#/pages/login/index