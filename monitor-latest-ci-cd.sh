#!/bin/bash

# 监控最新 CI/CD 工作流状态脚本
echo "🔍 监控最新 GitHub Actions CI/CD 工作流状态"
echo "==========================================="

# 获取最新工作流ID
LATEST_RUN=$(gh run list --workflow="ci-cd.yml" --limit 1 --json databaseId --jq '.[0].databaseId')

if [ -z "$LATEST_RUN" ]; then
    echo "❌ 无法获取最新工作流ID"
    exit 1
fi

echo "📊 工作流ID: $LATEST_RUN"

# 函数：获取状态图标
get_status_icon() {
    case $1 in
        "completed") echo "✅" ;;
        "in_progress") echo "🔄" ;;
        "queued") echo "⏳" ;;
        "failed") echo "❌" ;;
        "cancelled") echo "⚠️" ;;
        *) echo "❓" ;;
    esac
}

# 函数：获取结论图标
get_conclusion_icon() {
    case $1 in
        "success") echo "✅" ;;
        "failure") echo "❌" ;;
        "cancelled") echo "⚠️" ;;
        "skipped") echo "⏭️" ;;
        *) echo "❓" ;;
    esac
}

# 监控循环
for i in {1..15}; do
    echo -e "\n🔄 检查 #$i ($(date '+%H:%M:%S'))"
    echo "----------------------------------------"
    
    # 获取工作流状态
    if ! WORKFLOW_STATUS=$(gh run view $LATEST_RUN --json status,conclusion,jobs 2>/dev/null); then
        echo "❌ 无法获取工作流状态"
        break
    fi
    
    # 解析状态
    STATUS=$(echo "$WORKFLOW_STATUS" | jq -r '.status')
    CONCLUSION=$(echo "$WORKFLOW_STATUS" | jq -r '.conclusion')
    
    echo "📊 工作流总体状态: $(get_status_icon "$STATUS") $STATUS"
    if [ "$CONCLUSION" != "null" ]; then
        echo "🎯 工作流结论: $(get_conclusion_icon "$CONCLUSION") $CONCLUSION"
    fi
    
    echo -e "\n📋 作业状态:"
    echo "$WORKFLOW_STATUS" | jq -r '.jobs[] | "  \(.name): \(.status) \(if .conclusion != null then "(\(.conclusion))" else "" end)"'
    
    # 如果工作流完成，退出循环
    if [ "$STATUS" = "completed" ]; then
        echo -e "\n🎉 工作流已完成！"
        
        if [ "$CONCLUSION" = "success" ]; then
            echo "✅ 所有作业成功完成"
            echo -e "\n🎊 CI/CD 工作流运行成功！Prettier 修复已生效。"
        else
            echo "❌ 工作流执行失败"
            echo "📝 查看详细日志:"
            echo "   gh run view $LATEST_RUN --log"
        fi
        break
    fi
    
    # 如果不是最后一次检查，等待30秒
    if [ $i -lt 15 ]; then
        echo -e "\n⏳ 等待30秒后再次检查..."
        sleep 30
    fi
done

echo -e "\n🔗 GitHub Actions 页面:"
echo "   https://github.com/changxiaoyangbrain/assessment/actions/runs/$LATEST_RUN"

echo -e "\n💡 有用的命令:"
echo "   gh run list                    # 查看所有运行"
echo "   gh run view $LATEST_RUN       # 查看运行详情"
echo "   gh run view $LATEST_RUN --log # 查看完整日志"