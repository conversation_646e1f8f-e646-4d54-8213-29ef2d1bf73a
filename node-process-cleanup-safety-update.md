# Node.js 进程清理安全性更新

## 问题描述
原始脚本使用了过于宽泛的进程清理命令，可能会误杀系统中其他项目的Node.js进程：

```bash
# 危险的清理方式 - 会影响所有vite/webpack/dev-server进程
pkill -f "vite" 2>/dev/null || true
pkill -f "webpack" 2>/dev/null || true
pkill -f "dev-server" 2>/dev/null || true
```

## 解决方案

### 1. 精确的进程识别
使用多种方法精确识别项目相关的Node.js进程：

#### 方法1: 路径匹配
```bash
# 通过命令行参数匹配项目路径
PROJECT_NODE_PIDS1=$(ps aux | grep -E "(vite|webpack|dev-server)" | grep -E "($PROJECT_PATH|frontend/uni-app|frontend/admin)" | grep -v grep | awk '{print $2}' || true)
```

#### 方法2: 端口匹配
```bash
# 通过项目特定端口匹配（5273, 5274）
PROJECT_NODE_PIDS2=$(lsof -ti :5273,5274 2>/dev/null || true)
```

### 2. 安全的进程清理
```bash
# 合并两种方法找到的PID
ALL_PIDS=$(echo "$PROJECT_NODE_PIDS1 $PROJECT_NODE_PIDS2" | tr ' ' '\n' | sort -u | tr '\n' ' ')

# 逐个验证并清理进程
for pid in $ALL_PIDS; do
    if [ -n "$pid" ] && ps -p "$pid" >/dev/null 2>&1; then
        kill -9 "$pid" 2>/dev/null || true
    fi
done
```

### 3. 详细的进程信息显示
在清理前显示将要清理的进程详细信息：

```bash
echo -e "${BLUE}   📋 发现项目相关的Node.js进程:${NC}"
for pid in $ALL_PIDS; do
    if [ -n "$pid" ] && ps -p "$pid" >/dev/null 2>&1; then
        ps -p "$pid" -o pid,ppid,command | tail -1 | while read line; do
            echo "     $line"
        done
    fi
done
```

## 安全特性

### ✅ 只清理项目相关进程
- 通过项目路径匹配
- 通过项目特定端口匹配
- 避免误杀其他项目的进程

### ✅ 详细的操作日志
- 显示将要清理的进程信息
- 提供清理前后的状态对比
- 便于问题诊断和确认

### ✅ 安全的清理流程
- 逐个验证进程存在性
- 使用精确的PID而非模糊匹配
- 避免批量误杀

## 更新的函数

### 1. `pre_startup_check()` 函数
- 启动前检查项目相关的残留进程
- 显示详细的进程信息
- 安全清理残留进程

### 2. `clean_frontend_thoroughly()` 函数
- 前端启动前的彻底清理
- 精确识别项目相关的Node.js进程
- 安全的进程清理流程

## 使用效果

### 更新前（危险）
```
🔧 清理Node.js进程...
# 可能清理所有系统中的vite/webpack进程
```

### 更新后（安全）
```
🔧 清理项目相关的Node.js进程...
📋 发现项目相关的Node.js进程:
     12345  1234 node /path/to/Assessment/frontend/uni-app/node_modules/.bin/vite
     12346  1234 node /path/to/Assessment/frontend/admin/node_modules/.bin/vite
✅ 项目相关的Node.js进程已清理
```

## 测试验证

1. **启动其他Node.js项目**
   ```bash
   # 在其他目录启动一个vite项目
   cd /other/project && npm run dev
   ```

2. **运行本项目脚本**
   ```bash
   cd /path/to/Assessment && ./scripts/dev-start-m4.sh
   ```

3. **验证其他项目未受影响**
   - 其他项目的Node.js进程应该继续运行
   - 只有本项目相关的进程被清理

## 注意事项

- 脚本现在更加安全，不会影响系统中其他Node.js项目
- 清理过程更加透明，会显示具体清理了哪些进程
- 如果需要手动清理，可以查看日志中的进程信息进行确认