# Maven Wrapper 修复总结

## 问题描述
在启动后端服务时遇到Maven Wrapper错误：
```
错误: 找不到或无法加载主类 org.apache.maven.wrapper.MavenWrapperMain
原因: java.lang.ClassNotFoundException: org.apache.maven.wrapper.MavenWrapperMain
```

## 根本原因
Maven Wrapper的jar文件损坏或大小异常：
- 正常大小应该是 ~50KB
- 损坏的文件只有 554 字节

## 解决方案

### 1. 手动修复（已完成）
```bash
cd backend
rm -f .mvn/wrapper/maven-wrapper.jar
curl -o .mvn/wrapper/maven-wrapper.jar https://repo.maven.apache.org/maven2/io/takari/maven-wrapper/0.5.6/maven-wrapper-0.5.6.jar
```

### 2. 脚本自动修复（已添加）
在 `dev-start-m4.sh` 中添加了自动检查和修复逻辑：

```bash
# 检查Maven Wrapper是否正常
if [ ! -f ".mvn/wrapper/maven-wrapper.jar" ] || [ $(stat -f%z ".mvn/wrapper/maven-wrapper.jar" 2>/dev/null || echo 0) -lt 10000 ]; then
    echo -e "${YELLOW}⚠️ Maven Wrapper损坏，正在修复...${NC}"
    rm -f .mvn/wrapper/maven-wrapper.jar
    echo -e "${BLUE}🔄 下载Maven Wrapper...${NC}"
    curl -s -o .mvn/wrapper/maven-wrapper.jar https://repo.maven.apache.org/maven2/io/takari/maven-wrapper/0.5.6/maven-wrapper-0.5.6.jar
    if [ -f ".mvn/wrapper/maven-wrapper.jar" ] && [ $(stat -f%z ".mvn/wrapper/maven-wrapper.jar") -gt 10000 ]; then
        echo -e "${GREEN}✅ Maven Wrapper修复成功${NC}"
    else
        echo -e "${RED}❌ Maven Wrapper修复失败${NC}"
        exit 1
    fi
fi
```

## 修复特性

### ✅ 自动检测
- 检查Maven Wrapper jar文件是否存在
- 检查文件大小是否正常（>10KB）

### ✅ 自动修复
- 自动删除损坏的文件
- 从Maven中央仓库下载正确的版本
- 验证下载的文件大小

### ✅ 错误处理
- 下载失败时显示错误信息
- 验证失败时退出脚本

### ✅ 静默下载
- 使用 `curl -s` 避免冗余输出
- 添加 `--no-transfer-progress` 减少Maven输出

## 验证结果

### Maven Wrapper状态
```bash
$ ls -lh backend/.mvn/wrapper/maven-wrapper.jar
-rw-r--r--@ 1 <USER>  <GROUP>    50K Jun 25 04:48 maven-wrapper.jar
```

### 版本检查
```bash
$ cd backend && ./mvnw --version
Apache Maven 3.9.10 (5f519b97e944483d878815739f519b2eade0a91d)
Maven home: /Users/<USER>/.m2/wrapper/dists/apache-maven-3.9.10-bin/...
Java version: 21.0.7, vendor: Homebrew
```

### 编译测试
```bash
$ cd backend && ./mvnw clean compile -DskipTests --no-transfer-progress
[INFO] BUILD SUCCESS
[INFO] Total time:  2.694 s
```

## 现在的状态
✅ Maven Wrapper正常工作
✅ 编译功能正常
✅ 脚本具备自动修复能力
✅ 错误处理完善

现在 `dev-start-m4.sh` 脚本应该能够：
1. 自动检测Maven Wrapper问题
2. 自动修复损坏的文件
3. 正常编译和启动后端服务