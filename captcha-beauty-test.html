<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码美化效果测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 32px;
            margin-bottom: 12px;
            font-weight: 700;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 18px;
        }
        
        .improvements {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }
        
        .improvements h3 {
            margin-bottom: 15px;
            font-size: 22px;
        }
        
        .improvements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .improvement-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }
        
        .improvement-item h4 {
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .improvement-item p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .test-section {
            padding: 40px;
            text-align: center;
        }
        
        .test-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .test-btn.success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .captcha-display {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        
        .captcha-images {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
        }
        
        .captcha-item {
            text-align: center;
        }
        
        .captcha-item h4 {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .captcha-item img {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        @media (max-width: 768px) {
            .captcha-images {
                flex-direction: column;
                gap: 20px;
            }
            
            .test-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .improvements-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 验证码美化效果测试</h1>
            <p>解决"黑块太难看"问题 - 优雅的滑动验证码设计</p>
        </div>
        
        <div class="improvements">
            <h3>✨ 美化改进要点</h3>
            <div class="improvements-grid">
                <div class="improvement-item">
                    <h4>🎯 缺口优化</h4>
                    <p>移除难看的黑色/灰色填充，使用白色边框和淡蓝色阴影</p>
                </div>
                <div class="improvement-item">
                    <h4>🌈 滑块美化</h4>
                    <p>从刺眼的金黄/橙色改为柔和的蓝绿渐变色</p>
                </div>
                <div class="improvement-item">
                    <h4>✨ 高光效果</h4>
                    <p>添加内侧高光和柔和边框，增强立体感</p>
                </div>
                <div class="improvement-item">
                    <h4>🎨 色彩协调</h4>
                    <p>整体色调与天蓝色背景协调，视觉更舒适</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3 style="margin-bottom: 20px; color: #495057;">🧪 实时效果测试</h3>
            
            <div class="test-buttons">
                <button class="test-btn primary" onclick="testCaptcha()">🔄 获取新验证码</button>
                <a href="http://localhost:5275/login" class="test-btn success" target="_blank">🖥️ 查看登录页面</a>
            </div>
            
            <div id="status" class="status info">
                点击"获取新验证码"按钮查看美化效果
            </div>
            
            <div id="captcha-display" class="captcha-display">
                <p style="color: #6c757d; font-size: 18px;">准备显示验证码...</p>
            </div>
        </div>
    </div>

    <script>
        async function testCaptcha() {
            const statusDiv = document.getElementById('status');
            const displayDiv = document.getElementById('captcha-display');
            
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 正在获取新的美化验证码...';
            displayDiv.innerHTML = '<p style="color: #6c757d;">加载中...</p>';
            
            try {
                const response = await fetch('http://localhost:8181/api/captcha/get', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data) {
                    const { originalImageBase64, jigsawImageBase64, y, token } = data.data;
                    
                    displayDiv.innerHTML = `
                        <div style="margin-bottom: 20px;">
                            <h4 style="color: #495057; margin-bottom: 15px;">🎨 美化后的验证码效果</h4>
                        </div>
                        <div class="captcha-images">
                            <div class="captcha-item">
                                <h4>🖼️ 背景图片</h4>
                                <img src="data:image/png;base64,${originalImageBase64}" 
                                     style="max-width: 300px;" 
                                     alt="验证码背景"/>
                                <p style="font-size: 12px; color: #6c757d; margin-top: 8px;">
                                    ✅ 缺口：白色边框 + 淡蓝色阴影
                                </p>
                            </div>
                            <div class="captcha-item">
                                <h4>🧩 滑块拼图</h4>
                                <img src="data:image/png;base64,${jigsawImageBase64}" 
                                     style="max-width: 60px;" 
                                     alt="验证码滑块"/>
                                <p style="font-size: 12px; color: #6c757d; margin-top: 8px;">
                                    ✅ 蓝绿渐变 + 高光效果
                                </p>
                            </div>
                        </div>
                        <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 8px;">
                            <p style="font-size: 14px; color: #0066cc; margin: 0;">
                                <strong>📍 技术参数:</strong> Y坐标: ${y} | Token: ${token.substring(0, 20)}...
                            </p>
                        </div>
                    `;
                    
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        ✅ 美化验证码生成成功！<br>
                        <small>对比之前的黑块，现在是不是看起来舒服多了？</small>
                    `;
                } else {
                    throw new Error('API返回数据格式错误');
                }
                
            } catch (error) {
                console.error('获取验证码失败:', error);
                displayDiv.innerHTML = `<p style="color: #dc3545;">❌ 获取失败: ${error.message}</p>`;
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 获取验证码失败: ${error.message}`;
            }
        }

        // 页面加载时自动测试一次
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => testCaptcha(), 1000);
        });
    </script>
</body>
</html>