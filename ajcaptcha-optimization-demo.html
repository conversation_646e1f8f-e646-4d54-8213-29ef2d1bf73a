<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AjCaptcha 验证码优化演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 36px;
            margin-bottom: 12px;
            font-weight: 700;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 18px;
        }
        
        .improvements {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }
        
        .improvements h3 {
            margin-bottom: 15px;
            font-size: 24px;
        }
        
        .improvements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .improvement-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .improvement-item h4 {
            margin-bottom: 8px;
            font-size: 18px;
        }
        
        .improvement-item p {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .demo-section {
            padding: 40px;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .demo-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .demo-btn.success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }
        
        .demo-btn.warning {
            background: linear-gradient(135deg, #f6ad55 0%, #f56565 100%);
            color: white;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .captcha-display {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        
        .captcha-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .captcha-item {
            text-align: center;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .captcha-item h4 {
            margin-bottom: 15px;
            color: #495057;
            font-size: 18px;
        }
        
        .captcha-item img {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-width: 100%;
            height: auto;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            text-align: center;
        }
        
        .before-after {
            background: #2d3748;
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin: 30px 0;
        }
        
        .before-after h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #f7fafc;
        }
        
        .comparison-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .comparison-column h4 {
            margin-bottom: 10px;
            color: #fed7d7;
        }
        
        .comparison-column.after h4 {
            color: #c6f6d5;
        }
        
        .comparison-column ul {
            list-style: none;
        }
        
        .comparison-column li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .comparison-column li::before {
            content: "❌";
            position: absolute;
            left: 0;
        }
        
        .comparison-column.after li::before {
            content: "✅";
        }
        
        @media (max-width: 768px) {
            .captcha-comparison,
            .comparison-list {
                grid-template-columns: 1fr;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .improvements-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 AjCaptcha 验证码优化演示</h1>
            <p>基于AjCaptcha专业图片资源的验证码美化效果</p>
        </div>
        
        <div class="improvements">
            <h3>🚀 核心优化亮点</h3>
            <div class="improvements-grid">
                <div class="improvement-item">
                    <h4>🖼️ 专业背景图片</h4>
                    <p>使用AjCaptcha官方的9张高质量背景图片，随机选择，告别单调的渐变色背景</p>
                </div>
                <div class="improvement-item">
                    <h4>🧩 真实拼图效果</h4>
                    <p>滑块直接从背景图中提取，形成真实的拼图块，而不是简单的彩色填充</p>
                </div>
                <div class="improvement-item">
                    <h4>✨ 精致边框处理</h4>
                    <p>多层边框效果：深色阴影 + 白色高光 + 轻微立体感，媲美专业设计</p>
                </div>
                <div class="improvement-item">
                    <h4>🎯 智能缺口设计</h4>
                    <p>完全透明的缺口区域配合精致边框，自然的高斯模糊边缘处理</p>
                </div>
            </div>
        </div>
        
        <div class="before-after">
            <h3>📊 优化前后对比</h3>
            <div class="comparison-list">
                <div class="comparison-column">
                    <h4>🔴 优化前的问题</h4>
                    <ul>
                        <li>单调的渐变色背景</li>
                        <li>简单的彩色滑块填充</li>
                        <li>难看的黑色缺口块</li>
                        <li>缺乏立体感和质感</li>
                        <li>边框效果单一粗糙</li>
                        <li>整体视觉效果不专业</li>
                    </ul>
                </div>
                <div class="comparison-column after">
                    <h4>🟢 优化后的效果</h4>
                    <ul>
                        <li>9张AjCaptcha专业背景</li>
                        <li>真实拼图图像提取</li>
                        <li>完全透明的自然缺口</li>
                        <li>多层次立体边框效果</li>
                        <li>高斯模糊自然过渡</li>
                        <li>达到商业级视觉质量</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3 style="margin-bottom: 20px; color: #495057;">🧪 实时效果测试</h3>
            
            <div class="demo-buttons">
                <button class="demo-btn primary" onclick="testCaptcha()">🔄 生成新验证码</button>
                <a href="http://localhost:5275/login" class="demo-btn success" target="_blank">🖥️ 登录页面测试</a>
                <button class="demo-btn warning" onclick="compareEffects()">⚖️ 效果对比</button>
            </div>
            
            <div id="status" class="status info">
                点击"生成新验证码"查看AjCaptcha优化效果
            </div>
            
            <div id="captcha-display" class="captcha-display">
                <p style="color: #6c757d; font-size: 18px;">准备显示优化后的验证码...</p>
            </div>
            
            <div class="feature-highlight">
                <h4 style="margin-bottom: 10px;">🌟 技术实现亮点</h4>
                <p>采用Java BufferedImage + Graphics2D实现，支持图像裁剪、形状遮罩、多层绘制、高斯模糊等高级图像处理技术</p>
            </div>
        </div>
    </div>

    <script>
        let comparisonMode = false;
        
        async function testCaptcha() {
            const statusDiv = document.getElementById('status');
            const displayDiv = document.getElementById('captcha-display');
            
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 正在生成AjCaptcha优化验证码...';
            displayDiv.innerHTML = '<p style="color: #6c757d;">加载中...</p>';
            
            try {
                const response = await fetch('http://localhost:8181/api/captcha/get', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data) {
                    const { originalImageBase64, jigsawImageBase64, y, token } = data.data;
                    
                    displayDiv.innerHTML = `
                        <div style="margin-bottom: 25px;">
                            <h4 style="color: #495057; margin-bottom: 15px;">🎨 AjCaptcha 专业验证码效果</h4>
                        </div>
                        <div class="captcha-comparison">
                            <div class="captcha-item">
                                <h4>🖼️ 专业背景图片</h4>
                                <img src="data:image/png;base64,${originalImageBase64}" 
                                     style="max-width: 310px;" 
                                     alt="AjCaptcha背景图"/>
                                <p style="font-size: 12px; color: #28a745; margin-top: 10px; font-weight: 500;">
                                    ✅ 高质量背景 + 精致缺口设计
                                </p>
                            </div>
                            <div class="captcha-item">
                                <h4>🧩 真实拼图滑块</h4>
                                <img src="data:image/png;base64,${jigsawImageBase64}" 
                                     style="max-width: 60px;" 
                                     alt="AjCaptcha拼图块"/>
                                <p style="font-size: 12px; color: #28a745; margin-top: 10px; font-weight: 500;">
                                    ✅ 图像提取 + 多层边框效果
                                </p>
                            </div>
                        </div>
                        <div style="margin-top: 25px; padding: 20px; background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%); border-radius: 12px;">
                            <h5 style="color: #1976d2; margin-bottom: 10px;">📋 技术参数</h5>
                            <div style="font-size: 14px; color: #424242; line-height: 1.6;">
                                <strong>Y坐标:</strong> ${y}px &nbsp;|&nbsp; 
                                <strong>Token:</strong> ${token.substring(0, 16)}... &nbsp;|&nbsp;
                                <strong>背景尺寸:</strong> 310×155px &nbsp;|&nbsp;
                                <strong>拼图尺寸:</strong> 47×47px
                            </div>
                        </div>
                    `;
                    
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        ✅ AjCaptcha优化验证码生成成功！<br>
                        <small>现在的效果是不是比之前的"黑块"好看多了？</small>
                    `;
                } else {
                    throw new Error('API返回数据格式错误');
                }
                
            } catch (error) {
                console.error('获取验证码失败:', error);
                displayDiv.innerHTML = `<p style="color: #dc3545;">❌ 获取失败: ${error.message}</p>`;
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 获取验证码失败: ${error.message}`;
            }
        }
        
        async function compareEffects() {
            comparisonMode = !comparisonMode;
            
            if (comparisonMode) {
                // 显示多个验证码进行对比
                const displayDiv = document.getElementById('captcha-display');
                displayDiv.innerHTML = `
                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #495057;">⚖️ 多样性效果展示 - 随机背景对比</h4>
                        <p style="color: #6c757d; font-size: 14px;">每次刷新都会随机选择不同的背景图片</p>
                    </div>
                    <div id="comparison-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <p style="color: #6c757d;">正在生成对比效果...</p>
                    </div>
                `;
                
                // 生成3个验证码进行对比
                const comparisonGrid = document.getElementById('comparison-grid');
                comparisonGrid.innerHTML = '';
                
                for (let i = 1; i <= 3; i++) {
                    try {
                        const response = await fetch('http://localhost:8181/api/captcha/get');
                        const data = await response.json();
                        
                        if (data.success && data.data) {
                            const div = document.createElement('div');
                            div.className = 'captcha-item';
                            div.innerHTML = `
                                <h5>样本 ${i}</h5>
                                <img src="data:image/png;base64,${data.data.originalImageBase64}" 
                                     style="max-width: 200px;" alt="对比样本${i}"/>
                                <p style="font-size: 11px; color: #666; margin-top: 5px;">
                                    随机背景图片
                                </p>
                            `;
                            comparisonGrid.appendChild(div);
                        }
                    } catch (error) {
                        console.error(`生成对比样本${i}失败:`, error);
                    }
                }
                
                document.querySelector('.demo-btn.warning').textContent = '🔄 单个效果';
            } else {
                testCaptcha();
                document.querySelector('.demo-btn.warning').textContent = '⚖️ 效果对比';
            }
        }

        // 页面加载时自动展示一个验证码
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => testCaptcha(), 1000);
        });
    </script>
</body>
</html>