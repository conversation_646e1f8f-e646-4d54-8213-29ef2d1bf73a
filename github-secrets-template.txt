# GitHub Secrets 配置模板
# 智能评估平台 - chang<PERSON>oyangbrain/assessment

## 必需配置 (CI/CD 核心功能)
CODECOV_TOKEN=你的_Codecov_Upload_Token

## 推荐配置 (增强功能)  
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=你的token
DOCKER_USERNAME=你的DockerHub用户名
DOCKER_PASSWORD=dckr_pat_你的访问令牌

## 可选配置 (部署功能)
DEV_HOST=dev.assessment.com
DEV_USERNAME=deploy
DEV_SSH_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
...
-----END OPENSSH PRIVATE KEY-----

PROD_HOST=prod.assessment.com  
PROD_USERNAME=deploy
PROD_SSH_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
...
-----END OPENSSH PRIVATE KEY-----

## 扩展服务 (可选)
SONAR_TOKEN=sqp_你的SonarCloud令牌
K6_CLOUD_TOKEN=你的K6Cloud令牌

# 配置步骤:
# 1. 访问 GitHub 仓库 Settings > Secrets and variables > Actions
# 2. 点击 "New repository secret"
# 3. 按照上述格式添加每个 Secret
# 4. 运行测试工作流验证配置: .github/workflows/test-secrets.yml
