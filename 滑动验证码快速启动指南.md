# 🚀 滑动验证码快速启动指南

## 概述
智能评估平台已完成自研滑动验证码功能集成，本指南帮助您快速启动和测试验证码功能。

## ✅ 完成的工作

### 后端实现
- ✅ `SimpleCaptchaService` - 自研验证码生成和校验服务
- ✅ `CaptchaController` - REST API接口
- ✅ `ApiResponse` - 统一响应格式
- ✅ `CaptchaVerifyRequest` - 请求参数封装
- ✅ 安全配置更新 - 允许验证码接口访问
- ✅ Redis集成 - 验证码数据缓存

### 前端实现
- ✅ uni-app验证码组件 - 移动端滑动拼图
- ✅ Vue3验证码组件 - 管理后台滑动拼图
- ✅ 登录页面集成 - 自动触发验证码
- ✅ API接口封装 - 完整的前后端对接

## 🔧 启动步骤

### 1. 后端服务启动
```bash
# 进入后端目录
cd /Volumes/acasis/Assessment/backend

# 启动Spring Boot应用
./mvnw spring-boot:run -Dserver.port=8181
```

### 2. 前端服务启动

#### uni-app移动端
```bash
# 进入uni-app目录
cd /Volumes/acasis/Assessment/frontend/uni-app

# 安装依赖
npm install

# 启动H5开发服务器
npm run dev:h5
```

#### Vue3管理后台
```bash
# 进入admin目录
cd /Volumes/acasis/Assessment/frontend/admin

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 3. 测试验证码功能

#### 方法1: 使用测试页面
```bash
# 在浏览器中打开
open /Volumes/acasis/Assessment/test-captcha.html
```

#### 方法2: 直接测试API
```bash
# 生成验证码
curl -X GET "http://localhost:8181/api/captcha/get"

# 校验验证码 (需要先获取token)
curl -X POST "http://localhost:8181/api/captcha/check" \
  -H "Content-Type: application/json" \
  -d '{"token":"your-token","pointJson":"{\"x\":120,\"y\":5}","verification":"your-secret"}'
```

#### 方法3: 运行测试脚本
```bash
# 运行完整测试脚本
./scripts/test-captcha-functionality.sh
```

## 📱 功能体验

### 登录触发验证码
1. 打开移动端或管理后台登录页面
2. 故意输入错误密码3次
3. 系统自动显示滑动验证码
4. 完成滑动验证后继续登录流程

### 手动测试验证码
1. 在登录页面点击"显示验证码"按钮
2. 拖拽滑块到正确位置
3. 观察验证结果反馈

## 🛠️ 故障排除

### 常见问题

#### 1. 后端服务启动失败
**检查**: 端口8181是否被占用
```bash
lsof -i :8181
```

**解决**: 更换端口或停止占用进程
```bash
./mvnw spring-boot:run -Dserver.port=8182
```

#### 2. 验证码不显示
**检查**: 控制台网络请求是否成功
**解决**: 确认后端API地址配置正确

#### 3. 滑动验证失败
**原因**: 位置精度不匹配
**解决**: 调整tolerance配置值

#### 4. Redis连接失败
**检查**: Redis服务是否启动
```bash
redis-cli ping
```

**解决**: 启动Redis或使用内存缓存

## 📊 核心配置

### application.yml
```yaml
# 验证码配置
captcha:
  simple:
    expire-minutes: 5    # 过期时间
    tolerance: 5         # 误差容忍度
    image:
      width: 310
      height: 155
      piece-width: 47
      piece-height: 47
```

### 安全配置
```java
// SecurityConfig.java
.requestMatchers("/api/captcha/**")
.permitAll() // 允许验证码接口访问
```

## 🎯 API接口地址

- **生成验证码**: `GET /api/captcha/get`
- **校验验证码**: `POST /api/captcha/check`
- **二次验证**: `POST /api/captcha/verify`
- **API文档**: `http://localhost:8181/swagger-ui.html`

## 📁 重要文件位置

### 后端
- 核心服务: `backend/src/main/java/com/assessment/service/SimpleCaptchaService.java`
- 控制器: `backend/src/main/java/com/assessment/controller/CaptchaController.java`
- 配置文件: `backend/src/main/resources/application.yml`

### 前端
- uni-app组件: `frontend/uni-app/src/components/AjCaptcha/index.vue`
- Vue3组件: `frontend/admin/src/components/AjCaptcha.vue`
- API接口: `frontend/*/src/api/captcha.js`

### 文档
- 功能说明: `docs/滑动验证码功能说明.md`
- 测试页面: `test-captcha.html`
- 测试脚本: `scripts/test-captcha-functionality.sh`

## 🎉 验证成功标志

当看到以下现象时，说明验证码功能工作正常：

1. ✅ 后端API返回包含Base64图片的JSON响应
2. ✅ 前端页面显示拼图验证码界面
3. ✅ 滑动操作流畅，有视觉反馈
4. ✅ 验证成功/失败有明确提示
5. ✅ 登录流程与验证码无缝集成

## 📞 技术支持

如遇问题，请参考：
- 📖 详细文档: `docs/滑动验证码功能说明.md`
- 🧪 测试页面: `test-captcha.html`
- 🔧 测试脚本: `scripts/test-captcha-functionality.sh`

---

**最后更新**: 2025-06-23  
**维护团队**: 智能评估平台开发组