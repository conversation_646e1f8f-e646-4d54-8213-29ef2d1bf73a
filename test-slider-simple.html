<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单滑块测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        
        .slider-container {
            width: 300px;
            height: 40px;
            margin: 20px auto;
            position: relative;
            background: #f0f2f5;
            border-radius: 20px;
            overflow: hidden;
        }
        
        .slider-track {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }
        
        .slider-fill {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            transition: width 0.3s ease;
        }
        
        .slider-button {
            position: absolute;
            top: 2px;
            width: 36px;
            height: 36px;
            background: white;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 2;
            user-select: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .slider-button:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .slider-button.verified {
            background: #4CAF50;
            color: white;
        }
        
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            background: #e3f2fd;
        }
    </style>
</head>
<body>
    <h1>简单滑块测试</h1>
    <p>测试滑动验证码的基本功能</p>
    
    <div class="status">
        <div>滑块位置: <span id="position">2</span>px</div>
        <div>拖拽状态: <span id="dragging">否</span></div>
        <div>验证状态: <span id="verified">未验证</span></div>
    </div>
    
    <div class="slider-container">
        <div class="slider-track">
            <span id="sliderText">拖动滑块完成验证</span>
        </div>
        <div class="slider-fill" id="sliderFill" style="width: 22px;"></div>
        <div class="slider-button" id="sliderButton" style="left: 2px;">
            <span id="sliderIcon">→</span>
        </div>
    </div>
    
    <button onclick="resetSlider()">重置滑块</button>
    <button onclick="clearLog()">清除日志</button>
    
    <div class="log" id="log"></div>

    <script>
        let isDragging = false;
        let sliderPosition = 2;
        let startX = 0;
        let startPosition = 0;
        let verified = false;
        
        const sliderButton = document.getElementById('sliderButton');
        const sliderFill = document.getElementById('sliderFill');
        const positionSpan = document.getElementById('position');
        const draggingSpan = document.getElementById('dragging');
        const verifiedSpan = document.getElementById('verified');
        const sliderText = document.getElementById('sliderText');
        const sliderIcon = document.getElementById('sliderIcon');
        const log = document.getElementById('log');
        
        function logMessage(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }
        
        function updateUI() {
            sliderButton.style.left = sliderPosition + 'px';
            sliderFill.style.width = (sliderPosition + 20) + 'px';
            positionSpan.textContent = sliderPosition;
            draggingSpan.textContent = isDragging ? '是' : '否';
            verifiedSpan.textContent = verified ? '已验证' : '未验证';
            
            if (verified) {
                sliderButton.classList.add('verified');
                sliderIcon.textContent = '✓';
                sliderText.textContent = '验证成功';
            } else {
                sliderButton.classList.remove('verified');
                sliderIcon.textContent = '→';
                sliderText.textContent = '拖动滑块完成验证';
            }
        }
        
        function getEventCoordinates(event) {
            if (event.touches && event.touches[0]) {
                return {
                    x: event.touches[0].clientX,
                    y: event.touches[0].clientY
                };
            } else if (event.changedTouches && event.changedTouches[0]) {
                return {
                    x: event.changedTouches[0].clientX,
                    y: event.changedTouches[0].clientY
                };
            } else {
                return {
                    x: event.clientX || 0,
                    y: event.clientY || 0
                };
            }
        }
        
        function startSlide(event) {
            if (verified) {
                logMessage('已验证，跳过');
                return;
            }
            
            logMessage(`开始滑动 (${event.type})`);
            isDragging = true;
            
            const coords = getEventCoordinates(event);
            startX = coords.x;
            startPosition = sliderPosition;
            
            logMessage(`起始位置: x=${startX}, position=${startPosition}`);
            updateUI();
            
            // 阻止默认行为
            event.preventDefault();
        }
        
        function handleMove(event) {
            if (!isDragging) return;
            
            const coords = getEventCoordinates(event);
            const diff = coords.x - startX;
            const maxPosition = 300 - 36 - 2; // 容器宽度 - 滑块宽度 - 边距
            const newPosition = Math.max(2, Math.min(maxPosition, startPosition + diff));
            
            sliderPosition = newPosition;
            updateUI();
            
            // 阻止默认行为（特别是触摸滚动）
            if (event.type === 'touchmove') {
                event.preventDefault();
            }
        }
        
        function handleEnd(event) {
            if (!isDragging) return;
            
            logMessage(`滑动结束 (${event.type})`);
            isDragging = false;
            
            // 移除事件监听器
            document.removeEventListener('mousemove', handleMove);
            document.removeEventListener('mouseup', handleEnd);
            document.removeEventListener('touchmove', handleMove);
            document.removeEventListener('touchend', handleEnd);
            
            // 检查是否验证成功
            const moveDistance = sliderPosition - 2;
            logMessage(`移动距离: ${moveDistance}px`);
            
            if (moveDistance > 200) {
                verified = true;
                logMessage('验证成功！');
            } else {
                logMessage('验证失败，请滑动到最右边');
                // 可以选择重置位置
                // resetSlider();
            }
            
            updateUI();
        }
        
        function resetSlider() {
            logMessage('重置滑块');
            isDragging = false;
            sliderPosition = 2;
            verified = false;
            
            // 移除可能存在的事件监听器
            document.removeEventListener('mousemove', handleMove);
            document.removeEventListener('mouseup', handleEnd);
            document.removeEventListener('touchmove', handleMove);
            document.removeEventListener('touchend', handleEnd);
            
            updateUI();
        }
        
        function clearLog() {
            log.innerHTML = '';
        }
        
        // 绑定事件
        sliderButton.addEventListener('mousedown', function(e) {
            startSlide(e);
            
            // 添加全局事件监听器
            document.addEventListener('mousemove', handleMove, { passive: true });
            document.addEventListener('mouseup', handleEnd, { passive: true });
        });
        
        sliderButton.addEventListener('touchstart', function(e) {
            startSlide(e);
            
            // 添加全局事件监听器
            document.addEventListener('touchmove', handleMove, { passive: false });
            document.addEventListener('touchend', handleEnd, { passive: true });
        });
        
        // 初始化
        updateUI();
        logMessage('滑块测试页面加载完成');
    </script>
</body>
</html>