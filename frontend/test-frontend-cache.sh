#!/bin/bash

echo "=== 前端验证码缓存检查工具 ==="
echo "时间: $(date)"
echo ""

# 检查前端开发服务器状态
echo "1. 检查前端开发服务器..."

# 检查uni-app开发服务器
if curl -s http://localhost:5273 > /dev/null 2>&1; then
    echo "✅ uni-app开发服务器正常运行 (端口5273)"
else
    echo "❌ uni-app开发服务器未运行"
fi

# 检查admin开发服务器  
if curl -s http://localhost:5274 > /dev/null 2>&1; then
    echo "✅ admin开发服务器正常运行 (端口5274)"
else
    echo "❌ admin开发服务器未运行"
fi

echo ""
echo "2. 测试前端验证码API调用..."

# 从前端的角度模拟API调用
echo "--- 模拟uni-app环境API调用 ---"
response_uniapp=$(curl -s -H "Origin: http://localhost:5273" \
                      -H "Referer: http://localhost:5273" \
                      -H "Cache-Control: no-cache" \
                      "http://localhost:8181/api/captcha/get")

if echo "$response_uniapp" | jq -r '.success' 2>/dev/null | grep -q true; then
    token_uniapp=$(echo "$response_uniapp" | jq -r '.data.token' 2>/dev/null)
    echo "✅ uni-app API调用成功, Token: ${token_uniapp:0:16}..."
else
    echo "❌ uni-app API调用失败"
fi

echo ""
echo "--- 模拟admin环境API调用 ---"
response_admin=$(curl -s -H "Origin: http://localhost:5274" \
                     -H "Referer: http://localhost:5274" \
                     -H "Cache-Control: no-cache" \
                     "http://localhost:8181/api/captcha/get")

if echo "$response_admin" | jq -r '.success' 2>/dev/null | grep -q true; then
    token_admin=$(echo "$response_admin" | jq -r '.data.token' 2>/dev/null)
    echo "✅ admin API调用成功, Token: ${token_admin:0:16}..."
else
    echo "❌ admin API调用失败"
fi

echo ""
echo "3. 检查浏览器可能的缓存问题..."

echo "--- 建议的解决步骤 ---"
echo "1. 在浏览器中按 Ctrl+Shift+R (或 Cmd+Shift+R) 强制刷新"
echo "2. 打开浏览器开发者工具 (F12):"
echo "   - 在Network标签页中勾选 'Disable cache'"
echo "   - 在Application标签页中清除Local Storage和Session Storage"
echo "3. 或者使用无痕/隐私模式重新打开页面"
echo "4. 测试地址:"
echo "   - 后端测试页面: http://localhost:8181/test-captcha-simple.html"
echo "   - uni-app页面: http://localhost:5273 (如果运行中)"
echo "   - admin页面: http://localhost:5274 (如果运行中)"

echo ""
echo "4. 快速验证新样式..."
echo "访问这个链接查看最新的验证码图片:"
echo "http://localhost:8181/api/captcha/get"
echo ""
echo "期望看到:"
echo "- 天蓝色渐变背景"
echo "- 白色网格线"
echo "- 时间戳水印"
echo "- 金黄色滑块"
echo "- 红色边框"

echo ""
echo "=== 前端缓存检查完成 ==="