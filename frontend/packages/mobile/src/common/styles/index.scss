// 导入变量和混合
@use './variables.scss' as *;
@use './mixins.scss' as *;

// 基础样式重置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
}

page {
  background-color: $bg-color;
  font-family: $font-family;
  font-size: $font-size-base;
  color: $text-color;
  line-height: $line-height-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 通用组件样式
.uni-container {
  padding: $spacing-base;
}

.uni-section {
  margin-bottom: $spacing-lg;
}

.uni-section-header {
  @include flex-between;
  margin-bottom: $spacing-sm;
  padding-bottom: $spacing-xs;
  border-bottom: 1rpx solid $border-color-light;
}

.uni-section-title {
  font-size: $font-size-lg;
  font-weight: bold;
  color: $text-color;
}

.uni-section-content {
  padding: $spacing-xs 0;
}

// 卡片样式
.uni-card {
  @include card;
  margin-bottom: $spacing-sm;
  
  &.uni-card-spacing {
    margin: $spacing-sm;
  }
  
  .uni-card-header {
    @include flex-between;
    margin-bottom: $spacing-sm;
    padding-bottom: $spacing-xs;
    border-bottom: 1rpx solid $border-color-light;
    
    .uni-card-title {
      font-size: $font-size-base;
      font-weight: bold;
      color: $text-color;
    }
    
    .uni-card-extra {
      font-size: $font-size-sm;
      color: $text-color-light;
    }
  }
  
  .uni-card-content {
    padding: $spacing-xs 0;
  }
  
  .uni-card-footer {
    margin-top: $spacing-sm;
    padding-top: $spacing-xs;
    border-top: 1rpx solid $border-color-light;
  }
}

// 列表样式
.uni-list {
  background-color: $bg-color-white;
  border-radius: $border-radius-lg;
  overflow: hidden;
  
  .uni-list-item {
    @include flex-between;
    min-height: $list-item-height;
    padding: 0 $list-item-padding;
    @include hairline(bottom, $list-item-border-color);
    @include transition;
    
    &:last-child::after {
      display: none;
    }
    
    &:active {
      background-color: $bg-color-light;
    }
    
    .uni-list-item-content {
      flex: 1;
      @include flex(column, flex-start, flex-start);
      
      .uni-list-item-title {
        font-size: $font-size-base;
        color: $text-color;
        margin-bottom: 4rpx;
      }
      
      .uni-list-item-note {
        font-size: $font-size-sm;
        color: $text-color-light;
      }
    }
    
    .uni-list-item-extra {
      @include flex(row, flex-end, center);
      
      .uni-list-item-extra-text {
        font-size: $font-size-sm;
        color: $text-color-light;
        margin-right: $spacing-xs;
      }
      
      .uni-list-item-arrow {
        width: 20rpx;
        height: 20rpx;
        border-top: 2rpx solid $text-color-lighter;
        border-right: 2rpx solid $text-color-lighter;
        transform: rotate(45deg);
      }
    }
  }
}

// 表单样式
.uni-form {
  .uni-form-item {
    margin-bottom: $spacing-base;
    
    .uni-form-item-label {
      font-size: $font-size-base;
      color: $text-color;
      margin-bottom: $spacing-xs;
      
      &.required::before {
        content: '*';
        color: $danger-color;
        margin-right: 4rpx;
      }
    }
    
    .uni-form-item-content {
      position: relative;
    }
    
    .uni-form-item-error {
      font-size: $font-size-sm;
      color: $danger-color;
      margin-top: 4rpx;
    }
  }
}

// 输入框样式
.uni-input {
  @include input-variant;
  
  &.uni-input-border {
    border: 1rpx solid $input-border-color;
  }
  
  &.uni-input-error {
    border-color: $danger-color;
  }
}

// 按钮样式
.uni-button {
  @include flex-center;
  height: $button-height;
  padding: $button-padding;
  border-radius: $button-border-radius;
  font-size: $button-font-size;
  border: none;
  cursor: pointer;
  @include transition;
  
  &.uni-button-primary {
    @include button-variant($text-color-white, $primary-color);
  }
  
  &.uni-button-secondary {
    @include button-variant($text-color-white, $secondary-color);
  }
  
  &.uni-button-success {
    @include button-variant($text-color-white, $success-color);
  }
  
  &.uni-button-warning {
    @include button-variant($text-color-white, $warning-color);
  }
  
  &.uni-button-danger {
    @include button-variant($text-color-white, $danger-color);
  }
  
  &.uni-button-info {
    @include button-variant($text-color-white, $info-color);
  }
  
  &.uni-button-default {
    @include button-variant($text-color, $bg-color-white, $border-color);
    border: 1rpx solid $border-color;
  }
  
  &.uni-button-plain {
    background-color: transparent;
    
    &.uni-button-primary {
      color: $primary-color;
      border: 1rpx solid $primary-color;
    }
    
    &.uni-button-secondary {
      color: $secondary-color;
      border: 1rpx solid $secondary-color;
    }
    
    &.uni-button-success {
      color: $success-color;
      border: 1rpx solid $success-color;
    }
    
    &.uni-button-warning {
      color: $warning-color;
      border: 1rpx solid $warning-color;
    }
    
    &.uni-button-danger {
      color: $danger-color;
      border: 1rpx solid $danger-color;
    }
    
    &.uni-button-info {
      color: $info-color;
      border: 1rpx solid $info-color;
    }
  }
  
  &.uni-button-small {
    height: 60rpx;
    padding: 0 20rpx;
    font-size: $font-size-sm;
  }
  
  &.uni-button-large {
    height: 100rpx;
    padding: 0 40rpx;
    font-size: $font-size-lg;
  }
  
  &.uni-button-round {
    border-radius: calc($button-height / 2);
  }
  
  &.uni-button-circle {
    width: $button-height;
    padding: 0;
    border-radius: 50%;
  }
  
  &.uni-button-block {
    width: 100%;
  }
  
  &.uni-button-loading {
    position: relative;
    color: transparent;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      @include loading-spin(32rpx, currentColor);
    }
  }
}

// 标签样式
.uni-tag {
  display: inline-flex;
  align-items: center;
  padding: 4rpx 12rpx;
  font-size: $font-size-sm;
  border-radius: $border-radius-sm;
  
  &.uni-tag-primary {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
  }
  
  &.uni-tag-success {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
  }
  
  &.uni-tag-warning {
    background-color: rgba($warning-color, 0.1);
    color: $warning-color;
  }
  
  &.uni-tag-danger {
    background-color: rgba($danger-color, 0.1);
    color: $danger-color;
  }
  
  &.uni-tag-info {
    background-color: rgba($info-color, 0.1);
    color: $info-color;
  }
  
  &.uni-tag-default {
    background-color: $bg-color-light;
    color: $text-color-light;
  }
}

// 徽章样式
.uni-badge {
  position: relative;
  display: inline-block;
  
  .uni-badge-content {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    min-width: 32rpx;
    height: 32rpx;
    padding: 0 8rpx;
    background-color: $danger-color;
    color: $text-color-white;
    font-size: 20rpx;
    line-height: 32rpx;
    text-align: center;
    border-radius: 16rpx;
    transform: scale(0.8);
    
    &.uni-badge-dot {
      width: 16rpx;
      height: 16rpx;
      min-width: auto;
      padding: 0;
      border-radius: 50%;
    }
  }
}

// 分割线样式
.uni-divider {
  @include flex-center;
  margin: $spacing-base 0;
  
  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1rpx;
    background-color: $border-color;
  }
  
  .uni-divider-content {
    padding: 0 $spacing-sm;
    font-size: $font-size-sm;
    color: $text-color-light;
  }
  
  &.uni-divider-plain {
    &::before,
    &::after {
      display: none;
    }
  }
}

// 加载状态样式
.uni-loading {
  @include flex-center;
  padding: $spacing-lg;
  
  .uni-loading-icon {
    @include loading-spin;
    margin-right: $spacing-xs;
  }
  
  .uni-loading-text {
    font-size: $font-size-sm;
    color: $text-color-light;
  }
}

// 空状态样式
.uni-empty {
  @include flex-center;
  @include flex(column);
  padding: $spacing-xl;
  
  .uni-empty-icon {
    width: $empty-size;
    height: $empty-size;
    margin-bottom: $spacing-base;
    opacity: 0.6;
  }
  
  .uni-empty-text {
    font-size: $font-size-base;
    color: $empty-color;
    margin-bottom: $spacing-sm;
  }
  
  .uni-empty-description {
    font-size: $font-size-sm;
    color: $text-color-lighter;
    text-align: center;
    line-height: $line-height-lg;
  }
}

// 工具类
.uni-text-center { text-align: center; }
.uni-text-left { text-align: left; }
.uni-text-right { text-align: right; }

.uni-text-primary { color: $primary-color; }
.uni-text-success { color: $success-color; }
.uni-text-warning { color: $warning-color; }
.uni-text-danger { color: $danger-color; }
.uni-text-info { color: $info-color; }
.uni-text-muted { color: $text-color-light; }

.uni-bg-primary { background-color: $primary-color; }
.uni-bg-success { background-color: $success-color; }
.uni-bg-warning { background-color: $warning-color; }
.uni-bg-danger { background-color: $danger-color; }
.uni-bg-info { background-color: $info-color; }
.uni-bg-light { background-color: $bg-color-light; }
.uni-bg-white { background-color: $bg-color-white; }

.uni-font-bold { font-weight: bold; }
.uni-font-normal { font-weight: normal; }

.uni-text-xs { font-size: $font-size-xs; }
.uni-text-sm { font-size: $font-size-sm; }
.uni-text-base { font-size: $font-size-base; }
.uni-text-lg { font-size: $font-size-lg; }
.uni-text-xl { font-size: $font-size-xl; }
.uni-text-2xl { font-size: $font-size-2xl; }

.uni-rounded { border-radius: $border-radius-base; }
.uni-rounded-sm { border-radius: $border-radius-sm; }
.uni-rounded-lg { border-radius: $border-radius-lg; }
.uni-rounded-xl { border-radius: $border-radius-xl; }
.uni-rounded-full { border-radius: $border-radius-full; }

.uni-shadow { @include box-shadow; }
.uni-shadow-sm { @include box-shadow($box-shadow-sm); }
.uni-shadow-lg { @include box-shadow($box-shadow-lg); }
.uni-shadow-xl { @include box-shadow($box-shadow-xl); }

.uni-border { @include border; }
.uni-border-t { @include border; border-bottom: none; border-left: none; border-right: none; }
.uni-border-b { @include border; border-top: none; border-left: none; border-right: none; }
.uni-border-l { @include border; border-top: none; border-bottom: none; border-right: none; }
.uni-border-r { @include border; border-top: none; border-bottom: none; border-left: none; }

.uni-hidden { display: none; }
.uni-visible { visibility: visible; }
.uni-invisible { visibility: hidden; }

.uni-relative { position: relative; }
.uni-absolute { position: absolute; }
.uni-fixed { position: fixed; }
.uni-sticky { position: sticky; }

.uni-overflow-hidden { overflow: hidden; }
.uni-overflow-auto { overflow: auto; }
.uni-overflow-scroll { overflow: scroll; }

.uni-ellipsis { @include text-ellipsis(1); }
.uni-ellipsis-2 { @include text-ellipsis(2); }
.uni-ellipsis-3 { @include text-ellipsis(3); }