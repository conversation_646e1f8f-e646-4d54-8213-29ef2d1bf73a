import { defineStore } from 'pinia'

// 老人信息类型定义
export interface Elderly {
  id: string | number
  name: string
  gender: 'male' | 'female'
  age: number
  birthday?: string
  idCard?: string
  phone?: string
  address?: string
  emergencyContact?: string
  emergencyPhone?: string
  medicalHistory?: string
  allergies?: string
  medications?: string
  notes?: string
  status: 'active' | 'inactive' | 'discharged'
  createTime?: string
  updateTime?: string
  avatar?: string
  roomNumber?: string
  bedNumber?: string
  admissionDate?: string
}

// 老人统计信息类型定义
export interface ElderlyStats {
  totalCount: number
  maleCount: number
  femaleCount: number
  averageAge: number
  ageDistribution: {
    '60-70': number
    '70-80': number
    '80-90': number
    '90+': number
  }
  assessmentStats: {
    totalAssessments: number
    completedAssessments: number
    averageScore: number
  }
  activeCount?: number
  newCount?: number
}

// 搜索参数类型定义
export interface SearchParams {
  keyword: string
  gender: string
  ageRange: string
  status: string
  sortBy: string
  sortOrder: string
}

// 分页信息类型定义
export interface Pagination {
  current: number
  pageSize: number
  total: number
}

// Store 状态类型定义
interface ElderlyState {
  elderlyList: Elderly[]
  currentElderly: Elderly | null
  elderlyDetail: Elderly | null
  elderlyStats: ElderlyStats
  searchParams: SearchParams
  pagination: Pagination
}

export const useElderlyStore = defineStore('elderly', {
  state: (): ElderlyState => ({
    elderlyList: [],
    currentElderly: null,
    elderlyDetail: null,
    elderlyStats: {
      totalCount: 0,
      maleCount: 0,
      femaleCount: 0,
      averageAge: 0,
      ageDistribution: {
        '60-70': 0,
        '70-80': 0,
        '80-90': 0,
        '90+': 0
      },
      assessmentStats: {
        totalAssessments: 0,
        completedAssessments: 0,
        averageScore: 0
      }
    },
    searchParams: {
      keyword: '',
      gender: '',
      ageRange: '',
      status: '',
      sortBy: 'createTime',
      sortOrder: 'desc'
    },
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0
    }
  }),

  getters: {
    // 老人列表
    elderlyList: (state): Elderly[] => state.elderlyList,

    // 当前选中的老人
    currentElderly: (state): Elderly | null => state.currentElderly,

    // 老人详情
    elderlyDetail: (state): Elderly | null => state.elderlyDetail,

    // 老人统计信息
    elderlyStats: (state): ElderlyStats => state.elderlyStats,

    // 搜索条件
    searchParams: (state): SearchParams => state.searchParams,

    // 分页信息
    pagination: (state): Pagination => state.pagination,

    // 根据ID获取老人信息
    getElderlyById: (state) => (id: string | number): Elderly | undefined => {
      return state.elderlyList.find(elderly => elderly.id === id)
    },

    // 获取男性老人数量
    maleElderlyCount: (state): number => {
      return state.elderlyList.filter(elderly => elderly.gender === 'male').length
    },

    // 获取女性老人数量
    femaleElderlyCount: (state): number => {
      return state.elderlyList.filter(elderly => elderly.gender === 'female').length
    },

    // 获取平均年龄
    averageAge: (state): number => {
      if (state.elderlyList.length === 0) return 0

      const totalAge = state.elderlyList.reduce((sum, elderly) => {
        const age = elderly.age || 0
        return sum + age
      }, 0)

      return Math.round(totalAge / state.elderlyList.length)
    },

    // 获取年龄分布
    ageDistribution: (state): ElderlyStats['ageDistribution'] => {
      const distribution = {
        '60-70': 0,
        '70-80': 0,
        '80-90': 0,
        '90+': 0
      }

      state.elderlyList.forEach(elderly => {
        const age = elderly.age || 0
        if (age >= 60 && age < 70) {
          distribution['60-70']++
        } else if (age >= 70 && age < 80) {
          distribution['70-80']++
        } else if (age >= 80 && age < 90) {
          distribution['80-90']++
        } else if (age >= 90) {
          distribution['90+']++
        }
      })

      return distribution
    },

    // 检查是否有搜索条件
    hasSearchParams: (state): boolean => {
      const { keyword, gender, ageRange, status } = state.searchParams
      return !!(keyword || gender || ageRange || status)
    },

    // 获取搜索结果数量
    searchResultCount: (state): number => {
      return state.pagination.total
    },

    // 检查是否为空列表
    isEmptyList: (state): boolean => {
      return state.elderlyList.length === 0
    }
  },

  actions: {
    // 设置老人列表
    setElderlyList(list: Elderly[]) {
      this.elderlyList = list
    },

    // 设置当前老人
    setCurrentElderly(elderly: Elderly | null) {
      this.currentElderly = elderly
    },

    // 设置老人详情
    setElderlyDetail(detail: Elderly | null) {
      this.elderlyDetail = detail
    },

    // 设置老人统计
    setElderlyStats(stats: Partial<ElderlyStats>) {
      this.elderlyStats = { ...this.elderlyStats, ...stats }
    },

    // 设置搜索参数
    setSearchParams(params: Partial<SearchParams>) {
      this.searchParams = { ...this.searchParams, ...params }
    },

    // 设置分页信息
    setPagination(pagination: Partial<Pagination>) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 获取老人列表
    async getElderlyList(params: any = {}) {
      const searchParams = { ...this.searchParams, ...params }
      const pagination = { ...this.pagination, ...params }

      // 这里应该调用API获取老人列表
      // const response = await getElderlyList({
      //   ...searchParams,
      //   page: pagination.current,
      //   pageSize: pagination.pageSize
      // })
      //
      // this.setElderlyList(response.data.list)
      // this.setPagination({
      //   current: response.data.current,
      //   pageSize: response.data.pageSize,
      //   total: response.data.total
      // })
      // return response

      // 临时模拟数据
      const mockData = {
        list: [],
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: 0
      }

      this.setElderlyList(mockData.list)
      this.setPagination({
        current: mockData.current,
        pageSize: mockData.pageSize,
        total: mockData.total
      })

      return { data: mockData }
    },

    // 获取老人详情
    async getElderlyDetail(elderlyId: string | number) {
      // 这里应该调用API获取老人详情
      // const response = await getElderlyDetail(elderlyId)
      // this.setElderlyDetail(response.data)
      // return response

      // 临时返回空数据
      this.setElderlyDetail(null)
      return { data: null }
    },

    // 创建老人
    async createElderly(elderlyData: Partial<Elderly>) {
      // 这里应该调用API创建老人
      // const response = await createElderly(elderlyData)
      // this.addElderly(response.data)
      // return response

      // 临时模拟创建成功
      const mockElderly: Elderly = {
        id: Date.now(),
        ...elderlyData,
        name: elderlyData.name || '',
        gender: elderlyData.gender || 'male',
        age: elderlyData.age || 0,
        status: elderlyData.status || 'active',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }

      this.addElderly(mockElderly)
      return { data: mockElderly }
    },

    // 更新老人信息
    async updateElderly({ id, ...elderlyData }: Partial<Elderly> & { id: string | number }) {
      // 这里应该调用API更新老人信息
      // const response = await updateElderly(id, elderlyData)
      // this.updateElderlyInfo(response.data)
      // return response

      // 临时模拟更新成功
      const mockElderly: Elderly = {
        id,
        ...elderlyData,
        name: elderlyData.name || '',
        gender: elderlyData.gender || 'male',
        age: elderlyData.age || 0,
        status: elderlyData.status || 'active',
        updateTime: new Date().toISOString()
      }

      this.updateElderlyInfo(mockElderly)
      return { data: mockElderly }
    },

    // 删除老人
    async deleteElderly(elderlyId: string | number) {
      // 这里应该调用API删除老人
      // await deleteElderly(elderlyId)

      this.removeElderly(elderlyId)
    },

    // 批量删除老人
    async batchDeleteElderly(elderlyIds: (string | number)[]) {
      // 这里应该调用API批量删除老人
      // await batchDeleteElderly(elderlyIds)

      elderlyIds.forEach(id => {
        this.removeElderly(id)
      })
    },

    // 导入老人数据
    async importElderlyData(file: any) {
      // 这里应该调用API导入老人数据
      // const response = await importElderlyData(file)
      //
      // // 导入成功后刷新列表
      // await this.getElderlyList()
      // return response

      // 临时模拟导入成功
      await this.getElderlyList()
      return { data: { successCount: 0, failCount: 0, errors: [] } }
    },

    // 导出老人数据
    async exportElderlyData(params: any = {}) {
      // 这里应该调用API导出老人数据
      // const response = await exportElderlyData({
      //   ...this.searchParams,
      //   ...params
      // })
      // return response

      // 临时返回空数据
      return { data: null }
    },

    // 获取老人统计信息
    async getElderlyStats() {
      // 这里应该调用API获取老人统计信息
      // const response = await getElderlyStats()
      // this.setElderlyStats(response.data)
      // return response

      // 临时模拟数据
      const mockStats: ElderlyStats = {
        totalCount: 0,
        maleCount: 0,
        femaleCount: 0,
        averageAge: 0,
        ageDistribution: {
          '60-70': 0,
          '70-80': 0,
          '80-90': 0,
          '90+': 0
        },
        assessmentStats: {
          totalAssessments: 0,
          completedAssessments: 0,
          averageScore: 0
        }
      }

      this.setElderlyStats(mockStats)
      return { data: mockStats }
    },

    // 搜索老人
    async searchElderly(searchParams: Partial<SearchParams>) {
      this.setSearchParams(searchParams)
      this.resetPagination()
      return await this.getElderlyList()
    },

    // 清空当前老人
    clearCurrentElderly() {
      this.setCurrentElderly(null)
    },

    // 重置搜索条件
    resetSearchParams() {
      this.setSearchParams({
        keyword: '',
        gender: '',
        ageRange: '',
        status: '',
        sortBy: 'createTime',
        sortOrder: 'desc'
      })
      this.resetPagination()
    },

    // 重置分页
    resetPagination() {
      this.setPagination({
        current: 1,
        pageSize: 20,
        total: 0
      })
    },

    // 获取老人的评估历史
    async getElderlyAssessmentHistory(elderlyId: string | number) {
      // 这里应该调用API获取老人的评估历史
      // const response = await getElderlyAssessmentHistory(elderlyId)
      // return response

      // 临时返回空数据
      return { data: { list: [], total: 0 } }
    },

    // 获取老人的最新评估结果
    async getElderlyLatestAssessment(elderlyId: string | number) {
      // 这里应该调用API获取老人的最新评估结果
      // const response = await getElderlyLatestAssessment(elderlyId)
      // return response

      // 临时返回空数据
      return { data: null }
    },

    // 获取老人统计数据（用于首页显示）
    async getElderlyStatistics() {
      try {
        // 临时模拟数据
        const statistics = {
          total: 156,
          activeCount: 89,
          newCount: 12
        }

        this.setElderlyStats({
          totalCount: statistics.total,
          activeCount: statistics.activeCount,
          newCount: statistics.newCount
        })

        return { data: statistics }
      } catch (error) {
        console.error('获取老人统计数据失败:', error)
        const defaultStats = { total: 0, activeCount: 0, newCount: 0 }
        return { data: defaultStats }
      }
    },

    // 内部方法：添加老人
    addElderly(elderly: Elderly) {
      this.elderlyList.unshift(elderly)
      this.pagination.total += 1
    },

    // 内部方法：更新老人信息
    updateElderlyInfo(elderly: Elderly) {
      const index = this.elderlyList.findIndex(item => item.id === elderly.id)
      if (index !== -1) {
        this.elderlyList.splice(index, 1, elderly)
      }

      // 如果是当前选中的老人，也要更新
      if (this.currentElderly && this.currentElderly.id === elderly.id) {
        this.currentElderly = elderly
      }

      // 如果是详情页的老人，也要更新
      if (this.elderlyDetail && this.elderlyDetail.id === elderly.id) {
        this.elderlyDetail = elderly
      }
    },

    // 内部方法：删除老人
    removeElderly(elderlyId: string | number) {
      const index = this.elderlyList.findIndex(item => item.id === elderlyId)
      if (index !== -1) {
        this.elderlyList.splice(index, 1)
        this.pagination.total -= 1
      }

      // 如果删除的是当前选中的老人，清空选中状态
      if (this.currentElderly && this.currentElderly.id === elderlyId) {
        this.currentElderly = null
      }

      // 如果删除的是详情页的老人，清空详情
      if (this.elderlyDetail && this.elderlyDetail.id === elderlyId) {
        this.elderlyDetail = null
      }
    }
  }
})