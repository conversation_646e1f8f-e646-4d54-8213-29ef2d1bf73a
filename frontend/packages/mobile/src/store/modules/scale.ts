import { defineStore } from 'pinia'

// 量表选项类型定义
export interface ScaleOption {
  label: string
  value: string
  score: number
}

// 量表问题类型定义
export interface ScaleQuestion {
  id: string | number
  type: 'single_choice' | 'multiple_choice' | 'text' | 'score'
  content: string
  required: boolean
  options?: ScaleOption[]
  description?: string
}

// 量表类型定义
export interface Scale {
  id: string | number
  name: string
  description?: string
  category: string
  status: 'active' | 'inactive' | 'draft'
  questionCount: number
  questions?: ScaleQuestion[]
  createTime?: string
  updateTime?: string
  version?: string
  maxScore?: number
  passingScore?: number
  timeLimit?: number
}

// 搜索参数类型定义
export interface ScaleSearchParams {
  keyword: string
  category: string
  status: string
}

// 分页信息类型定义
export interface ScalePagination {
  page: number
  pageSize: number
  total: number
}

// Store 状态类型定义
interface ScaleState {
  list: Scale[]
  total: number
  current: Scale | null
  pagination: ScalePagination
  loading: boolean
  searchParams: ScaleSearchParams
}

export const useScaleStore = defineStore('scale', {
  state: (): ScaleState => ({
    list: [],
    total: 0,
    current: null,
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    },
    loading: false,
    searchParams: {
      keyword: '',
      category: '',
      status: ''
    }
  }),

  getters: {
    // 量表列表
    scaleList: (state): Scale[] => state.list,

    // 当前量表
    currentScale: (state): Scale | null => state.current,

    // 量表总数
    scaleTotal: (state): number => state.total,

    // 分页信息
    scalePagination: (state): ScalePagination => state.pagination,

    // 是否加载中
    isLoading: (state): boolean => state.loading,

    // 搜索参数
    searchParams: (state): ScaleSearchParams => state.searchParams,

    // 根据分类获取量表
    getScalesByCategory: (state) => (category: string): Scale[] => {
      return state.list.filter(scale => scale.category === category)
    },

    // 获取活跃的量表
    getActiveScales: (state): Scale[] => {
      return state.list.filter(scale => scale.status === 'active')
    },

    // 根据ID获取量表
    getScaleById: (state) => (id: string | number): Scale | undefined => {
      return state.list.find(scale => scale.id === id)
    }
  },

  actions: {
    // 设置量表列表
    setScaleList(list: Scale[]) {
      this.list = list
    },

    // 设置量表总数
    setScaleTotal(total: number) {
      this.total = total
      this.pagination.total = total
    },

    // 设置当前量表
    setCurrentScale(scale: Scale | null) {
      this.current = scale
    },

    // 设置分页信息
    setPagination(pagination: Partial<ScalePagination>) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 设置加载状态
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // 设置搜索参数
    setSearchParams(params: Partial<ScaleSearchParams>) {
      this.searchParams = { ...this.searchParams, ...params }
    },

    // 获取量表列表
    async getScaleList(params: any = {}) {
      try {
        this.setLoading(true)

        const requestParams = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...this.searchParams,
          ...params
        }

        // 模拟API调用，实际应该调用真实API
        const mockData = {
          list: [
            {
              id: 1,
              name: '老年人能力评估量表',
              description: '用于评估老年人基本生活能力',
              category: 'ability',
              status: 'active' as const,
              questionCount: 25,
              createTime: '2024-01-15 10:30:00',
              updateTime: '2024-01-15 10:30:00'
            },
            {
              id: 2,
              name: '认知功能评估量表',
              description: '用于评估老年人认知功能状态',
              category: 'cognitive',
              status: 'active' as const,
              questionCount: 30,
              createTime: '2024-01-16 14:20:00',
              updateTime: '2024-01-16 14:20:00'
            }
          ],
          total: 2,
          page: requestParams.page,
          pageSize: requestParams.pageSize
        }

        this.setScaleList(mockData.list)
        this.setScaleTotal(mockData.total)
        this.setPagination({
          page: mockData.page,
          pageSize: mockData.pageSize
        })

        return mockData
      } catch (error) {
        console.error('获取量表列表失败:', error)
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取量表详情
    async getScaleDetail(scaleId: string | number) {
      try {
        this.setLoading(true)

        // 模拟API调用
        const mockScale: Scale = {
          id: scaleId,
          name: '老年人能力评估量表',
          description: '用于评估老年人基本生活能力',
          category: 'ability',
          status: 'active',
          questionCount: 25,
          questions: [
            {
              id: 1,
              type: 'single_choice',
              content: '您能否独立进行日常洗漱？',
              required: true,
              options: [
                { label: '完全可以', value: 'A', score: 4 },
                { label: '需要少量帮助', value: 'B', score: 3 },
                { label: '需要大量帮助', value: 'C', score: 2 },
                { label: '完全无法完成', value: 'D', score: 1 }
              ]
            }
          ],
          createTime: '2024-01-15 10:30:00',
          updateTime: '2024-01-15 10:30:00'
        }

        this.setCurrentScale(mockScale)
        return mockScale
      } catch (error) {
        console.error('获取量表详情失败:', error)
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 创建量表
    async createScale(scaleData: Partial<Scale>) {
      try {
        this.setLoading(true)

        // 模拟API调用
        const newScale: Scale = {
          id: Date.now(),
          ...scaleData,
          name: scaleData.name || '',
          category: scaleData.category || '',
          status: 'active',
          questionCount: scaleData.questionCount || 0,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        }

        this.addScale(newScale)
        return newScale
      } catch (error) {
        console.error('创建量表失败:', error)
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 更新量表
    async updateScale({ scaleId, ...scaleData }: { scaleId: string | number } & Partial<Scale>) {
      try {
        this.setLoading(true)

        // 模拟API调用
        const updatedScale: Scale = {
          id: scaleId,
          ...scaleData,
          name: scaleData.name || '',
          category: scaleData.category || '',
          status: scaleData.status || 'active',
          questionCount: scaleData.questionCount || 0,
          updateTime: new Date().toISOString()
        }

        this.updateScaleInfo(updatedScale)
        return updatedScale
      } catch (error) {
        console.error('更新量表失败:', error)
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 删除量表
    async deleteScale(scaleId: string | number) {
      try {
        this.setLoading(true)

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        this.removeScale(scaleId)
        return true
      } catch (error) {
        console.error('删除量表失败:', error)
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 搜索量表
    async searchScales(searchParams: Partial<ScaleSearchParams>) {
      this.setSearchParams(searchParams)
      this.setPagination({ page: 1 })
      return this.getScaleList()
    },

    // 清空量表列表
    clearScaleList() {
      this.list = []
      this.total = 0
      this.current = null
    },

    // 内部方法：添加量表
    addScale(scale: Scale) {
      this.list.unshift(scale)
      this.total++
    },

    // 内部方法：更新量表信息
    updateScaleInfo(updatedScale: Scale) {
      const index = this.list.findIndex(scale => scale.id === updatedScale.id)
      if (index !== -1) {
        this.list.splice(index, 1, updatedScale)
      }
    },

    // 内部方法：删除量表
    removeScale(scaleId: string | number) {
      const index = this.list.findIndex(scale => scale.id === scaleId)
      if (index !== -1) {
        this.list.splice(index, 1)
        this.total--
      }
    }
  }
})