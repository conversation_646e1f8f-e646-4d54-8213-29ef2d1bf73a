import { defineStore } from 'pinia'
import { login, logout, getUserInfo } from '../../api/auth'

// 用户信息类型定义
export interface UserInfo {
  id: string | number
  username: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  status: 'active' | 'inactive'
  roles: string[]
  createTime?: string
  tenantId?: string
}

// 用户列表类型定义
export interface UserListItem extends UserInfo {
  id: number
}

// 分页信息类型定义
export interface Pagination {
  page: number
  pageSize: number
  total: number
}

// Store 状态类型定义
interface UserState {
  token: string
  userInfo: UserInfo | null
  permissions: string[]
  roles: string[]
  list: UserListItem[]
  total: number
  pagination: Pagination
  loading: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: uni.getStorageSync('token') || '',
    userInfo: uni.getStorageSync('userInfo') || null,
    permissions: [],
    roles: [],
    list: [],
    total: 0,
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    },
    loading: false
  }),

  // 持久化配置
  persist: {
    storage: {
      getItem: (key: string) => uni.getStorageSync(key),
      setItem: (key: string, value: string) => uni.setStorageSync(key, value)
    }
  },

  getters: {
    // 是否已登录
    isLoggedIn: (state): boolean => !!state.token,

    // 用户ID
    userId: (state): string | number | undefined => state.userInfo?.id,

    // 用户名
    username: (state): string | undefined => state.userInfo?.username,

    // 用户昵称
    nickname: (state): string | undefined => 
      state.userInfo?.nickname || state.userInfo?.username,

    // 用户头像
    avatar: (state): string | undefined => state.userInfo?.avatar,

    // 用户角色
    userRoles: (state): string[] => state.roles,

    // 用户权限
    userPermissions: (state): string[] => state.permissions,

    // 检查是否有指定权限
    hasPermission: (state) => (permission: string): boolean => {
      return state.permissions.includes(permission)
    },

    // 检查是否有指定角色
    hasRole: (state) => (role: string): boolean => {
      return state.roles.includes(role)
    },

    // 检查是否有任一权限
    hasAnyPermission: (state) => (permissions: string[]): boolean => {
      return permissions.some(permission => state.permissions.includes(permission))
    },

    // 检查是否有任一角色
    hasAnyRole: (state) => (roles: string[]): boolean => {
      return roles.some(role => state.roles.includes(role))
    },

    // 用户列表
    userList: (state): UserListItem[] => state.list,

    // 用户总数
    userTotal: (state): number => state.total,

    // 分页信息
    userPagination: (state): Pagination => state.pagination,

    // 是否加载中
    userLoading: (state): boolean => state.loading
  },

  actions: {
    // 设置Token
    setToken(token: string) {
      this.token = token
      if (token) {
        uni.setStorageSync('token', token)
      } else {
        uni.removeStorageSync('token')
      }
    },

    // 设置用户信息
    setUserInfo(userInfo: UserInfo | null) {
      this.userInfo = userInfo
      if (userInfo) {
        uni.setStorageSync('userInfo', userInfo)
      } else {
        uni.removeStorageSync('userInfo')
      }
    },

    // 设置权限
    setPermissions(permissions: string[]) {
      this.permissions = permissions
    },

    // 设置角色
    setRoles(roles: string[]) {
      this.roles = roles
    },

    // 用户登录
    async login(loginForm: any) {
      try {
        const response = await login(loginForm)
        const { token, userInfo } = response.data

        this.token = token
        this.userInfo = userInfo
        uni.setStorageSync('token', token)
        uni.setStorageSync('userInfo', userInfo)

        return response
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        if (!this.token) {
          throw new Error('Token不存在')
        }

        const response = await getUserInfo()
        const { userInfo, permissions, roles } = response.data

        this.userInfo = userInfo
        this.permissions = permissions
        this.roles = roles
        uni.setStorageSync('userInfo', userInfo)

        return response
      } catch (error) {
        // token无效，清除登录状态
        this.token = ''
        this.userInfo = null
        this.permissions = []
        this.roles = []
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        throw error
      }
    },

    // 用户登出
    async logout() {
      try {
        await logout()
      } catch (error) {
        console.error('登出失败:', error)
      } finally {
        // 清除本地状态
        this.token = ''
        this.userInfo = null
        this.permissions = []
        this.roles = []
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')

        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/index'
        })
      }
    },

    // 验证token
    async validateToken(): Promise<boolean> {
      if (!this.token) {
        return false
      }

      try {
        const userInfo = await getUserInfo()
        this.userInfo = userInfo.data.userInfo
        this.permissions = userInfo.data.permissions || []
        this.roles = userInfo.data.roles || []
        return true
      } catch (error) {
        return false
      }
    },

    // 重置用户状态
    resetUserState() {
      this.token = ''
      this.userInfo = null
      this.permissions = []
      this.roles = []
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
    },

    // 获取用户列表
    async getUserList(params: any = {}) {
      try {
        this.loading = true

        const requestParams = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...params
        }

        // 模拟API调用，实际应该调用真实API
        const mockData = {
          list: [
            {
              id: 1,
              username: 'admin',
              nickname: '管理员',
              email: '<EMAIL>',
              phone: '138****1234',
              avatar: '',
              status: 'active' as const,
              roles: ['admin'],
              createTime: '2024-01-15 10:30:00'
            },
            {
              id: 2,
              username: 'nurse01',
              nickname: '护理员001',
              email: '<EMAIL>',
              phone: '139****5678',
              avatar: '',
              status: 'active' as const,
              roles: ['nurse'],
              createTime: '2024-01-16 14:20:00'
            }
          ],
          total: 2,
          page: requestParams.page,
          pageSize: requestParams.pageSize
        }

        this.list = mockData.list
        this.total = mockData.total
        this.pagination = {
          ...this.pagination,
          page: mockData.page,
          pageSize: mockData.pageSize,
          total: mockData.total
        }

        return mockData
      } catch (error) {
        console.error('获取用户列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 设置分页信息
    setPagination(pagination: Partial<Pagination>) {
      this.pagination = { ...this.pagination, ...pagination }
    }
  }
})