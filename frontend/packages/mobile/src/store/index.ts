import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

// 创建 Pinia 实例
const pinia = createPinia()

// 配置持久化插件，适配 uni-app 的存储接口
pinia.use(
  createPersistedState({
    storage: {
      getItem: (key: string) => {
        return uni.getStorageSync(key)
      },
      setItem: (key: string, value: string) => {
        uni.setStorageSync(key, value)
      }
      // removeItem 不是必需的，pinia-plugin-persistedstate 会处理
    }
  })
)

// 导出 Pinia 实例
export default pinia

// 导出所有 stores
export { useUserStore } from './modules/user'
export { useAssessmentStore } from './modules/assessment'
export { useElderlyStore } from './modules/elderly'
export { useScaleStore } from './modules/scale'
export { useConfigStore } from './modules/config'

// 导出常用类型
export type { UserInfo, UserListItem, Pagination as UserPagination } from './modules/user'
export type { Assessment, AssessmentQuestion, AssessmentSection, Scale as AssessmentScale, AssessmentProgress, AssessmentResult, AssessmentStats } from './modules/assessment'
export type { Elderly, ElderlyStats, SearchParams as ElderlySearchParams, Pagination as ElderlyPagination } from './modules/elderly'
export type { Scale, ScaleOption, ScaleQuestion, ScaleSearchParams, ScalePagination } from './modules/scale'
export type { AppConfig, SystemSettings, BusinessConfig, Dictionaries, DictItem } from './modules/config'