<template>
  <view class="picker-wrapper" :class="wrapperClass" @click="handleClick">
    <!-- 显示区域 -->
    <view class="picker-display">
      <!-- 前置图标 -->
      <view v-if="prefixIcon" class="picker-prefix">
        <text class="icon-text" :style="{ fontSize: iconSize + 'px', color: iconColor }">☰</text>
      </view>

      <!-- 显示文本 -->
      <view class="picker-text" :class="{ placeholder: !hasValue }">
        <text>{{ displayText }}</text>
      </view>

      <!-- 后置图标 -->
      <view class="picker-suffix">
        <text
          class="icon-text"
          :style="{ fontSize: iconSize + 'px', color: iconColor }"
          :class="{ rotate: visible }"
          >▼</text
        >
      </view>
    </view>

    <!-- 原生选择器 -->
    <picker
      v-if="mode === 'selector'"
      class="picker-native"
      :value="selectorIndex"
      :range="range"
      :range-key="rangeKey"
      :disabled="disabled"
      @change="handleSelectorChange"
      @cancel="handleCancel"
    />

    <picker
      v-else-if="mode === 'multiSelector'"
      class="picker-native"
      mode="multiSelector"
      :value="multiSelectorIndex"
      :range="range"
      :range-key="rangeKey"
      :disabled="disabled"
      @change="handleMultiSelectorChange"
      @columnchange="handleColumnChange"
      @cancel="handleCancel"
    />

    <picker
      v-else-if="mode === 'time'"
      class="picker-native"
      mode="time"
      :value="timeValue"
      :start="start"
      :end="end"
      :disabled="disabled"
      @change="handleTimeChange"
      @cancel="handleCancel"
    />

    <picker
      v-else-if="mode === 'date'"
      class="picker-native"
      mode="date"
      :value="dateValue"
      :start="start"
      :end="end"
      :fields="fields"
      :disabled="disabled"
      @change="handleDateChange"
      @cancel="handleCancel"
    />

    <picker
      v-else-if="mode === 'region'"
      class="picker-native"
      mode="region"
      :value="regionValue"
      :custom-item="customItem"
      :disabled="disabled"
      @change="handleRegionChange"
      @cancel="handleCancel"
    />
  </view>
</template>

<script>
export default {
  name: 'Picker',

  props: {
    // v-model 值
    modelValue: {
      type: [String, Number, Array],
      default: ''
    },

    // 选择器模式
    mode: {
      type: String,
      default: 'selector'
    },

    // 选择器数据
    range: {
      type: Array,
      default: () => []
    },

    // 当range是对象数组时，指定显示的key
    rangeKey: {
      type: String,
      default: ''
    },

    // 占位符
    placeholder: {
      type: String,
      default: '请选择'
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 前置图标
    prefixIcon: {
      type: String,
      default: ''
    },

    // 尺寸
    size: {
      type: String,
      default: 'medium'
    },

    // 时间选择器的开始时间
    start: {
      type: String,
      default: ''
    },

    // 时间选择器的结束时间
    end: {
      type: String,
      default: ''
    },

    // 日期选择器的精度
    fields: {
      type: String,
      default: 'day'
    },

    // 地区选择器的自定义项
    customItem: {
      type: String,
      default: ''
    },

    // 自定义格式化函数
    formatter: {
      type: Function,
      default: null
    }
  },
  emits: ['update:modelValue', 'change', 'cancel', 'columnchange'],

  data() {
    return {
      visible: false
    }
  },

  computed: {
    wrapperClass() {
      return {
        'picker-wrapper--disabled': this.disabled,
        [`picker-wrapper--${this.size}`]: true
      }
    },

    hasValue() {
      if (Array.isArray(this.modelValue)) {
        return this.modelValue.length > 0
      }
      return this.modelValue !== '' && this.modelValue !== null && this.modelValue !== undefined
    },

    displayText() {
      if (!this.hasValue) {
        return this.placeholder
      }

      if (this.formatter && typeof this.formatter === 'function') {
        return this.formatter(this.modelValue, this.range)
      }

      return this.getDisplayText()
    },

    selectorIndex() {
      if (this.mode !== 'selector') return 0

      if (this.rangeKey) {
        return this.range.findIndex(item => item[this.rangeKey] === this.modelValue)
      }

      return this.range.findIndex(item => item === this.modelValue)
    },

    multiSelectorIndex() {
      if (this.mode !== 'multiSelector') return []
      return Array.isArray(this.modelValue) ? this.modelValue : []
    },

    timeValue() {
      if (this.mode !== 'time') return ''
      return this.modelValue || ''
    },

    dateValue() {
      if (this.mode !== 'date') return ''
      return this.modelValue || ''
    },

    regionValue() {
      if (this.mode !== 'region') return []
      return Array.isArray(this.modelValue) ? this.modelValue : []
    },

    iconSize() {
      const sizeMap = {
        small: 16,
        medium: 18,
        large: 20
      }
      return sizeMap[this.size] || 18
    },

    iconColor() {
      return this.disabled ? '#c0c4cc' : '#909399'
    }
  },

  methods: {
    handleClick() {
      if (this.disabled) return
      this.visible = true
    },

    handleSelectorChange(e) {
      const index = e.detail.value
      const item = this.range[index]

      let value = item
      if (this.rangeKey && typeof item === 'object') {
        value = item[this.rangeKey]
      }

      this.$emit('update:modelValue', value)
      this.$emit('change', {
        value,
        index,
        item
      })

      this.visible = false
    },

    handleMultiSelectorChange(e) {
      const value = e.detail.value
      this.$emit('update:modelValue', value)
      this.$emit('change', {
        value,
        items: this.getMultiSelectorItems(value)
      })

      this.visible = false
    },

    handleColumnChange(e) {
      this.$emit('columnchange', e)
    },

    handleTimeChange(e) {
      const value = e.detail.value
      this.$emit('update:modelValue', value)
      this.$emit('change', { value })

      this.visible = false
    },

    handleDateChange(e) {
      const value = e.detail.value
      this.$emit('update:modelValue', value)
      this.$emit('change', { value })

      this.visible = false
    },

    handleRegionChange(e) {
      const value = e.detail.value
      this.$emit('update:modelValue', value)
      this.$emit('change', { value })

      this.visible = false
    },

    handleCancel() {
      this.$emit('cancel')
      this.visible = false
    },

    getDisplayText() {
      switch (this.mode) {
        case 'selector':
          return this.getSelectorDisplayText()
        case 'multiSelector':
          return this.getMultiSelectorDisplayText()
        case 'time':
        case 'date':
          return this.modelValue
        case 'region':
          return Array.isArray(this.modelValue) ? this.modelValue.join(' ') : ''
        default:
          return this.modelValue
      }
    },

    getSelectorDisplayText() {
      const index = this.selectorIndex
      if (index === -1) return this.modelValue

      const item = this.range[index]
      if (this.rangeKey && typeof item === 'object') {
        return item[this.rangeKey]
      }

      return item
    },

    getMultiSelectorDisplayText() {
      if (!Array.isArray(this.modelValue)) return ''

      const items = this.getMultiSelectorItems(this.modelValue)
      return items
        .map(item => {
          if (this.rangeKey && typeof item === 'object') {
            return item[this.rangeKey]
          }
          return item
        })
        .join(' ')
    },

    getMultiSelectorItems(indexes) {
      if (!Array.isArray(indexes) || !Array.isArray(this.range)) return []

      return indexes
        .map((index, columnIndex) => {
          const column = this.range[columnIndex]
          return Array.isArray(column) ? column[index] : null
        })
        .filter(item => item !== null)
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.picker-wrapper {
  position: relative;
  background-color: $bg-color-white;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-base;
  transition: border-color 0.2s;
  cursor: pointer;

  &:hover {
    border-color: $primary-color;
  }

  &.picker-wrapper--disabled {
    background-color: $bg-color-disabled;
    border-color: $border-color-light;
    cursor: not-allowed;

    .picker-text {
      color: $text-color-disabled;
    }
  }

  // 尺寸
  &.picker-wrapper--small {
    min-height: 32px;
    padding: $spacing-xs $spacing-sm;

    .picker-text text {
      font-size: $font-size-sm;
    }
  }

  &.picker-wrapper--medium {
    min-height: 40px;
    padding: $spacing-sm;

    .picker-text text {
      font-size: $font-size-base;
    }
  }

  &.picker-wrapper--large {
    min-height: 48px;
    padding: $spacing-sm $spacing-md;

    .picker-text text {
      font-size: $font-size-lg;
    }
  }
}

.picker-display {
  display: flex;
  align-items: center;
  width: 100%;
}

.picker-prefix {
  display: flex;
  align-items: center;
  margin-right: $spacing-xs;
  color: $text-color-secondary;
}

.picker-text {
  flex: 1;

  text {
    color: $text-color-primary;
    line-height: 1.4;
  }

  &.placeholder text {
    color: $text-color-placeholder;
  }
}

.picker-suffix {
  display: flex;
  align-items: center;
  margin-left: $spacing-xs;
  color: $text-color-secondary;
  transition: transform 0.2s;

  &.rotate {
    transform: rotate(180deg);
  }
}

.picker-native {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 1;
}
</style>
