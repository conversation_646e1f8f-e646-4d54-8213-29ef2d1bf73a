<template>
  <view class="form-item" :class="itemClass">
    <!-- 标签 -->
    <view v-if="label || $slots.label" class="form-label" :style="labelStyle">
      <text v-if="required" class="required-mark">*</text>
      <text v-if="label" class="label-text">{{ label }}</text>
      <slot name="label"></slot>
    </view>

    <!-- 表单控件容器 -->
    <view class="form-control" :style="controlStyle">
      <slot></slot>
    </view>

    <!-- 错误信息 -->
    <view v-if="errorMessage" class="form-error">
      <text class="error-text">{{ errorMessage }}</text>
    </view>

    <!-- 帮助信息 -->
    <view v-if="helpText" class="form-help">
      <text class="help-text">{{ helpText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FormItem',
  props: {
    // 标签文本
    label: {
      type: String,
      default: ''
    },

    // 是否必填
    required: {
      type: Boolean,
      default: false
    },

    // 错误信息
    errorMessage: {
      type: String,
      default: ''
    },

    // 帮助信息
    helpText: {
      type: String,
      default: ''
    },

    // 标签宽度
    labelWidth: {
      type: [String, Number],
      default: ''
    },

    // 标签位置 (top, left)
    labelPosition: {
      type: String,
      default: 'left'
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    },

    // 是否显示边框
    border: {
      type: Boolean,
      default: true
    },

    // 尺寸 (small, medium, large)
    size: {
      type: String,
      default: 'medium'
    }
  },

  computed: {
    itemClass() {
      return {
        'form-item--required': this.required,
        'form-item--error': !!this.errorMessage,
        'form-item--disabled': this.disabled,
        'form-item--no-border': !this.border,
        [`form-item--${this.labelPosition}`]: true,
        [`form-item--${this.size}`]: true,
        [this.customClass]: !!this.customClass
      }
    },

    labelStyle() {
      const style = {}

      if (this.labelWidth) {
        const width = typeof this.labelWidth === 'number' ? `${this.labelWidth}px` : this.labelWidth
        style.width = width
        style.minWidth = width
      }

      return style
    },

    controlStyle() {
      const style = {}

      if (this.labelPosition === 'left' && this.labelWidth) {
        const width = typeof this.labelWidth === 'number' ? `${this.labelWidth}px` : this.labelWidth
        style.marginLeft = width
      }

      return style
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.form-item {
  position: relative;
  margin-bottom: $spacing-md;

  &.form-item--no-border {
    .form-control {
      border: none;
      padding: 0;
    }
  }

  &.form-item--disabled {
    opacity: 0.6;

    .form-label {
      color: $text-color-disabled;
    }
  }

  &.form-item--error {
    .form-control {
      border-color: $error-color;
    }

    .form-label {
      color: $error-color;
    }
  }

  // 标签位置
  &.form-item--top {
    .form-label {
      margin-bottom: $spacing-xs;
    }

    .form-control {
      margin-left: 0 !important;
    }
  }

  &.form-item--left {
    display: flex;
    align-items: flex-start;

    .form-label {
      flex-shrink: 0;
      padding-top: $spacing-sm;
    }

    .form-control {
      flex: 1;
    }
  }

  // 尺寸
  &.form-item--small {
    margin-bottom: $spacing-sm;

    .form-label {
      font-size: $font-size-sm;
    }

    .form-control {
      min-height: 32px;
    }
  }

  &.form-item--medium {
    .form-label {
      font-size: $font-size-base;
    }

    .form-control {
      min-height: 40px;
    }
  }

  &.form-item--large {
    margin-bottom: $spacing-lg;

    .form-label {
      font-size: $font-size-lg;
    }

    .form-control {
      min-height: 48px;
    }
  }
}

.form-label {
  display: flex;
  align-items: center;
  color: $text-color-primary;
  font-weight: 500;
  line-height: 1.4;

  .required-mark {
    color: $error-color;
    margin-right: 2px;
    font-size: inherit;
  }

  .label-text {
    flex: 1;
  }
}

.form-control {
  position: relative;
  background-color: $bg-color-white;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-base;
  padding: $spacing-sm;
  transition: border-color 0.2s;

  &:focus-within {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
  }
}

.form-error {
  margin-top: $spacing-xs;

  .error-text {
    font-size: $font-size-sm;
    color: $error-color;
    line-height: 1.4;
  }
}

.form-help {
  margin-top: $spacing-xs;

  .help-text {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    line-height: 1.4;
  }
}
</style>
