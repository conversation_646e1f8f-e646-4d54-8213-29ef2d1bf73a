<template>
  <button
    class="btn"
    :class="buttonClass"
    :disabled="disabled || loading"
    :form-type="formType"
    :open-type="openType"
    :hover-class="hoverClass"
    :hover-start-time="hoverStartTime"
    :hover-stay-time="hoverStayTime"
    :lang="lang"
    :session-from="sessionFrom"
    :send-message-title="sendMessageTitle"
    :send-message-path="sendMessagePath"
    :send-message-img="sendMessageImg"
    :show-message-card="showMessageCard"
    :app-parameter="appParameter"
    @click="handleClick"
    @getuserinfo="handleGetUserInfo"
    @contact="handleContact"
    @getphonenumber="handleGetPhoneNumber"
    @error="handleError"
    @opensetting="handleOpenSetting"
    @launchapp="handleLaunchApp"
  >
    <!-- 加载图标 -->
    <view v-if="loading" class="btn-loading">
      <view class="loading-spinner" />
    </view>

    <!-- 前置图标 -->
    <view v-if="prefixIcon && !loading" class="btn-icon btn-icon--prefix">
      <text class="icon-text" :style="{ fontSize: iconSize + 'px' }">{{
        getPrefixIconText()
      }}</text>
    </view>

    <!-- 按钮文本 -->
    <view class="btn-text">
      <slot>
        <text>{{ text }}</text>
      </slot>
    </view>

    <!-- 后置图标 -->
    <view v-if="suffixIcon && !loading" class="btn-icon btn-icon--suffix">
      <text class="icon-text" :style="{ fontSize: iconSize + 'px' }">{{
        getSuffixIconText()
      }}</text>
    </view>
  </button>
</template>

<script>
export default {
  name: 'Button',

  props: {
    // 按钮文本
    text: {
      type: String,
      default: ''
    },

    // 按钮类型
    type: {
      type: String,
      default: 'default'
    },

    // 按钮尺寸
    size: {
      type: String,
      default: 'medium'
    },

    // 是否为朴素按钮
    plain: {
      type: Boolean,
      default: false
    },

    // 是否为圆形按钮
    round: {
      type: Boolean,
      default: false
    },

    // 是否为圆角按钮
    circle: {
      type: Boolean,
      default: false
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 是否加载中
    loading: {
      type: Boolean,
      default: false
    },

    // 是否块级按钮
    block: {
      type: Boolean,
      default: false
    },

    // 前置图标
    prefixIcon: {
      type: String,
      default: ''
    },

    // 后置图标
    suffixIcon: {
      type: String,
      default: ''
    },

    // 表单类型
    formType: {
      type: String,
      default: ''
    },

    // 开放能力
    openType: {
      type: String,
      default: ''
    },

    // 指定按下去的样式类
    hoverClass: {
      type: String,
      default: 'btn-hover'
    },

    // 按住后多久出现点击态
    hoverStartTime: {
      type: Number,
      default: 20
    },

    // 手指松开后点击态保留时间
    hoverStayTime: {
      type: Number,
      default: 70
    },

    // 指定返回用户信息的语言
    lang: {
      type: String,
      default: 'en'
    },

    // 会话来源
    sessionFrom: {
      type: String,
      default: ''
    },

    // 会话内消息卡片标题
    sendMessageTitle: {
      type: String,
      default: ''
    },

    // 会话内消息卡片点击跳转小程序路径
    sendMessagePath: {
      type: String,
      default: ''
    },

    // 会话内消息卡片图片
    sendMessageImg: {
      type: String,
      default: ''
    },

    // 是否显示会话内消息卡片
    showMessageCard: {
      type: Boolean,
      default: false
    },

    // 打开APP时向APP传递的参数
    appParameter: {
      type: String,
      default: ''
    }
  },
  emits: ['click', 'getuserinfo', 'contact', 'getphonenumber', 'error', 'opensetting', 'launchapp'],

  computed: {
    buttonClass() {
      return {
        [`btn--${this.type}`]: true,
        [`btn--${this.size}`]: true,
        'btn--plain': this.plain,
        'btn--round': this.round,
        'btn--circle': this.circle,
        'btn--disabled': this.disabled,
        'btn--loading': this.loading,
        'btn--block': this.block
      }
    },

    iconSize() {
      const sizeMap = {
        mini: 14,
        small: 16,
        medium: 18,
        large: 20
      }
      return sizeMap[this.size] || 18
    }
  },

  methods: {
    handleClick(e) {
      if (this.disabled || this.loading) return
      this.$emit('click', e)
    },

    handleGetUserInfo(e) {
      this.$emit('getuserinfo', e)
    },

    handleContact(e) {
      this.$emit('contact', e)
    },

    handleGetPhoneNumber(e) {
      this.$emit('getphonenumber', e)
    },

    handleError(e) {
      this.$emit('error', e)
    },

    handleOpenSetting(e) {
      this.$emit('opensetting', e)
    },

    handleLaunchApp(e) {
      this.$emit('launchapp', e)
    },

    getPrefixIconText() {
      return '🔧' // Generic prefix icon
    },

    getSuffixIconText() {
      return '➤' // Generic suffix icon
    }
  }
}
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  border-radius: $border-radius-base;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
  outline: none;

  &::after {
    border: none;
  }

  // 按钮类型
  &.btn--default {
    background-color: $bg-color-white;
    border-color: $border-color-base;
    color: $text-color-primary;

    &:hover {
      border-color: $primary-color;
      color: $primary-color;
    }

    &.btn--plain {
      background-color: transparent;
    }
  }

  &.btn--primary {
    background-color: $primary-color;
    border-color: $primary-color;
    color: $color-white;

    &:hover {
      background-color: color.adjust($primary-color, $lightness: 10%);
      border-color: color.adjust($primary-color, $lightness: 10%);
    }

    &.btn--plain {
      background-color: transparent;
      color: $primary-color;

      &:hover {
        background-color: $primary-color;
        color: $color-white;
      }
    }
  }

  &.btn--success {
    background-color: $success-color;
    border-color: $success-color;
    color: $color-white;

    &:hover {
      background-color: color.adjust($success-color, $lightness: 10%);
      border-color: color.adjust($success-color, $lightness: 10%);
    }

    &.btn--plain {
      background-color: transparent;
      color: $success-color;

      &:hover {
        background-color: $success-color;
        color: $color-white;
      }
    }
  }

  &.btn--warning {
    background-color: $warning-color;
    border-color: $warning-color;
    color: $color-white;

    &:hover {
      background-color: color.adjust($warning-color, $lightness: 10%);
      border-color: color.adjust($warning-color, $lightness: 10%);
    }

    &.btn--plain {
      background-color: transparent;
      color: $warning-color;

      &:hover {
        background-color: $warning-color;
        color: $color-white;
      }
    }
  }

  &.btn--danger {
    background-color: $error-color;
    border-color: $error-color;
    color: $color-white;

    &:hover {
      background-color: color.adjust($error-color, $lightness: 10%);
      border-color: color.adjust($error-color, $lightness: 10%);
    }

    &.btn--plain {
      background-color: transparent;
      color: $error-color;

      &:hover {
        background-color: $error-color;
        color: $color-white;
      }
    }
  }

  &.btn--info {
    background-color: $info-color;
    border-color: $info-color;
    color: $color-white;

    &:hover {
      background-color: color.adjust($info-color, $lightness: 10%);
      border-color: color.adjust($info-color, $lightness: 10%);
    }

    &.btn--plain {
      background-color: transparent;
      color: $info-color;

      &:hover {
        background-color: $info-color;
        color: $color-white;
      }
    }
  }

  // 按钮尺寸
  &.btn--mini {
    padding: 4px 8px;
    font-size: $font-size-xs;
    min-height: 24px;
  }

  &.btn--small {
    padding: 6px 12px;
    font-size: $font-size-sm;
    min-height: 32px;
  }

  &.btn--medium {
    padding: 8px 16px;
    font-size: $font-size-base;
    min-height: 40px;
  }

  &.btn--large {
    padding: 12px 24px;
    font-size: $font-size-lg;
    min-height: 48px;
  }

  // 按钮形状
  &.btn--round {
    border-radius: 20px;
  }

  &.btn--circle {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 0;

    &.btn--mini {
      width: 24px;
      height: 24px;
    }

    &.btn--small {
      width: 32px;
      height: 32px;
    }

    &.btn--large {
      width: 48px;
      height: 48px;
    }
  }

  // 块级按钮
  &.btn--block {
    width: 100%;
    display: flex;
  }

  // 禁用状态
  &.btn--disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      background-color: inherit;
      border-color: inherit;
      color: inherit;
    }
  }

  // 加载状态
  &.btn--loading {
    cursor: default;

    &:hover {
      background-color: inherit;
      border-color: inherit;
      color: inherit;
    }
  }
}

.btn-loading {
  margin-right: 6px;

  .loading-spinner {
    width: 14px;
    height: 14px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.btn-icon {
  display: flex;
  align-items: center;

  &.btn-icon--prefix {
    margin-right: 6px;
  }

  &.btn-icon--suffix {
    margin-left: 6px;
  }
}

.btn-text {
  display: flex;
  align-items: center;
}

.btn-hover {
  opacity: 0.8;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
