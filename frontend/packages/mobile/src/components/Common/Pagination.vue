<template>
  <view class="pagination">
    <button class="pagination-btn" :disabled="current === 1" @click="handlePrev">上一页</button>

    <view class="pagination-info">
      <text>{{ current }} / {{ totalPages }}</text>
    </view>

    <button class="pagination-btn" :disabled="current === totalPages" @click="handleNext">
      下一页
    </button>
  </view>
</template>

<script>
export default {
  name: 'Pagination',

  props: {
    current: {
      type: Number,
      default: 1
    },
    total: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 10
    }
  },

  computed: {
    totalPages() {
      return Math.ceil(this.total / this.pageSize) || 1
    }
  },

  methods: {
    handlePrev() {
      if (this.current > 1) {
        this.$emit('change', this.current - 1)
      }
    },

    handleNext() {
      if (this.current < this.totalPages) {
        this.$emit('change', this.current + 1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md 0;
}

.pagination-btn {
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-sm;
  background-color: $bg-color-white;
  border: 1px solid $border-color;
  border-radius: $border-radius-base;
  color: $text-color;
  transition: all 0.3s;

  &:not(:disabled):hover {
    background-color: $primary-color;
    color: $text-color-white;
    border-color: $primary-color;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.pagination-info {
  margin: 0 $spacing-lg;
  font-size: $font-size-sm;
  color: $text-color-secondary;
}
</style>
