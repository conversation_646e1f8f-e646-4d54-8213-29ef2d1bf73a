<template>
  <view v-if="visible" class="loading" :class="loadingClass">
    <!-- 遮罩层 -->
    <view v-if="overlay" class="loading-overlay" @click="handleOverlayClick" />

    <!-- 加载内容 -->
    <view class="loading-content" :style="contentStyle">
      <!-- 加载图标 -->
      <view class="loading-spinner" :class="spinnerClass">
        <view v-if="type === 'spinner'" class="spinner-circle" />
        <view v-else-if="type === 'dots'" class="spinner-dots">
          <view v-for="i in 3" :key="i" class="dot" />
        </view>
        <view v-else-if="type === 'pulse'" class="spinner-pulse" />
        <view v-else-if="type === 'bars'" class="spinner-bars">
          <view v-for="i in 4" :key="i" class="bar" />
        </view>
        <view v-else-if="type === 'ring'" class="spinner-ring">
          <view />
          <view />
          <view />
          <view />
        </view>
      </view>

      <!-- 加载文本 -->
      <view v-if="text" class="loading-text">
        <text>{{ text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Loading',

  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: true
    },

    // 加载类型
    type: {
      type: String,
      default: 'spinner'
    },

    // 加载文本
    text: {
      type: String,
      default: ''
    },

    // 尺寸
    size: {
      type: [String, Number],
      default: 'medium'
    },

    // 颜色
    color: {
      type: String,
      default: ''
    },

    // 是否显示遮罩
    overlay: {
      type: Boolean,
      default: false
    },

    // 是否垂直居中
    vertical: {
      type: Boolean,
      default: false
    },

    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },

  computed: {
    loadingClass() {
      return {
        'loading--overlay': this.overlay,
        'loading--vertical': this.vertical,
        [`loading--${this.size}`]: typeof this.size === 'string',
        [this.customClass]: !!this.customClass
      }
    },

    spinnerClass() {
      return {
        [`spinner--${this.type}`]: true,
        [`spinner--${this.size}`]: typeof this.size === 'string'
      }
    },

    contentStyle() {
      const style = {}

      if (this.color) {
        style.color = this.color
      }

      if (typeof this.size === 'number') {
        style.fontSize = `${this.size}px`
      }

      return style
    }
  },

  methods: {
    handleOverlayClick() {
      // 可以添加点击遮罩关闭的逻辑
      this.$emit('overlay-click')
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.loading {
  display: flex;
  align-items: center;
  justify-content: center;

  &.loading--overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
  }

  &.loading--vertical {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: -1;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: $border-radius-base;
  color: $primary-color;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;

  &.spinner--small {
    width: 20px;
    height: 20px;
  }

  &.spinner--medium {
    width: 30px;
    height: 30px;
  }

  &.spinner--large {
    width: 40px;
    height: 40px;
  }
}

.loading-text {
  margin-top: $spacing-sm;
  font-size: $font-size-sm;
  color: $text-color-secondary;
  text-align: center;
}

// 旋转圆圈
.spinner-circle {
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// 跳动点
.spinner-dots {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .dot {
    width: 6px;
    height: 6px;
    background-color: currentColor;
    border-radius: 50%;
    animation: dot-bounce 1.4s ease-in-out infinite both;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

// 脉冲
.spinner-pulse {
  width: 100%;
  height: 100%;
  background-color: currentColor;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

// 条形
.spinner-bars {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;

  .bar {
    width: 3px;
    height: 100%;
    background-color: currentColor;
    animation: bar-scale 1.2s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: -1.1s;
    }

    &:nth-child(2) {
      animation-delay: -1s;
    }

    &:nth-child(3) {
      animation-delay: -0.9s;
    }

    &:nth-child(4) {
      animation-delay: -0.8s;
    }
  }
}

// 环形
.spinner-ring {
  position: relative;
  width: 100%;
  height: 100%;

  view {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: ring-rotate 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;

    &:nth-child(1) {
      animation-delay: -0.45s;
    }

    &:nth-child(2) {
      animation-delay: -0.3s;
    }

    &:nth-child(3) {
      animation-delay: -0.15s;
    }
  }
}

// 动画定义
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dot-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes bar-scale {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes ring-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
