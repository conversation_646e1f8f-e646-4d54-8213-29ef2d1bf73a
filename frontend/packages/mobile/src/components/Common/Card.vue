<template>
  <view class="card" :class="cardClass" :style="cardStyle">
    <!-- 卡片头部 -->
    <view v-if="title || $slots.header" class="card-header" :class="headerClass">
      <view v-if="title" class="card-title">
        <text>{{ title }}</text>
      </view>
      <view v-if="subtitle" class="card-subtitle">
        <text>{{ subtitle }}</text>
      </view>
      <slot name="header"></slot>

      <!-- 头部操作区 -->
      <view v-if="$slots.extra" class="card-extra">
        <slot name="extra"></slot>
      </view>
    </view>

    <!-- 卡片内容 -->
    <view class="card-body" :class="bodyClass" :style="bodyStyle">
      <slot></slot>
    </view>

    <!-- 卡片底部 -->
    <view v-if="$slots.footer" class="card-footer" :class="footerClass">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Card',

  props: {
    // 卡片标题
    title: {
      type: String,
      default: ''
    },

    // 卡片副标题
    subtitle: {
      type: String,
      default: ''
    },

    // 是否显示阴影
    shadow: {
      type: [Boolean, String],
      default: true
    },

    // 是否显示边框
    border: {
      type: Boolean,
      default: true
    },

    // 圆角大小
    radius: {
      type: [String, Number],
      default: ''
    },

    // 内边距
    padding: {
      type: [String, Number],
      default: ''
    },

    // 外边距
    margin: {
      type: [String, Number],
      default: ''
    },

    // 背景色
    background: {
      type: String,
      default: ''
    },

    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },

    // 头部样式类
    headerClass: {
      type: String,
      default: ''
    },

    // 内容样式类
    bodyClass: {
      type: String,
      default: ''
    },

    // 底部样式类
    footerClass: {
      type: String,
      default: ''
    },

    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },

  computed: {
    cardClass() {
      return {
        'card--no-shadow': this.shadow === false,
        'card--hover-shadow': this.shadow === 'hover',
        'card--always-shadow': this.shadow === 'always' || this.shadow === true,
        'card--no-border': !this.border,
        'card--clickable': this.clickable,
        [this.customClass]: !!this.customClass
      }
    },

    cardStyle() {
      const style = {}

      if (this.radius) {
        const radius = typeof this.radius === 'number' ? `${this.radius}px` : this.radius
        style.borderRadius = radius
      }

      if (this.margin) {
        const margin = typeof this.margin === 'number' ? `${this.margin}px` : this.margin
        style.margin = margin
      }

      if (this.background) {
        style.backgroundColor = this.background
      }

      return style
    },

    bodyStyle() {
      const style = {}

      if (this.padding) {
        const padding = typeof this.padding === 'number' ? `${this.padding}px` : this.padding
        style.padding = padding
      }

      return style
    }
  },

  methods: {
    handleClick(e) {
      if (this.clickable) {
        this.$emit('click', e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.card {
  position: relative;
  background-color: $bg-color-white;
  border-radius: $border-radius-base;
  overflow: hidden;
  transition: all 0.3s;

  // 阴影样式
  &.card--always-shadow {
    box-shadow: $box-shadow-base;
  }

  &.card--hover-shadow {
    &:hover {
      box-shadow: $box-shadow-base;
    }
  }

  &.card--no-shadow {
    box-shadow: none;
  }

  // 边框样式
  &:not(.card--no-border) {
    border: 1px solid $border-color-light;
  }

  // 可点击样式
  &.card--clickable {
    cursor: pointer;

    &:hover {
      box-shadow: $box-shadow-hover;
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.card-header {
  position: relative;
  padding: $spacing-md $spacing-md $spacing-sm;
  border-bottom: 1px solid $border-color-lighter;

  &:last-child {
    border-bottom: none;
  }

  .card-title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-color-primary;
    line-height: 1.4;
    margin-bottom: $spacing-xs;
  }

  .card-subtitle {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    line-height: 1.4;
  }

  .card-extra {
    position: absolute;
    top: 50%;
    right: $spacing-md;
    transform: translateY(-50%);
  }
}

.card-body {
  padding: $spacing-md;

  // 当没有头部时，增加顶部内边距
  .card:not(:has(.card-header)) & {
    padding-top: $spacing-lg;
  }

  // 当没有底部时，增加底部内边距
  .card:not(:has(.card-footer)) & {
    padding-bottom: $spacing-lg;
  }
}

.card-footer {
  padding: $spacing-sm $spacing-md $spacing-md;
  border-top: 1px solid $border-color-lighter;
  background-color: $bg-color-page;

  &:first-child {
    border-top: none;
    background-color: transparent;
  }
}

// 特殊布局样式
.card-header + .card-body {
  padding-top: $spacing-sm;
}

.card-body + .card-footer {
  padding-top: $spacing-xs;
}

// 响应式设计
@media (max-width: 768px) {
  .card {
    margin: $spacing-sm;
    border-radius: $border-radius-sm;
  }

  .card-header {
    padding: $spacing-sm;

    .card-title {
      font-size: $font-size-base;
    }

    .card-extra {
      right: $spacing-sm;
    }
  }

  .card-body {
    padding: $spacing-sm;
  }

  .card-footer {
    padding: $spacing-xs $spacing-sm $spacing-sm;
  }
}

// 卡片组合样式
.card + .card {
  margin-top: $spacing-md;
}

// 卡片内的常用元素样式
.card {
  .divider {
    margin: $spacing-md 0;
    border-bottom: 1px solid $border-color-lighter;
  }

  .text-muted {
    color: $text-color-secondary;
  }

  .text-primary {
    color: $primary-color;
  }

  .text-success {
    color: $success-color;
  }

  .text-warning {
    color: $warning-color;
  }

  .text-danger {
    color: $error-color;
  }
}
</style>
