<template>
  <view v-if="visible" class="action-sheet">
    <!-- 遮罩层 -->
    <view class="action-sheet-mask" @tap="handleMaskClick" />

    <!-- 操作菜单 -->
    <view class="action-sheet-container" :class="{ 'action-sheet-show': visible }">
      <!-- 标题 -->
      <view v-if="title" class="action-sheet-header">
        <text class="action-sheet-title">{{ title }}</text>
      </view>

      <!-- 操作项列表 -->
      <view class="action-sheet-content">
        <view
          v-for="(item, index) in items"
          :key="index"
          class="action-sheet-item"
          :class="{
            'action-sheet-item--destructive': item.destructive,
            'action-sheet-item--disabled': item.disabled
          }"
          @tap="handleItemClick(item, index)"
        >
          <!-- 图标 -->
          <view v-if="item.icon" class="action-sheet-icon">
            <text class="icon-text">{{ item.icon }}</text>
          </view>

          <!-- 文本 -->
          <text class="action-sheet-text">{{ item.text || item.label || item.name }}</text>

          <!-- 描述 -->
          <text v-if="item.description" class="action-sheet-description">
            {{ item.description }}
          </text>
        </view>
      </view>

      <!-- 取消按钮 -->
      <view v-if="showCancel" class="action-sheet-cancel">
        <view class="action-sheet-item action-sheet-cancel-btn" @tap="handleCancel">
          <text class="action-sheet-text">{{ cancelText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ActionSheet',

  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },

    // 标题
    title: {
      type: String,
      default: ''
    },

    // 操作项列表
    items: {
      type: Array,
      default: () => []
    },

    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    },

    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },

    // 点击遮罩是否关闭
    maskClosable: {
      type: Boolean,
      default: true
    },

    // 是否禁用安全区域
    safeAreaInsetBottom: {
      type: Boolean,
      default: true
    }
  },
  emits: ['select', 'cancel', 'close', 'mask-click'],

  methods: {
    handleItemClick(item, index) {
      if (item.disabled) return

      this.$emit('select', {
        item,
        index,
        value: item.value
      })

      // 自动关闭
      this.close()
    },

    handleCancel() {
      this.$emit('cancel')
      this.close()
    },

    handleMaskClick() {
      if (this.maskClosable) {
        this.$emit('mask-click')
        this.close()
      }
    },

    close() {
      this.$emit('close')
    },

    // 外部调用的方法
    show() {
      this.$emit('update:visible', true)
    },

    hide() {
      this.$emit('update:visible', false)
    }
  }
})
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.action-sheet {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: flex-end;
}

.action-sheet-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 1;
  transition: opacity 0.3s;
}

.action-sheet-container {
  position: relative;
  width: 100%;
  background-color: $bg-color-white;
  border-radius: $border-radius-lg $border-radius-lg 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;

  &.action-sheet-show {
    transform: translateY(0);
  }
}

.action-sheet-header {
  padding: $spacing-lg $spacing-md $spacing-sm;
  text-align: center;
  border-bottom: 1px solid $border-color-light;
}

.action-sheet-title {
  font-size: $font-size-md;
  font-weight: 500;
  color: $text-color-primary;
  line-height: 1.4;
}

.action-sheet-content {
  padding: 0;
}

.action-sheet-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-bottom: 1px solid $border-color-light;
  cursor: pointer;
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: $bg-color-light;
  }

  &.action-sheet-item--destructive {
    .action-sheet-text {
      color: $error-color;
    }

    .action-sheet-icon {
      color: $error-color;
    }
  }

  &.action-sheet-item--disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:active {
      background-color: $bg-color-white;
    }
  }
}

.action-sheet-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $text-color-secondary;
  font-size: $font-size-lg;
  flex-shrink: 0;
}

.action-sheet-text {
  flex: 1;
  font-size: $font-size-base;
  color: $text-color-primary;
  line-height: 1.4;
}

.action-sheet-description {
  font-size: $font-size-xs;
  color: $text-color-secondary;
  line-height: 1.3;
  margin-top: 2px;
}

.action-sheet-cancel {
  margin-top: $spacing-xs;

  .action-sheet-cancel-btn {
    border-bottom: none;
    font-weight: 500;

    .action-sheet-text {
      text-align: center;
      color: $text-color-secondary;
    }

    &:active {
      background-color: $bg-color-light;
    }
  }
}

// 安全区域适配
.action-sheet-container {
  padding-bottom: env(safe-area-inset-bottom);
}

// 动画效果
.action-sheet {
  &.action-sheet-enter-active,
  &.action-sheet-leave-active {
    transition: opacity 0.3s;

    .action-sheet-container {
      transition: transform 0.3s ease;
    }
  }

  &.action-sheet-enter-from,
  &.action-sheet-leave-to {
    opacity: 0;

    .action-sheet-container {
      transform: translateY(100%);
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .action-sheet-item {
    padding: $spacing-lg $spacing-md;
    min-height: 48px;
  }

  .action-sheet-text {
    font-size: $font-size-md;
  }
}

// 暗黑模式支持
@media (prefers-color-scheme: dark) {
  .action-sheet-mask {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .action-sheet-container {
    background-color: #1f1f1f;
  }

  .action-sheet-item {
    background-color: #1f1f1f;
    border-bottom-color: #333;

    &:active {
      background-color: #333;
    }
  }

  .action-sheet-title {
    color: #fff;
  }

  .action-sheet-text {
    color: #fff;
  }

  .action-sheet-description {
    color: #999;
  }

  .action-sheet-icon {
    color: #999;
  }
}

// 自定义主题变体
.action-sheet {
  &.action-sheet-theme-ios {
    .action-sheet-container {
      border-radius: $border-radius-xl $border-radius-xl 0 0;
    }

    .action-sheet-item {
      padding: $spacing-lg;

      .action-sheet-text {
        text-align: center;
        font-weight: 400;
      }
    }

    .action-sheet-cancel {
      margin-top: $spacing-sm;

      .action-sheet-cancel-btn {
        background-color: $bg-color-white;
        border-radius: $border-radius-lg;
        margin: 0 $spacing-sm $spacing-sm;
        border: 1px solid $border-color-light;

        .action-sheet-text {
          font-weight: 600;
          color: $primary-color;
        }
      }
    }
  }

  &.action-sheet-theme-android {
    .action-sheet-container {
      border-radius: 0;
    }

    .action-sheet-item {
      padding: $spacing-md $spacing-lg;

      .action-sheet-text {
        font-weight: 500;
      }
    }
  }
}

// 大尺寸屏幕适配
@media screen and (min-width: 1024px) {
  .action-sheet {
    align-items: center;
    justify-content: center;

    .action-sheet-container {
      width: 400px;
      max-width: 90vw;
      border-radius: $border-radius-lg;
      transform: scale(0.8);
      opacity: 0;

      &.action-sheet-show {
        transform: scale(1);
        opacity: 1;
      }
    }
  }
}
</style>
