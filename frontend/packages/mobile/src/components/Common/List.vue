<template>
  <view class="list" :class="listClass">
    <!-- 列表头部 -->
    <view v-if="title || $slots.header" class="list-header">
      <view v-if="title" class="list-title">
        <text>{{ title }}</text>
      </view>
      <slot name="header"></slot>
    </view>

    <!-- 列表内容 -->
    <view class="list-content">
      <slot></slot>
    </view>

    <!-- 列表底部 -->
    <view v-if="$slots.footer" class="list-footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'List',

  props: {
    // 列表标题
    title: {
      type: String,
      default: ''
    },

    // 是否显示边框
    border: {
      type: Boolean,
      default: true
    },

    // 是否显示分割线
    divider: {
      type: Boolean,
      default: true
    },

    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },

  computed: {
    listClass() {
      return {
        'list--no-border': !this.border,
        'list--no-divider': !this.divider,
        [this.customClass]: !!this.customClass
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.list {
  background-color: $bg-color-white;

  &:not(.list--no-border) {
    border: 1px solid $border-color-light;
    border-radius: $border-radius-base;
  }

  &.list--no-divider {
    :deep(.list-item:not(:last-child)) {
      border-bottom: none;
    }
  }
}

.list-header {
  padding: $spacing-md $spacing-md $spacing-sm;
  border-bottom: 1px solid $border-color-lighter;

  .list-title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-color-primary;
    line-height: 1.4;
  }
}

.list-footer {
  padding: $spacing-sm $spacing-md $spacing-md;
  border-top: 1px solid $border-color-lighter;
  background-color: $bg-color-page;
}
</style>
