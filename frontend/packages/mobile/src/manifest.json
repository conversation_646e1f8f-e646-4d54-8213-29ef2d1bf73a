{"name": "智慧养老评估平台", "appid": "__UNI__assessment", "description": "智慧养老评估平台移动端应用", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Camera": {}, "Gallery": {}, "File": {}, "Uploader": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />", "<uses-permission android:name=\"android.permission.VIBRATE\" />", "<uses-permission android:name=\"android.permission.READ_LOGS\" />", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />", "<uses-feature android:name=\"android.hardware.camera.autofocus\" />", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.CAMERA\" />", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\" />", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\" />", "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />", "<uses-permission android:name=\"android.permission.FLASHLIGHT\" />", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\" />", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />"]}, "ios": {}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于小程序位置接口的效果展示"}}}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "h5": {"title": "智慧养老评估平台", "template": "index.html", "router": {"mode": "hash", "base": "./"}, "optimization": {"treeShaking": {"enable": true}}, "devServer": {"port": 5273, "disableHostCheck": true, "proxy": {"/api": {"target": "http://localhost:8181", "changeOrigin": true, "secure": false}}}, "performance": {"maxEntrypointSize": 512000, "maxAssetSize": 512000}, "experimental": {"enablePassiveListeners": true}}}