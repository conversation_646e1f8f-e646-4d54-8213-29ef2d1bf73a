import { request } from '@/utils/request'

// 获取老年人列表
export function getElderlyList(params) {
  return request({
    url: '/api/elderly/list',
    method: 'GET',
    params
  })
}

// 获取老年人详情
export function getElderlyDetail(id) {
  return request({
    url: `/api/elderly/${id}`,
    method: 'GET'
  })
}

// 创建老年人信息
export function createElderly(data) {
  return request({
    url: '/api/elderly',
    method: 'POST',
    data
  })
}

// 更新老年人信息
export function updateElderly(id, data) {
  return request({
    url: `/api/elderly/${id}`,
    method: 'PUT',
    data
  })
}

// 删除老年人信息
export function deleteElderly(id) {
  return request({
    url: `/api/elderly/${id}`,
    method: 'DELETE'
  })
}

// 批量删除老人
export function batchDeleteElderly(ids) {
  return request({
    url: '/api/elderly/batch-delete',
    method: 'POST',
    data: { ids }
  })
}

// 搜索老人
export function searchElderly(params) {
  return request({
    url: '/api/elderly/search',
    method: 'GET',
    params
  })
}

// 导入老人数据
export function importElderlyData(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/api/elderly/import',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出老人数据
export function exportElderlyData(params) {
  return request({
    url: '/api/elderly/export',
    method: 'GET',
    params,
    responseType: 'blob'
  })
}

// 获取老年人统计信息
export function getElderlyStatistics() {
  return request({
    url: '/api/elderly/statistics',
    method: 'GET'
  })
}

// 获取老人的评估历史
export function getElderlyAssessmentHistory(id, params) {
  return request({
    url: `/api/elderly/${id}/assessment-history`,
    method: 'GET',
    params
  })
}

// 获取老人的最新评估结果
export function getElderlyLatestAssessment(id) {
  return request({
    url: `/api/elderly/${id}/latest-assessment`,
    method: 'GET'
  })
}
