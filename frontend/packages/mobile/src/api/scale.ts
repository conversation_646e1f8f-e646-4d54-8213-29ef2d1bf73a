import { request } from '@/utils/request'

// 获取量表列表
export function getScaleList(params) {
  return request({
    url: '/api/scale/list',
    method: 'GET',
    params
  })
}

// 获取量表详情
export function getScaleDetail(id) {
  return request({
    url: `/api/scale/${id}`,
    method: 'GET'
  })
}

// 获取量表问题
export function getScaleQuestions(id) {
  return request({
    url: `/api/scale/${id}/questions`,
    method: 'GET'
  })
}

// 创建量表
export function createScale(data) {
  return request({
    url: '/api/scale',
    method: 'POST',
    data
  })
}

// 更新量表
export function updateScale(id, data) {
  return request({
    url: `/api/scale/${id}`,
    method: 'PUT',
    data
  })
}

// 删除量表
export function deleteScale(id) {
  return request({
    url: `/api/scale/${id}`,
    method: 'DELETE'
  })
}

// 发布量表
export function publishScale(id) {
  return request({
    url: `/api/scale/${id}/publish`,
    method: 'POST'
  })
}

// 禁用量表
export function disableScale(id) {
  return request({
    url: `/api/scale/${id}/disable`,
    method: 'POST'
  })
}

// 启用量表
export function enableScale(id) {
  return request({
    url: `/api/scale/${id}/enable`,
    method: 'POST'
  })
}

// 复制量表
export function copyScale(id, name) {
  return request({
    url: `/api/scale/${id}/copy`,
    method: 'POST',
    data: { name }
  })
}

// 导出量表
export function exportScale(id) {
  return request({
    url: `/api/scale/${id}/export`,
    method: 'GET',
    responseType: 'blob'
  })
}

// 获取量表使用统计
export function getScaleUsageStats(id) {
  return request({
    url: `/api/scale/${id}/usage-stats`,
    method: 'GET'
  })
}