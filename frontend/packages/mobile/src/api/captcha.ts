import { request } from '@/utils/request'

/**
 * 验证码相关API
 */

// 类型定义
interface CaptchaCheckData {
  captchaType: 'blockPuzzle' | 'clickWord'
  token: string
  pointJson: string
  verification: string
}

interface CaptchaVerifyData {
  captchaType: 'blockPuzzle' | 'clickWord'
  token: string
  verification: string
}

interface ApiResponse<T = any> {
  success: boolean
  data: T | null
  message: string
}

/**
 * 获取滑动验证码
 * @returns {Promise} 验证码数据
 */
export function getCaptcha(): Promise<ApiResponse> {
  return request({
    url: '/api/captcha/get',
    method: 'GET',
    showError: false, // 关闭自动错误提示
    showLoading: false // 关闭loading提示，避免刷新时闪烁
  })
    .then(response => {
      // 验证码API直接返回数据，包装成统一格式
      return {
        success: true,
        data: response,
        message: '获取验证码成功'
      }
    })
    .catch(error => {
      console.error('获取验证码失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取验证码失败'
      }
    })
}

/**
 * 校验滑动验证码
 * @param {Object} data 验证数据
 * @param {string} data.captchaType 验证码类型 (blockPuzzle/clickWord)
 * @param {string} data.token 验证码token
 * @param {string} data.pointJson 滑动坐标JSON字符串
 * @param {string} data.verification 验证密钥
 * @returns {Promise} 校验结果
 */
export function checkCaptcha(data: CaptchaCheckData): Promise<ApiResponse> {
  return request({
    url: '/api/captcha/check',
    method: 'POST',
    data,
    showError: false // 关闭自动错误提示
  })
    .then(response => {
      // 后端返回格式: {success: true, data: {result: bool, ...}, message?: string}
      // 前端期望格式: {success: bool, data: {...}, message: string}
      const isValid = response.data && response.data.result
      return {
        success: isValid,
        data: response.data,
        message: response.message || response.data?.message || (isValid ? '验证成功' : '验证失败')
      }
    })
    .catch(error => {
      console.error('验证码校验失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '验证码校验失败'
      }
    })
}

/**
 * 验证码二次验证（用于登录）
 * @param {Object} data 验证数据
 * @param {string} data.captchaType 验证码类型
 * @param {string} data.token 验证码token
 * @param {string} data.verification 验证密钥
 * @returns {Promise} 验证结果
 */
export function verifyCaptcha(data: CaptchaVerifyData): Promise<ApiResponse> {
  return request({
    url: '/api/captcha/verify',
    method: 'POST',
    data,
    showError: false // 关闭自动错误提示
  })
    .then(response => {
      // 后端返回格式: {success: true, data: {result: bool, ...}, message?: string}
      // 前端期望格式: {success: bool, data: {...}, message: string}
      const isValid = response.data && response.data.result
      return {
        success: isValid,
        data: response.data,
        message: response.message || response.data?.message || (isValid ? '验证成功' : '验证失败')
      }
    })
    .catch(error => {
      console.error('验证码二次验证失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '验证码二次验证失败'
      }
    })
}