import { request } from '@/utils/request'

// 获取评估记录列表
export function getAssessmentList(params) {
  return request({
    url: '/api/assessment/list',
    method: 'GET',
    params
  })
}

// 获取评估详情
export function getAssessmentDetail(id) {
  return request({
    url: `/api/assessment/${id}`,
    method: 'GET'
  })
}

// 创建评估
export function createAssessment(data) {
  return request({
    url: '/api/assessment',
    method: 'POST',
    data
  })
}

// 更新评估
export function updateAssessment(id, data) {
  return request({
    url: `/api/assessment/${id}`,
    method: 'PUT',
    data
  })
}

// 删除评估
export function deleteAssessment(id) {
  return request({
    url: `/api/assessment/${id}`,
    method: 'DELETE'
  })
}

// 获取评估问题
export function getAssessmentQuestions(id) {
  return request({
    url: `/api/assessment/${id}/questions`,
    method: 'GET'
  })
}

// 获取评估统计信息
export function getAssessmentStatistics() {
  return request({
    url: '/api/assessment/statistics',
    method: 'GET'
  })
}

// 获取最近评估记录
export function getRecentAssessments(params) {
  return request({
    url: '/api/assessment/recent',
    method: 'GET',
    params
  })
}

// 保存评估答案
export function saveAssessmentAnswers(id, data) {
  return request({
    url: `/api/assessment/${id}/answers`,
    method: 'POST',
    data
  })
}

// 提交评估答案
export function submitAssessmentAnswer(id, data) {
  return request({
    url: `/api/assessment/${id}/answer`,
    method: 'POST',
    data
  })
}

// 提交评估答案（批量）
export function submitAssessmentAnswers(id, data) {
  return request({
    url: `/api/assessment/${id}/submit`,
    method: 'POST',
    data
  })
}

// 完成评估
export function completeAssessment(id) {
  return request({
    url: `/api/assessment/${id}/complete`,
    method: 'POST'
  })
}

// 取消评估
export function cancelAssessment(id) {
  return request({
    url: `/api/assessment/${id}/cancel`,
    method: 'POST'
  })
}

// 获取评估报告
export function getAssessmentReport(id) {
  return request({
    url: `/api/assessment/${id}/report`,
    method: 'GET'
  })
}

// 导出评估报告
export function exportAssessmentReport(id, format = 'pdf') {
  return request({
    url: `/api/assessment/${id}/report/export`,
    method: 'GET',
    params: { format },
    responseType: 'blob'
  })
}

// 导出评估列表
export function exportAssessments(params) {
  return request({
    url: '/api/assessment/export',
    method: 'GET',
    params,
    responseType: 'blob'
  })
}

// 获取评估历史
export function getAssessmentHistory(elderlyId, params) {
  return request({
    url: `/api/assessment/history/${elderlyId}`,
    method: 'GET',
    params
  })
}

// 获取最新评估结果
export function getLatestAssessment(elderlyId) {
  return request({
    url: `/api/assessment/latest/${elderlyId}`,
    method: 'GET'
  })
}
