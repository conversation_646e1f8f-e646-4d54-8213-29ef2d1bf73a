import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { formatDate, validateForm, generateId, debounce, throttle } from '../index.js'

describe('工具函数测试', () => {
  describe('formatDate', () => {
    it('应该正确格式化日期', () => {
      const date = new Date('2024-01-01 12:30:45')
      expect(formatDate(date, 'YYYY-MM-DD')).toBe('2024-01-01')
      expect(formatDate(date, 'YYYY-MM-DD HH:mm:ss')).toBe('2024-01-01 12:30:45')
    })

    it('应该处理无效日期', () => {
      expect(formatDate(null)).toBe('')
      expect(formatDate(undefined)).toBe('')
      expect(formatDate('invalid')).toBe('')
    })
  })

  describe('validateForm', () => {
    it('应该验证必填字段', () => {
      const rules = {
        name: { required: true, message: '姓名不能为空' },
        age: { required: true, min: 0, max: 120, message: '年龄必须在0-120之间' }
      }

      const validData = { name: '张三', age: 25 }
      const invalidData = { name: '', age: -1 }

      expect(validateForm(validData, rules).valid).toBe(true)
      expect(validateForm(invalidData, rules).valid).toBe(false)
      expect(validateForm(invalidData, rules).errors.length).toBeGreaterThan(0)
    })
  })

  describe('generateId', () => {
    it('应该生成唯一ID', () => {
      const id1 = generateId()
      const id2 = generateId()

      expect(id1).toBeTruthy()
      expect(id2).toBeTruthy()
      expect(id1).not.toBe(id2)
      expect(typeof id1).toBe('string')
    })
  })

  describe('防抖和节流', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('防抖函数应该延迟执行', () => {
      const mockFn = vi.fn()
      const debouncedFn = debounce(mockFn, 100)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      expect(mockFn).not.toHaveBeenCalled()

      vi.advanceTimersByTime(100)
      expect(mockFn).toHaveBeenCalledTimes(1)
    })

    it('节流函数应该限制执行频率', () => {
      const mockFn = vi.fn()
      const throttledFn = throttle(mockFn, 100)

      throttledFn()
      throttledFn()
      throttledFn()

      expect(mockFn).toHaveBeenCalledTimes(1)

      vi.advanceTimersByTime(100)
      throttledFn()

      expect(mockFn).toHaveBeenCalledTimes(2)
    })
  })
})
