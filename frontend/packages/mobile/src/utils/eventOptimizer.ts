/**
 * 事件监听器优化工具
 * 解决 passive event listener 警告
 */

// 类型定义
type EventListenerOptions = boolean | AddEventListenerOptions;

interface ThrottledFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): void;
}

interface DebouncedFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): void;
}

// 优化触摸事件监听器
export function optimizeEventListeners(): void {
  // 检查是否支持 passive 事件监听器
  let supportsPassive = false
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true
        return false
      }
    })
    window.addEventListener('testPassive', null as any, opts)
    window.removeEventListener('testPassive', null as any, opts)
  } catch (e) {
    // 不支持 passive
  }

  // 如果支持 passive，优化常见的滚动事件
  if (supportsPassive) {
    // 重写原生的 addEventListener 方法
    const originalAddEventListener = EventTarget.prototype.addEventListener

    EventTarget.prototype.addEventListener = function (
      type: string, 
      listener: EventListenerOrEventListenerObject | null, 
      options?: EventListenerOptions
    ) {
      // 对于滚动相关事件，默认使用 passive
      const passiveEvents = ['touchstart', 'touchmove', 'touchend', 'wheel', 'scroll']

      if (passiveEvents.includes(type)) {
        if (typeof options === 'boolean') {
          options = { capture: options, passive: true }
        } else if (typeof options === 'object') {
          options = { ...options, passive: options.passive !== false }
        } else {
          options = { passive: true }
        }
      }

      return originalAddEventListener.call(this, type, listener, options)
    }
  }
}

// 为 uni-app 组件添加优化的事件处理
export function addPassiveEventListener(
  element: Element, 
  event: string, 
  handler: EventListener, 
  options: AddEventListenerOptions = {}
): () => void {
  const passiveOptions = {
    ...options,
    passive: true
  }

  element.addEventListener(event, handler, passiveOptions)

  return () => {
    element.removeEventListener(event, handler, passiveOptions)
  }
}

// 优化滚动性能的工具函数
export function throttle<T extends (...args: any[]) => any>(
  func: T, 
  wait: number
): ThrottledFunction<T> {
  let timeout: number | undefined
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export function debounce<T extends (...args: any[]) => any>(
  func: T, 
  wait: number, 
  immediate?: boolean
): DebouncedFunction<T> {
  let timeout: number | undefined
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = undefined
      if (!immediate) func(...args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func(...args)
  }
}