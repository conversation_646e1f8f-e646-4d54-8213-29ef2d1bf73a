/**
 * 通用工具函数库
 * 提供常用的工具方法，包括日期处理、数据验证、格式化等
 */

/**
 * 日期时间工具
 */
export const dateUtils = {
  /**
   * 格式化日期
   * @param {Date|String|Number} date 日期
   * @param {String} format 格式 (YYYY-MM-DD HH:mm:ss)
   * @returns {String} 格式化后的日期字符串
   */
  format(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return ''

    const d = new Date(date)
    if (isNaN(d.getTime())) return ''

    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  },

  /**
   * 获取相对时间描述
   * @param {Date|String|Number} date 日期
   * @returns {String} 相对时间描述
   */
  relative(date) {
    if (!date) return ''

    const d = new Date(date)
    if (isNaN(d.getTime())) return ''

    const now = new Date()
    const diff = now.getTime() - d.getTime()
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (seconds < 60) {
      return '刚刚'
    } else if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return this.format(date, 'YYYY-MM-DD')
    }
  },

  /**
   * 计算年龄
   * @param {Date|String} birthDate 出生日期
   * @returns {Number} 年龄
   */
  calculateAge(birthDate) {
    if (!birthDate) return 0

    const birth = new Date(birthDate)
    if (isNaN(birth.getTime())) return 0

    const today = new Date()
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }

    return age
  },

  /**
   * 判断是否为今天
   * @param {Date|String|Number} date 日期
   * @returns {Boolean} 是否为今天
   */
  isToday(date) {
    if (!date) return false

    const d = new Date(date)
    if (isNaN(d.getTime())) return false

    const today = new Date()
    return d.toDateString() === today.toDateString()
  },

  /**
   * 获取日期范围
   * @param {String} type 类型 (today, yesterday, week, month, year)
   * @returns {Object} {start, end}
   */
  getDateRange(type) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    switch (type) {
      case 'today':
        return {
          start: today,
          end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
        }

      case 'yesterday':
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
        return {
          start: yesterday,
          end: new Date(today.getTime() - 1)
        }

      case 'week':
        const weekStart = new Date(
          today.getTime() - (today.getDay() || 7 - 1) * 24 * 60 * 60 * 1000
        )
        return {
          start: weekStart,
          end: new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000 - 1)
        }

      case 'month':
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
        const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999)
        return {
          start: monthStart,
          end: monthEnd
        }

      case 'year':
        const yearStart = new Date(now.getFullYear(), 0, 1)
        const yearEnd = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999)
        return {
          start: yearStart,
          end: yearEnd
        }

      default:
        return { start: today, end: today }
    }
  }
}

/**
 * 数据验证工具
 */
export const validator = {
  /**
   * 验证手机号
   * @param {String} phone 手机号
   * @returns {Boolean} 是否有效
   */
  isPhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone)
  },

  /**
   * 验证邮箱
   * @param {String} email 邮箱
   * @returns {Boolean} 是否有效
   */
  isEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  },

  /**
   * 验证身份证号
   * @param {String} idCard 身份证号
   * @returns {Boolean} 是否有效
   */
  isIdCard(idCard) {
    return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)
  },

  /**
   * 验证密码强度
   * @param {String} password 密码
   * @returns {Object} {valid, strength, message}
   */
  checkPassword(password) {
    if (!password) {
      return { valid: false, strength: 0, message: '密码不能为空' }
    }

    if (password.length < 6) {
      return { valid: false, strength: 0, message: '密码长度至少6位' }
    }

    let strength = 0
    let message = ''

    // 检查长度
    if (password.length >= 8) strength++

    // 检查是否包含数字
    if (/\d/.test(password)) strength++

    // 检查是否包含小写字母
    if (/[a-z]/.test(password)) strength++

    // 检查是否包含大写字母
    if (/[A-Z]/.test(password)) strength++

    // 检查是否包含特殊字符
    if (/[^\w\s]/.test(password)) strength++

    switch (strength) {
      case 0:
      case 1:
        message = '密码强度：弱'
        break
      case 2:
      case 3:
        message = '密码强度：中'
        break
      case 4:
      case 5:
        message = '密码强度：强'
        break
    }

    return {
      valid: strength >= 2,
      strength,
      message
    }
  },

  /**
   * 验证URL
   * @param {String} url URL
   * @returns {Boolean} 是否有效
   */
  isUrl(url) {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  /**
   * 验证IP地址
   * @param {String} ip IP地址
   * @returns {Boolean} 是否有效
   */
  isIP(ip) {
    return /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(
      ip
    )
  }
}

/**
 * 格式化工具
 */
export const formatter = {
  /**
   * 格式化文件大小
   * @param {Number} bytes 字节数
   * @param {Number} decimals 小数位数
   * @returns {String} 格式化后的大小
   */
  fileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 B'

    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`
  },

  /**
   * 格式化数字
   * @param {Number} num 数字
   * @param {Number} decimals 小数位数
   * @returns {String} 格式化后的数字
   */
  number(num, decimals = 0) {
    if (isNaN(num)) return '0'
    return Number(num).toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    })
  },

  /**
   * 格式化百分比
   * @param {Number} num 数字
   * @param {Number} decimals 小数位数
   * @returns {String} 格式化后的百分比
   */
  percentage(num, decimals = 1) {
    if (isNaN(num)) return '0%'
    return `${(Number(num) * 100).toFixed(decimals)}%`
  },

  /**
   * 格式化货币
   * @param {Number} amount 金额
   * @param {String} currency 货币符号
   * @returns {String} 格式化后的货币
   */
  currency(amount, currency = '¥') {
    if (isNaN(amount)) return `${currency}0.00`
    return (
      currency +
      Number(amount)
        .toFixed(2)
        .replace(/\d(?=(\d{3})+\.)/g, '$&,')
    )
  },

  /**
   * 隐藏手机号中间4位
   * @param {String} phone 手机号
   * @returns {String} 隐藏后的手机号
   */
  hidePhone(phone) {
    if (!phone || phone.length !== 11) return phone
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  },

  /**
   * 隐藏身份证号
   * @param {String} idCard 身份证号
   * @returns {String} 隐藏后的身份证号
   */
  hideIdCard(idCard) {
    if (!idCard) return idCard
    if (idCard.length === 15) {
      return idCard.replace(/(\d{6})\d{6}(\d{3})/, '$1******$2')
    } else if (idCard.length === 18) {
      return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    }
    return idCard
  }
}

/**
 * 数组工具
 */
export const arrayUtils = {
  /**
   * 数组去重
   * @param {Array} arr 数组
   * @param {String} key 对象数组的去重键
   * @returns {Array} 去重后的数组
   */
  unique(arr, key) {
    if (!Array.isArray(arr)) return []

    if (key) {
      const seen = new Set()
      return arr.filter(item => {
        const value = item[key]
        if (seen.has(value)) {
          return false
        }
        seen.add(value)
        return true
      })
    }

    return [...new Set(arr)]
  },

  /**
   * 数组分组
   * @param {Array} arr 数组
   * @param {String|Function} key 分组键或函数
   * @returns {Object} 分组后的对象
   */
  groupBy(arr, key) {
    if (!Array.isArray(arr)) return {}

    return arr.reduce((groups, item) => {
      const groupKey = typeof key === 'function' ? key(item) : item[key]
      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push(item)
      return groups
    }, {})
  },

  /**
   * 数组排序
   * @param {Array} arr 数组
   * @param {String} key 排序键
   * @param {String} order 排序方向 (asc, desc)
   * @returns {Array} 排序后的数组
   */
  sortBy(arr, key, order = 'asc') {
    if (!Array.isArray(arr)) return []

    return [...arr].sort((a, b) => {
      const aVal = a[key]
      const bVal = b[key]

      if (aVal < bVal) {
        return order === 'asc' ? -1 : 1
      }
      if (aVal > bVal) {
        return order === 'asc' ? 1 : -1
      }
      return 0
    })
  },

  /**
   * 数组分页
   * @param {Array} arr 数组
   * @param {Number} page 页码
   * @param {Number} pageSize 每页大小
   * @returns {Object} {list, total, page, pageSize}
   */
  paginate(arr, page = 1, pageSize = 10) {
    if (!Array.isArray(arr)) {
      return { list: [], total: 0, page: 1, pageSize }
    }

    const total = arr.length
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const list = arr.slice(start, end)

    return { list, total, page, pageSize }
  }
}

/**
 * 对象工具
 */
export const objectUtils = {
  /**
   * 深拷贝
   * @param {Any} obj 要拷贝的对象
   * @returns {Any} 拷贝后的对象
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => this.deepClone(item))
    if (typeof obj === 'object') {
      const clonedObj = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key])
        }
      }
      return clonedObj
    }
  },

  /**
   * 对象合并
   * @param {Object} target 目标对象
   * @param {...Object} sources 源对象
   * @returns {Object} 合并后的对象
   */
  merge(target, ...sources) {
    if (!target) target = {}

    sources.forEach(source => {
      if (source) {
        Object.keys(source).forEach(key => {
          if (
            source[key] !== null &&
            typeof source[key] === 'object' &&
            !Array.isArray(source[key])
          ) {
            target[key] = this.merge(target[key] || {}, source[key])
          } else {
            target[key] = source[key]
          }
        })
      }
    })

    return target
  },

  /**
   * 获取对象属性值
   * @param {Object} obj 对象
   * @param {String} path 属性路径 (a.b.c)
   * @param {Any} defaultValue 默认值
   * @returns {Any} 属性值
   */
  get(obj, path, defaultValue) {
    if (!obj || !path) return defaultValue

    const keys = path.split('.')
    let result = obj

    for (const key of keys) {
      if (result === null || result === undefined || !result.hasOwnProperty(key)) {
        return defaultValue
      }
      result = result[key]
    }

    return result
  },

  /**
   * 设置对象属性值
   * @param {Object} obj 对象
   * @param {String} path 属性路径 (a.b.c)
   * @param {Any} value 值
   * @returns {Object} 对象
   */
  set(obj, path, value) {
    if (!obj || !path) return obj

    const keys = path.split('.')
    let current = obj

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }

    current[keys[keys.length - 1]] = value
    return obj
  },

  /**
   * 删除对象中的空值
   * @param {Object} obj 对象
   * @returns {Object} 清理后的对象
   */
  removeEmpty(obj) {
    if (!obj || typeof obj !== 'object') return obj

    const result = {}

    Object.keys(obj).forEach(key => {
      const value = obj[key]
      if (value !== null && value !== undefined && value !== '') {
        if (typeof value === 'object' && !Array.isArray(value)) {
          const cleaned = this.removeEmpty(value)
          if (Object.keys(cleaned).length > 0) {
            result[key] = cleaned
          }
        } else {
          result[key] = value
        }
      }
    })

    return result
  }
}

/**
 * 字符串工具
 */
export const stringUtils = {
  /**
   * 首字母大写
   * @param {String} str 字符串
   * @returns {String} 处理后的字符串
   */
  capitalize(str) {
    if (!str) return ''
    return str.charAt(0).toUpperCase() + str.slice(1)
  },

  /**
   * 驼峰转下划线
   * @param {String} str 字符串
   * @returns {String} 处理后的字符串
   */
  camelToSnake(str) {
    if (!str) return ''
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
  },

  /**
   * 下划线转驼峰
   * @param {String} str 字符串
   * @returns {String} 处理后的字符串
   */
  snakeToCamel(str) {
    if (!str) return ''
    return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase())
  },

  /**
   * 生成随机字符串
   * @param {Number} length 长度
   * @param {String} chars 字符集
   * @returns {String} 随机字符串
   */
  random(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },

  /**
   * 截断字符串
   * @param {String} str 字符串
   * @param {Number} length 长度
   * @param {String} suffix 后缀
   * @returns {String} 截断后的字符串
   */
  truncate(str, length = 50, suffix = '...') {
    if (!str || str.length <= length) return str
    return str.substring(0, length) + suffix
  }
}

/**
 * 存储工具
 */
export const storage = {
  /**
   * 设置本地存储
   * @param {String} key 键
   * @param {Any} value 值
   * @param {Number} expire 过期时间(秒)
   */
  set(key, value, expire) {
    const data = {
      value,
      expire: expire ? Date.now() + expire * 1000 : null
    }
    uni.setStorageSync(key, JSON.stringify(data))
  },

  /**
   * 获取本地存储
   * @param {String} key 键
   * @param {Any} defaultValue 默认值
   * @returns {Any} 值
   */
  get(key, defaultValue = null) {
    try {
      const data = uni.getStorageSync(key)
      if (!data) return defaultValue

      const parsed = JSON.parse(data)

      // 检查是否过期
      if (parsed.expire && Date.now() > parsed.expire) {
        this.remove(key)
        return defaultValue
      }

      return parsed.value
    } catch (error) {
      return defaultValue
    }
  },

  /**
   * 删除本地存储
   * @param {String} key 键
   */
  remove(key) {
    uni.removeStorageSync(key)
  },

  /**
   * 清空本地存储
   */
  clear() {
    uni.clearStorageSync()
  }
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {Number} wait 等待时间
 * @param {Boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300, immediate = false) {
  let timeout

  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }

    const callNow = immediate && !timeout

    clearTimeout(timeout)
    timeout = setTimeout(later, wait)

    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {Number} limit 限制时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit = 300) {
  let inThrottle

  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 生成UUID
 * @returns {String} UUID
 */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * 获取设备信息
 * @returns {Object} 设备信息
 */
export function getDeviceInfo() {
  const systemInfo = uni.getSystemInfoSync()
  return {
    platform: systemInfo.platform,
    system: systemInfo.system,
    version: systemInfo.version,
    model: systemInfo.model,
    brand: systemInfo.brand,
    screenWidth: systemInfo.screenWidth,
    screenHeight: systemInfo.screenHeight,
    windowWidth: systemInfo.windowWidth,
    windowHeight: systemInfo.windowHeight,
    statusBarHeight: systemInfo.statusBarHeight,
    safeArea: systemInfo.safeArea,
    safeAreaInsets: systemInfo.safeAreaInsets
  }
}

/**
 * 获取网络状态
 * @returns {Promise} 网络状态
 */
export function getNetworkType() {
  return new Promise(resolve => {
    uni.getNetworkType({
      success: res => {
        resolve({
          networkType: res.networkType,
          isConnected: res.networkType !== 'none'
        })
      },
      fail: () => {
        resolve({
          networkType: 'unknown',
          isConnected: false
        })
      }
    })
  })
}

/**
 * 格式化日期时间 - 兼容性函数
 * @param {Date|String|Number} date 日期
 * @param {String} format 格式
 * @returns {String} 格式化后的日期字符串
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  return dateUtils.format(date, format)
}

/**
 * 格式化身份证号 - 隐藏中间位数
 * @param {String} idNumber 身份证号
 * @returns {String} 格式化后的身份证号
 */
export function formatIdNumber(idNumber) {
  return formatter.hideIdCard(idNumber)
}

/**
 * 格式化日期 - 兼容性函数
 * @param {Date|String|Number} date 日期
 * @param {String} format 格式
 * @returns {String} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  return dateUtils.format(date, format)
}

// 默认导出所有工具
export default {
  dateUtils,
  validator,
  formatter,
  arrayUtils,
  objectUtils,
  stringUtils,
  storage,
  debounce,
  throttle,
  generateUUID,
  getDeviceInfo,
  getNetworkType,
  formatDateTime,
  formatIdNumber,
  formatDate
}
