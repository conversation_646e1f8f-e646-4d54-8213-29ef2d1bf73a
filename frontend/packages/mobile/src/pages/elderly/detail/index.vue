<template>
  <PageContainer title="老年人详情">
    <Loading v-if="loading" type="spinner" text="加载中..." />

    <view v-else-if="elderlyDetail" class="detail-container">
      <!-- 基本信息卡片 -->
      <Card class="info-card">
        <template #header>
          <view class="card-header">
            <text class="card-title">基本信息</text>
            <Button
              type="primary"
              size="small"
              @click="navigateTo(`/pages/elderly/edit/index?id=${elderlyDetail.id}`)"
            >
              编辑
            </Button>
          </view>
        </template>

        <view class="info-content">
          <view class="avatar-section">
            <view class="elderly-avatar">
              <image
                v-if="elderlyDetail.avatar"
                :src="elderlyDetail.avatar"
                class="avatar-image"
                mode="aspectFill"
              />
              <text v-else class="avatar-text">{{ elderlyDetail.name.charAt(0) }}</text>
            </view>
            <view class="basic-info">
              <text class="name">{{ elderlyDetail.name }}</text>
              <text class="id-number">{{ elderlyDetail.idNumber }}</text>
            </view>
          </view>

          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">性别</text>
              <text class="info-value">{{ getGenderText(elderlyDetail.gender) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">年龄</text>
              <text class="info-value">{{ elderlyDetail.age }}岁</text>
            </view>
            <view class="info-item">
              <text class="info-label">出生日期</text>
              <text class="info-value">{{ elderlyDetail.birthDate }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">联系电话</text>
              <text class="info-value">{{ elderlyDetail.phone || '-' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">紧急联系人</text>
              <text class="info-value">{{ elderlyDetail.emergencyContact || '-' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">紧急联系电话</text>
              <text class="info-value">{{ elderlyDetail.emergencyPhone || '-' }}</text>
            </view>
            <view class="info-item full-width">
              <text class="info-label">居住地址</text>
              <text class="info-value">{{ elderlyDetail.address || '-' }}</text>
            </view>
          </view>
        </view>
      </Card>

      <!-- 健康状况卡片 -->
      <Card class="health-card">
        <template #header>
          <text class="card-title">健康状况</text>
        </template>

        <view class="health-content">
          <view class="health-item">
            <text class="health-label">护理等级</text>
            <view class="care-level" :class="`level-${elderlyDetail.careLevel}`">
              <text>{{ getCareLevelText(elderlyDetail.careLevel) }}</text>
            </view>
          </view>

          <view v-if="elderlyDetail.medicalHistory" class="health-item">
            <text class="health-label">病史</text>
            <text class="health-value">{{ elderlyDetail.medicalHistory }}</text>
          </view>

          <view v-if="elderlyDetail.medications" class="health-item">
            <text class="health-label">用药情况</text>
            <text class="health-value">{{ elderlyDetail.medications }}</text>
          </view>

          <view v-if="elderlyDetail.allergies" class="health-item">
            <text class="health-label">过敏史</text>
            <text class="health-value">{{ elderlyDetail.allergies }}</text>
          </view>

          <view v-if="elderlyDetail.notes" class="health-item">
            <text class="health-label">备注</text>
            <text class="health-value">{{ elderlyDetail.notes }}</text>
          </view>
        </view>
      </Card>

      <!-- 评估历史卡片 -->
      <Card class="assessment-card">
        <template #header>
          <view class="card-header">
            <text class="card-title">评估历史</text>
            <Button type="primary" size="small" @click="startAssessment"> 开始评估 </Button>
          </view>
        </template>

        <view class="assessment-content">
          <view v-if="assessmentHistory.length > 0" class="assessment-list">
            <view
              v-for="assessment in assessmentHistory"
              :key="assessment.id"
              class="assessment-item"
              @click="viewAssessment(assessment)"
            >
              <view class="assessment-info">
                <text class="assessment-scale">{{ assessment.scaleName }}</text>
                <text class="assessment-time">{{ assessment.createTime }}</text>
              </view>
              <view class="assessment-status">
                <view class="status-badge" :class="`status-${assessment.status}`">
                  <text>{{ getStatusText(assessment.status) }}</text>
                </view>
                <text class="icon-text" style="font-size: 16px; color: #c0c4cc">→</text>
              </view>
            </view>

            <view v-if="assessmentHistory.length >= 5" class="view-more">
              <Button type="default" size="small" @click="viewAllAssessments">
                查看全部评估记录
              </Button>
            </view>
          </view>

          <Empty
            v-else
            type="nodata"
            description="暂无评估记录"
            button-text="开始评估"
            @button-click="startAssessment"
          />
        </view>
      </Card>

      <!-- 最新评估结果卡片 -->
      <Card v-if="latestAssessment" class="result-card">
        <template #header>
          <view class="card-header">
            <text class="card-title">最新评估结果</text>
            <Button type="default" size="small" @click="viewAssessment(latestAssessment)">
              查看详情
            </Button>
          </view>
        </template>

        <view class="result-content">
          <view class="result-header">
            <text class="result-scale">{{ latestAssessment.scaleName }}</text>
            <text class="result-time">{{ latestAssessment.completeTime }}</text>
          </view>

          <view class="result-score">
            <text class="score-label">总分</text>
            <text class="score-value">{{ latestAssessment.totalScore }}</text>
            <text class="score-max">/{{ latestAssessment.maxScore }}</text>
          </view>

          <view class="result-level">
            <text class="level-label">评估等级</text>
            <view class="level-badge" :class="`level-${latestAssessment.level}`">
              <text>{{ latestAssessment.levelText }}</text>
            </view>
          </view>

          <view v-if="latestAssessment.recommendation" class="result-recommendation">
            <text class="recommendation-label">建议</text>
            <text class="recommendation-text">{{ latestAssessment.recommendation }}</text>
          </view>
        </view>
      </Card>
    </view>

    <Empty
      v-else
      type="error"
      description="老年人信息不存在"
      button-text="返回列表"
      @button-click="navigateBack"
    />

    <!-- 操作按钮 -->
    <view v-if="elderlyDetail" class="action-buttons">
      <Button type="primary" size="large" @click="startAssessment"> 开始评估 </Button>
      <Button type="default" size="large" @click="showMoreActions"> 更多操作 </Button>
    </view>
  </PageContainer>
</template>

<script>
import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from '@/store'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import Empty from '@/components/Common/Empty.vue'
import Loading from '@/components/Common/Loading.vue'
import Button from '@/components/Common/Button.vue'

export default {
  name: 'ElderlyDetail',

  components: {
    PageContainer,
    Card,
    Empty,
    Loading,
    Button
  },
  setup() {
    const userStore = useUserStore()
    const assessmentStore = useAssessmentStore()
    const elderlyStore = useElderlyStore()
    const scaleStore = useScaleStore()
    const configStore = useConfigStore()

    return {
      userStore,
      assessmentStore,
      elderlyStore,
      scaleStore,
      configStore
    }
  },

  data() {
    return {
      loading: false,
      elderlyId: '',
      assessmentHistory: [],
      latestAssessment: null
    }
  },

  computed: {
    elderlyDetail() {
      return this.elderlyStore.elderlyDetail
    }
  },

  onLoad(options) {
    if (options.id) {
      this.elderlyId = options.id
      this.initPage()
    } else {
      this.navigateBack()
    }
  },

  onShow() {
    if (this.elderlyId) {
      this.loadElderlyDetail()
    }
  },

  onPullDownRefresh() {
    this.loadElderlyDetail().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {async initPage() {
      try {
        this.loading = true
        await Promise.all([
          this.loadElderlyDetail(),
          this.loadAssessmentHistory(),
          this.loadLatestAssessment()
        ])
      } catch (error) {
        console.error('页面初始化失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    async loadElderlyDetail() {
      try {
        await this.elderlyStore.getElderlyDetail(this.elderlyId)
      } catch (error) {
        console.error('加载老年人详情失败:', error)
        throw error
      }
    },

    async loadAssessmentHistory() {
      try {
        const result = await this.getAssessmentHistory({
          elderlyId: this.elderlyId,
          limit: 5
        })
        this.assessmentHistory = result.list || []
      } catch (error) {
        console.error('加载评估历史失败:', error)
        this.assessmentHistory = []
      }
    },

    async loadLatestAssessment() {
      try {
        this.latestAssessment = await this.getLatestAssessment(this.elderlyId)
      } catch (error) {
        console.error('加载最新评估失败:', error)
        this.latestAssessment = null
      }
    },

    startAssessment() {
      this.navigateTo(`/pages/assessment/create/index?elderlyId=${this.elderlyId}`)
    },

    viewAssessment(assessment) {
      if (assessment.status === 'completed') {
        this.navigateTo(`/pages/assessment/result/index?id=${assessment.id}`)
      } else {
        this.navigateTo(`/pages/assessment/conduct/index?id=${assessment.id}`)
      }
    },

    viewAllAssessments() {
      this.navigateTo(`/pages/assessment/records/index?elderlyId=${this.elderlyId}`)
    },

    showMoreActions() {
      uni.showActionSheet({
        itemList: ['编辑信息', '评估历史', '导出数据', '删除'],
        success: res => {
          switch (res.tapIndex) {
            case 0:
              this.navigateTo(`/pages/elderly/edit/index?id=${this.elderlyId}`)
              break
            case 1:
              this.viewAllAssessments()
              break
            case 2:
              this.exportData()
              break
            case 3:
              this.confirmDelete()
              break
          }
        }
      })
    },

    async exportData() {
      try {
        uni.showLoading({ title: '导出中...' })
        // 调用导出接口
        uni.showToast({
          title: '导出成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('导出失败:', error)
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    confirmDelete() {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除 ${this.elderlyDetail.name} 的信息吗？此操作不可恢复。`,
        success: async res => {
          if (res.confirm) {
            try {
              await this.deleteElderly(this.elderlyId)
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              this.navigateBack()
            } catch (error) {
              console.error('删除失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    navigateTo(url) {
      uni.navigateTo({ url })
    },

    navigateBack() {
      uni.navigateBack()
    },

    getGenderText(gender) {
      const genderMap = {
        male: '男',
        female: '女'
      }
      return genderMap[gender] || '-'
    },

    getCareLevelText(level) {
      const levelMap = {
        independent: '自理',
        mild: '轻度依赖',
        moderate: '中度依赖',
        severe: '重度依赖'
      }
      return levelMap[level] || '-'
    },

    getStatusText(status) {
      const statusMap = {
        pending: '待开始',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.detail-container {
  padding-bottom: 80px; // 为底部按钮留出空间
}

.info-card,
.health-card,
.assessment-card,
.result-card {
  margin-bottom: $spacing-lg;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-color-primary;
}

// 基本信息样式
.info-content {
  padding: $spacing-lg;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-lg;
}

.elderly-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: $primary-color;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $spacing-lg;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-text {
  font-size: $font-size-xxl;
  font-weight: 600;
}

.basic-info {
  flex: 1;
}

.name {
  display: block;
  font-size: $font-size-xl;
  font-weight: 600;
  color: $text-color-primary;
  margin-bottom: $spacing-xs;
}

.id-number {
  display: block;
  font-size: $font-size-base;
  color: $text-color-secondary;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.info-item {
  &.full-width {
    grid-column: 1 / -1;
  }
}

.info-label {
  display: block;
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-bottom: $spacing-xs;
}

.info-value {
  display: block;
  font-size: $font-size-base;
  color: $text-color-primary;
  word-break: break-all;
}

// 健康状况样式
.health-content {
  padding: $spacing-lg;
}

.health-item {
  margin-bottom: $spacing-md;

  &:last-child {
    margin-bottom: 0;
  }
}

.health-label {
  display: block;
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-bottom: $spacing-xs;
}

.health-value {
  display: block;
  font-size: $font-size-base;
  color: $text-color-primary;
  line-height: 1.5;
}

.care-level {
  display: inline-block;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-base;
  font-size: $font-size-sm;
  font-weight: 500;

  &.level-independent {
    background-color: $success-color-light;
    color: $success-color;
  }

  &.level-mild {
    background-color: $warning-color-light;
    color: $warning-color;
  }

  &.level-moderate {
    background-color: $danger-color-light;
    color: $danger-color;
  }

  &.level-severe {
    background-color: $error-color-light;
    color: $error-color;
  }
}

// 评估历史样式
.assessment-content {
  padding: $spacing-lg;
}

.assessment-list {
  .assessment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-md 0;
    border-bottom: 1px solid $border-color-light;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: $bg-color-light;
    }
  }
}

.assessment-info {
  flex: 1;
}

.assessment-scale {
  display: block;
  font-size: $font-size-base;
  color: $text-color-primary;
  margin-bottom: $spacing-xs;
}

.assessment-time {
  display: block;
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.assessment-status {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.status-badge {
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-base;
  font-size: $font-size-xs;

  &.status-pending {
    background-color: $warning-color-light;
    color: $warning-color;
  }

  &.status-in_progress {
    background-color: $primary-color-light;
    color: $primary-color;
  }

  &.status-completed {
    background-color: $success-color-light;
    color: $success-color;
  }

  &.status-cancelled {
    background-color: $info-color-light;
    color: $info-color;
  }
}

.view-more {
  text-align: center;
  margin-top: $spacing-md;
}

// 评估结果样式
.result-content {
  padding: $spacing-lg;
}

.result-header {
  margin-bottom: $spacing-md;
}

.result-scale {
  display: block;
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-color-primary;
  margin-bottom: $spacing-xs;
}

.result-time {
  display: block;
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.result-score {
  display: flex;
  align-items: baseline;
  margin-bottom: $spacing-md;
}

.score-label {
  font-size: $font-size-base;
  color: $text-color-secondary;
  margin-right: $spacing-sm;
}

.score-value {
  font-size: $font-size-xxl;
  font-weight: 600;
  color: $primary-color;
}

.score-max {
  font-size: $font-size-lg;
  color: $text-color-secondary;
}

.result-level {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-md;
}

.level-label {
  font-size: $font-size-base;
  color: $text-color-secondary;
  margin-right: $spacing-sm;
}

.level-badge {
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-base;
  font-size: $font-size-sm;
  font-weight: 500;

  &.level-excellent {
    background-color: $success-color-light;
    color: $success-color;
  }

  &.level-good {
    background-color: $primary-color-light;
    color: $primary-color;
  }

  &.level-fair {
    background-color: $warning-color-light;
    color: $warning-color;
  }

  &.level-poor {
    background-color: $danger-color-light;
    color: $danger-color;
  }
}

.result-recommendation {
  .recommendation-label {
    display: block;
    font-size: $font-size-sm;
    color: $text-color-secondary;
    margin-bottom: $spacing-xs;
  }

  .recommendation-text {
    display: block;
    font-size: $font-size-base;
    color: $text-color-primary;
    line-height: 1.5;
  }
}

// 操作按钮
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  z-index: 100;

  .button {
    flex: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .avatar-section {
    flex-direction: column;
    text-align: center;
  }

  .elderly-avatar {
    margin-right: 0;
    margin-bottom: $spacing-md;
  }

  .assessment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }

  .assessment-status {
    align-self: flex-end;
  }

  .result-score {
    flex-direction: column;
    align-items: flex-start;
  }

  .result-level {
    flex-direction: column;
    align-items: flex-start;
  }

  .level-label {
    margin-right: 0;
    margin-bottom: $spacing-xs;
  }
}
</style>
