<template>
  <PageContainer
    :title="assessmentData ? `评估进行中 - ${assessmentData.scaleName}` : '评估进行中'"
    :show-back="false"
  >
    <view v-if="loading" class="loading-container">
      <Loading type="spinner" text="加载评估数据..." />
    </view>

    <view v-else-if="assessmentData" class="assessment-container">
      <!-- 评估信息头部 -->
      <Card class="assessment-header">
        <view class="header-content">
          <view class="elderly-info">
            <image
              v-if="assessmentData.elderlyAvatar"
              :src="assessmentData.elderlyAvatar"
              class="elderly-avatar"
              mode="aspectFill"
            />
            <view v-else class="elderly-avatar-placeholder">
              <text class="icon-text" style="font-size: 24px; color: #c0c4cc">👤</text>
            </view>

            <view class="elderly-details">
              <text class="elderly-name">{{ assessmentData.elderlyName }}</text>
              <text class="elderly-meta">
                {{ assessmentData.elderlyGender === 'male' ? '男' : '女' }} |
                {{ assessmentData.elderlyAge }}岁
              </text>
            </view>
          </view>

          <view class="assessment-info">
            <text class="scale-name">{{ assessmentData.scaleName }}</text>
            <text class="assessment-time">{{ formatDateTime(assessmentData.assessmentTime) }}</text>
          </view>
        </view>
      </Card>

      <!-- 进度条 -->
      <Card class="progress-section">
        <view class="progress-header">
          <text class="progress-title">评估进度</text>
          <text class="progress-text">{{ currentQuestionIndex + 1 }} / {{ totalQuestions }}</text>
        </view>

        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progressPercentage + '%' }" />
        </view>

        <view class="progress-stats">
          <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value">{{ answeredQuestions }}题</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">剩余</text>
            <text class="stat-value">{{ totalQuestions - answeredQuestions }}题</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">用时</text>
            <text class="stat-value">{{ formatDuration(elapsedTime) }}</text>
          </view>
        </view>
      </Card>

      <!-- 当前题目 -->
      <Card v-if="currentQuestion" class="question-section">
        <view class="question-header">
          <text class="question-number">第 {{ currentQuestionIndex + 1 }} 题</text>
          <text v-if="currentQuestion.required" class="required-mark">*</text>
        </view>

        <view class="question-content">
          <text class="question-text">{{ currentQuestion.content }}</text>

          <!-- 题目说明 -->
          <view v-if="currentQuestion.description" class="question-description">
            <text>{{ currentQuestion.description }}</text>
          </view>

          <!-- 题目图片 -->
          <view v-if="currentQuestion.image" class="question-image">
            <image
              :src="currentQuestion.image"
              class="image"
              mode="widthFix"
              @click="previewImage(currentQuestion.image)"
            />
          </view>
        </view>

        <!-- 答题区域 -->
        <view class="answer-section">
          <!-- 单选题 -->
          <view v-if="currentQuestion.type === 'single_choice'" class="answer-options">
            <view
              v-for="(option, index) in currentQuestion.options"
              :key="index"
              class="option-item"
              :class="{ active: currentAnswer === option.value }"
              @click="selectOption(option.value)"
            >
              <view class="option-radio">
                <view v-if="currentAnswer === option.value" class="radio-checked" />
              </view>
              <text class="option-text">{{ option.label }}</text>
              <text v-if="option.score !== undefined" class="option-score"
                >({{ option.score }}分)</text
              >
            </view>
          </view>

          <!-- 多选题 -->
          <view v-else-if="currentQuestion.type === 'multiple_choice'" class="answer-options">
            <view
              v-for="(option, index) in currentQuestion.options"
              :key="index"
              class="option-item"
              :class="{ active: isOptionSelected(option.value) }"
              @click="toggleOption(option.value)"
            >
              <view class="option-checkbox">
                <text
                  v-if="isOptionSelected(option.value)"
                  class="icon-text"
                  style="font-size: 18px; color: #007aff"
                  >✓</text
                >
                <view v-else class="checkbox-empty" />
              </view>
              <text class="option-text">{{ option.label }}</text>
              <text v-if="option.score !== undefined" class="option-score"
                >({{ option.score }}分)</text
              >
            </view>
          </view>

          <!-- 评分题 -->
          <view v-else-if="currentQuestion.type === 'rating'" class="rating-section">
            <view class="rating-scale">
              <view
                v-for="score in getRatingRange()"
                :key="score"
                class="rating-item"
                :class="{ active: currentAnswer === score }"
                @click="selectRating(score)"
              >
                <text class="rating-number">{{ score }}</text>
              </view>
            </view>

            <view class="rating-labels">
              <text class="rating-label">{{ currentQuestion.minLabel || '最低' }}</text>
              <text class="rating-label">{{ currentQuestion.maxLabel || '最高' }}</text>
            </view>
          </view>

          <!-- 文本题 -->
          <view v-else-if="currentQuestion.type === 'text'" class="text-section">
            <Input
              v-model="currentAnswer"
              :placeholder="currentQuestion.placeholder || '请输入答案'"
              type="textarea"
              :maxlength="currentQuestion.maxLength || 500"
              :auto-height="true"
              @input="handleTextInput"
            />
          </view>

          <!-- 数字题 -->
          <view v-else-if="currentQuestion.type === 'number'" class="number-section">
            <Input
              v-model="currentAnswer"
              :placeholder="currentQuestion.placeholder || '请输入数字'"
              type="number"
              :min="currentQuestion.min"
              :max="currentQuestion.max"
              @input="handleNumberInput"
            />
          </view>
        </view>

        <!-- 题目备注 -->
        <view v-if="currentQuestion.note" class="question-note">
          <text>备注：{{ currentQuestion.note }}</text>
        </view>
      </Card>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <Button :disabled="currentQuestionIndex <= 0" @click="previousQuestion"> 上一题 </Button>

        <Button type="default" :loading="saving" @click="saveAsDraft"> 保存草稿 </Button>

        <Button
          v-if="currentQuestionIndex < totalQuestions - 1"
          type="primary"
          :disabled="!isCurrentQuestionAnswered"
          @click="nextQuestion"
        >
          下一题
        </Button>

        <Button
          v-else
          type="primary"
          :disabled="!canSubmit"
          :loading="submitting"
          @click="submitAssessment"
        >
          提交评估
        </Button>
      </view>
    </view>

    <!-- 错误状态 -->
    <Empty
      v-else
      type="error"
      description="加载评估数据失败"
      :show-button="true"
      button-text="重新加载"
      @button-click="loadAssessmentData"
    />
  </PageContainer>
</template>

<script>
import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from '@/store'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import Input from '@/components/Form/Input.vue'
import Button from '@/components/Common/Button.vue'
import Loading from '@/components/Common/Loading.vue'
import Empty from '@/components/Common/Empty.vue'
import { formatDateTime } from '@/utils'

export default {
  name: 'AssessmentConduct',

  components: {
    PageContainer,
    Card,
    Input,
    Button,
    Loading,
    Empty
  },
  setup() {
    const userStore = useUserStore()
    const assessmentStore = useAssessmentStore()
    const elderlyStore = useElderlyStore()
    const scaleStore = useScaleStore()
    const configStore = useConfigStore()

    return {
      userStore,
      assessmentStore,
      elderlyStore,
      scaleStore,
      configStore
    }
  },

  data() {
    return {
      loading: true,
      saving: false,
      submitting: false,

      assessmentId: '',
      assessmentData: null,
      questions: [],
      answers: {},

      currentQuestionIndex: 0,
      startTime: null,
      elapsedTime: 0,
      timer: null
    }
  },

  computed: {
    currentQuestion() {
      return this.questions[this.currentQuestionIndex] || null
    },

    currentAnswer: {
      get() {
        const questionId = this.currentQuestion?.id
        return questionId ? this.answers[questionId] : ''
      },
      set(value) {
        if (this.currentQuestion) {
          this.$set(this.answers, this.currentQuestion.id, value)
        }
      }
    },

    totalQuestions() {
      return this.questions.length
    },

    answeredQuestions() {
      return Object.keys(this.answers).filter(questionId => {
        const answer = this.answers[questionId]
        return answer !== null && answer !== undefined && answer !== ''
      }).length
    },

    progressPercentage() {
      return this.totalQuestions > 0 ? (this.answeredQuestions / this.totalQuestions) * 100 : 0
    },

    isCurrentQuestionAnswered() {
      if (!this.currentQuestion) return false

      const answer = this.currentAnswer

      if (this.currentQuestion.type === 'multiple_choice') {
        return Array.isArray(answer) && answer.length > 0
      }

      return answer !== null && answer !== undefined && answer !== ''
    },

    canSubmit() {
      // 检查所有必填题是否已回答
      return this.questions.every(question => {
        if (!question.required) return true

        const answer = this.answers[question.id]

        if (question.type === 'multiple_choice') {
          return Array.isArray(answer) && answer.length > 0
        }

        return answer !== null && answer !== undefined && answer !== ''
      })
    }
  },

  onLoad(options) {
    if (options.id) {
      this.assessmentId = options.id
      this.loadAssessmentData()
    } else {
      uni.showToast({
        title: '评估ID不存在',
        icon: 'error'
      })
      uni.navigateBack()
    }
  },

  onShow() {
    this.startTimer()
  },

  onHide() {
    this.stopTimer()
    this.autoSave()
  },

  onUnload() {
    this.stopTimer()
    this.autoSave()
  },

  methods: {async loadAssessmentData() {
      try {
        this.loading = true

        // 加载评估基本信息
        this.assessmentData = await this.getAssessmentDetail(this.assessmentId)

        // 加载评估题目
        const questionsResponse = await this.getAssessmentQuestions(this.assessmentId)
        this.questions = questionsResponse.questions

        // 加载已保存的答案
        if (questionsResponse.answers) {
          this.answers = questionsResponse.answers
        }

        // 设置开始时间
        this.startTime = new Date()

        // 如果有保存的进度，跳转到对应题目
        if (questionsResponse.currentQuestionIndex !== undefined) {
          this.currentQuestionIndex = questionsResponse.currentQuestionIndex
        }
      } catch (error) {
        console.error('加载评估数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    startTimer() {
      if (this.timer) return

      this.timer = setInterval(() => {
        if (this.startTime) {
          this.elapsedTime = Math.floor((new Date() - this.startTime) / 1000)
        }
      }, 1000)
    },

    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    selectOption(value) {
      this.currentAnswer = value
    },

    toggleOption(value) {
      let answer = this.currentAnswer

      if (!Array.isArray(answer)) {
        answer = []
      }

      const index = answer.indexOf(value)
      if (index > -1) {
        answer.splice(index, 1)
      } else {
        answer.push(value)
      }

      this.currentAnswer = [...answer]
    },

    isOptionSelected(value) {
      const answer = this.currentAnswer
      return Array.isArray(answer) && answer.includes(value)
    },

    selectRating(score) {
      this.currentAnswer = score
    },

    getRatingRange() {
      const min = this.currentQuestion.minScore || 1
      const max = this.currentQuestion.maxScore || 5
      const range = []

      for (let i = min; i <= max; i++) {
        range.push(i)
      }

      return range
    },

    handleTextInput(e) {
      this.currentAnswer = e.detail.value
    },

    handleNumberInput(e) {
      const value = parseFloat(e.detail.value)
      this.currentAnswer = isNaN(value) ? '' : value
    },

    previousQuestion() {
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--
      }
    },

    nextQuestion() {
      if (this.currentQuestionIndex < this.totalQuestions - 1) {
        this.currentQuestionIndex++
      }
    },

    async saveAsDraft() {
      try {
        this.saving = true

        await this.saveAssessmentAnswers({
          assessmentId: this.assessmentId,
          answers: this.answers,
          currentQuestionIndex: this.currentQuestionIndex,
          elapsedTime: this.elapsedTime
        })

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('保存草稿失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    },

    async autoSave() {
      if (Object.keys(this.answers).length > 0) {
        try {
          await this.saveAssessmentAnswers({
            assessmentId: this.assessmentId,
            answers: this.answers,
            currentQuestionIndex: this.currentQuestionIndex,
            elapsedTime: this.elapsedTime
          })
        } catch (error) {
          console.error('自动保存失败:', error)
        }
      }
    },

    async submitAssessment() {
      if (!this.canSubmit) {
        uni.showToast({
          title: '请完成所有必填题',
          icon: 'error'
        })
        return
      }

      uni.showModal({
        title: '确认提交',
        content: '提交后将无法修改答案，确定要提交评估吗？',
        success: async res => {
          if (res.confirm) {
            await this.doSubmitAssessment()
          }
        }
      })
    },

    async doSubmitAssessment() {
      try {
        this.submitting = true

        const _result = await this.submitAssessmentAnswers({
          assessmentId: this.assessmentId,
          answers: this.answers,
          elapsedTime: this.elapsedTime
        })

        uni.showToast({
          title: '提交成功',
          icon: 'success'
        })

        // 跳转到评估报告页面
        setTimeout(() => {
          uni.redirectTo({
            url: `/pages/assessment/report/index?id=${this.assessmentId}`
          })
        }, 1500)
      } catch (error) {
        console.error('提交评估失败:', error)
        uni.showToast({
          title: '提交失败',
          icon: 'error'
        })
      } finally {
        this.submitting = false
      }
    },

    previewImage(src) {
      uni.previewImage({
        urls: [src],
        current: src
      })
    },

    formatDuration(seconds) {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60

      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`
      }
    },

    formatDateTime
  }
}
</script>

<style lang="scss" scoped>
@use 'sass:math';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.loading-container {
  padding: $spacing-xl;
}

.assessment-container {
  padding-bottom: 80px; // 为底部按钮留出空间
}

.assessment-header {
  margin-bottom: $spacing-lg;

  .header-content {
    padding: $spacing-lg;
  }
}

.elderly-info {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  margin-bottom: $spacing-md;
}

.elderly-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.elderly-avatar-placeholder {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: $bg-color-light;
  display: flex;
  align-items: center;
  justify-content: center;
}

.elderly-details {
  .elderly-name {
    display: block;
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-xs;
  }

  .elderly-meta {
    font-size: $font-size-sm;
    color: $text-color-secondary;
  }
}

.assessment-info {
  .scale-name {
    display: block;
    font-size: $font-size-md;
    font-weight: 500;
    color: $text-color-primary;
    margin-bottom: $spacing-xs;
  }

  .assessment-time {
    font-size: $font-size-sm;
    color: $text-color-secondary;
  }
}

.progress-section {
  margin-bottom: $spacing-lg;

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    .progress-title {
      font-size: $font-size-md;
      font-weight: 600;
      color: $text-color-primary;
    }

    .progress-text {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }

  .progress-bar {
    height: 8px;
    background-color: $bg-color-light;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: $spacing-md;

    .progress-fill {
      height: 100%;
      background-color: $primary-color;
      transition: width 0.3s ease;
    }
  }

  .progress-stats {
    display: flex;
    justify-content: space-around;
  }

  .stat-item {
    text-align: center;

    .stat-label {
      display: block;
      font-size: $font-size-xs;
      color: $text-color-secondary;
      margin-bottom: $spacing-xs;
    }

    .stat-value {
      font-size: $font-size-sm;
      font-weight: 600;
      color: $primary-color;
    }
  }
}

.question-section {
  margin-bottom: $spacing-lg;

  .question-header {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    margin-bottom: $spacing-md;

    .question-number {
      font-size: $font-size-md;
      font-weight: 600;
      color: $primary-color;
    }

    .required-mark {
      color: $error-color;
      font-size: $font-size-lg;
    }
  }

  .question-content {
    margin-bottom: $spacing-lg;

    .question-text {
      font-size: $font-size-md;
      color: $text-color-primary;
      line-height: 1.6;
      display: block;
      margin-bottom: $spacing-md;
    }

    .question-description {
      padding: $spacing-sm $spacing-md;
      background-color: $bg-color-light;
      border-radius: $border-radius-sm;
      margin-bottom: $spacing-md;

      text {
        font-size: $font-size-sm;
        color: $text-color-secondary;
        line-height: 1.5;
      }
    }

    .question-image {
      margin-bottom: $spacing-md;

      .image {
        width: 100%;
        border-radius: $border-radius-md;
        cursor: pointer;
      }
    }
  }

  .answer-section {
    margin-bottom: $spacing-lg;
  }

  .answer-options {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
  }

  .option-item {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    padding: $spacing-md;
    border: 1px solid $border-color-light;
    border-radius: $border-radius-md;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: $primary-color;
    }

    &.active {
      border-color: $primary-color;
      background-color: rgba($primary-color, 0.05);
    }
  }

  .option-radio {
    width: 18px;
    height: 18px;
    border: 2px solid $border-color-base;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .radio-checked {
      width: 10px;
      height: 10px;
      background-color: $primary-color;
      border-radius: 50%;
    }
  }

  .option-checkbox {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .checkbox-empty {
      width: 16px;
      height: 16px;
      border: 2px solid $border-color-base;
      border-radius: $border-radius-xs;
    }
  }

  .option-text {
    flex: 1;
    font-size: $font-size-sm;
    color: $text-color-primary;
    line-height: 1.5;
  }

  .option-score {
    font-size: $font-size-xs;
    color: $text-color-secondary;
  }

  .rating-section {
    .rating-scale {
      display: flex;
      justify-content: space-between;
      margin-bottom: $spacing-md;
    }

    .rating-item {
      width: 40px;
      height: 40px;
      border: 2px solid $border-color-base;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: $primary-color;
      }

      &.active {
        border-color: $primary-color;
        background-color: $primary-color;

        .rating-number {
          color: $bg-color-white;
        }
      }
    }

    .rating-number {
      font-size: $font-size-md;
      font-weight: 600;
      color: $text-color-primary;
    }

    .rating-labels {
      display: flex;
      justify-content: space-between;

      .rating-label {
        font-size: $font-size-xs;
        color: $text-color-secondary;
      }
    }
  }

  .text-section,
  .number-section {
    .input {
      width: 100%;
    }
  }

  .question-note {
    padding: $spacing-sm $spacing-md;
    background-color: #fff7e6;
    border-left: 4px solid #faad14;
    border-radius: $border-radius-sm;

    text {
      font-size: $font-size-xs;
      color: #d48806;
      line-height: 1.5;
    }
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: $spacing-xs;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  z-index: 100;

  .button {
    flex: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .elderly-info {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }

  .progress-stats {
    flex-direction: column;
    gap: $spacing-sm;
  }

  .rating-scale {
    flex-wrap: wrap;
    gap: $spacing-sm;
  }

  .action-buttons {
    flex-wrap: wrap;

    .button {
      min-width: calc(50% - #{math.div($spacing-xs, 2)});
    }
  }
}
</style>
