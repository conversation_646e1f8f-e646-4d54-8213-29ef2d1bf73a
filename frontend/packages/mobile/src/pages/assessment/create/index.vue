<template>
  <PageContainer title="新建评估">
    <view class="form-container">
      <form @submit="handleSubmit">
        <!-- 选择老年人 -->
        <Card class="form-section">
          <template #header>
            <text class="section-title">选择老年人</text>
          </template>

          <view class="form-content">
            <!-- 搜索老年人 -->
            <FormItem label="搜索老年人" required :error="errors.elderlyId">
              <view class="elderly-selector">
                <Input
                  v-model="elderlySearchKeyword"
                  placeholder="输入姓名或身份证号搜索"
                  prefix-icon="search"
                  clearable
                  @input="handleElderlySearch"
                  @focus="showElderlyList = true"
                />

                <!-- 已选择的老年人 -->
                <view v-if="selectedElderly" class="selected-elderly">
                  <view class="elderly-card">
                    <image
                      v-if="selectedElderly.avatar"
                      :src="selectedElderly.avatar"
                      class="elderly-avatar"
                      mode="aspectFill"
                    />
                    <view v-else class="elderly-avatar-placeholder">
                      <text class="icon-text" style="font-size: 20px; color: #c0c4cc">👤</text>
                    </view>

                    <view class="elderly-info">
                      <text class="elderly-name">{{ selectedElderly.name }}</text>
                      <text class="elderly-details">
                        {{ selectedElderly.gender === 'male' ? '男' : '女' }} |
                        {{ selectedElderly.age }}岁 |
                        {{ formatIdNumber(selectedElderly.idNumber) }}
                      </text>
                    </view>

                    <Button type="default" size="mini" @click="clearSelectedElderly">
                      重新选择
                    </Button>
                  </view>
                </view>

                <!-- 老年人搜索结果 -->
                <view v-if="showElderlyList && elderlyList.length > 0" class="elderly-list">
                  <view
                    v-for="elderly in elderlyList"
                    :key="elderly.id"
                    class="elderly-item"
                    @click="selectElderly(elderly)"
                  >
                    <image
                      v-if="elderly.avatar"
                      :src="elderly.avatar"
                      class="item-avatar"
                      mode="aspectFill"
                    />
                    <view v-else class="item-avatar-placeholder">
                      <text class="icon-text" style="font-size: 16px; color: #c0c4cc">👤</text>
                    </view>

                    <view class="item-info">
                      <text class="item-name">{{ elderly.name }}</text>
                      <text class="item-details">
                        {{ elderly.gender === 'male' ? '男' : '女' }} | {{ elderly.age }}岁 |
                        {{ formatIdNumber(elderly.idNumber) }}
                      </text>
                    </view>
                  </view>
                </view>

                <!-- 无搜索结果 -->
                <view
                  v-if="showElderlyList && elderlySearchKeyword && elderlyList.length === 0"
                  class="no-results"
                >
                  <text>未找到相关老年人</text>
                  <Button type="primary" size="mini" @click="navigateToCreateElderly">
                    新增老年人
                  </Button>
                </view>
              </view>
            </FormItem>
          </view>
        </Card>

        <!-- 选择评估量表 -->
        <Card class="form-section">
          <template #header>
            <text class="section-title">选择评估量表</text>
          </template>

          <view class="form-content">
            <FormItem label="评估量表" required :error="errors.scaleId">
              <Picker
                v-model="formData.scaleId"
                mode="selector"
                :range="scaleOptions"
                range-key="name"
                placeholder="请选择评估量表"
                @change="handleScaleChange"
              />
            </FormItem>

            <!-- 量表详情 -->
            <view v-if="selectedScale" class="scale-details">
              <view class="scale-info">
                <text class="scale-name">{{ selectedScale.name }}</text>
                <text class="scale-description">{{ selectedScale.description }}</text>
              </view>

              <view class="scale-stats">
                <view class="stat-item">
                  <text class="stat-label">题目数量</text>
                  <text class="stat-value">{{ selectedScale.questionCount }}题</text>
                </view>
                <view class="stat-item">
                  <text class="stat-label">预计时长</text>
                  <text class="stat-value">{{ selectedScale.estimatedTime }}分钟</text>
                </view>
                <view class="stat-item">
                  <text class="stat-label">适用范围</text>
                  <text class="stat-value">{{ selectedScale.applicableRange }}</text>
                </view>
              </view>
            </view>
          </view>
        </Card>

        <!-- 评估信息 -->
        <Card class="form-section">
          <template #header>
            <text class="section-title">评估信息</text>
          </template>

          <view class="form-content">
            <!-- 评估时间 -->
            <FormItem label="评估时间" required :error="errors.assessmentTime">
              <view class="datetime-picker">
                <Picker
                  v-model="formData.assessmentDate"
                  mode="date"
                  :start="minDate"
                  :end="maxDate"
                  placeholder="选择日期"
                  @change="validateField('assessmentTime')"
                />
                <Picker
                  v-model="formData.assessmentTime"
                  mode="time"
                  placeholder="选择时间"
                  @change="validateField('assessmentTime')"
                />
              </view>
            </FormItem>

            <!-- 评估地点 -->
            <FormItem label="评估地点" :error="errors.location">
              <Input v-model="formData.location" placeholder="请输入评估地点" :maxlength="100" />
            </FormItem>

            <!-- 评估员 -->
            <FormItem label="评估员">
              <Picker
                v-model="formData.assessorId"
                mode="selector"
                :range="assessorOptions"
                range-key="name"
                placeholder="请选择评估员（默认为当前用户）"
                @change="handleAssessorChange"
              />
            </FormItem>

            <!-- 备注 -->
            <FormItem label="备注">
              <Input
                v-model="formData.notes"
                placeholder="请输入备注信息"
                type="textarea"
                :maxlength="300"
                :auto-height="true"
              />
            </FormItem>
          </view>
        </Card>
      </form>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <Button type="default" size="large" @click="handleCancel"> 取消 </Button>
      <Button type="primary" size="large" :loading="submitting" @click="handleSubmit">
        创建并开始评估
      </Button>
    </view>
  </PageContainer>
</template>

<script>
import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from '@/store'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import FormItem from '@/components/Form/FormItem.vue'
import Input from '@/components/Form/Input.vue'
import Picker from '@/components/Form/Picker.vue'
import Button from '@/components/Common/Button.vue'
import { formatIdNumber } from '@/utils'

export default {
  name: 'AssessmentCreate',

  components: {
    PageContainer,
    Card,
    FormItem,
    Input,
    Picker,
    Button
  },
  setup() {
    const userStore = useUserStore()
    const assessmentStore = useAssessmentStore()
    const elderlyStore = useElderlyStore()
    const scaleStore = useScaleStore()
    const configStore = useConfigStore()

    return {
      userStore,
      assessmentStore,
      elderlyStore,
      scaleStore,
      configStore
    }
  },

  data() {
    return {
      submitting: false,
      showElderlyList: false,
      elderlySearchKeyword: '',

      formData: {
        elderlyId: '',
        scaleId: '',
        assessmentDate: '',
        assessmentTime: '',
        location: '',
        assessorId: '',
        notes: ''
      },

      errors: {},

      selectedElderly: null,
      selectedScale: null,

      elderlyList: [],
      scaleOptions: [],
      assessorOptions: []
    }
  },

  computed: {
    userInfo() { return this.userStore.userInfo },

    minDate() {
      const today = new Date()
      return today.toISOString().split('T')[0]
    },

    maxDate() {
      const future = new Date()
      future.setMonth(future.getMonth() + 3) // 最多3个月后
      return future.toISOString().split('T')[0]
    }
  },

  onLoad() {
    this.initializeForm()
    this.loadScaleOptions()
    this.loadAssessorOptions()
  },

  methods: {
    initializeForm() {
      // 设置默认评估时间为当前时间
      const now = new Date()
      this.formData.assessmentDate = now.toISOString().split('T')[0]
      this.formData.assessmentTime = now.toTimeString().slice(0, 5)

      // 设置默认评估员为当前用户
      if (this.userInfo) {
        this.formData.assessorId = this.userInfo.id
      }
    },

    async loadScaleOptions() {
      try {
        const response = await this.getScaleList({
          page: 1,
          pageSize: 100,
          status: 'active'
        })
        this.scaleOptions = response.list
      } catch (error) {
        console.error('加载量表选项失败:', error)
        uni.showToast({
          title: '加载量表失败',
          icon: 'error'
        })
      }
    },

    async loadAssessorOptions() {
      try {
        const response = await this.getUserList({
          page: 1,
          pageSize: 100,
          role: 'assessor'
        })
        this.assessorOptions = response.list
      } catch (error) {
        console.error('加载评估员选项失败:', error)
      }
    },

    async handleElderlySearch() {
      if (!this.elderlySearchKeyword.trim()) {
        this.elderlyList = []
        this.showElderlyList = false
        return
      }

      try {
        const response = await this.searchElderly({
          keyword: this.elderlySearchKeyword,
          page: 1,
          pageSize: 10
        })
        this.elderlyList = response.list
        this.showElderlyList = true
      } catch (error) {
        console.error('搜索老年人失败:', error)
        this.elderlyList = []
      }
    },

    selectElderly(elderly) {
      this.selectedElderly = elderly
      this.formData.elderlyId = elderly.id
      this.elderlySearchKeyword = elderly.name
      this.showElderlyList = false
      this.validateField('elderlyId')
    },

    clearSelectedElderly() {
      this.selectedElderly = null
      this.formData.elderlyId = ''
      this.elderlySearchKeyword = ''
      this.showElderlyList = false
    },

    async handleScaleChange(e) {
      const index = e.detail.value
      const scale = this.scaleOptions[index]

      if (scale) {
        this.formData.scaleId = scale.id

        try {
          // 获取量表详情
          this.selectedScale = await this.scaleStore.getScaleDetail(scale.id)
        } catch (error) {
          console.error('获取量表详情失败:', error)
          this.selectedScale = scale
        }

        this.validateField('scaleId')
      }
    },

    handleAssessorChange(e) {
      const index = e.detail.value
      const assessor = this.assessorOptions[index]

      if (assessor) {
        this.formData.assessorId = assessor.id
      }
    },

    navigateToCreateElderly() {
      uni.navigateTo({
        url: '/pages/elderly/create/index'
      })
    },

    validateField(field) {
      let error = ''

      switch (field) {
        case 'elderlyId':
          if (!this.formData.elderlyId) {
            error = '请选择老年人'
          }
          break

        case 'scaleId':
          if (!this.formData.scaleId) {
            error = '请选择评估量表'
          }
          break

        case 'assessmentTime':
          if (!this.formData.assessmentDate || !this.formData.assessmentTime) {
            error = '请选择评估时间'
          }
          break
      }

      if (error) {
        this.$set(this.errors, field, error)
      } else {
        this.$delete(this.errors, field)
      }

      return !error
    },

    validateForm() {
      const requiredFields = ['elderlyId', 'scaleId', 'assessmentTime']
      let isValid = true

      // 清空之前的错误
      this.errors = {}

      // 验证必填字段
      requiredFields.forEach(field => {
        if (!this.validateField(field)) {
          isValid = false
        }
      })

      return isValid
    },

    async handleSubmit() {
      if (!this.validateForm()) {
        uni.showToast({
          title: '请检查表单信息',
          icon: 'error'
        })
        return
      }

      try {
        this.submitting = true

        // 组合评估时间
        const assessmentDateTime = `${this.formData.assessmentDate} ${this.formData.assessmentTime}:00`

        const assessmentData = {
          elderlyId: this.formData.elderlyId,
          scaleId: this.formData.scaleId,
          assessmentTime: assessmentDateTime,
          location: this.formData.location,
          assessorId: this.formData.assessorId || this.userInfo.id,
          notes: this.formData.notes
        }

        const assessment = await this.assessmentStore.createAssessment(assessmentData)

        uni.showToast({
          title: '创建成功',
          icon: 'success'
        })

        // 跳转到评估执行页面
        setTimeout(() => {
          uni.redirectTo({
            url: `/pages/assessment/conduct/index?id=${assessment.id}`
          })
        }, 1500)
      } catch (error) {
        console.error('创建评估失败:', error)
        uni.showToast({
          title: '创建失败',
          icon: 'error'
        })
      } finally {
        this.submitting = false
      }
    },

    handleCancel() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消创建评估吗？',
        success: res => {
          if (res.confirm) {
            uni.navigateBack()
          }
        }
      })
    },

    formatIdNumber
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.form-container {
  padding-bottom: 80px; // 为底部按钮留出空间
}

.form-section {
  margin-bottom: $spacing-lg;
}

.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-color-primary;
}

.form-content {
  padding: $spacing-lg;
}

.elderly-selector {
  position: relative;
}

.selected-elderly {
  margin-top: $spacing-md;

  .elderly-card {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    padding: $spacing-md;
    background-color: $bg-color-light;
    border-radius: $border-radius-md;
    border: 1px solid $border-color-light;
  }
}

.elderly-avatar,
.item-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
}

.elderly-avatar-placeholder,
.item-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: $bg-color-light;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.elderly-info,
.item-info {
  flex: 1;

  .elderly-name,
  .item-name {
    display: block;
    font-size: $font-size-md;
    font-weight: 500;
    color: $text-color-primary;
    margin-bottom: $spacing-xs;
  }

  .elderly-details,
  .item-details {
    font-size: $font-size-sm;
    color: $text-color-secondary;
  }
}

.elderly-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: $bg-color-white;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-md;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 100;
}

.elderly-item {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-md;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: $bg-color-light;
  }

  &:not(:last-child) {
    border-bottom: 1px solid $border-color-light;
  }
}

.item-avatar {
  width: 32px;
  height: 32px;
}

.item-avatar-placeholder {
  width: 32px;
  height: 32px;
}

.no-results {
  padding: $spacing-lg;
  text-align: center;
  color: $text-color-secondary;

  text {
    display: block;
    margin-bottom: $spacing-md;
  }
}

.scale-details {
  margin-top: $spacing-md;
  padding: $spacing-md;
  background-color: $bg-color-light;
  border-radius: $border-radius-md;

  .scale-info {
    margin-bottom: $spacing-md;

    .scale-name {
      display: block;
      font-size: $font-size-md;
      font-weight: 600;
      color: $text-color-primary;
      margin-bottom: $spacing-xs;
    }

    .scale-description {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      line-height: 1.5;
    }
  }

  .scale-stats {
    display: flex;
    gap: $spacing-lg;
  }

  .stat-item {
    flex: 1;
    text-align: center;

    .stat-label {
      display: block;
      font-size: $font-size-xs;
      color: $text-color-secondary;
      margin-bottom: $spacing-xs;
    }

    .stat-value {
      font-size: $font-size-sm;
      font-weight: 600;
      color: $primary-color;
    }
  }
}

.datetime-picker {
  display: flex;
  gap: $spacing-sm;

  .picker {
    flex: 1;
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  z-index: 100;

  .button {
    flex: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-content {
    padding: $spacing-md;
  }

  .scale-stats {
    flex-direction: column;
    gap: $spacing-md;
  }

  .datetime-picker {
    flex-direction: column;
  }
}
</style>
