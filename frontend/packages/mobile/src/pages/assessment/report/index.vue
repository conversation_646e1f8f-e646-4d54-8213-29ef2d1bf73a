<template>
  <PageContainer
    :title="reportData ? `评估报告 - ${reportData.scaleName}` : '评估报告'"
    :show-back="true"
  >
    <view v-if="loading" class="loading-container">
      <Loading type="spinner" text="生成报告中..." />
    </view>

    <view v-else-if="reportData" class="report-container">
      <!-- 报告头部 -->
      <Card class="report-header">
        <view class="header-content">
          <view class="elderly-info">
            <image
              v-if="reportData.elderlyAvatar"
              :src="reportData.elderlyAvatar"
              class="elderly-avatar"
              mode="aspectFill"
            />
            <view v-else class="elderly-avatar-placeholder">
              <text class="icon-text" style="font-size: 24px; color: #c0c4cc">👤</text>
            </view>

            <view class="elderly-details">
              <text class="elderly-name">{{ reportData.elderlyName }}</text>
              <text class="elderly-meta">
                {{ reportData.elderlyGender === 'male' ? '男' : '女' }} |
                {{ reportData.elderlyAge }}岁 | 身份证：{{
                  formatIdNumber(reportData.elderlyIdNumber)
                }}
              </text>
            </view>
          </view>

          <view class="assessment-info">
            <view class="info-row">
              <text class="label">评估量表：</text>
              <text class="value">{{ reportData.scaleName }}</text>
            </view>
            <view class="info-row">
              <text class="label">评估时间：</text>
              <text class="value">{{ formatDateTime(reportData.assessmentTime) }}</text>
            </view>
            <view class="info-row">
              <text class="label">评估师：</text>
              <text class="value">{{ reportData.assessorName }}</text>
            </view>
            <view class="info-row">
              <text class="label">评估地点：</text>
              <text class="value">{{ reportData.assessmentLocation || '未填写' }}</text>
            </view>
          </view>
        </view>
      </Card>

      <!-- 评估结果概览 -->
      <Card class="result-overview">
        <template #header>
          <text class="card-title">评估结果概览</text>
        </template>

        <view class="overview-content">
          <view class="score-section">
            <view class="total-score">
              <text class="score-label">总分</text>
              <text class="score-value">{{ reportData.totalScore }}</text>
              <text class="score-max">/ {{ reportData.maxScore }}</text>
            </view>

            <view class="score-percentage">
              <text>得分率：{{ scorePercentage }}%</text>
            </view>
          </view>

          <view class="result-level">
            <view class="level-badge" :class="`level-${reportData.riskLevel}`">
              <text>{{ formatRiskLevel(reportData.riskLevel) }}</text>
            </view>

            <text class="level-description">{{ reportData.riskDescription }}</text>
          </view>
        </view>
      </Card>

      <!-- 维度得分 -->
      <Card
        v-if="reportData.dimensionScores && reportData.dimensionScores.length > 0"
        class="dimension-scores"
      >
        <template #header>
          <text class="card-title">各维度得分</text>
        </template>

        <view class="dimensions-content">
          <view
            v-for="dimension in reportData.dimensionScores"
            :key="dimension.id"
            class="dimension-item"
          >
            <view class="dimension-header">
              <text class="dimension-name">{{ dimension.name }}</text>
              <text class="dimension-score">{{ dimension.score }} / {{ dimension.maxScore }}</text>
            </view>

            <view class="dimension-progress">
              <view
                class="progress-fill"
                :style="{ width: (dimension.score / dimension.maxScore) * 100 + '%' }"
              />
            </view>

            <view v-if="dimension.description" class="dimension-description">
              <text>{{ dimension.description }}</text>
            </view>
          </view>
        </view>
      </Card>

      <!-- 详细分析 -->
      <Card class="detailed-analysis">
        <template #header>
          <text class="card-title">详细分析</text>
        </template>

        <view class="analysis-content">
          <!-- 优势分析 -->
          <view
            v-if="reportData.strengths && reportData.strengths.length > 0"
            class="analysis-section"
          >
            <view class="section-header">
              <text class="icon-text" style="font-size: 16px; color: #52c41a">✓</text>
              <text class="section-title">优势表现</text>
            </view>

            <view class="analysis-list">
              <view
                v-for="(strength, index) in reportData.strengths"
                :key="index"
                class="analysis-item"
              >
                <text>{{ strength }}</text>
              </view>
            </view>
          </view>

          <!-- 风险分析 -->
          <view v-if="reportData.risks && reportData.risks.length > 0" class="analysis-section">
            <view class="section-header">
              <text class="icon-text" style="font-size: 16px; color: #ff4d4f">⚠️</text>
              <text class="section-title">风险提示</text>
            </view>

            <view class="analysis-list">
              <view
                v-for="(risk, index) in reportData.risks"
                :key="index"
                class="analysis-item risk-item"
              >
                <text>{{ risk }}</text>
              </view>
            </view>
          </view>

          <!-- 建议 -->
          <view
            v-if="reportData.recommendations && reportData.recommendations.length > 0"
            class="analysis-section"
          >
            <view class="section-header">
              <text class="icon-text" style="font-size: 16px; color: #1890ff">💡</text>
              <text class="section-title">护理建议</text>
            </view>

            <view class="analysis-list">
              <view
                v-for="(recommendation, index) in reportData.recommendations"
                :key="index"
                class="analysis-item"
              >
                <text>{{ recommendation }}</text>
              </view>
            </view>
          </view>
        </view>
      </Card>

      <!-- 答题详情 -->
      <Card v-if="showDetails" class="answer-details">
        <template #header>
          <view class="details-header">
            <text class="card-title">答题详情</text>
            <Button type="default" size="small" @click="toggleDetails">
              {{ showDetails ? '收起' : '展开' }}
            </Button>
          </view>
        </template>

        <view class="details-content">
          <view
            v-for="(question, index) in reportData.questionDetails"
            :key="question.id"
            class="question-detail"
          >
            <view class="question-header">
              <text class="question-number">第 {{ index + 1 }} 题</text>
              <text class="question-score">{{ question.score }} / {{ question.maxScore }} 分</text>
            </view>

            <view class="question-content">
              <text class="question-text">{{ question.content }}</text>
            </view>

            <view class="answer-content">
              <text class="answer-label">答案：</text>
              <text class="answer-text">{{ formatAnswer(question) }}</text>
            </view>

            <view v-if="question.analysis" class="question-analysis">
              <text class="analysis-label">解析：</text>
              <text class="analysis-text">{{ question.analysis }}</text>
            </view>
          </view>
        </view>
      </Card>

      <!-- 操作按钮 -->
      <view class="action-section">
        <Button v-if="!showDetails" type="default" @click="toggleDetails"> 查看答题详情 </Button>

        <Button type="primary" :loading="exporting" @click="exportReport"> 导出报告 </Button>

        <Button type="default" @click="shareReport"> 分享报告 </Button>
      </view>
    </view>

    <!-- 错误状态 -->
    <Empty
      v-else
      type="error"
      description="加载报告失败"
      :show-button="true"
      button-text="重新加载"
      @button-click="loadReportData"
    />
  </PageContainer>
</template>

<script>
import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from '@/store'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import Button from '@/components/Common/Button.vue'
import Loading from '@/components/Common/Loading.vue'
import Empty from '@/components/Common/Empty.vue'
import { formatDateTime, formatIdNumber } from '@/utils'

export default {
  name: 'AssessmentReport',

  components: {
    PageContainer,
    Card,
    Button,
    Loading,
    Empty
  },
  setup() {
    const userStore = useUserStore()
    const assessmentStore = useAssessmentStore()
    const elderlyStore = useElderlyStore()
    const scaleStore = useScaleStore()
    const configStore = useConfigStore()

    return {
      userStore,
      assessmentStore,
      elderlyStore,
      scaleStore,
      configStore
    }
  },

  data() {
    return {
      loading: true,
      exporting: false,

      assessmentId: '',
      reportData: null,
      showDetails: false
    }
  },

  computed: {
    scorePercentage() {
      if (!this.reportData || this.reportData.maxScore === 0) return 0
      return Math.round((this.reportData.totalScore / this.reportData.maxScore) * 100)
    }
  },

  onLoad(options) {
    if (options.id) {
      this.assessmentId = options.id
      this.loadReportData()
    } else {
      uni.showToast({
        title: '评估ID不存在',
        icon: 'error'
      })
      uni.navigateBack()
    }
  },

  onShareAppMessage() {
    return {
      title: `${this.reportData?.elderlyName}的评估报告`,
      path: `/pages/assessment/report/index?id=${this.assessmentId}`
    }
  },

  methods: {async loadReportData() {
      try {
        this.loading = true

        this.reportData = await this.getAssessmentReport(this.assessmentId)
      } catch (error) {
        console.error('加载报告失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    toggleDetails() {
      this.showDetails = !this.showDetails
    },

    async exportReport() {
      try {
        this.exporting = true

        const result = await this.exportAssessmentReport({
          assessmentId: this.assessmentId,
          format: 'pdf'
        })

        // 下载文件
        uni.downloadFile({
          url: result.downloadUrl,
          success: res => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: '导出成功',
                icon: 'success'
              })

              // 保存到相册或文件
              uni.saveFile({
                tempFilePath: res.tempFilePath,
                success: () => {
                  uni.showToast({
                    title: '已保存到本地',
                    icon: 'success'
                  })
                }
              })
            }
          },
          fail: () => {
            uni.showToast({
              title: '下载失败',
              icon: 'error'
            })
          }
        })
      } catch (error) {
        console.error('导出报告失败:', error)
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      } finally {
        this.exporting = false
      }
    },

    shareReport() {
      // #ifdef MP-WEIXIN
      uni.showShareMenu({
        withShareTicket: true
      })
      // #endif

      // #ifdef H5
      if (navigator.share) {
        navigator.share({
          title: `${this.reportData?.elderlyName}的评估报告`,
          text: '查看详细的评估报告',
          url: window.location.href
        })
      } else {
        // 复制链接
        uni.setClipboardData({
          data: window.location.href,
          success: () => {
            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            })
          }
        })
      }
      // #endif

      // #ifdef APP-PLUS
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: `pages/assessment/report/index?id=${this.assessmentId}`,
        title: `${this.reportData?.elderlyName}的评估报告`,
        summary: '查看详细的评估报告',
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          })
        }
      })
      // #endif
    },

    formatRiskLevel(level) {
      const levelMap = {
        low: '低风险',
        medium: '中风险',
        high: '高风险',
        very_high: '极高风险'
      }
      return levelMap[level] || level
    },

    formatAnswer(question) {
      if (question.type === 'single_choice' || question.type === 'multiple_choice') {
        if (Array.isArray(question.answer)) {
          return question.answer
            .map(value => {
              const option = question.options?.find(opt => opt.value === value)
              return option ? option.label : value
            })
            .join('、')
        } else {
          const option = question.options?.find(opt => opt.value === question.answer)
          return option ? option.label : question.answer
        }
      } else if (question.type === 'rating') {
        return `${question.answer} 分`
      } else {
        return question.answer || '未作答'
      }
    },

    formatDateTime,
    formatIdNumber
  }
}
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.loading-container {
  padding: $spacing-xl;
}

.report-container {
  padding-bottom: $spacing-lg;
}

.report-header {
  margin-bottom: $spacing-lg;

  .header-content {
    padding: $spacing-lg;
  }
}

.elderly-info {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
  padding-bottom: $spacing-md;
  border-bottom: 1px solid $border-color-light;
}

.elderly-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.elderly-avatar-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: $bg-color-light;
  display: flex;
  align-items: center;
  justify-content: center;
}

.elderly-details {
  .elderly-name {
    display: block;
    font-size: $font-size-xl;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-xs;
  }

  .elderly-meta {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    line-height: 1.4;
  }
}

.assessment-info {
  .info-row {
    display: flex;
    margin-bottom: $spacing-xs;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      width: 80px;
      flex-shrink: 0;
    }

    .value {
      font-size: $font-size-sm;
      color: $text-color-primary;
      flex: 1;
    }
  }
}

.result-overview {
  margin-bottom: $spacing-lg;

  .overview-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: $spacing-lg;
  }
}

.score-section {
  text-align: center;

  .total-score {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: $spacing-xs;
    margin-bottom: $spacing-sm;

    .score-label {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }

    .score-value {
      font-size: 48px;
      font-weight: 700;
      color: $primary-color;
      line-height: 1;
    }

    .score-max {
      font-size: $font-size-lg;
      color: $text-color-secondary;
    }
  }

  .score-percentage {
    font-size: $font-size-sm;
    color: $text-color-secondary;
  }
}

.result-level {
  text-align: center;

  .level-badge {
    display: inline-block;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-lg;
    font-size: $font-size-sm;
    font-weight: 600;
    margin-bottom: $spacing-sm;

    &.level-low {
      background-color: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }

    &.level-medium {
      background-color: #fff7e6;
      color: #fa8c16;
      border: 1px solid #ffd591;
    }

    &.level-high {
      background-color: #fff2f0;
      color: #ff4d4f;
      border: 1px solid #ffb3b3;
    }

    &.level-very_high {
      background-color: #fff0f6;
      color: #c41d7f;
      border: 1px solid #ffadd2;
    }
  }

  .level-description {
    display: block;
    font-size: $font-size-sm;
    color: $text-color-secondary;
    line-height: 1.5;
  }
}

.dimension-scores {
  margin-bottom: $spacing-lg;

  .dimensions-content {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }
}

.dimension-item {
  .dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-sm;

    .dimension-name {
      font-size: $font-size-md;
      font-weight: 500;
      color: $text-color-primary;
    }

    .dimension-score {
      font-size: $font-size-sm;
      font-weight: 600;
      color: $primary-color;
    }
  }

  .dimension-progress {
    height: 8px;
    background-color: $bg-color-light;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: $spacing-sm;

    .progress-fill {
      height: 100%;
      background: linear-gradient(
        90deg,
        $primary-color,
        color.adjust($primary-color, $lightness: 10%)
      );
      transition: width 0.3s ease;
    }
  }

  .dimension-description {
    font-size: $font-size-xs;
    color: $text-color-secondary;
    line-height: 1.5;
  }
}

.detailed-analysis {
  margin-bottom: $spacing-lg;

  .analysis-content {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }
}

.analysis-section {
  .section-header {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    margin-bottom: $spacing-md;

    .section-title {
      font-size: $font-size-md;
      font-weight: 600;
      color: $text-color-primary;
    }
  }

  .analysis-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
  }

  .analysis-item {
    padding: $spacing-sm $spacing-md;
    background-color: $bg-color-light;
    border-radius: $border-radius-sm;
    border-left: 4px solid $primary-color;

    &.risk-item {
      border-left-color: $error-color;
      background-color: #fff2f0;
    }

    text {
      font-size: $font-size-sm;
      color: $text-color-primary;
      line-height: 1.6;
    }
  }
}

.answer-details {
  margin-bottom: $spacing-lg;

  .details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .details-content {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }
}

.question-detail {
  padding: $spacing-md;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-md;

  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-sm;

    .question-number {
      font-size: $font-size-sm;
      font-weight: 600;
      color: $primary-color;
    }

    .question-score {
      font-size: $font-size-sm;
      font-weight: 600;
      color: $success-color;
    }
  }

  .question-content {
    margin-bottom: $spacing-sm;

    .question-text {
      font-size: $font-size-sm;
      color: $text-color-primary;
      line-height: 1.6;
    }
  }

  .answer-content {
    display: flex;
    gap: $spacing-xs;
    margin-bottom: $spacing-sm;

    .answer-label {
      font-size: $font-size-xs;
      color: $text-color-secondary;
      flex-shrink: 0;
    }

    .answer-text {
      font-size: $font-size-xs;
      color: $text-color-primary;
      flex: 1;
    }
  }

  .question-analysis {
    display: flex;
    gap: $spacing-xs;
    padding: $spacing-sm;
    background-color: #f0f9ff;
    border-radius: $border-radius-sm;

    .analysis-label {
      font-size: $font-size-xs;
      color: $text-color-secondary;
      flex-shrink: 0;
    }

    .analysis-text {
      font-size: $font-size-xs;
      color: $text-color-primary;
      line-height: 1.5;
      flex: 1;
    }
  }
}

.action-section {
  display: flex;
  gap: $spacing-md;
  padding: $spacing-md;

  .button {
    flex: 1;
  }
}

.card-title {
  font-size: $font-size-md;
  font-weight: 600;
  color: $text-color-primary;
}

// 响应式设计
@media (max-width: 768px) {
  .elderly-info {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }

  .overview-content {
    flex-direction: column;
    gap: $spacing-md;
  }

  .action-section {
    flex-direction: column;

    .button {
      width: 100%;
    }
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;
  }

  .answer-content,
  .question-analysis {
    flex-direction: column;
    gap: $spacing-xs;
  }
}
</style>
