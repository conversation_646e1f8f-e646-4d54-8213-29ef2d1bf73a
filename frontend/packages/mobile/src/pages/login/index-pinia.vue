<template>
  <view class="login-container">
    <!-- 头部logo区域 -->
    <view class="login-header">
      <view class="logo">
        <text class="logo-icon">🏥</text>
        <text class="logo-text">智能评估平台</text>
      </view>
      <text class="subtitle">多租户登录系统</text>
    </view>

    <!-- 登录表单 -->
    <form class="login-form" @submit.prevent="handleLogin">
      <!-- 租户代码输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">机构代码</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <text class="input-icon">🏢</text>
          <input
            v-model="loginForm.tenantCode"
            class="input-field"
            type="text"
            placeholder="请输入您的机构代码"
            maxlength="50"
            @input="clearError('tenantCode')"
          />
        </view>
        <text v-if="errors.tenantCode" class="error-text">{{ errors.tenantCode }}</text>
        <text class="hint-text">示例：demo_hospital</text>
      </view>

      <!-- 用户名输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">用户名</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <text class="input-icon">👤</text>
          <input
            v-model="loginForm.username"
            class="input-field"
            type="text"
            placeholder="请输入用户名"
            maxlength="50"
            @input="clearError('username')"
          />
        </view>
        <text v-if="errors.username" class="error-text">{{ errors.username }}</text>
      </view>

      <!-- 密码输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">密码</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <text class="input-icon">🔒</text>
          <input
            v-model="loginForm.password"
            class="input-field"
            :type="showPassword ? 'text' : 'password'"
            placeholder="请输入密码"
            maxlength="50"
            @input="clearError('password')"
          />
          <text class="toggle-password" @click="togglePassword">
            {{ showPassword ? '👁️' : '👁️‍🗨️' }}
          </text>
        </view>
        <text v-if="errors.password" class="error-text">{{ errors.password }}</text>
      </view>

      <!-- 滑动验证码 -->
      <view class="form-item">
        <SlideCaptcha
          v-if="captchaToken"
          :token="captchaToken"
          @success="handleCaptchaSuccess"
          @fail="handleCaptchaFail"
          @error="handleCaptchaError"
        />
      </view>

      <!-- 记住我选项 -->
      <view class="form-options">
        <label class="remember-me">
          <checkbox
            :checked="loginForm.rememberMe"
            @change="loginForm.rememberMe = $event.detail.value[0]"
            color="#409EFF"
          />
          <text class="remember-text">记住我</text>
        </label>
      </view>

      <!-- 登录按钮 -->
      <view class="form-actions">
        <button
          class="login-button"
          type="submit"
          :disabled="loading || !captchaVerified"
          :loading="loading"
        >
          <text v-if="loading">登录中...</text>
          <text v-else-if="!captchaVerified">请先完成验证码</text>
          <text v-else>登录</text>
        </button>
      </view>
    </form>

    <!-- 演示账户信息 -->
    <view class="demo-section">
      <view class="demo-header" @click="toggleDemoInfo">
        <text class="demo-title">💡 演示账户</text>
        <text class="demo-toggle">{{ showDemo ? '收起' : '展开' }}</text>
      </view>

      <view v-if="showDemo" class="demo-content">
        <view class="demo-item" @click="fillDemoAccount('hospital')">
          <text class="demo-type">🏥 医院机构</text>
          <view class="demo-details">
            <text class="demo-text">机构代码: demo_hospital</text>
            <text class="demo-text">用户名: demo_hospital_admin</text>
            <text class="demo-text">密码: password123</text>
          </view>
        </view>

        <view class="demo-item" @click="fillDemoAccount('nursing')">
          <text class="demo-type">🏠 护理机构</text>
          <view class="demo-details">
            <text class="demo-text">机构代码: demo_nursing</text>
            <text class="demo-text">用户名: demo_nursing_admin</text>
            <text class="demo-text">密码: password123</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">智能评估平台 v2.0.0</text>
      <text class="copyright">支持多租户架构</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { login } from '@/api/auth'
import { getCaptcha, checkCaptcha } from '@/api/captcha'
import { useUserStore } from '@/store'
import SlideCaptcha from '@/components/SlideCaptcha.vue'

// 使用 Pinia store
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const showDemo = ref(false)

const loginForm = reactive({
  tenantCode: 'demo_hospital',
  username: 'demo_hospital_admin',
  password: 'password123',
  rememberMe: false
})

const errors = reactive<Record<string, string>>({})

// 验证码相关
const captchaToken = ref('')
const captchaVerification = ref('')
const captchaVerified = ref(false)

// 生命周期
onMounted(() => {
  // 检查是否已经登录
  const token = uni.getStorageSync('token')
  if (token) {
    redirectToMain()
  }

  // 自动填充上次登录信息
  loadRememberedInfo()
  
  // 获取验证码
  getCaptchaToken()
})

// 方法
const validateForm = (): boolean => {
  Object.keys(errors).forEach(key => delete errors[key])

  if (!loginForm.tenantCode.trim()) {
    errors.tenantCode = '请输入机构代码'
  }

  if (!loginForm.username.trim()) {
    errors.username = '请输入用户名'
  }

  if (!loginForm.password.trim()) {
    errors.password = '请输入密码'
  }

  return Object.keys(errors).length === 0
}

const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  if (!captchaVerified.value) {
    uni.showToast({
      title: '请先完成验证码',
      icon: 'none'
    })
    return
  }

  loading.value = true

  try {
    const response = await login({
      ...loginForm,
      captchaToken: captchaToken.value,
      captchaVerification: captchaVerification.value
    })

    if (response.code === 200) {
      const { token, userInfo } = response.data

      // 使用 Pinia store 保存登录信息
      userStore.$patch({
        token,
        userInfo
      })
      
      // 手动设置存储
      uni.setStorageSync('token', token)
      uni.setStorageSync('userInfo', userInfo)

      // 记住登录信息
      if (loginForm.rememberMe) {
        saveRememberedInfo()
      }

      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })

      setTimeout(() => {
        redirectToMain()
      }, 1500)
    } else {
      throw new Error(response.message || '登录失败')
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    uni.showToast({
      title: error.message || '登录失败，请重试',
      icon: 'none',
      duration: 2000
    })

    // 登录失败后刷新验证码
    getCaptchaToken()
    captchaVerified.value = false
  } finally {
    loading.value = false
  }
}

const getCaptchaToken = async () => {
  try {
    const response = await getCaptcha()
    if (response.code === 200) {
      captchaToken.value = response.data.token
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

const handleCaptchaSuccess = (verification: string) => {
  captchaVerification.value = verification
  captchaVerified.value = true
  uni.showToast({
    title: '验证成功',
    icon: 'success',
    duration: 1000
  })
}

const handleCaptchaFail = () => {
  captchaVerified.value = false
  uni.showToast({
    title: '验证失败，请重试',
    icon: 'none'
  })
}

const handleCaptchaError = (error: any) => {
  console.error('验证码错误:', error)
  getCaptchaToken()
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleDemoInfo = () => {
  showDemo.value = !showDemo.value
}

const fillDemoAccount = (type: 'hospital' | 'nursing') => {
  if (type === 'hospital') {
    loginForm.tenantCode = 'demo_hospital'
    loginForm.username = 'demo_hospital_admin'
    loginForm.password = 'password123'
  } else if (type === 'nursing') {
    loginForm.tenantCode = 'demo_nursing'
    loginForm.username = 'demo_nursing_admin'
    loginForm.password = 'password123'
  }
}

const clearError = (field: string) => {
  delete errors[field]
}

const saveRememberedInfo = () => {
  uni.setStorageSync('remembered_tenant', loginForm.tenantCode)
  uni.setStorageSync('remembered_username', loginForm.username)
}

const loadRememberedInfo = () => {
  const rememberedTenant = uni.getStorageSync('remembered_tenant')
  const rememberedUsername = uni.getStorageSync('remembered_username')

  if (rememberedTenant) {
    loginForm.tenantCode = rememberedTenant
  }
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
  }
}

const redirectToMain = () => {
  uni.reLaunch({
    url: '/pages/home/<USER>'
  })
}
</script>

<style lang="scss">
/* 样式保持不变 */
</style>