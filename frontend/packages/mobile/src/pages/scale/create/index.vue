<template>
  <PageContainer :title="isEdit ? '编辑量表' : '创建量表'" :show-back="true">
    <Loading v-if="loading" type="spinner" text="加载中..." />

    <view v-else class="scale-create">
      <!-- 基本信息 -->
      <Card title="基本信息" class="basic-info-card">
        <view class="form-content">
          <FormItem label="量表名称" required>
            <Input
              v-model="formData.name"
              placeholder="请输入量表名称"
              :maxlength="50"
              @blur="validateField('name')"
            />
            <text v-if="errors.name" class="error-text">{{ errors.name }}</text>
          </FormItem>

          <FormItem label="量表分类" required>
            <Picker
              :range="categoryOptions"
              range-key="label"
              :value="categoryIndex"
              @change="onCategoryChange"
            >
              <view class="picker-display">
                <text :class="{ placeholder: !formData.category }">{{
                  formData.category || '请选择量表分类'
                }}</text>
                <text class="picker-arrow">></text>
              </view>
            </Picker>
            <text v-if="errors.category" class="error-text">{{ errors.category }}</text>
          </FormItem>

          <FormItem label="版本号">
            <Input v-model="formData.version" placeholder="如：1.0" :maxlength="10" />
          </FormItem>

          <FormItem label="预计时长">
            <view class="time-input">
              <Input
                v-model="formData.estimatedTime"
                type="number"
                placeholder="请输入时长"
                @blur="validateField('estimatedTime')"
              />
              <text class="time-unit">分钟</text>
            </view>
            <text v-if="errors.estimatedTime" class="error-text">{{ errors.estimatedTime }}</text>
          </FormItem>

          <FormItem label="量表描述">
            <Input
              v-model="formData.description"
              type="textarea"
              placeholder="请输入量表描述"
              :maxlength="500"
              :auto-height="true"
            />
          </FormItem>

          <FormItem label="使用说明">
            <Input
              v-model="formData.instructions"
              type="textarea"
              placeholder="请输入使用说明"
              :maxlength="1000"
              :auto-height="true"
            />
          </FormItem>
        </view>
      </Card>

      <!-- 评分规则 -->
      <Card title="评分规则" class="scoring-card">
        <view class="form-content">
          <FormItem label="总分范围">
            <view class="score-range">
              <Input
                v-model="formData.scoringRules.totalScore.min"
                type="number"
                placeholder="最低分"
                @blur="validateScoreRange"
              />
              <text class="range-separator">-</text>
              <Input
                v-model="formData.scoringRules.totalScore.max"
                type="number"
                placeholder="最高分"
                @blur="validateScoreRange"
              />
            </view>
            <text v-if="errors.scoreRange" class="error-text">{{ errors.scoreRange }}</text>
          </FormItem>

          <FormItem label="评估等级">
            <view class="levels-section">
              <view
                v-for="(level, index) in formData.scoringRules.levels"
                :key="index"
                class="level-item"
              >
                <view class="level-header">
                  <text class="level-index">等级 {{ index + 1 }}</text>
                  <Button
                    v-if="formData.scoringRules.levels.length > 1"
                    type="text"
                    size="small"
                    @click="removeLevel(index)"
                  >
                    删除
                  </Button>
                </view>

                <view class="level-form">
                  <FormItem label="等级名称">
                    <Input
                      v-model="level.name"
                      placeholder="如：正常、轻度、中度、重度"
                      :maxlength="20"
                    />
                  </FormItem>

                  <FormItem label="分数范围">
                    <view class="score-range">
                      <Input v-model="level.minScore" type="number" placeholder="最低分" />
                      <text class="range-separator">-</text>
                      <Input v-model="level.maxScore" type="number" placeholder="最高分" />
                    </view>
                  </FormItem>

                  <FormItem label="等级描述">
                    <Input
                      v-model="level.description"
                      type="textarea"
                      placeholder="请输入等级描述"
                      :maxlength="200"
                      :auto-height="true"
                    />
                  </FormItem>
                </view>
              </view>

              <Button type="dashed" class="add-level-btn" @click="addLevel"> + 添加等级 </Button>
            </view>
          </FormItem>
        </view>
      </Card>

      <!-- 题目管理 -->
      <Card title="题目管理" class="questions-card">
        <view class="questions-header">
          <text class="questions-count">共 {{ formData.questions.length }} 道题目</text>
          <Button type="primary" size="small" @click="addQuestion"> + 添加题目 </Button>
        </view>

        <view v-if="formData.questions.length === 0" class="empty-questions">
          <text>暂无题目，请添加题目</text>
        </view>

        <view v-else class="questions-list">
          <view
            v-for="(question, index) in formData.questions"
            :key="question.tempId || question.id"
            class="question-item"
          >
            <view class="question-header">
              <view class="question-info">
                <text class="question-number">{{ index + 1 }}</text>
                <text class="question-title">{{ question.title || '未设置标题' }}</text>
                <text class="question-type">{{ getQuestionTypeText(question.type) }}</text>
              </view>

              <view class="question-actions">
                <Button type="text" size="small" @click="editQuestion(index)"> 编辑 </Button>
                <Button type="text" size="small" @click="copyQuestion(index)"> 复制 </Button>
                <Button type="text" size="small" @click="removeQuestion(index)"> 删除 </Button>
              </view>
            </view>

            <view class="question-preview">
              <text v-if="question.description" class="question-description">{{
                question.description
              }}</text>

              <!-- 选择题预览 -->
              <view
                v-if="question.type === 'single_choice' || question.type === 'multiple_choice'"
                class="options-preview"
              >
                <text
                  v-for="(option, optIndex) in question.options.slice(0, 3)"
                  :key="optIndex"
                  class="option-preview"
                >
                  {{ option.text }}
                </text>
                <text v-if="question.options.length > 3" class="more-options"
                  >等{{ question.options.length }}个选项</text
                >
              </view>

              <!-- 评分题预览 -->
              <view v-else-if="question.type === 'rating'" class="rating-preview">
                <text>评分范围：{{ question.minScore }} - {{ question.maxScore }}分</text>
              </view>
            </view>
          </view>
        </view>
      </Card>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <Button type="default" :loading="saving" @click="saveDraft"> 保存草稿 </Button>

      <Button type="primary" :loading="publishing" @click="saveAndPublish"> 保存并发布 </Button>
    </view>

    <!-- 题目编辑弹窗 -->
    <QuestionEditor
      v-if="showQuestionEditor"
      :visible="showQuestionEditor"
      :question="currentQuestion"
      :question-index="currentQuestionIndex"
      @close="closeQuestionEditor"
      @save="saveQuestion"
    />
  </PageContainer>
</template>

<script>
import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from '@/store'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import FormItem from '@/components/Form/FormItem.vue'
import Input from '@/components/Form/Input.vue'
import Picker from '@/components/Form/Picker.vue'
import Button from '@/components/Common/Button.vue'
import Loading from '@/components/Common/Loading.vue'
import QuestionEditor from '@/components/Scale/QuestionEditor.vue'

export default {
  name: 'ScaleCreate',

  components: {
    PageContainer,
    Card,
    FormItem,
    Input,
    Picker,
    Button,
    Loading,
    QuestionEditor
  },
  setup() {
    const userStore = useUserStore()
    const assessmentStore = useAssessmentStore()
    const elderlyStore = useElderlyStore()
    const scaleStore = useScaleStore()
    const configStore = useConfigStore()

    return {
      userStore,
      assessmentStore,
      elderlyStore,
      scaleStore,
      configStore
    }
  },

  data() {
    return {
      loading: false,
      saving: false,
      publishing: false,
      isEdit: false,
      scaleId: '',

      formData: {
        name: '',
        category: '',
        version: '1.0',
        estimatedTime: '',
        description: '',
        instructions: '',
        scoringRules: {
          totalScore: {
            min: '',
            max: ''
          },
          levels: [
            {
              name: '',
              minScore: '',
              maxScore: '',
              description: ''
            }
          ]
        },
        questions: []
      },

      errors: {},

      categoryOptions: [
        { label: '认知功能评估', value: 'cognitive' },
        { label: '日常生活能力', value: 'daily_living' },
        { label: '心理健康评估', value: 'mental_health' },
        { label: '社会功能评估', value: 'social_function' },
        { label: '身体功能评估', value: 'physical_function' },
        { label: '综合评估', value: 'comprehensive' },
        { label: '其他', value: 'other' }
      ],

      showQuestionEditor: false,
      currentQuestion: null,
      currentQuestionIndex: -1
    }
  },

  computed: {
    categoryIndex() {
      return this.categoryOptions.findIndex(item => item.value === this.formData.category)
    }
  },

  onLoad(options) {
    if (options.id) {
      this.isEdit = true
      this.scaleId = options.id
      this.loadScaleData()
    }
  },

  methods: {async loadScaleData() {
      try {
        this.loading = true
        const scaleDetail = await this.scaleStore.getScaleDetail(this.scaleId)

        // 填充表单数据
        this.formData = {
          ...this.formData,
          ...scaleDetail,
          scoringRules: {
            totalScore: scaleDetail.scoringRules?.totalScore || { min: '', max: '' },
            levels: scaleDetail.scoringRules?.levels || [
              {
                name: '',
                minScore: '',
                maxScore: '',
                description: ''
              }
            ]
          },
          questions: scaleDetail.questions || []
        }
      } catch (error) {
        console.error('加载量表数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    onCategoryChange(e) {
      const index = e.detail.value
      this.formData.category = this.categoryOptions[index].value
      this.validateField('category')
    },

    validateField(field) {
      this.errors = { ...this.errors }

      switch (field) {
        case 'name':
          if (!this.formData.name.trim()) {
            this.errors.name = '请输入量表名称'
          } else {
            delete this.errors.name
          }
          break

        case 'category':
          if (!this.formData.category) {
            this.errors.category = '请选择量表分类'
          } else {
            delete this.errors.category
          }
          break

        case 'estimatedTime':
          if (
            this.formData.estimatedTime &&
            (isNaN(this.formData.estimatedTime) || this.formData.estimatedTime <= 0)
          ) {
            this.errors.estimatedTime = '请输入有效的时长'
          } else {
            delete this.errors.estimatedTime
          }
          break
      }
    },

    validateScoreRange() {
      const min = Number(this.formData.scoringRules.totalScore.min)
      const max = Number(this.formData.scoringRules.totalScore.max)

      if (min && max && min >= max) {
        this.errors.scoreRange = '最高分必须大于最低分'
      } else {
        delete this.errors.scoreRange
      }
    },

    validateForm() {
      this.errors = {}

      // 验证必填字段
      if (!this.formData.name.trim()) {
        this.errors.name = '请输入量表名称'
      }

      if (!this.formData.category) {
        this.errors.category = '请选择量表分类'
      }

      if (
        this.formData.estimatedTime &&
        (isNaN(this.formData.estimatedTime) || this.formData.estimatedTime <= 0)
      ) {
        this.errors.estimatedTime = '请输入有效的时长'
      }

      // 验证分数范围
      const min = Number(this.formData.scoringRules.totalScore.min)
      const max = Number(this.formData.scoringRules.totalScore.max)
      if (min && max && min >= max) {
        this.errors.scoreRange = '最高分必须大于最低分'
      }

      // 验证题目
      if (this.formData.questions.length === 0) {
        this.errors.questions = '请至少添加一道题目'
        uni.showToast({
          title: '请至少添加一道题目',
          icon: 'error'
        })
      }

      return Object.keys(this.errors).length === 0
    },

    addLevel() {
      this.formData.scoringRules.levels.push({
        name: '',
        minScore: '',
        maxScore: '',
        description: ''
      })
    },

    removeLevel(index) {
      if (this.formData.scoringRules.levels.length > 1) {
        this.formData.scoringRules.levels.splice(index, 1)
      }
    },

    addQuestion() {
      this.currentQuestion = {
        tempId: Date.now(),
        title: '',
        description: '',
        type: 'single_choice',
        required: true,
        options: [
          { text: '', score: 0 },
          { text: '', score: 1 }
        ]
      }
      this.currentQuestionIndex = -1
      this.showQuestionEditor = true
    },

    editQuestion(index) {
      this.currentQuestion = JSON.parse(JSON.stringify(this.formData.questions[index]))
      this.currentQuestionIndex = index
      this.showQuestionEditor = true
    },

    copyQuestion(index) {
      const originalQuestion = this.formData.questions[index]
      const copiedQuestion = {
        ...JSON.parse(JSON.stringify(originalQuestion)),
        tempId: Date.now(),
        title: `${originalQuestion.title} (副本)`
      }
      delete copiedQuestion.id

      this.formData.questions.splice(index + 1, 0, copiedQuestion)

      uni.showToast({
        title: '题目已复制',
        icon: 'success'
      })
    },

    removeQuestion(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这道题目吗？',
        success: res => {
          if (res.confirm) {
            this.formData.questions.splice(index, 1)
            uni.showToast({
              title: '题目已删除',
              icon: 'success'
            })
          }
        }
      })
    },

    closeQuestionEditor() {
      this.showQuestionEditor = false
      this.currentQuestion = null
      this.currentQuestionIndex = -1
    },

    saveQuestion(questionData) {
      if (this.currentQuestionIndex >= 0) {
        // 编辑现有题目
        this.formData.questions.splice(this.currentQuestionIndex, 1, questionData)
      } else {
        // 添加新题目
        this.formData.questions.push(questionData)
      }

      this.closeQuestionEditor()

      uni.showToast({
        title: '题目已保存',
        icon: 'success'
      })
    },

    getQuestionTypeText(type) {
      const typeMap = {
        single_choice: '单选题',
        multiple_choice: '多选题',
        rating: '评分题',
        text: '文本题',
        number: '数字题'
      }
      return typeMap[type] || '未知类型'
    },

    async saveDraft() {
      if (!this.validateForm()) {
        return
      }

      try {
        this.saving = true

        const scaleData = {
          ...this.formData,
          status: 'draft'
        }

        if (this.isEdit) {
          await this.scaleStore.updateScale({ id: this.scaleId, data: scaleData })
        } else {
          const result = await this.scaleStore.createScale(scaleData)
          this.scaleId = result.id
          this.isEdit = true
        }

        uni.showToast({
          title: '草稿已保存',
          icon: 'success'
        })
      } catch (error) {
        console.error('保存草稿失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    },

    async saveAndPublish() {
      if (!this.validateForm()) {
        return
      }

      try {
        this.publishing = true

        const scaleData = {
          ...this.formData,
          status: 'published'
        }

        let scaleId = this.scaleId

        if (this.isEdit) {
          await this.scaleStore.updateScale({ id: this.scaleId, data: scaleData })
        } else {
          const result = await this.scaleStore.createScale(scaleData)
          scaleId = result.id
        }

        // 如果是从草稿发布，需要调用发布接口
        if (this.formData.status === 'draft') {
          await this.publishScale(scaleId)
        }

        uni.showToast({
          title: '量表已发布',
          icon: 'success'
        })

        // 返回量表列表
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        console.error('发布失败:', error)
        uni.showToast({
          title: '发布失败',
          icon: 'error'
        })
      } finally {
        this.publishing = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.scale-create {
  padding: $spacing-md;
  padding-bottom: 80px; // 为底部按钮留出空间
}

.basic-info-card,
.scoring-card,
.questions-card {
  margin-bottom: $spacing-md;
}

.form-content {
  .time-input {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    .time-unit {
      font-size: $font-size-base;
      color: $text-color-secondary;
    }
  }

  .score-range {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    .range-separator {
      font-size: $font-size-base;
      color: $text-color-secondary;
    }
  }

  .error-text {
    font-size: $font-size-xs;
    color: $color-danger;
    margin-top: $spacing-xs;
  }
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-sm $spacing-md;
  background-color: $bg-color-light;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-sm;

  text {
    font-size: $font-size-base;

    &.placeholder {
      color: $text-color-placeholder;
    }
  }

  .picker-arrow {
    color: $text-color-secondary;
    transform: rotate(90deg);
  }
}

.levels-section {
  .level-item {
    margin-bottom: $spacing-md;
    padding: $spacing-md;
    background-color: $bg-color-light;
    border-radius: $border-radius-sm;

    .level-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-sm;

      .level-index {
        font-size: $font-size-base;
        font-weight: 500;
        color: $text-color-primary;
      }
    }
  }

  .add-level-btn {
    width: 100%;
    margin-top: $spacing-sm;
  }
}

.questions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;

  .questions-count {
    font-size: $font-size-base;
    color: $text-color-secondary;
  }
}

.empty-questions {
  text-align: center;
  padding: $spacing-xl;
  color: $text-color-secondary;
}

.questions-list {
  .question-item {
    margin-bottom: $spacing-md;
    padding: $spacing-md;
    background-color: $bg-color-light;
    border-radius: $border-radius-sm;

    .question-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: $spacing-sm;

      .question-info {
        flex: 1;
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        .question-number {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: $color-primary;
          color: white;
          border-radius: 50%;
          font-size: $font-size-xs;
          font-weight: 600;
          flex-shrink: 0;
        }

        .question-title {
          flex: 1;
          font-size: $font-size-base;
          color: $text-color-primary;
          font-weight: 500;
        }

        .question-type {
          font-size: $font-size-xs;
          color: $text-color-secondary;
          padding: 2px 6px;
          background-color: $bg-color-white;
          border-radius: $border-radius-xs;
        }
      }

      .question-actions {
        display: flex;
        gap: $spacing-xs;
      }
    }

    .question-preview {
      margin-left: 32px; // 对齐题目内容

      .question-description {
        font-size: $font-size-sm;
        color: $text-color-secondary;
        line-height: 1.5;
        margin-bottom: $spacing-xs;
      }

      .options-preview {
        display: flex;
        flex-direction: column;
        gap: $spacing-xs;

        .option-preview {
          font-size: $font-size-sm;
          color: $text-color-secondary;

          &::before {
            content: '• ';
            margin-right: $spacing-xs;
          }
        }

        .more-options {
          font-size: $font-size-xs;
          color: $text-color-placeholder;
          font-style: italic;
        }
      }

      .rating-preview {
        font-size: $font-size-sm;
        color: $text-color-secondary;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  z-index: 100;
}

// 响应式设计
@media (max-width: 768px) {
  .score-range {
    flex-direction: column;
    align-items: stretch;

    .range-separator {
      text-align: center;
    }
  }

  .question-header {
    flex-direction: column;
    gap: $spacing-sm;

    .question-actions {
      align-self: flex-end;
    }
  }

  .bottom-actions {
    flex-direction: column;
  }
}
</style>
