<template>
  <PageContainer :title="scaleDetail.name || '量表详情'" :show-back="true">
    <Loading v-if="loading" type="spinner" text="加载中..." />

    <Empty
      v-else-if="!scaleDetail.id"
      type="error"
      description="量表不存在或已被删除"
      :show-button="true"
      button-text="返回列表"
      @button-click="goBack"
    />

    <view v-else class="scale-detail">
      <!-- 基本信息 -->
      <Card title="基本信息" class="info-card">
        <view class="info-content">
          <view class="info-header">
            <view class="scale-title">
              <text class="scale-name">{{ scaleDetail.name }}</text>
              <view class="scale-status" :class="`status-${scaleDetail.status}`">
                <text>{{ formatStatus(scaleDetail.status) }}</text>
              </view>
            </view>

            <view class="scale-meta">
              <text class="scale-category">{{ scaleDetail.category }}</text>
              <text class="scale-version">版本 v{{ scaleDetail.version }}</text>
            </view>
          </view>

          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">题目数量</text>
              <text class="info-value">{{ scaleDetail.questionCount }}题</text>
            </view>

            <view class="info-item">
              <text class="info-label">预计时长</text>
              <text class="info-value">{{ scaleDetail.estimatedTime }}分钟</text>
            </view>

            <view class="info-item">
              <text class="info-label">使用次数</text>
              <text class="info-value">{{ scaleDetail.usageCount }}次</text>
            </view>

            <view class="info-item">
              <text class="info-label">创建时间</text>
              <text class="info-value">{{ formatDate(scaleDetail.createdAt) }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">创建者</text>
              <text class="info-value">{{ scaleDetail.creatorName }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">最后更新</text>
              <text class="info-value">{{ formatDate(scaleDetail.updatedAt) }}</text>
            </view>
          </view>

          <view v-if="scaleDetail.description" class="description">
            <text class="description-label">量表描述</text>
            <text class="description-text">{{ scaleDetail.description }}</text>
          </view>

          <view v-if="scaleDetail.instructions" class="instructions">
            <text class="instructions-label">使用说明</text>
            <text class="instructions-text">{{ scaleDetail.instructions }}</text>
          </view>
        </view>
      </Card>

      <!-- 评分规则 -->
      <Card v-if="scaleDetail.scoringRules" title="评分规则" class="scoring-card">
        <view class="scoring-content">
          <view v-if="scaleDetail.scoringRules.totalScore" class="scoring-section">
            <text class="scoring-title">总分范围</text>
            <text class="scoring-text"
              >{{ scaleDetail.scoringRules.totalScore.min }} -
              {{ scaleDetail.scoringRules.totalScore.max }}分</text
            >
          </view>

          <view
            v-if="scaleDetail.scoringRules.levels && scaleDetail.scoringRules.levels.length > 0"
            class="scoring-section"
          >
            <text class="scoring-title">评估等级</text>
            <view class="levels-list">
              <view
                v-for="level in scaleDetail.scoringRules.levels"
                :key="level.id"
                class="level-item"
              >
                <view class="level-header">
                  <text class="level-name">{{ level.name }}</text>
                  <text class="level-range">{{ level.minScore }} - {{ level.maxScore }}分</text>
                </view>
                <text v-if="level.description" class="level-description">{{
                  level.description
                }}</text>
              </view>
            </view>
          </view>

          <view
            v-if="
              scaleDetail.scoringRules.dimensions && scaleDetail.scoringRules.dimensions.length > 0
            "
            class="scoring-section"
          >
            <text class="scoring-title">维度评分</text>
            <view class="dimensions-list">
              <view
                v-for="dimension in scaleDetail.scoringRules.dimensions"
                :key="dimension.id"
                class="dimension-item"
              >
                <view class="dimension-header">
                  <text class="dimension-name">{{ dimension.name }}</text>
                  <text class="dimension-weight">权重: {{ dimension.weight }}%</text>
                </view>
                <text v-if="dimension.description" class="dimension-description">{{
                  dimension.description
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </Card>

      <!-- 题目预览 -->
      <Card title="题目预览" class="questions-card">
        <view class="questions-content">
          <view class="questions-header">
            <text class="questions-count">共 {{ questionList.length }} 道题目</text>
            <Button type="default" size="small" @click="toggleQuestionsExpand">
              {{ showAllQuestions ? '收起' : '展开全部' }}
            </Button>
          </view>

          <view class="questions-list">
            <view
              v-for="(question, index) in displayQuestions"
              :key="question.id"
              class="question-item"
            >
              <view class="question-header">
                <text class="question-number">{{ index + 1 }}.</text>
                <text class="question-title">{{ question.title }}</text>
                <view v-if="question.required" class="question-required">
                  <text>*</text>
                </view>
              </view>

              <text v-if="question.description" class="question-description">{{
                question.description
              }}</text>

              <view class="question-options">
                <view
                  v-if="question.type === 'single_choice' || question.type === 'multiple_choice'"
                  class="choice-options"
                >
                  <view v-for="option in question.options" :key="option.id" class="option-item">
                    <text class="option-label">{{ option.label }}</text>
                    <text class="option-text">{{ option.text }}</text>
                    <text v-if="option.score !== undefined" class="option-score"
                      >({{ option.score }}分)</text
                    >
                  </view>
                </view>

                <view v-else-if="question.type === 'rating'" class="rating-options">
                  <text class="rating-range"
                    >评分范围: {{ question.minScore }} - {{ question.maxScore }}分</text
                  >
                  <text v-if="question.step" class="rating-step">步长: {{ question.step }}</text>
                </view>

                <view v-else-if="question.type === 'text'" class="text-options">
                  <text class="text-placeholder">{{
                    question.placeholder || '请输入文本答案'
                  }}</text>
                  <text v-if="question.maxLength" class="text-limit"
                    >最大长度: {{ question.maxLength }}字符</text
                  >
                </view>

                <view v-else-if="question.type === 'number'" class="number-options">
                  <text class="number-range"
                    >数值范围: {{ question.min || '无限制' }} - {{ question.max || '无限制' }}</text
                  >
                  <text v-if="question.unit" class="number-unit">单位: {{ question.unit }}</text>
                </view>
              </view>
            </view>
          </view>

          <view v-if="!showAllQuestions && questionList.length > 3" class="questions-more">
            <text>还有 {{ questionList.length - 3 }} 道题目...</text>
          </view>
        </view>
      </Card>

      <!-- 使用统计 -->
      <Card v-if="usageStats" title="使用统计" class="stats-card">
        <view class="stats-content">
          <view class="stats-grid">
            <view class="stat-item">
              <text class="stat-value">{{ usageStats.totalAssessments }}</text>
              <text class="stat-label">总评估次数</text>
            </view>

            <view class="stat-item">
              <text class="stat-value">{{ usageStats.thisMonthAssessments }}</text>
              <text class="stat-label">本月评估</text>
            </view>

            <view class="stat-item">
              <text class="stat-value">{{ usageStats.averageScore }}</text>
              <text class="stat-label">平均得分</text>
            </view>

            <view class="stat-item">
              <text class="stat-value">{{ usageStats.averageTime }}</text>
              <text class="stat-label">平均用时(分钟)</text>
            </view>
          </view>

          <view
            v-if="usageStats.recentAssessments && usageStats.recentAssessments.length > 0"
            class="recent-assessments"
          >
            <text class="recent-title">最近评估</text>
            <view class="recent-list">
              <view
                v-for="assessment in usageStats.recentAssessments"
                :key="assessment.id"
                class="recent-item"
                @click="viewAssessment(assessment)"
              >
                <view class="recent-info">
                  <text class="recent-elderly">{{ assessment.elderlyName }}</text>
                  <text class="recent-time">{{ formatDate(assessment.createdAt) }}</text>
                </view>
                <view class="recent-result">
                  <text class="recent-score">{{ assessment.totalScore }}分</text>
                  <text class="recent-level">{{ assessment.riskLevel }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </Card>
    </view>

    <!-- 底部操作按钮 -->
    <view v-if="scaleDetail.id" class="bottom-actions">
      <Button type="default" @click="previewScale"> 预览量表 </Button>

      <Button v-if="scaleDetail.status === 'published'" type="primary" @click="useScale">
        开始评估
      </Button>

      <Button v-else-if="scaleDetail.status === 'draft'" type="primary" @click="editScale">
        编辑量表
      </Button>

      <Button type="default" @click="showMoreActions"> 更多操作 </Button>
    </view>

    <!-- 更多操作弹窗 -->
    <ActionSheet
      v-model="showActionSheet"
      :actions="actionSheetActions"
      @select="handleActionSelect"
    />
  </PageContainer>
</template>

<script>
import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from '@/store'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import Button from '@/components/Common/Button.vue'
import Loading from '@/components/Common/Loading.vue'
import Empty from '@/components/Common/Empty.vue'
import ActionSheet from '@/components/Common/ActionSheet.vue'
import { formatDate } from '@/utils'

export default {
  name: 'ScaleDetail',

  components: {
    PageContainer,
    Card,
    Button,
    Loading,
    Empty,
    ActionSheet
  },
  setup() {
    const userStore = useUserStore()
    const assessmentStore = useAssessmentStore()
    const elderlyStore = useElderlyStore()
    const scaleStore = useScaleStore()
    const configStore = useConfigStore()

    return {
      userStore,
      assessmentStore,
      elderlyStore,
      scaleStore,
      configStore
    }
  },

  data() {
    return {
      loading: false,
      scaleId: '',
      scaleDetail: {},
      questionList: [],
      usageStats: null,

      showAllQuestions: false,
      showActionSheet: false
    }
  },

  computed: {
    userInfo() { return this.userStore.userInfo },

    displayQuestions() {
      if (this.showAllQuestions) {
        return this.questionList
      }
      return this.questionList.slice(0, 3)
    },

    actionSheetActions() {
      const actions = [
        { text: '预览量表', value: 'preview' },
        { text: '导出量表', value: 'export' },
        { text: '分享量表', value: 'share' }
      ]

      if (this.scaleDetail.status === 'draft') {
        actions.unshift({ text: '编辑量表', value: 'edit' }, { text: '发布量表', value: 'publish' })
      } else if (this.scaleDetail.status === 'published') {
        actions.unshift(
          { text: '开始评估', value: 'use' },
          { text: '复制量表', value: 'copy' },
          { text: '停用量表', value: 'disable' }
        )
      } else if (this.scaleDetail.status === 'disabled') {
        actions.unshift(
          { text: '启用量表', value: 'enable' },
          { text: '删除量表', value: 'delete', color: '#ff4d4f' }
        )
      }

      return actions
    }
  },

  onLoad(options) {
    if (options.id) {
      this.scaleId = options.id
      this.loadScaleDetail()
    }
  },

  onPullDownRefresh() {
    this.loadScaleDetail().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {

    async loadScaleDetail() {
      try {
        this.loading = true

        // 并行加载数据
        const [detailResult, questionsResult, statsResult] = await Promise.all([
          this.scaleStore.getScaleDetail(this.scaleId),
          this.scaleStore.getScaleQuestions(this.scaleId),
          this.scaleStore.getScaleUsageStats(this.scaleId)
        ])

        this.scaleDetail = detailResult
        this.questionList = questionsResult
        this.usageStats = statsResult
      } catch (error) {
        console.error('加载量表详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    toggleQuestionsExpand() {
      this.showAllQuestions = !this.showAllQuestions
    },

    previewScale() {
      uni.navigateTo({
        url: `/pages/scale/preview/index?id=${this.scaleId}`
      })
    },

    editScale() {
      uni.navigateTo({
        url: `/pages/scale/create/index?id=${this.scaleId}`
      })
    },

    useScale() {
      uni.navigateTo({
        url: `/pages/assessment/create/index?scaleId=${this.scaleId}`
      })
    },

    viewAssessment(assessment) {
      uni.navigateTo({
        url: `/pages/assessment/report/index?id=${assessment.id}`
      })
    },

    showMoreActions() {
      this.showActionSheet = true
    },

    async handleActionSelect(action) {
      switch (action.value) {
        case 'preview':
          this.previewScale()
          break

        case 'edit':
          this.editScale()
          break

        case 'use':
          this.useScale()
          break

        case 'publish':
          await this.handlePublishScale()
          break

        case 'disable':
          await this.handleDisableScale()
          break

        case 'enable':
          await this.handleEnableScale()
          break

        case 'copy':
          await this.handleCopyScale()
          break

        case 'delete':
          await this.handleDeleteScale()
          break

        case 'export':
          await this.handleExportScale()
          break

        case 'share':
          this.handleShareScale()
          break
      }
    },

    async handlePublishScale() {
      try {
        await this.publishScale(this.scaleId)
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        })
        this.loadScaleDetail()
      } catch (error) {
        uni.showToast({
          title: '发布失败',
          icon: 'error'
        })
      }
    },

    async handleDisableScale() {
      uni.showModal({
        title: '确认停用',
        content: '停用后将无法使用该量表进行评估，确定要停用吗？',
        success: async res => {
          if (res.confirm) {
            try {
              await this.disableScale(this.scaleId)
              uni.showToast({
                title: '停用成功',
                icon: 'success'
              })
              this.loadScaleDetail()
            } catch (error) {
              uni.showToast({
                title: '停用失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleEnableScale() {
      try {
        await this.enableScale(this.scaleId)
        uni.showToast({
          title: '启用成功',
          icon: 'success'
        })
        this.loadScaleDetail()
      } catch (error) {
        uni.showToast({
          title: '启用失败',
          icon: 'error'
        })
      }
    },

    async handleCopyScale() {
      try {
        const result = await this.copyScale(this.scaleId)
        uni.showToast({
          title: '复制成功',
          icon: 'success'
        })

        // 跳转到编辑页面
        uni.navigateTo({
          url: `/pages/scale/create/index?id=${result.id}`
        })
      } catch (error) {
        uni.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    },

    async handleDeleteScale() {
      uni.showModal({
        title: '确认删除',
        content: '删除后无法恢复，确定要删除该量表吗？',
        success: async res => {
          if (res.confirm) {
            try {
              await this.deleteScale(this.scaleId)
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })

              // 返回列表页
              uni.navigateBack()
            } catch (error) {
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleExportScale() {
      try {
        uni.showLoading({ title: '导出中...' })

        const result = await this.exportScale({
          scaleId: this.scaleId,
          format: 'json'
        })

        // 下载文件
        uni.downloadFile({
          url: result.downloadUrl,
          success: res => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: '导出成功',
                icon: 'success'
              })
            }
          }
        })
      } catch (error) {
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    handleShareScale() {
      // 分享量表
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: `pages/scale/detail/index?id=${this.scaleId}`,
        title: this.scaleDetail.name,
        summary: this.scaleDetail.description || '查看量表详情',
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          })
        }
      })
    },

    goBack() {
      uni.navigateBack()
    },

    formatStatus(status) {
      const statusMap = {
        draft: '草稿',
        published: '已发布',
        disabled: '已停用'
      }
      return statusMap[status] || status
    },

    formatDate
  }
}
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.scale-detail {
  padding: $spacing-md;
  padding-bottom: 80px; // 为底部按钮留出空间

  .info-card,
  .scoring-card,
  .questions-card,
  .stats-card {
    margin-bottom: $spacing-md;
  }
}

.info-content {
  .info-header {
    margin-bottom: $spacing-lg;

    .scale-title {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      margin-bottom: $spacing-xs;

      .scale-name {
        font-size: $font-size-xl;
        font-weight: 600;
        color: $text-color-primary;
      }

      .scale-status {
        font-size: $font-size-xs;
        padding: 4px 8px;
        border-radius: $border-radius-xs;

        &.status-draft {
          background-color: #f0f0f0;
          color: #666;
        }

        &.status-published {
          background-color: #f6ffed;
          color: #52c41a;
        }

        &.status-disabled {
          background-color: #fff2f0;
          color: #ff4d4f;
        }
      }
    }

    .scale-meta {
      display: flex;
      gap: $spacing-md;

      .scale-category {
        font-size: $font-size-sm;
        color: $text-color-secondary;
        padding: 2px 6px;
        background-color: $bg-color-light;
        border-radius: $border-radius-xs;
      }

      .scale-version {
        font-size: $font-size-sm;
        color: $text-color-secondary;
      }
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-md;
    margin-bottom: $spacing-lg;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;

    .info-label {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }

    .info-value {
      font-size: $font-size-base;
      color: $text-color-primary;
      font-weight: 500;
    }
  }

  .description,
  .instructions {
    margin-bottom: $spacing-md;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .description-label,
  .instructions-label {
    display: block;
    font-size: $font-size-sm;
    color: $text-color-secondary;
    margin-bottom: $spacing-xs;
  }

  .description-text,
  .instructions-text {
    font-size: $font-size-base;
    color: $text-color-primary;
    line-height: 1.6;
  }
}

.scoring-content {
  .scoring-section {
    margin-bottom: $spacing-lg;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .scoring-title {
    display: block;
    font-size: $font-size-base;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-sm;
  }

  .scoring-text {
    font-size: $font-size-base;
    color: $text-color-secondary;
  }

  .levels-list,
  .dimensions-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
  }

  .level-item,
  .dimension-item {
    padding: $spacing-sm;
    background-color: $bg-color-light;
    border-radius: $border-radius-sm;
  }

  .level-header,
  .dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xs;
  }

  .level-name,
  .dimension-name {
    font-size: $font-size-base;
    font-weight: 500;
    color: $text-color-primary;
  }

  .level-range,
  .dimension-weight {
    font-size: $font-size-sm;
    color: $text-color-secondary;
  }

  .level-description,
  .dimension-description {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    line-height: 1.5;
  }
}

.questions-content {
  .questions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    .questions-count {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }

  .questions-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }

  .question-item {
    padding: $spacing-md;
    background-color: $bg-color-light;
    border-radius: $border-radius-sm;
  }

  .question-header {
    display: flex;
    align-items: flex-start;
    gap: $spacing-xs;
    margin-bottom: $spacing-sm;

    .question-number {
      font-size: $font-size-base;
      font-weight: 600;
      color: $color-primary;
      flex-shrink: 0;
    }

    .question-title {
      flex: 1;
      font-size: $font-size-base;
      color: $text-color-primary;
      line-height: 1.5;
    }

    .question-required {
      color: $color-danger;
      font-size: $font-size-sm;
    }
  }

  .question-description {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    line-height: 1.5;
    margin-bottom: $spacing-sm;
    margin-left: 20px;
  }

  .question-options {
    margin-left: 20px;
  }

  .choice-options {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
  }

  .option-item {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    .option-label {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      min-width: 20px;
    }

    .option-text {
      flex: 1;
      font-size: $font-size-sm;
      color: $text-color-primary;
    }

    .option-score {
      font-size: $font-size-xs;
      color: $color-primary;
    }
  }

  .rating-options,
  .text-options,
  .number-options {
    font-size: $font-size-sm;
    color: $text-color-secondary;

    text {
      display: block;
      margin-bottom: $spacing-xs;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .questions-more {
    text-align: center;
    padding: $spacing-md;
    color: $text-color-secondary;
    font-size: $font-size-sm;
  }
}

.stats-content {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-md;
    margin-bottom: $spacing-lg;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-xs;
    padding: $spacing-md;
    background-color: $bg-color-light;
    border-radius: $border-radius-sm;

    .stat-value {
      font-size: $font-size-xl;
      font-weight: 600;
      color: $color-primary;
    }

    .stat-label {
      font-size: $font-size-xs;
      color: $text-color-secondary;
    }
  }

  .recent-assessments {
    .recent-title {
      display: block;
      font-size: $font-size-base;
      font-weight: 600;
      color: $text-color-primary;
      margin-bottom: $spacing-sm;
    }

    .recent-list {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
    }

    .recent-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-sm;
      background-color: $bg-color-light;
      border-radius: $border-radius-sm;
      cursor: pointer;

      &:active {
        background-color: color.adjust($bg-color-light, $lightness: -5%);
      }
    }

    .recent-info {
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;

      .recent-elderly {
        font-size: $font-size-sm;
        color: $text-color-primary;
      }

      .recent-time {
        font-size: $font-size-xs;
        color: $text-color-secondary;
      }
    }

    .recent-result {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: $spacing-xs;

      .recent-score {
        font-size: $font-size-sm;
        font-weight: 600;
        color: $color-primary;
      }

      .recent-level {
        font-size: $font-size-xs;
        color: $text-color-secondary;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  z-index: 100;
}

// 响应式设计
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .questions-header {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;
  }

  .bottom-actions {
    flex-direction: column;
  }
}
</style>
