{"name": "@assessment/mobile", "version": "1.0.0", "description": "智能评估平台移动端", "main": "main.js", "scripts": {"dev:h5": "uni -p h5 --port 5273", "dev:mp-weixin": "uni -p mp-weixin", "dev:app": "uni -p app", "build:h5": "uni build -p h5", "build:mp-weixin": "uni build -p mp-weixin", "build:app": "uni build -p app", "lint": "eslint --ext .js,.ts,.vue src --ignore-path .gitignore", "lint:fix": "eslint --ext .js,.ts,.vue src --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "format": "prettier --write src/**/*.{js,ts,vue,json,css,scss}", "format:check": "prettier --check src/**/*.{js,ts,vue,json,css,scss}"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-ui": "^1.5.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "vue": "^3.4.25", "vue-i18n": "^9.11.1", "weui": "^2.6.21"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "^3.0.0-alpha-4070120250530001", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/runtime-core": "^3.4.25", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.20.1", "prettier": "^3.2.4", "sass": "^1.77.0", "sass-loader": "^14.1.0", "typescript": "^5.4.0", "vite": "^5.4.19", "vue-tsc": "^2.0.0"}, "browserslist": ["Android >= 4.4", "iOS >= 9"], "uni-app": {"scripts": {}}, "overrides": {"esbuild": "^0.25.5", "jpeg-js": "^0.4.4", "phin": "^3.7.1", "@intlify/message-resolver": "^9.2.0", "@intlify/message-compiler": "^9.2.0", "@intlify/runtime": "^9.2.0", "@intlify/core-base": "^9.2.0", "@intlify/vue-devtools": "^9.2.0"}}