{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["@dcloudio/types"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist", "**/*.js"]}