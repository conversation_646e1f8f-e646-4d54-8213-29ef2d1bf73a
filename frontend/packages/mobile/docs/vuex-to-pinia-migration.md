# Vuex 到 Pinia 迁移指南

## 1. 主要变更点

### 1.1 Store 导入方式

**Vuex 方式：**
```javascript
import { mapActions, mapGetters, mapState } from 'vuex'
```

**Pinia 方式：**
```javascript
import { useUserStore, useAssessmentStore } from '@/store'
import { storeToRefs } from 'pinia'
```

### 1.2 组件中使用 Store

#### Options API 组件迁移示例

**原 Vuex 代码：**
```javascript
export default {
  computed: {
    ...mapState('user', ['userInfo', 'token']),
    ...mapGetters('user', ['isLoggedIn', 'username'])
  },
  methods: {
    ...mapActions('user', ['login', 'logout', 'setUserInfo', 'setToken']),
    
    async handleLogin() {
      await this.login(this.loginForm)
    }
  }
}
```

**新 Pinia 代码：**
```javascript
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'

export default {
  setup() {
    const userStore = useUserStore()
    
    // 响应式数据需要使用 storeToRefs
    const { userInfo, token, isLoggedIn, username } = storeToRefs(userStore)
    
    // actions 直接解构即可
    const { login, logout, setUserInfo, setToken } = userStore
    
    return {
      // state 和 getters
      userInfo,
      token,
      isLoggedIn,
      username,
      
      // actions
      login,
      logout,
      setUserInfo,
      setToken
    }
  },
  
  methods: {
    async handleLogin() {
      const userStore = useUserStore()
      await userStore.login(this.loginForm)
    }
  }
}
```

#### Composition API 组件迁移示例

**原 Vuex 代码：**
```javascript
import { computed } from 'vue'
import { useStore } from 'vuex'

export default {
  setup() {
    const store = useStore()
    
    const userInfo = computed(() => store.state.user.userInfo)
    const isLoggedIn = computed(() => store.getters['user/isLoggedIn'])
    
    const login = async (form) => {
      await store.dispatch('user/login', form)
    }
    
    return {
      userInfo,
      isLoggedIn,
      login
    }
  }
}
```

**新 Pinia 代码：**
```javascript
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'

export default {
  setup() {
    const userStore = useUserStore()
    const { userInfo, isLoggedIn } = storeToRefs(userStore)
    
    const login = async (form) => {
      await userStore.login(form)
    }
    
    return {
      userInfo,
      isLoggedIn,
      login
    }
  }
}
```

### 1.3 具体组件迁移示例

#### 登录页面迁移

**原代码片段：**
```javascript
import { mapActions } from 'vuex'

export default {
  methods: {
    ...mapActions('user', ['setUserInfo', 'setToken']),
    
    async handleLogin() {
      const response = await login(this.loginForm)
      const { token, userInfo } = response.data
      
      // 保存登录信息
      this.setToken(token)
      this.setUserInfo(userInfo)
    }
  }
}
```

**新代码片段：**
```javascript
import { useUserStore } from '@/store'

export default {
  setup() {
    const userStore = useUserStore()
    
    const handleLogin = async () => {
      const response = await login(this.loginForm)
      const { token, userInfo } = response.data
      
      // 保存登录信息
      userStore.setToken(token)
      userStore.setUserInfo(userInfo)
    }
    
    return {
      handleLogin
    }
  }
}
```

### 1.4 Store 模块访问

**Vuex 方式：**
```javascript
// 访问其他模块
this.$store.state.config.systemSettings
this.$store.getters['assessment/currentAssessment']
this.$store.dispatch('elderly/getElderlyList')
```

**Pinia 方式：**
```javascript
import { useConfigStore, useAssessmentStore, useElderlyStore } from '@/store'

// 在 setup 中
const configStore = useConfigStore()
const assessmentStore = useAssessmentStore()
const elderlyStore = useElderlyStore()

// 访问数据
configStore.systemSettings
assessmentStore.currentAssessment
await elderlyStore.getElderlyList()
```

## 2. 完整迁移清单

- [x] 升级 Pinia 到 v3.x
- [x] 安装 pinia-plugin-persistedstate
- [x] 创建 Pinia 配置文件
- [x] 迁移所有 store 模块到 TypeScript
- [x] 更新 main.js/ts 文件
- [ ] 迁移所有组件使用 Pinia
- [ ] 移除 Vuex 依赖

## 3. 需要更新的主要文件

1. `/pages/login/index.vue` - 登录页面
2. `/pages/home/<USER>
3. `/pages/assessment/*/` - 评估相关页面
4. `/pages/elderly/*/` - 老人管理相关页面
5. `/pages/scale/*/` - 量表管理相关页面
6. `/pages/user/*/` - 用户相关页面
7. `/components/` - 所有使用 store 的组件

## 4. 注意事项

1. **响应式数据**：必须使用 `storeToRefs` 解构 state 和 getters
2. **Actions**：可以直接解构，不需要 `storeToRefs`
3. **TypeScript**：所有 store 已迁移到 TypeScript，获得更好的类型支持
4. **持久化**：已配置持久化插件，`user` 和 `config` store 会自动持久化部分数据
5. **命名空间**：Pinia 不再需要命名空间，每个 store 都是独立的

## 5. 性能优化

Pinia 相比 Vuex 的优势：
- 更小的打包体积
- 更好的 TypeScript 支持
- 更简单的 API
- 更好的代码分割支持
- 自动的代码提示和类型推断