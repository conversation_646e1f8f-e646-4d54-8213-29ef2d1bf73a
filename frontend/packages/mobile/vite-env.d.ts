/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_TITLE: string
  readonly BASE_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare global {
  const process: {
    env: {
      NODE_ENV: string
    }
  }
}

// JS 模块声明
declare module '@/api/*' {
  const api: any
  export default api
}

declare module '@/utils/*' {
  const utils: any
  export default utils
}