/**
 * Vuex 到 Pinia 自动迁移脚本
 */
const fs = require('fs');
const path = require('path');

// 定义需要替换的映射规则
const MIGRATION_RULES = [
  // 导入替换
  {
    pattern: /import\s+{\s*mapActions,?\s*mapState,?\s*mapGetters,?\s*useStore\s*}\s+from\s+['"]vuex['"]/g,
    replacement: 'import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from \'@/store\''
  },
  {
    pattern: /import\s+{\s*mapActions\s*}\s+from\s+['"]vuex['"]/g,
    replacement: 'import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from \'@/store\''
  },
  {
    pattern: /import\s+{\s*mapState\s*}\s+from\s+['"]vuex['"]/g,
    replacement: 'import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from \'@/store\''
  },
  {
    pattern: /import\s+{\s*mapGetters\s*}\s+from\s+['"]vuex['"]/g,
    replacement: 'import { useUserStore, useAssessmentStore, useElderlyStore, useScaleStore, useConfigStore } from \'@/store\''
  },
  
  // 组件内 mapActions 替换
  {
    pattern: /\.\.\.mapActions\('user',\s*\[(.*?)\]\)/g,
    replacement: (match, methods) => {
      const methodList = methods.replace(/['"`]/g, '').split(',').map(m => m.trim());
      return `// User store actions: ${methodList.join(', ')}`;
    }
  },
  {
    pattern: /\.\.\.mapActions\('assessment',\s*\[(.*?)\]\)/g,
    replacement: (match, methods) => {
      const methodList = methods.replace(/['"`]/g, '').split(',').map(m => m.trim());
      return `// Assessment store actions: ${methodList.join(', ')}`;
    }
  },
  {
    pattern: /\.\.\.mapActions\('elderly',\s*\[(.*?)\]\)/g,
    replacement: (match, methods) => {
      const methodList = methods.replace(/['"`]/g, '').split(',').map(m => m.trim());
      return `// Elderly store actions: ${methodList.join(', ')}`;
    }
  },
  {
    pattern: /\.\.\.mapActions\('scale',\s*\[(.*?)\]\)/g,
    replacement: (match, methods) => {
      const methodList = methods.replace(/['"`]/g, '').split(',').map(m => m.trim());
      return `// Scale store actions: ${methodList.join(', ')}`;
    }
  },
  {
    pattern: /\.\.\.mapActions\('config',\s*\[(.*?)\]\)/g,
    replacement: (match, methods) => {
      const methodList = methods.replace(/['"`]/g, '').split(',').map(m => m.trim());
      return `// Config store actions: ${methodList.join(', ')}`;
    }
  },

  // 组件内 mapState 替换  
  {
    pattern: /\.\.\.mapState\('user',\s*\[(.*?)\]\)/g,
    replacement: (match, states) => {
      const stateList = states.replace(/['"`]/g, '').split(',').map(s => s.trim());
      return `// User store state: ${stateList.join(', ')}`;
    }
  },
  {
    pattern: /\.\.\.mapState\('assessment',\s*\[(.*?)\]\)/g,
    replacement: (match, states) => {
      const stateList = states.replace(/['"`]/g, '').split(',').map(s => s.trim());
      return `// Assessment store state: ${stateList.join(', ')}`;
    }
  },
  {
    pattern: /\.\.\.mapState\('elderly',\s*\[(.*?)\]\)/g,
    replacement: (match, states) => {
      const stateList = states.replace(/['"`]/g, '').split(',').map(s => s.trim());
      return `// Elderly store state: ${stateList.join(', ')}`;
    }
  },
  {
    pattern: /\.\.\.mapState\('scale',\s*\[(.*?)\]\)/g,
    replacement: (match, states) => {
      const stateList = states.replace(/['"`]/g, '').split(',').map(s => s.trim());
      return `// Scale store state: ${stateList.join(', ')}`;
    }
  },
  {
    pattern: /\.\.\.mapState\('config',\s*\[(.*?)\]\)/g,
    replacement: (match, states) => {
      const stateList = states.replace(/['"`]/g, '').split(',').map(s => s.trim());
      return `// Config store state: ${stateList.join(', ')}`;
    }
  }
];

// setup() 函数模板
const SETUP_TEMPLATE = `
  setup() {
    const userStore = useUserStore()
    const assessmentStore = useAssessmentStore()
    const elderlyStore = useElderlyStore()
    const scaleStore = useScaleStore()
    const configStore = useConfigStore()
    
    return {
      userStore,
      assessmentStore,
      elderlyStore,
      scaleStore,
      configStore
    }
  },
`;

function migrateVueFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    // 应用所有迁移规则
    MIGRATION_RULES.forEach(rule => {
      const newContent = content.replace(rule.pattern, rule.replacement);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
      }
    });
    
    // 如果文件包含 mapActions 或 mapState，添加 setup() 函数
    if (content.includes('mapActions') || content.includes('mapState') || content.includes('mapGetters')) {
      // 在 methods 之前添加 setup 函数
      if (!content.includes('setup()') && !content.includes('setup ')) {
        const methodsIndex = content.indexOf('methods: {');
        if (methodsIndex !== -1) {
          content = content.slice(0, methodsIndex) + SETUP_TEMPLATE + '\n  ' + content.slice(methodsIndex);
          hasChanges = true;
        }
      }
    }
    
    if (hasChanges) {
      // 备份原文件
      fs.copyFileSync(filePath, filePath + '.backup');
      
      // 写入新内容
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已迁移: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 迁移失败 ${filePath}:`, error.message);
    return false;
  }
}

function findVueFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (item.endsWith('.vue')) {
        files.push(fullPath);
      }
    });
  }
  
  traverse(dir);
  return files;
}

function main() {
  const pagesDir = path.join(__dirname, '../src/pages');
  const componentsDir = path.join(__dirname, '../src/components');
  
  console.log('🚀 开始 Vuex 到 Pinia 迁移...\n');
  
  // 查找所有 Vue 文件
  const pageFiles = findVueFiles(pagesDir);
  const componentFiles = findVueFiles(componentsDir);
  const allFiles = [...pageFiles, ...componentFiles];
  
  console.log(`📁 找到 ${allFiles.length} 个 Vue 文件`);
  
  let migratedCount = 0;
  
  // 迁移每个文件
  allFiles.forEach(file => {
    if (migrateVueFile(file)) {
      migratedCount++;
    }
  });
  
  console.log(`\n✨ 迁移完成！`);
  console.log(`📊 总计: ${allFiles.length} 个文件`);
  console.log(`✅ 已迁移: ${migratedCount} 个文件`);
  console.log(`ℹ️  未改动: ${allFiles.length - migratedCount} 个文件`);
  
  if (migratedCount > 0) {
    console.log('\n📋 后续步骤:');
    console.log('1. 检查生成的备份文件 (*.vue.backup)');
    console.log('2. 手动调整 store 方法调用 (this.userStore.methodName())');
    console.log('3. 测试所有功能是否正常');
    console.log('4. 删除备份文件');
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  migrateVueFile,
  findVueFiles,
  MIGRATION_RULES,
  SETUP_TEMPLATE
};