<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="评估记录统计"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="stats-container" v-loading="loading">
      <!-- 总体统计 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-number">{{ stats.totalRecords || 0 }}</div>
              <div class="stats-label">总记录数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-number pending">{{ stats.pendingRecords || 0 }}</div>
              <div class="stats-label">待审核</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-number approved">{{ stats.approvedRecords || 0 }}</div>
              <div class="stats-label">已通过</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-number rejected">{{ stats.rejectedRecords || 0 }}</div>
              <div class="stats-label">已拒绝</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选条件 -->
      <el-card class="filter-card">
        <template #header>
          <div class="card-header">
            <span>筛选条件</span>
          </div>
        </template>
        
        <el-form :model="filterForm" inline>
          <el-form-item label="租户">
            <el-select v-model="filterForm.tenantId" placeholder="选择租户" style="width: 200px">
              <el-option label="全部租户" value="" />
              <el-option 
                v-for="tenant in tenants" 
                :key="tenant.id" 
                :label="tenant.name" 
                :value="tenant.id" 
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 300px"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="refreshStats">查询统计</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 简化的图表显示 -->
      <el-row :gutter="20" class="charts-row">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>状态分布</span>
              </div>
            </template>
            <div class="chart-placeholder">
              <el-empty description="图表功能开发中" :image-size="80" />
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>评估趋势</span>
              </div>
            </template>
            <div class="chart-placeholder">
              <el-empty description="图表功能开发中" :image-size="80" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="refreshStats" :loading="loading">
          刷新数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { systemAssessmentApi, tenantManageApi } from '@/api/multiTenantAdapter';

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible', 'refresh']);

// 数据状态
const loading = ref(false);
const stats = ref({
  totalRecords: 0,
  pendingRecords: 0,
  approvedRecords: 0,
  rejectedRecords: 0
});
const tenants = ref<any[]>([]);

// 筛选表单
const filterForm = reactive<any>({
  tenantId: '',
  dateRange: []
});

// 获取统计数据
const fetchStats = async () => {
  try {
    loading.value = true;
    
    const params = {
      tenantId: filterForm.tenantId || undefined,
      startDate: filterForm.dateRange?.[0]?.toISOString(),
      endDate: filterForm.dateRange?.[1]?.toISOString()
    };

    const response = await systemAssessmentApi.getAssessmentStats(params);
    const { data }: { data: any } = response;
    
    if (data.success) {
      stats.value = data.data;
    } else {
      ElMessage.error(data.message || '获取统计数据失败');
    }
  } catch (error: any) {
    console.error('获取统计数据失败:', error);
    ElMessage.error(error.message || '获取统计数据失败');
  } finally {
    loading.value = false;
  }
};

// 获取租户列表
const fetchTenants = async () => {
  try {
    const response = await tenantManageApi.getTenants({ size: 1000 });
    const { data }: { data: any } = response;
    
    if (data.success) {
      tenants.value = data.data.content || [];
    }
  } catch (error) {
    console.warn('获取租户列表失败:', error);
  }
};

// 刷新统计
const refreshStats = () => {
  fetchStats();
  emit('refresh');
};

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
};

// 监听对话框显示
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    fetchStats();
    fetchTenants();
  }
});
</script>

<style scoped>
.stats-container {
  min-height: 400px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-item {
  padding: 10px;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stats-number.pending {
  color: #E6A23C;
}

.stats-number.approved {
  color: #67C23A;
}

.stats-number.rejected {
  color: #F56C6C;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.filter-card {
  margin-bottom: 20px;
}

.charts-row {
  margin-bottom: 20px;
}

.card-header {
  font-weight: 600;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  text-align: right;
}
</style>