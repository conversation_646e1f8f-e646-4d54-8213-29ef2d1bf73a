<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="getDialogTitle()"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <!-- 基本信息 -->
      <el-card shadow="never" class="form-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="量表代码" prop="code">
              <el-input
                v-model="formData.code"
                placeholder="请输入唯一的量表代码"
                :disabled="mode === 'edit'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="量表名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入量表名称"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="版本号" prop="version">
              <el-input
                v-model="formData.version"
                placeholder="如: 1.0.0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分类" prop="category">
              <el-select
                v-model="formData.category"
                placeholder="选择或输入分类"
                filterable
                allow-create
                style="width: 100%"
              >
                <el-option
                  v-for="category in categories"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发布者类型" prop="publisherType">
              <el-select
                v-model="formData.publisherType"
                placeholder="选择发布者类型"
                style="width: 100%"
              >
                <el-option label="平台官方" value="PLATFORM" />
                <el-option label="租户" value="TENANT" />
                <el-option label="合作伙伴" value="PARTNER" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="可见性" prop="visibility">
              <el-select
                v-model="formData.visibility"
                placeholder="选择可见性"
                style="width: 100%"
              >
                <el-option label="公开" value="PUBLIC" />
                <el-option label="私有" value="PRIVATE" />
                <el-option label="付费" value="PREMIUM" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="价格" prop="price">
              <el-input-number
                v-model="formData.price"
                :min="0"
                :precision="2"
                placeholder="0.00"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发布者ID" prop="publisherId">
              <el-input
                v-model="formData.publisherId"
                placeholder="发布者ID（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="行业标签">
              <el-select
                v-model="formData.industryTags"
                multiple
                filterable
                allow-create
                placeholder="选择或输入行业标签"
                style="width: 100%"
              >
                <el-option label="医疗护理" value="医疗护理" />
                <el-option label="康复治疗" value="康复治疗" />
                <el-option label="养老服务" value="养老服务" />
                <el-option label="心理评估" value="心理评估" />
                <el-option label="能力评估" value="能力评估" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关键词">
              <el-select
                v-model="formData.keywords"
                multiple
                filterable
                allow-create
                placeholder="选择或输入关键词"
                style="width: 100%"
              >
                <el-option label="老年人" value="老年人" />
                <el-option label="认知功能" value="认知功能" />
                <el-option label="日常生活" value="日常生活" />
                <el-option label="身体功能" value="身体功能" />
                <el-option label="精神状态" value="精神状态" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-checkbox v-model="formData.isOfficial">官方量表</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 量表定义 -->
      <el-card shadow="never" class="form-card">
        <template #header>
          <div class="card-header">
            <span>量表定义</span>
            <el-tooltip content="量表的表单结构、评分规则等技术定义">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>

        <el-form-item label="表单结构" prop="formSchema">
          <div class="json-editor-container">
            <el-input
              v-model="formData.formSchema"
              type="textarea"
              :rows="8"
              placeholder="请输入表单结构的JSON定义"
            />
            <div class="json-editor-tips">
              <small>定义表单字段、类型、验证规则等的JSON格式数据</small>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="评分规则" prop="scoringRules">
          <div class="json-editor-container">
            <el-input
              v-model="formData.scoringRules"
              type="textarea"
              :rows="8"
              placeholder="请输入评分规则的JSON定义"
            />
            <div class="json-editor-tips">
              <small>定义如何计算分数、结果等级判断等的JSON格式数据</small>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="验证规则">
          <div class="json-editor-container">
            <el-input
              v-model="formData.validationRules"
              type="textarea"
              :rows="4"
              placeholder="请输入验证规则的JSON定义（可选）"
            />
            <div class="json-editor-tips">
              <small>定义数据验证规则的JSON格式数据，可选</small>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="报告模板">
          <div class="json-editor-container">
            <el-input
              v-model="formData.reportTemplate"
              type="textarea"
              :rows="4"
              placeholder="请输入报告模板的JSON定义（可选）"
            />
            <div class="json-editor-tips">
              <small>定义评估报告生成模板的JSON格式数据，可选</small>
            </div>
          </div>
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          {{ mode === 'view' ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ mode === 'create' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { QuestionFilled } from '@element-plus/icons-vue';
import { systemScaleApi } from '@/api/multiTenantAdapter';

interface Props {
  visible: boolean;
  scale?: any;
  mode: 'create' | 'edit' | 'view';
  categories: string[];
}

const props = withDefaults(defineProps<Props>(), {
  scale: null,
  categories: () => []
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用和状态
const formRef = ref();
const submitting = ref(false);

// 表单数据
const formData = reactive({
  code: '',
  name: '',
  version: '1.0.0',
  category: '',
  industryTags: [] as string[],
  keywords: [] as string[],
  formSchema: '',
  scoringRules: '',
  validationRules: '',
  reportTemplate: '',
  publisherType: 'PLATFORM' as 'PLATFORM' | 'TENANT' | 'PARTNER',
  publisherId: '',
  visibility: 'PUBLIC' as 'PUBLIC' | 'PRIVATE' | 'PREMIUM',
  price: 0,
  isOfficial: true
});

// 表单验证规则
const formRules = {
  code: [
    { required: true, message: '请输入量表代码', trigger: 'blur' },
    { min: 2, max: 50, message: '代码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '代码只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入量表名称', trigger: 'blur' },
    { min: 2, max: 100, message: '名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  formSchema: [
    { required: true, message: '请输入表单结构', trigger: 'blur' },
    { validator: validateJSON, trigger: 'blur' }
  ],
  scoringRules: [
    { required: true, message: '请输入评分规则', trigger: 'blur' },
    { validator: validateJSON, trigger: 'blur' }
  ],
  publisherType: [
    { required: true, message: '请选择发布者类型', trigger: 'change' }
  ],
  visibility: [
    { required: true, message: '请选择可见性', trigger: 'change' }
  ]
};

// JSON 验证器
function validateJSON(rule: any, value: string, callback: Function) {
  if (!value) {
    callback();
    return;
  }
  
  try {
    JSON.parse(value);
    callback();
  } catch (error) {
    callback(new Error('请输入有效的JSON格式'));
  }
}

// 监听props变化，初始化表单数据
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      initFormData();
    });
  }
});

// 初始化表单数据
const initFormData = () => {
  if (props.scale) {
    Object.assign(formData, {
      code: props.scale.code || '',
      name: props.scale.name || '',
      version: props.scale.version || '1.0.0',
      category: props.scale.category || '',
      industryTags: props.scale.industryTags || [],
      keywords: props.scale.keywords || [],
      formSchema: props.scale.formSchema ? JSON.stringify(props.scale.formSchema, null, 2) : '',
      scoringRules: props.scale.scoringRules ? JSON.stringify(props.scale.scoringRules, null, 2) : '',
      validationRules: props.scale.validationRules ? JSON.stringify(props.scale.validationRules, null, 2) : '',
      reportTemplate: props.scale.reportTemplate ? JSON.stringify(props.scale.reportTemplate, null, 2) : '',
      publisherType: props.scale.publisherType || 'PLATFORM',
      publisherId: props.scale.publisherId || '',
      visibility: props.scale.visibility || 'PUBLIC',
      price: props.scale.price || 0,
      isOfficial: props.scale.isOfficial !== false
    });
  } else {
    // 重置表单数据
    Object.assign(formData, {
      code: '',
      name: '',
      version: '1.0.0',
      category: '',
      industryTags: [],
      keywords: [],
      formSchema: '',
      scoringRules: '',
      validationRules: '',
      reportTemplate: '',
      publisherType: 'PLATFORM',
      publisherId: '',
      visibility: 'PUBLIC',
      price: 0,
      isOfficial: true
    });
  }
  
  // 清除验证状态
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 获取对话框标题
const getDialogTitle = () => {
  const titleMap = {
    create: '新增量表',
    edit: '编辑量表',
    view: '查看量表'
  };
  return titleMap[props.mode] || '量表管理';
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    submitting.value = true;

    // 准备提交数据
    const submitData = {
      ...formData,
      industryTags: formData.industryTags.length > 0 ? formData.industryTags : undefined,
      keywords: formData.keywords.length > 0 ? formData.keywords : undefined,
      formSchema: JSON.parse(formData.formSchema),
      scoringRules: JSON.parse(formData.scoringRules),
      validationRules: formData.validationRules ? JSON.parse(formData.validationRules) : null,
      reportTemplate: formData.reportTemplate ? JSON.parse(formData.reportTemplate) : null
    };

    let response;
    if (props.mode === 'create') {
      response = await systemScaleApi.createScale(submitData);
    } else {
      response = await systemScaleApi.updateScale(props.scale.id, submitData);
    }

    const { data }: { data: any } = response;
    if (data.success) {
      ElMessage.success(data.message || `量表${props.mode === 'create' ? '创建' : '更新'}成功`);
      emit('success');
    } else {
      ElMessage.error(data.message || '操作失败');
    }
  } catch (error: any) {
    console.error('提交量表失败:', error);
    ElMessage.error(error.message || '操作失败');
  } finally {
    submitting.value = false;
  }
};

// 初始化
initFormData();
</script>

<style scoped>
.form-card {
  margin-bottom: 20px;
}

.form-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.json-editor-container {
  width: 100%;
}

.json-editor-tips {
  margin-top: 4px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

.el-dialog {
  max-height: 90vh;
  overflow-y: auto;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea .el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 20px auto;
  }
  
  .el-col {
    margin-bottom: 10px;
  }
}
</style>