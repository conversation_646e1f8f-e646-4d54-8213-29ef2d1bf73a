<template>
  <el-card class="stage-card stage2-parse-edit" :class="{ disabled: !enabled }">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ completed: isCompleted, active: enabled }">
          <el-icon v-if="isCompleted"><Check /></el-icon>
          <span v-else>2</span>
        </div>
        <div class="stage-title">
          <h3>解析编辑</h3>
          <span class="stage-description">Markdown内容编辑与预览</span>
        </div>
        <div class="stage-actions">
          <el-tag v-if="isCompleted" type="success">已完成</el-tag>
          <el-tag v-else-if="!enabled" type="info">等待上传</el-tag>
          <el-tag v-else type="warning">编辑中</el-tag>
        </div>
      </div>
    </template>

    <div class="parse-edit-content" :class="{ disabled: !enabled }">
      <!-- 解析状态提示 -->
      <div v-if="uploadResult && !markdownContent" class="loading-state">
        <el-alert
          title="正在解析文档..."
          type="info"
          :closable="false"
          show-icon
        >
          <div class="flex items-center">
            <el-icon class="animate-spin mr-2"><Loading /></el-icon>
            <span>Docling AI正在处理您的文档，请稍候...</span>
          </div>
        </el-alert>
      </div>

      <!-- 编辑模式切换 -->
      <div v-if="markdownContent" class="mode-selector mb-4">
        <el-radio-group
          v-model="currentEditMode"
          size="default"
          @change="handleModeChange"
        >
          <el-radio-button value="preview">
            <div class="flex items-center">
              <EyeIcon class="h-4 w-4 mr-1" />
              <span>预览模式</span>
            </div>
          </el-radio-button>
          <el-radio-button value="edit">
            <div class="flex items-center">
              <PencilIcon class="h-4 w-4 mr-1" />
              <span>编辑模式</span>
            </div>
          </el-radio-button>
          <el-radio-button value="split">
            <div class="flex items-center">
              <RectangleStackIcon class="h-4 w-4 mr-1" />
              <span>分屏模式</span>
            </div>
          </el-radio-button>
        </el-radio-group>
        
        <div class="mode-actions">
          <el-button
            size="small"
            @click="handleSave"
            :loading="saving"
            type="primary"
          >
            <ArchiveBoxIcon class="h-4 w-4 mr-1" />
            保存内容
          </el-button>
          <el-button
            size="small"
            @click="handleValidate"
            :loading="validating"
          >
            <CheckCircleIcon class="h-4 w-4 mr-1" />
            验证内容
          </el-button>
        </div>
      </div>

      <!-- Markdown编辑器 -->
      <div v-if="markdownContent" class="editor-container">
        <!-- 预览模式 -->
        <div v-if="currentEditMode === 'preview'" class="preview-container">
          <div class="preview-content" v-html="renderedMarkdown"></div>
        </div>

        <!-- 编辑模式 -->
        <div v-else-if="currentEditMode === 'edit'" class="edit-container">
          <el-input
            v-model="editableContent"
            type="textarea"
            :rows="20"
            placeholder="Markdown内容将在此显示..."
            class="markdown-textarea"
            @input="handleContentChange"
          />
        </div>

        <!-- 分屏模式 -->
        <div v-else-if="currentEditMode === 'split'" class="split-container">
          <div class="split-left">
            <div class="split-header">编辑区</div>
            <el-input
              v-model="editableContent"
              type="textarea"
              :rows="18"
              placeholder="Markdown内容..."
              class="markdown-textarea"
              @input="handleContentChange"
            />
          </div>
          <div class="split-right">
            <div class="split-header">预览区</div>
            <div class="preview-content" v-html="renderedMarkdown"></div>
          </div>
        </div>

        <!-- 内容统计 -->
        <div class="content-stats mt-3">
          <div class="stats-info">
            <span class="text-sm text-gray-600">
              字符数: {{ editableContent.length }} | 
              行数: {{ editableContent.split('\n').length }} |
              词数: {{ getWordCount() }}
            </span>
          </div>
          <div class="validation-status">
            <el-tag v-if="isValidated" type="success" size="small">
              <CheckCircleIcon class="h-3 w-3 mr-1" />
              内容已验证
            </el-tag>
            <el-tag v-else-if="hasChanges" type="warning" size="small">
              <ExclamationTriangleIcon class="h-3 w-3 mr-1" />
              内容已修改
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="enabled" class="empty-state">
        <el-empty
          :image-size="120"
          description="等待文档上传完成"
        >
          <template #image>
            <DocumentTextIcon class="h-24 w-24 text-gray-400" />
          </template>
        </el-empty>
      </div>

      <!-- 禁用状态 -->
      <div v-else class="disabled-state">
        <el-empty
          :image-size="120"
          description="请先完成文档上传"
        >
          <template #image>
            <LockClosedIcon class="h-24 w-24 text-gray-400" />
          </template>
        </el-empty>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { marked } from 'marked';
import { Check, Loading } from '@element-plus/icons-vue';
import {
  EyeIcon,
  PencilIcon,
  RectangleStackIcon,
  ArchiveBoxIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  LockClosedIcon,
} from '@heroicons/vue/24/outline';

// Types
interface UploadResult {
  fileName: string;
  fileSize: number;
  uploadSuccess: boolean;
  doclingResult?: any;
  markdownContent?: string;
}

interface EditResult {
  markdownContent: string;
  contentValidated: boolean;
  editMode: 'preview' | 'edit' | 'split';
  wordCount: number;
  hasChanges: boolean;
}

// Props
interface Props {
  enabled?: boolean;
  uploadResult?: UploadResult | null;
}

const props = withDefaults(defineProps<Props>(), {
  enabled: false,
  uploadResult: null,
});

// Emits
const emit = defineEmits<{
  'stage-complete': [result: EditResult];
}>();

// Reactive data
const currentEditMode = ref<'preview' | 'edit' | 'split'>('preview');
const editableContent = ref('');
const originalContent = ref('');
const saving = ref(false);
const validating = ref(false);
const isValidated = ref(false);
const isCompleted = ref(false);

// Computed
const markdownContent = computed(() => {
  return props.uploadResult?.markdownContent || '';
});

const renderedMarkdown = computed(() => {
  if (!editableContent.value) return '';
  try {
    return marked(editableContent.value);
  } catch (error) {
    console.error('Markdown rendering error:', error);
    return '<p class="text-red-500">Markdown格式错误</p>';
  }
});

const hasChanges = computed(() => {
  return editableContent.value !== originalContent.value;
});

// Watch for upload result changes
watch(() => props.uploadResult, (newResult) => {
  if (newResult?.markdownContent) {
    editableContent.value = newResult.markdownContent;
    originalContent.value = newResult.markdownContent;
    isValidated.value = false;
    isCompleted.value = false;
  }
}, { immediate: true });

// Methods
const handleModeChange = (mode: 'preview' | 'edit' | 'split') => {
  currentEditMode.value = mode;
};

const handleContentChange = () => {
  isValidated.value = false;
  
  // 自动保存逻辑（可选）
  // 这里可以添加防抖保存
};

const handleSave = async () => {
  saving.value = true;
  try {
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    originalContent.value = editableContent.value;
    isValidated.value = true;
    
    ElMessage.success('内容保存成功');
    
    // 检查是否可以完成阶段
    checkStageCompletion();
  } catch (error) {
    ElMessage.error('保存失败');
  } finally {
    saving.value = false;
  }
};

const handleValidate = async () => {
  validating.value = true;
  try {
    // 模拟验证操作
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // 简单的内容验证
    if (editableContent.value.length < 10) {
      ElMessage.warning('内容过短，请确保包含完整的量表信息');
      return;
    }
    
    if (!editableContent.value.includes('#')) {
      ElMessage.warning('建议添加标题结构以便更好地解析');
      return;
    }
    
    isValidated.value = true;
    ElMessage.success('内容验证通过');
    
    // 检查是否可以完成阶段
    checkStageCompletion();
  } catch (error) {
    ElMessage.error('验证失败');
  } finally {
    validating.value = false;
  }
};

const checkStageCompletion = () => {
  if (isValidated.value && editableContent.value.length > 0) {
    isCompleted.value = true;
    
    const result: EditResult = {
      markdownContent: editableContent.value,
      contentValidated: isValidated.value,
      editMode: currentEditMode.value,
      wordCount: getWordCount(),
      hasChanges: hasChanges.value,
    };
    
    emit('stage-complete', result);
  }
};

const getWordCount = () => {
  if (!editableContent.value) return 0;
  // 简单的中英文词数统计
  const chinese = editableContent.value.match(/[\u4e00-\u9fa5]/g) || [];
  const english = editableContent.value.match(/[a-zA-Z]+/g) || [];
  return chinese.length + english.length;
};

// 配置marked选项
marked.setOptions({
  breaks: true,
  gfm: true,
});
</script>

<style scoped>
.stage-card {
  height: 950px; /* 与量表属性卡片保持一致 */
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stage-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stage-card.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.stage-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stage-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  background: #f3f4f6;
  color: #6b7280;
  transition: all 0.3s ease;
}

.stage-number.active {
  background: #3b82f6;
  color: white;
}

.stage-number.completed {
  background: #10b981;
  color: white;
}

.stage-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.stage-description {
  font-size: 14px;
  color: #6b7280;
}

.parse-edit-content {
  padding: 8px 0;
  height: calc(100% - 80px); /* 减去header高度 */
  overflow-y: auto; /* 内容超出时滚动 */
}

.parse-edit-content.disabled {
  opacity: 0.5;
}

.mode-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e5e7eb;
}

.mode-actions {
  display: flex;
  gap: 8px;
}

.editor-container {
  margin-top: 16px;
}

.edit-container {
  height: 750px; /* 进一步增加编辑区高度 */
}

.markdown-textarea :deep(.el-textarea__inner) {
  height: 750px !important; /* 进一步增加textarea高度 */
  resize: none;
}

.preview-container {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
  height: 750px; /* 进一步增加编辑区域高度 */
  background: #fafafa;
  overflow-y: auto; /* 内容超出时滚动 */
}

.preview-content {
  line-height: 1.6;
  color: #374151;
}

.preview-content :deep(h1),
.preview-content :deep(h2),
.preview-content :deep(h3) {
  color: #1f2937;
  font-weight: 600;
  margin: 16px 0 8px 0;
}

.preview-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.preview-content :deep(th),
.preview-content :deep(td) {
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  text-align: left;
}

.preview-content :deep(th) {
  background: #f3f4f6;
  font-weight: 600;
}

.edit-container {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.markdown-textarea :deep(textarea) {
  border: none !important;
  box-shadow: none !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
}

.split-container {
  display: flex;
  gap: 16px;
  height: 750px; /* 与其他编辑区域保持一致 */
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.split-left,
.split-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.split-left {
  border-right: 1px solid #e5e7eb;
}

.split-header {
  padding: 8px 12px;
  background: #f3f4f6;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.split-left .markdown-textarea,
.split-right .preview-content {
  flex: 1;
  height: 100%;
}

.split-right .preview-content {
  padding: 16px;
  overflow-y: auto;
  background: #fafafa;
}

.content-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.loading-state,
.empty-state,
.disabled-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>