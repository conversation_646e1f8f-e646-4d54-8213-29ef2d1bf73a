import request from '@/utils/request'
import axios from 'axios'

/**
 * 验证码相关API
 */

/**
 * 获取滑动验证码（无需认证）
 * @returns {Promise} 验证码数据
 */
export function getCaptcha() {
  // 验证码API不需要认证，使用代理路径
  return axios({
    url: '/api/captcha/get',
    method: 'GET',
    baseURL: '', // 使用相对路径，依赖Vite代理
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 获取滑动验证码（需要认证的版本，保留用于其他场景）
 * @returns {Promise} 验证码数据
 */
export function getCaptchaWithAuth() {
  return request({
    url: '/api/captcha/get',
    method: 'GET'
  })
}

/**
 * 校验滑动验证码（无需认证）
 * @param {Object} data 验证数据
 * @param {string} data.captchaType 验证码类型 (blockPuzzle/clickWord)
 * @param {string} data.token 验证码token
 * @param {string} data.pointJson 滑动坐标JSON字符串
 * @param {string} data.verification 验证密钥
 * @returns {Promise} 校验结果
 */
export function checkCaptcha(data) {
  // 验证码校验API不需要认证，使用代理路径
  return axios({
    url: '/api/captcha/check',
    method: 'POST',
    data,
    baseURL: '', // 使用相对路径，依赖Vite代理
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 校验滑动验证码（需要认证的版本）
 * @param {Object} data 验证数据
 * @returns {Promise} 校验结果
 */
export function checkCaptchaWithAuth(data) {
  return request({
    url: '/api/captcha/check',
    method: 'POST',
    data
  })
}

/**
 * 验证码二次验证（用于登录）
 * @param {Object} data 验证数据
 * @param {string} data.captchaType 验证码类型
 * @param {string} data.token 验证码token
 * @param {string} data.verification 验证密钥
 * @returns {Promise} 验证结果
 */
export function verifyCaptcha(data) {
  return request({
    url: '/api/captcha/verify',
    method: 'POST',
    data
  })
}