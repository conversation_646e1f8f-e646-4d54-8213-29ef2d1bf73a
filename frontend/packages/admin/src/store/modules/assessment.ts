import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import type { Assessment, AssessmentQuestion, AssessmentResult } from '@/types/assessment'
import { assessmentScaleApi, assessmentRecordApi } from '@/api/assessment'

interface AssessmentState {
  assessments: Assessment[]
  currentAssessment: Assessment | null
  currentQuestions: AssessmentQuestion[]
  currentAnswers: Record<string, any>
  assessmentResults: AssessmentResult[]
  loading: boolean
  error: string | null
  pagination: {
    current: number
    pageSize: number
    total: number
  }
  filters: {
    status: string
    category: string
    startDate: string
    endDate: string
    keyword: string
  }
  stats: any | null
  cache: Map<string, { data: any; timestamp: number }>
}

export const useAssessmentStore = defineStore('assessment', {
  state: (): AssessmentState => ({
    assessments: [],
    currentAssessment: null,
    currentQuestions: [],
    currentAnswers: {},
    assessmentResults: [],
    loading: false,
    error: null,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    },
    filters: {
      status: '',
      category: '',
      startDate: '',
      endDate: '',
      keyword: ''
    },
    stats: null,
    cache: new Map()
  }),

  getters: {
    filteredAssessments(state): Assessment[] {
      let filtered = state.assessments
      
      if (state.filters.status) {
        filtered = filtered.filter(item => item.status === state.filters.status)
      }
      
      if (state.filters.category) {
        filtered = filtered.filter(item => item.category === state.filters.category)
      }
      
      if (state.filters.keyword) {
        const keyword = state.filters.keyword.toLowerCase()
        filtered = filtered.filter(item => 
          item.title?.toLowerCase().includes(keyword) ||
          item.description?.toLowerCase().includes(keyword)
        )
      }
      
      if (state.filters.startDate && state.filters.endDate) {
        filtered = filtered.filter(item => {
          const createTime = new Date(item.createTime || '')
          return createTime >= new Date(state.filters.startDate) && 
                 createTime <= new Date(state.filters.endDate)
        })
      }
      
      return filtered
    },

    assessmentCategories(state): string[] {
      const categories = new Set(state.assessments.map(item => item.category).filter(Boolean))
      return Array.from(categories)
    },

    totalAssessments(state): number {
      return state.assessments.length
    },

    completedAssessments(state): number {
      return state.assessments.filter(item => item.status === 'completed').length
    },

    pendingAssessments(state): number {
      return state.assessments.filter(item => item.status === 'pending').length
    }
  },

  actions: {
    // 缓存工具方法
    getCache(key: string) {
      const CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存
      const cached = this.cache.get(key)
      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        return cached.data
      }
      this.cache.delete(key)
      return null
    },

    setCache(key: string, data: any) {
      this.cache.set(key, { data, timestamp: Date.now() })
    },

    // 获取评估列表
    async fetchAssessments(params: any = {}) {
      const cacheKey = `assessments_${JSON.stringify(params)}`
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.assessments = cachedData.data
        this.pagination = cachedData.pagination
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        const response = await assessmentScaleApi.getScales({
          page: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...params
        })
        
        if (response.success) {
          this.assessments = response.data?.records || []
          this.pagination = {
            current: response.data?.current || 1,
            pageSize: response.data?.size || 10,
            total: response.data?.total || 0
          }
          
          this.setCache(cacheKey, { data: this.assessments, pagination: this.pagination })
          return response.data
        } else {
          throw new Error(response.message || '获取评估列表失败')
        }
      } catch (err: any) {
        this.error = err.message || '获取评估列表失败'
        ElMessage.error(this.error || '操作失败')
        throw err
      } finally {
        this.loading = false
      }
    },

    // 获取评估详情
    async fetchAssessmentById(id: string) {
      const cacheKey = `assessment_${id}`
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.currentAssessment = cachedData
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        const response = await assessmentScaleApi.getScale(id)
        
        if (response.success) {
          this.currentAssessment = response.data
          this.setCache(cacheKey, response.data)
          return response.data
        } else {
          throw new Error(response.message || '获取评估详情失败')
        }
      } catch (err: any) {
        this.error = err.message || '获取评估详情失败'
        ElMessage.error(this.error || '操作失败')
        throw err
      } finally {
        this.loading = false
      }
    },

    // 获取评估问题
    async fetchAssessmentQuestions(assessmentId: string) {
      const cacheKey = `questions_${assessmentId}`
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.currentQuestions = cachedData
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        // 暂时使用模拟数据，因为量表问题通常包含在量表详情中
        const scaleResponse = await assessmentScaleApi.getScale(assessmentId)
        const response = { 
          success: true, 
          data: scaleResponse.data?.formSchema?.questions || [] 
        }
        
        if (response.success) {
          this.currentQuestions = response.data || []
          this.setCache(cacheKey, response.data || [])
          return response.data
        } else {
          throw new Error(response.message || '获取评估问题失败')
        }
      } catch (err: any) {
        this.error = err.message || '获取评估问题失败'
        ElMessage.error(this.error || '操作失败')
        throw err
      } finally {
        this.loading = false
      }
    },

    // 创建评估
    async createAssessment(data: Partial<Assessment>) {
      this.loading = true
      this.error = null
      
      try {
        // 创建评估记录而不是评估量表
        const response = await assessmentRecordApi.createRecord(data)
        
        if (response.success) {
          this.assessments.unshift(response.data)
          this.pagination.total += 1
          
          // 清理相关缓存
          this.cache.clear()
          
          ElMessage.success('创建评估成功')
          return response.data
        } else {
          throw new Error(response.message || '创建评估失败')
        }
      } catch (err: any) {
        this.error = err.message || '创建评估失败'
        ElMessage.error(this.error || '操作失败')
        throw err
      } finally {
        this.loading = false
      }
    },

    // 更新评估
    async updateAssessment(id: string, data: Partial<Assessment>) {
      this.loading = true
      this.error = null
      
      try {
        const response = await assessmentRecordApi.updateRecord(id, data)
        
        if (response.success) {
          const index = this.assessments.findIndex(item => item.id === id)
          if (index !== -1) {
            this.assessments[index] = { ...this.assessments[index], ...response.data }
          }
          
          if (this.currentAssessment?.id === id) {
            this.currentAssessment = { ...this.currentAssessment, ...response.data }
          }
          
          // 清理相关缓存
          this.cache.clear()
          
          ElMessage.success('更新评估成功')
          return response.data
        } else {
          throw new Error(response.message || '更新评估失败')
        }
      } catch (err: any) {
        this.error = err.message || '更新评估失败'
        ElMessage.error(this.error || '操作失败')
        throw err
      } finally {
        this.loading = false
      }
    },

    // 删除评估
    async deleteAssessment(id: string) {
      this.loading = true
      this.error = null
      
      try {
        const response = await assessmentRecordApi.deleteRecord(id)
        
        if (response.success) {
          const index = this.assessments.findIndex(item => item.id === id)
          if (index !== -1) {
            this.assessments.splice(index, 1)
            this.pagination.total -= 1
          }
          
          if (this.currentAssessment?.id === id) {
            this.currentAssessment = null
          }
          
          // 清理相关缓存
          this.cache.clear()
          
          ElMessage.success('删除评估成功')
          return true
        } else {
          throw new Error(response.message || '删除评估失败')
        }
      } catch (err: any) {
        this.error = err.message || '删除评估失败'
        ElMessage.error(this.error || '操作失败')
        throw err
      } finally {
        this.loading = false
      }
    },

    // 提交评估答案
    async submitAssessmentAnswers(assessmentId: string, answers: Record<string, any>) {
      this.loading = true
      this.error = null
      
      try {
        const response = await assessmentRecordApi.submitAnswers(assessmentId, answers)
        
        if (response.success) {
          this.currentAnswers = {}
          
          // 更新评估状态
          const index = this.assessments.findIndex(item => item.id === assessmentId)
          if (index !== -1) {
            this.assessments[index].status = 'completed'
          }
          
          if (this.currentAssessment?.id === assessmentId) {
            this.currentAssessment.status = 'completed'
          }
          
          // 清理相关缓存
          this.cache.clear()
          
          ElMessage.success('提交评估成功')
          return response.data
        } else {
          throw new Error(response.message || '提交评估失败')
        }
      } catch (err: any) {
        this.error = err.message || '提交评估失败'
        ElMessage.error(this.error || '操作失败')
        throw err
      } finally {
        this.loading = false
      }
    },

    // 获取评估结果
    async fetchAssessmentResults(assessmentId: string) {
      const cacheKey = `results_${assessmentId}`
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.assessmentResults = cachedData
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        const response = await assessmentRecordApi.getResults(assessmentId)
        
        if (response.success) {
          this.assessmentResults = response.data || []
          this.setCache(cacheKey, response.data || [])
          return response.data
        } else {
          throw new Error(response.message || '获取评估结果失败')
        }
      } catch (err: any) {
        this.error = err.message || '获取评估结果失败'
        ElMessage.error(this.error || '操作失败')
        throw err
      } finally {
        this.loading = false
      }
    },

    // 设置过滤条件
    setFilters(newFilters: Partial<AssessmentState['filters']>) {
      this.filters = { ...this.filters, ...newFilters }
    },

    // 重置过滤条件
    resetFilters() {
      this.filters = {
        status: '',
        category: '',
        startDate: '',
        endDate: '',
        keyword: ''
      }
    },

    // 设置分页
    setPagination(newPagination: Partial<AssessmentState['pagination']>) {
      this.pagination = { ...this.pagination, ...newPagination }
    },

    // 设置答案
    setAnswer(questionId: string, answer: any) {
      this.currentAnswers[questionId] = answer
    },

    // 清理当前状态
    clearCurrent() {
      this.currentAssessment = null
      this.currentQuestions = []
      this.currentAnswers = {}
    },

    // 获取统计数据
    async fetchStats() {
      const cacheKey = 'assessment_stats'
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.stats = cachedData
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        // 这里需要调用实际的统计API，暂时先导入
        const { systemDashboardApi } = await import('@/api/multiTenantAdapter')
        const response = await systemDashboardApi.getAssessmentStats()
        
        if (response.data) {
          this.stats = response.data
          this.setCache(cacheKey, response.data)
          return response.data
        } else {
          throw new Error('获取评估统计失败')
        }
      } catch (err: any) {
        const errorMsg = err.message || '获取评估统计失败'
        this.error = errorMsg
        ElMessage.error(errorMsg)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 清理缓存
    clearCache() {
      this.cache.clear()
    }
  }
})