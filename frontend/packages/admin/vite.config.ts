import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import tailwindcss from '@tailwindcss/postcss';
import autoprefixer from 'autoprefixer';

export default defineConfig({
  plugins: [
    vue({
      // Vue 3 优化配置
      include: [/\.vue$/],
    }),
  ],
  
  css: {
    postcss: {
      plugins: [
        tailwindcss,
        autoprefixer,
      ],
    },
  },
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@shared': resolve(__dirname, '../shared'),
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
  },
  
  server: {
    port: 5274,
    host: '0.0.0.0',
    open: true,
    // 开发服务器优化
    hmr: {
      overlay: false, // 禁用错误覆盖层以提升开发体验
    },
    proxy: {
      '/api': {
        target: 'http://localhost:8181',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, '/api'),
      },
    },
  },
  
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    
    // 构建优化
    target: 'es2020',   // 现代浏览器目标
    
    rollupOptions: {
      output: {
        // 优化 chunk 分割
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const extType = assetInfo.name?.split('.').pop();
          if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(assetInfo.name || '')) {
            return 'images/[name]-[hash].[ext]';
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name || '')) {
            return 'fonts/[name]-[hash].[ext]';
          }
          return `${extType || 'assets'}/[name]-[hash].[ext]`;
        },
        
        // 手动 chunk 分割策略
        manualChunks: {
          // Vue 核心
          'vue-vendor': ['vue', 'vue-router'],
          // Element Plus
          'element-vendor': ['element-plus'],
          // 图标库
          'icons-vendor': ['@element-plus/icons-vue', '@heroicons/vue'],
          // 工具库
          'utils-vendor': ['axios', 'dayjs', 'lodash-es'],
          // 图表库（如果使用）
          'charts-vendor': ['echarts'],
        },
      },
      
      // 外部依赖优化
      external: [],
    },
    
    // 使用 esbuild 进行压缩（更快）
    minify: 'esbuild',
    
    // 警告过滤
    chunkSizeWarningLimit: 1000, // 增加 chunk 大小警告阈值
  },
  
  // 依赖预构建优化
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'element-plus',
      '@element-plus/icons-vue',
      '@heroicons/vue',
      'axios',
      'dayjs',
      'lodash-es',
      'crypto-js',
      'js-cookie',
      'nprogress',
      'pinia',
      'echarts',
      'highlight.js',
    ],
    exclude: [
      // 排除某些不需要预构建的包
    ],
  },
  
  
  // 性能优化
  esbuild: {
    // 开发环境保留console输出
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
    legalComments: 'none',
  },
  
  
  // 定义全局常量
  define: {
    __VUE_OPTIONS_API__: false, // 禁用 Options API 以减少包大小
    __VUE_PROD_DEVTOOLS__: false, // 生产环境禁用 devtools
  },
});