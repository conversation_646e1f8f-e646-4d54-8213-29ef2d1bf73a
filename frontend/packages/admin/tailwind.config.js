/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: 'var(--el-color-primary)',
          50: '#eaebf8',
          100: '#d6d8f2', 
          200: '#bec1e5',
          300: '#9ea1d0',
          400: '#7e82bb',
          500: '#5357A0', // 长春花蓝 - 主色
          600: '#4a4e91',
          700: '#414583', // 深色 - 用于标题
          800: '#373a6d',
          900: '#30335a',
          'light-3': 'var(--el-color-primary-light-3)',
          'light-5': 'var(--el-color-primary-light-5)',
          'light-7': 'var(--el-color-primary-light-7)',
          'light-8': 'var(--el-color-primary-light-8)',
          'light-9': 'var(--el-color-primary-light-9)',
          'dark-2': 'var(--el-color-primary-dark-2)',
        },
        success: {
          DEFAULT: 'var(--el-color-success)',
          light: 'var(--el-color-success-light-9)',
          dark: 'var(--el-color-success-dark-2)',
        },
        warning: {
          DEFAULT: 'var(--el-color-warning)',
          light: 'var(--el-color-warning-light-9)', 
          dark: 'var(--el-color-warning-dark-2)',
        },
        danger: {
          DEFAULT: 'var(--el-color-danger)',
          light: 'var(--el-color-danger-light-9)',
          dark: 'var(--el-color-danger-dark-2)',
        },
        info: {
          DEFAULT: 'var(--el-color-info)',
          light: 'var(--el-color-info-light-9)',
          dark: 'var(--el-color-info-dark-2)',
        }
      }
    }
  }
}