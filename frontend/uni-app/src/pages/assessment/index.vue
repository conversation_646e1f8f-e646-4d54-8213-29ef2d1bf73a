<template>
  <PageContainer title="评估记录">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <Input
        v-model="searchKeyword"
        placeholder="搜索老年人姓名、身份证号"
        prefix-icon="search"
        clearable
        @input="handleSearch"
        @clear="handleSearchClear"
      />
      <Button type="primary" size="small" @click="showFilterModal = true"> 筛选 </Button>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar">
      <view class="total-info">
        <text>共 {{ total }} 条记录</text>
      </view>
      <view class="action-buttons">
        <Button type="default" size="small" @click="handleExport"> 导出 </Button>
        <Button type="primary" size="small" @click="navigateToCreate"> 新建评估 </Button>
      </view>
    </view>

    <!-- 评估列表 -->
    <view class="assessment-list">
      <Loading v-if="loading" type="spinner" text="加载中..." />

      <Empty
        v-else-if="!loading && assessmentList.length === 0"
        type="nodata"
        description="暂无评估记录"
        :show-button="true"
        button-text="新建评估"
        @button-click="navigateToCreate"
      />

      <view v-else class="list-content">
        <Card
          v-for="assessment in assessmentList"
          :key="assessment.id"
          class="assessment-item"
          clickable
          @click="handleAssessmentClick(assessment)"
        >
          <view class="assessment-header">
            <view class="elderly-info">
              <text class="elderly-name">{{ assessment.elderlyName }}</text>
              <text class="elderly-id">{{ formatIdNumber(assessment.elderlyIdNumber) }}</text>
            </view>
            <view class="assessment-status">
              <text class="status-tag" :class="getStatusClass(assessment.status)">
                {{ getStatusText(assessment.status) }}
              </text>
            </view>
          </view>

          <view class="assessment-content">
            <view class="info-row">
              <text class="label">评估量表：</text>
              <text class="value">{{ assessment.scaleName }}</text>
            </view>
            <view class="info-row">
              <text class="label">评估时间：</text>
              <text class="value">{{ formatDateTime(assessment.assessmentTime) }}</text>
            </view>
            <view class="info-row">
              <text class="label">评估员：</text>
              <text class="value">{{ assessment.assessorName }}</text>
            </view>
            <view v-if="assessment.totalScore !== null" class="info-row">
              <text class="label">总分：</text>
              <text class="value score">{{ assessment.totalScore }}分</text>
            </view>
            <view v-if="assessment.result" class="info-row">
              <text class="label">评估结果：</text>
              <text class="value result">{{ assessment.result }}</text>
            </view>
          </view>

          <view class="assessment-actions">
            <Button
              v-if="assessment.status === 'draft'"
              type="primary"
              size="mini"
              @click.stop="handleContinueAssessment(assessment)"
            >
              继续评估
            </Button>
            <Button
              v-if="assessment.status === 'completed'"
              type="default"
              size="mini"
              @click.stop="handleViewReport(assessment)"
            >
              查看报告
            </Button>
            <Button type="default" size="mini" @click.stop="handleMoreActions(assessment)">
              更多
            </Button>
          </view>
        </Card>
      </view>
    </view>

    <!-- 分页 -->
    <view v-if="!loading && assessmentList.length > 0" class="pagination">
      <Button :disabled="currentPage <= 1" @click="handlePageChange(currentPage - 1)">
        上一页
      </Button>
      <text class="page-info">{{ currentPage }} / {{ totalPages }}</text>
      <Button :disabled="currentPage >= totalPages" @click="handlePageChange(currentPage + 1)">
        下一页
      </Button>
    </view>

    <!-- 筛选弹窗 -->
    <Modal
      v-model:visible="showFilterModal"
      title="筛选条件"
      :show-cancel="false"
      position="bottom"
    >
      <view class="filter-modal">
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <Button type="default" size="mini" @click="resetFilter"> 重置 </Button>
        </view>

        <view class="filter-content">
          <!-- 评估状态 -->
          <FormItem label="评估状态">
            <view class="checkbox-group">
              <label v-for="status in statusOptions" :key="status.value" class="checkbox-item">
                <checkbox
                  :value="status.value"
                  :checked="filterData.status.includes(status.value)"
                  @change="handleStatusChange"
                />
                <text>{{ status.label }}</text>
              </label>
            </view>
          </FormItem>

          <!-- 评估时间 -->
          <FormItem label="评估时间">
            <view class="date-range">
              <Picker
                v-model="filterData.startDate"
                mode="date"
                :end="filterData.endDate || maxDate"
                placeholder="开始日期"
              />
              <text class="date-separator">至</text>
              <Picker
                v-model="filterData.endDate"
                mode="date"
                :start="filterData.startDate"
                :end="maxDate"
                placeholder="结束日期"
              />
            </view>
          </FormItem>

          <!-- 评估量表 -->
          <FormItem label="评估量表">
            <Picker
              v-model="filterData.scaleId"
              mode="selector"
              :range="scaleOptions"
              range-key="name"
              placeholder="请选择评估量表"
              @change="handleScaleChange"
            />
          </FormItem>

          <!-- 评估员 -->
          <FormItem label="评估员">
            <Picker
              v-model="filterData.assessorId"
              mode="selector"
              :range="assessorOptions"
              range-key="name"
              placeholder="请选择评估员"
              @change="handleAssessorChange"
            />
          </FormItem>
        </view>

        <view class="modal-actions">
          <Button type="default" @click="closeFilterModal"> 取消 </Button>
          <Button type="primary" @click="applyFilter"> 确定 </Button>
        </view>
      </view>
    </Modal>
  </PageContainer>
</template>

<script>
import { useUserStore, useAssessmentStore, useScaleStore } from '@/store'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import FormItem from '@/components/Form/FormItem.vue'
import Input from '@/components/Form/Input.vue'
import Picker from '@/components/Form/Picker.vue'
import Button from '@/components/Common/Button.vue'
import Loading from '@/components/Common/Loading.vue'
import Empty from '@/components/Common/Empty.vue'
import Modal from '@/components/Common/Modal.vue'
import { formatDateTime, formatIdNumber } from '@/utils'

export default {
  name: 'AssessmentList',

  components: {
    PageContainer,
    Card,
    FormItem,
    Input,
    Picker,
    Button,
    Loading,
    Empty,
    Modal
  },

  setup() {
    const userStore = useUserStore()
    const assessmentStore = useAssessmentStore()
    const scaleStore = useScaleStore()

    return {
      userStore,
      assessmentStore,
      scaleStore
    }
  },

  data() {
    return {
      loading: false,
      searchKeyword: '',
      showFilterModal: false,

      assessmentList: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,

      filterData: {
        status: [],
        startDate: '',
        endDate: '',
        scaleId: '',
        assessorId: ''
      },

      statusOptions: [
        { label: '草稿', value: 'draft' },
        { label: '进行中', value: 'in_progress' },
        { label: '已完成', value: 'completed' },
        { label: '已取消', value: 'cancelled' }
      ],

      scaleOptions: [],
      assessorOptions: []
    }
  },

  computed: {
    userInfo() {
      return this.userStore.userInfo
    },

    totalPages() {
      return Math.ceil(this.total / this.pageSize)
    },

    maxDate() {
      const today = new Date()
      return today.toISOString().split('T')[0]
    }
  },

  onLoad() {
    this.loadAssessmentList()
    this.loadScaleOptions()
    this.loadAssessorOptions()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    if (this.currentPage < this.totalPages) {
      this.loadMore()
    }
  },

  methods: {
    async loadAssessmentList(append = false) {
      try {
        if (!append) {
          this.loading = true
        }

        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          keyword: this.searchKeyword,
          ...this.filterData
        }

        const response = await this.assessmentStore.getAssessmentList(params)

        if (append) {
          this.assessmentList = [...this.assessmentList, ...response.data?.list || []]
        } else {
          this.assessmentList = response.data?.list || []
        }

        this.total = response.data?.total || 0
      } catch (error) {
        console.error('加载评估列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },

    async loadScaleOptions() {
      try {
        const response = await this.scaleStore.getScaleList({ page: 1, pageSize: 100 })
        this.scaleOptions = response.data?.list || []
      } catch (error) {
        console.error('加载量表选项失败:', error)
      }
    },

    async loadAssessorOptions() {
      try {
        const response = await this.userStore.getUserList({ page: 1, pageSize: 100, role: 'assessor' })
        this.assessorOptions = response.data?.list || []
      } catch (error) {
        console.error('加载评估员选项失败:', error)
      }
    },

    refreshData() {
      this.currentPage = 1
      this.loadAssessmentList()
    },

    loadMore() {
      this.currentPage++
      this.loadAssessmentList(true)
    },

    handleSearch() {
      this.currentPage = 1
      this.loadAssessmentList()
    },

    handleSearchClear() {
      this.searchKeyword = ''
      this.handleSearch()
    },

    handlePageChange(page) {
      this.currentPage = page
      this.loadAssessmentList()
    },

    handleAssessmentClick(assessment) {
      if (assessment.status === 'completed') {
        this.handleViewReport(assessment)
      } else {
        this.handleContinueAssessment(assessment)
      }
    },

    handleContinueAssessment(assessment) {
      uni.navigateTo({
        url: `/pages/assessment/conduct/index?id=${assessment.id}`
      })
    },

    handleViewReport(assessment) {
      uni.navigateTo({
        url: `/pages/assessment/report/index?id=${assessment.id}`
      })
    },

    handleMoreActions(assessment) {
      const actions = ['查看详情']

      if (assessment.status === 'draft') {
        actions.push('删除')
      }

      if (assessment.status === 'completed') {
        actions.push('导出报告')
      }

      actions.push('取消')

      uni.showActionSheet({
        itemList: actions,
        success: res => {
          const action = actions[res.tapIndex]

          switch (action) {
            case '查看详情':
              this.navigateToDetail(assessment)
              break
            case '删除':
              this.handleDelete(assessment)
              break
            case '导出报告':
              this.handleExportReport(assessment)
              break
          }
        }
      })
    },

    navigateToDetail(assessment) {
      uni.navigateTo({
        url: `/pages/assessment/detail/index?id=${assessment.id}`
      })
    },

    navigateToCreate() {
      uni.navigateTo({
        url: '/pages/assessment/create/index'
      })
    },

    async handleDelete(assessment) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除 ${assessment.elderlyName} 的评估记录吗？`,
        success: async res => {
          if (res.confirm) {
            try {
              await this.assessmentStore.deleteAssessment(assessment.id)
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              this.refreshData()
            } catch (error) {
              console.error('删除评估失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleExport() {
      try {
        uni.showLoading({ title: '导出中...' })

        const params = {
          keyword: this.searchKeyword,
          ...this.filterData
        }

        await this.assessmentStore.exportAssessments(params)

        uni.showToast({
          title: '导出成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('导出失败:', error)
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    async handleExportReport(_assessment) {
      try {
        uni.showLoading({ title: '导出中...' })
        // 导出单个评估报告的逻辑
        uni.showToast({
          title: '导出成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('导出报告失败:', error)
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    handleStatusChange(e) {
      const value = e.detail.value
      this.filterData.status = value
    },

    handleScaleChange(e) {
      const index = e.detail.value
      this.filterData.scaleId = this.scaleOptions[index]?.id || ''
    },

    handleAssessorChange(e) {
      const index = e.detail.value
      this.filterData.assessorId = this.assessorOptions[index]?.id || ''
    },

    resetFilter() {
      this.filterData = {
        status: [],
        startDate: '',
        endDate: '',
        scaleId: '',
        assessorId: ''
      }
    },

    closeFilterModal() {
      this.showFilterModal = false
    },

    applyFilter() {
      this.closeFilterModal()
      this.currentPage = 1
      this.loadAssessmentList()
    },

    getStatusClass(status) {
      const statusMap = {
        draft: 'status-draft',
        in_progress: 'status-progress',
        completed: 'status-completed',
        cancelled: 'status-cancelled'
      }
      return statusMap[status] || ''
    },

    getStatusText(status) {
      const statusMap = {
        draft: '草稿',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    },

    formatDateTime,
    formatIdNumber
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.search-bar {
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-bottom: 1px solid $border-color-light;

  .input {
    flex: 1;
  }
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-bottom: 1px solid $border-color-light;
}

.total-info {
  color: $text-color-secondary;
  font-size: $font-size-sm;
}

.action-buttons {
  display: flex;
  gap: $spacing-sm;
}

.assessment-list {
  padding: $spacing-md;
}

.list-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.assessment-item {
  .assessment-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-sm;
  }

  .elderly-info {
    .elderly-name {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-color-primary;
      display: block;
      margin-bottom: $spacing-xs;
    }

    .elderly-id {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }

  .assessment-status {
    .status-tag {
      padding: $spacing-xs $spacing-sm;
      border-radius: $border-radius-sm;
      font-size: $font-size-xs;
      font-weight: 500;

      &.status-draft {
        background-color: #f0f0f0;
        color: #666;
      }

      &.status-progress {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      &.status-completed {
        background-color: #f6ffed;
        color: #52c41a;
      }

      &.status-cancelled {
        background-color: #fff2f0;
        color: #ff4d4f;
      }
    }
  }

  .assessment-content {
    margin-bottom: $spacing-md;
  }

  .info-row {
    display: flex;
    margin-bottom: $spacing-xs;

    .label {
      color: $text-color-secondary;
      font-size: $font-size-sm;
      width: 80px;
      flex-shrink: 0;
    }

    .value {
      color: $text-color-primary;
      font-size: $font-size-sm;
      flex: 1;

      &.score {
        color: $primary-color;
        font-weight: 600;
      }

      &.result {
        color: $success-color;
        font-weight: 500;
      }
    }
  }

  .assessment-actions {
    display: flex;
    gap: $spacing-sm;
    justify-content: flex-end;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-lg;

  .page-info {
    color: $text-color-secondary;
    font-size: $font-size-sm;
  }
}

.filter-modal {
  background-color: $bg-color-white;
  border-radius: $border-radius-lg $border-radius-lg 0 0;
  max-height: 80vh;
  overflow: hidden;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-lg;
    border-bottom: 1px solid $border-color-light;

    .modal-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-color-primary;
    }
  }

  .filter-content {
    padding: $spacing-lg;
    max-height: 60vh;
    overflow-y: auto;
  }

  .checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;
  }

  .checkbox-item {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    text {
      font-size: $font-size-sm;
      color: $text-color-primary;
    }
  }

  .date-range {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    .date-separator {
      color: $text-color-secondary;
      font-size: $font-size-sm;
    }
  }

  .modal-actions {
    display: flex;
    gap: $spacing-sm;
    padding: $spacing-lg;
    border-top: 1px solid $border-color-light;

    .button {
      flex: 1;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    gap: $spacing-sm;
  }

  .action-bar {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;
  }

  .assessment-header {
    flex-direction: column;
    gap: $spacing-sm;
  }

  .assessment-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}
</style>
