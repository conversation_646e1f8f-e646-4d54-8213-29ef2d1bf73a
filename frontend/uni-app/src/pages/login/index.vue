<template>
  <view class="login-container">
    <!-- 头部logo区域 -->
    <view class="login-header">
      <view class="logo">
        <text class="logo-icon">🏥</text>
        <text class="logo-text">智能评估平台</text>
      </view>
      <text class="subtitle">多租户登录系统</text>
    </view>

    <!-- 登录表单 -->
    <form class="login-form" @submit.prevent="handleLogin">
      <!-- 租户代码输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">机构代码</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <text class="input-icon">🏢</text>
          <input
            v-model="loginForm.tenantCode"
            class="input-field"
            type="text"
            placeholder="请输入您的机构代码"
            maxlength="50"
            @input="clearError('tenantCode')"
          />
        </view>
        <text v-if="errors.tenantCode" class="error-text">{{ errors.tenantCode }}</text>
        <text class="hint-text">示例：demo_hospital</text>
      </view>

      <!-- 用户名输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">用户名</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <text class="input-icon">👤</text>
          <input
            v-model="loginForm.username"
            class="input-field"
            type="text"
            placeholder="请输入用户名"
            maxlength="50"
            @input="clearError('username')"
          />
        </view>
        <text v-if="errors.username" class="error-text">{{ errors.username }}</text>
      </view>

      <!-- 密码输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">密码</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <text class="input-icon">🔒</text>
          <input
            v-model="loginForm.password"
            class="input-field"
            :type="showPassword ? 'text' : 'password'"
            placeholder="请输入密码"
            maxlength="50"
            @input="clearError('password')"
          />
          <view class="password-toggle" @click="togglePassword">
            <text>{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
          </view>
        </view>
        <text v-if="errors.password" class="error-text">{{ errors.password }}</text>
      </view>

      <!-- 记住登录选项 -->
      <view class="remember-section">
        <view class="checkbox-wrapper" @click="toggleRemember">
          <view class="checkbox" :class="{ checked: loginForm.rememberMe }">
            <text v-if="loginForm.rememberMe" class="check-icon">✓</text>
          </view>
          <text class="checkbox-label">记住登录状态</text>
        </view>
      </view>

      <!-- 滑动验证码组件 -->
      <view class="captcha-section">
        <SlideCaptcha @verified="handleCaptchaVerified" @error="handleCaptchaError" />
      </view>

      <!-- 登录按钮 -->
      <view class="login-button-section">
        <button
          class="login-button"
          :class="{ disabled: loading || !captchaVerified }"
          type="submit"
          :disabled="loading || !captchaVerified"
        >
          <text v-if="loading">登录中...</text>
          <text v-else-if="!captchaVerified">请先完成验证码</text>
          <text v-else>登录</text>
        </button>
      </view>
    </form>

    <!-- 演示账户信息 -->
    <view class="demo-section">
      <view class="demo-header" @click="toggleDemoInfo">
        <text class="demo-title">💡 演示账户</text>
        <text class="demo-toggle">{{ showDemo ? '收起' : '展开' }}</text>
      </view>

      <view v-if="showDemo" class="demo-content">
        <view class="demo-item" @click="fillDemoAccount('hospital')">
          <text class="demo-type">🏥 医院机构</text>
          <view class="demo-details">
            <text class="demo-text">机构代码: demo_hospital</text>
            <text class="demo-text">用户名: demo_hospital_admin</text>
            <text class="demo-text">密码: password123</text>
          </view>
        </view>

        <view class="demo-item" @click="fillDemoAccount('nursing')">
          <text class="demo-type">🏠 护理机构</text>
          <view class="demo-details">
            <text class="demo-text">机构代码: demo_nursing</text>
            <text class="demo-text">用户名: demo_nursing_admin</text>
            <text class="demo-text">密码: password123</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">智能评估平台 v2.0.0</text>
      <text class="copyright">支持多租户架构</text>
    </view>
  </view>
</template>

<script>
import { login } from '@/api/auth'
import { getCaptcha, checkCaptcha } from '@/api/captcha'
import { mapActions } from 'vuex'
import SlideCaptcha from '@/components/SlideCaptcha.vue'

export default {
  name: 'LoginPage',

  components: {
    SlideCaptcha
  },

  data() {
    return {
      loading: false,
      showPassword: false,
      showDemo: false,

      loginForm: {
        tenantCode: 'demo_hospital',
        username: 'demo_hospital_admin',
        password: 'password123',
        rememberMe: false
      },

      errors: {},

      // 验证码相关
      captchaToken: '',
      captchaVerification: '',
      captchaVerified: false // 验证码是否已通过
    }
  },

  onLoad() {
    // 检查是否已经登录
    const token = uni.getStorageSync('token')
    if (token) {
      this.redirectToMain()
    }

    // 自动填充上次登录信息
    this.loadRememberedInfo()
  },

  methods: {
    ...mapActions('user', ['setUserInfo', 'setToken']),

    // 表单验证
    validateForm() {
      this.errors = {}

      if (!this.loginForm.tenantCode.trim()) {
        this.errors.tenantCode = '请输入机构代码'
      }

      if (!this.loginForm.username.trim()) {
        this.errors.username = '请输入用户名'
      }

      if (!this.loginForm.password.trim()) {
        this.errors.password = '请输入密码'
      } else if (this.loginForm.password.length < 6) {
        this.errors.password = '密码长度不能少于6位'
      }

      return Object.keys(this.errors).length === 0
    },

    // 清除单个字段错误
    clearError(field) {
      if (this.errors[field]) {
        delete this.errors[field]
        this.$forceUpdate()
      }
    },

    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword
    },

    // 切换记住登录
    toggleRemember() {
      this.loginForm.rememberMe = !this.loginForm.rememberMe
    },

    // 切换演示信息显示
    toggleDemoInfo() {
      this.showDemo = !this.showDemo
    },

    // 填充演示账户
    fillDemoAccount(type) {
      const accounts = {
        hospital: {
          tenantCode: 'demo_hospital',
          username: 'demo_hospital_admin',
          password: 'password123'
        },
        nursing: {
          tenantCode: 'demo_nursing',
          username: 'demo_nursing_admin',
          password: 'password123'
        }
      }

      const account = accounts[type]
      if (account) {
        this.loginForm.tenantCode = account.tenantCode
        this.loginForm.username = account.username
        this.loginForm.password = account.password
        this.clearAllErrors()

        uni.showToast({
          title: '演示账户已填充',
          icon: 'success',
          duration: 1500
        })
      }
    },

    // 清除所有错误
    clearAllErrors() {
      this.errors = {}
    },

    // 加载记住的登录信息
    loadRememberedInfo() {
      try {
        const remembered = uni.getStorageSync('rememberedLogin')
        if (remembered) {
          const info = JSON.parse(remembered)
          this.loginForm.tenantCode = info.tenantCode || ''
          this.loginForm.username = info.username || ''
          this.loginForm.rememberMe = true
        }
      } catch (error) {
        console.error('加载记住的登录信息失败:', error)
      }
    },

    // 保存登录信息
    saveLoginInfo() {
      if (this.loginForm.rememberMe) {
        const info = {
          tenantCode: this.loginForm.tenantCode,
          username: this.loginForm.username
        }
        uni.setStorageSync('rememberedLogin', JSON.stringify(info))
      } else {
        uni.removeStorageSync('rememberedLogin')
      }
    },

    // 处理登录
    async handleLogin() {
      if (this.loading) return

      // 表单验证
      if (!this.validateForm()) {
        uni.showToast({
          title: '请检查输入信息',
          icon: 'error'
        })
        return
      }

      // 检查验证码是否已通过
      if (!this.captchaVerified) {
        uni.showToast({
          title: '请先完成滑动验证码',
          icon: 'error'
        })
        return
      }

      this.loading = true

      try {
        // 添加客户端信息和验证码信息
        const loginData = {
          ...this.loginForm,
          clientInfo: `uni-app/${uni.getSystemInfoSync().platform}`
        }

        // 添加验证码信息（必须有）
        loginData.captchaToken = this.captchaToken
        loginData.captchaVerification = this.captchaVerification

        const response = await login(loginData)

        if (response.success || response.code === 200) {
          const result = response.data || response

          // 登录成功，清除失败次数
          uni.removeStorageSync('loginAttempts')

          // 保存登录信息
          this.saveLoginInfo()

          // 保存用户信息和token
          uni.setStorageSync('token', result.token)
          uni.setStorageSync('refreshToken', result.refreshToken || '')
          uni.setStorageSync('userInfo', JSON.stringify(result.user || {}))

          // 更新Vuex状态
          await this.setToken(result.token)
          await this.setUserInfo(result.user || {})

          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })

          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            this.redirectToMain()
          }, 1500)
        } else {
          throw new Error(response.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)

        // 记录登录失败次数
        const currentAttempts = uni.getStorageSync('loginAttempts') || 0
        uni.setStorageSync('loginAttempts', currentAttempts + 1)

        let errorMessage = '登录失败'
        if (error.message) {
          errorMessage = error.message
        } else if (error.data && error.data.message) {
          errorMessage = error.data.message
        }

        // 如果是验证码相关错误，重置验证码状态
        if (errorMessage.includes('验证码')) {
          this.captchaVerified = false
        }

        uni.showToast({
          title: errorMessage,
          icon: 'error',
          duration: 3000
        })
      } finally {
        this.loading = false
      }
    },

    // 跳转到主页
    redirectToMain() {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    },

    // 验证码事件处理方法
    handleCaptchaVerified(data) {
      console.log('✅ 验证码验证成功:', data)
      this.captchaVerified = true
      this.captchaToken = data.token
      this.captchaVerification = data.verification

      uni.showToast({
        title: '验证码验证成功',
        icon: 'success',
        duration: 1500
      })
    },

    handleCaptchaError(error) {
      console.error('❌ 验证码验证失败:', error)
      this.captchaVerified = false
      this.captchaToken = ''
      this.captchaVerification = ''

      uni.showToast({
        title: error || '验证码验证失败',
        icon: 'error',
        duration: 2000
      })
    },

    // API方法（传递给共享组件）
    getCaptcha,
    checkCaptcha
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;

.login-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
}

.login-header {
  text-align: center;
  margin-bottom: 80rpx;
  margin-top: 120rpx;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.logo-icon {
  font-size: 64rpx;
  margin-right: 20rpx;
}

.logo-text {
  font-size: 48rpx;
  font-weight: 600;
  color: #333333;
}

.subtitle {
  font-size: 28rpx;
  color: #666666;
}

.login-form {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-left: 8rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx 20rpx;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #5357a0;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(83, 87, 160, 0.1);
}

.input-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: #6c757d;
}

.input-field {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  border: none;
  outline: none;
}

.input-field::placeholder {
  color: #adb5bd;
}

.password-toggle {
  padding: 10rpx;
  cursor: pointer;
}

.error-text {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 12rpx;
  display: block;
}

.hint-text {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}

.remember-section {
  margin-bottom: 60rpx;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #dee2e6;
  border-radius: 6rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #5357a0;
  border-color: #5357a0;
}

.check-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

.checkbox-label {
  font-size: 26rpx;
  color: #6c757d;
}

/* 验证码组件容器 */
.captcha-section {
  margin-bottom: 30rpx;
}

.login-button-section {
  margin-bottom: 20rpx;
}

.login-button {
  width: 100%;
  background: #5357a0;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 28rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.login-button:not(.disabled):active {
  transform: translateY(2rpx);
}

.login-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.demo-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx;
  cursor: pointer;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.demo-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.demo-toggle {
  font-size: 24rpx;
  color: #5357a0;
}

.demo-content {
  padding: 20rpx 32rpx 32rpx;
}

.demo-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-item:last-child {
  margin-bottom: 0;
}

.demo-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.demo-type {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.demo-details {
  display: flex;
  flex-direction: column;
}

.demo-text {
  font-size: 22rpx;
  color: #6c757d;
  margin-bottom: 4rpx;
}

.demo-text:last-child {
  margin-bottom: 0;
}

.version-info {
  text-align: center;
  margin-top: auto;
  padding-top: 40rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 8rpx;
  display: block;
}

.copyright {
  font-size: 22rpx;
  color: #aaaaaa;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .login-container {
    padding: 30rpx 30rpx;
  }

  .login-header {
    margin-top: 80rpx;
    margin-bottom: 60rpx;
  }

  .logo-text {
    font-size: 40rpx;
  }

  .login-form {
    padding: 40rpx 30rpx;
  }
}
</style>
