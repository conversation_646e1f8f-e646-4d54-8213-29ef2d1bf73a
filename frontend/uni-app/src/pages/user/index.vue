<template>
  <PageContainer title="用户管理" :show-back="true">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <Input
          v-model="searchKeyword"
          placeholder="搜索用户姓名、工号、手机号"
          :show-search="true"
          @search="handleSearch"
          @clear="handleClear"
        />
        <Button type="default" size="small" @click="showFilterModal = true"> 筛选 </Button>
      </view>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar">
      <text class="total-count">共 {{ pagination.total }} 个用户</text>
      <view class="action-buttons">
        <Button type="default" size="small" :loading="exporting" @click="exportUsers">
          导出
        </Button>
        <Button type="primary" size="small" @click="addUser"> 添加用户 </Button>
      </view>
    </view>

    <!-- 用户列表 -->
    <view class="user-list">
      <Loading v-if="loading" type="spinner" text="加载中..." />

      <Empty
        v-else-if="userList.length === 0"
        type="search"
        :description="searchKeyword ? '未找到相关用户' : '暂无用户数据'"
        :show-button="!searchKeyword"
        button-text="添加用户"
        @button-click="addUser"
      />

      <view v-else class="user-items">
        <Card
          v-for="user in userList"
          :key="user.id"
          class="user-card"
          @click="viewUserDetail(user.id)"
        >
          <view class="user-content">
            <view class="user-avatar">
              <image v-if="user.avatar" :src="user.avatar" mode="aspectFill" />
              <view v-else class="avatar-placeholder">
                <text>{{ user.name ? user.name.charAt(0) : 'U' }}</text>
              </view>
            </view>

            <view class="user-info">
              <view class="user-header">
                <text class="user-name">{{ user.name }}</text>
                <view class="user-status">
                  <text :class="['status-badge', user.status]">{{
                    formatUserStatus(user.status)
                  }}</text>
                </view>
              </view>

              <view class="user-details">
                <text class="user-detail">工号：{{ user.employeeId || '未设置' }}</text>
                <text class="user-detail">部门：{{ user.department || '未设置' }}</text>
                <text class="user-detail">角色：{{ formatUserRole(user.role) }}</text>
              </view>

              <view class="user-contact">
                <text class="contact-info">{{ user.phone || '未设置手机号' }}</text>
                <text class="contact-info">{{ user.email || '未设置邮箱' }}</text>
              </view>

              <view class="user-meta">
                <text class="meta-info">创建时间：{{ formatDate(user.createdAt) }}</text>
                <text class="meta-info"
                  >最后登录：{{
                    user.lastLoginAt ? formatDate(user.lastLoginAt) : '从未登录'
                  }}</text
                >
              </view>
            </view>

            <view class="user-actions" @click.stop>
              <Button type="text" size="small" @click="editUser(user.id)"> 编辑 </Button>
              <Button type="text" size="small" @click="showUserActions(user)"> 更多 </Button>
            </view>
          </view>
        </Card>
      </view>
    </view>

    <!-- 分页 -->
    <Pagination
      v-if="userList.length > 0"
      :current="pagination.current"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      @change="handlePageChange"
    />

    <!-- 筛选弹窗 -->
    <Modal v-model="showFilterModal" title="筛选条件" @confirm="applyFilter" @cancel="resetFilter">
      <view class="filter-content">
        <FormItem label="用户状态">
          <Picker
            :range="statusOptions"
            range-key="label"
            :value="filterStatusIndex"
            @change="onFilterStatusChange"
          >
            <view class="picker-display">
              <text :class="{ placeholder: !filterData.status }">{{
                filterData.status ? getStatusLabel(filterData.status) : '全部状态'
              }}</text>
              <text class="picker-arrow">></text>
            </view>
          </Picker>
        </FormItem>

        <FormItem label="用户角色">
          <Picker
            :range="roleOptions"
            range-key="label"
            :value="filterRoleIndex"
            @change="onFilterRoleChange"
          >
            <view class="picker-display">
              <text :class="{ placeholder: !filterData.role }">{{
                filterData.role ? getRoleLabel(filterData.role) : '全部角色'
              }}</text>
              <text class="picker-arrow">></text>
            </view>
          </Picker>
        </FormItem>

        <FormItem label="所属部门">
          <Input v-model="filterData.department" placeholder="请输入部门名称" />
        </FormItem>

        <FormItem label="创建时间">
          <view class="date-range">
            <Picker mode="date" :value="filterData.startDate" @change="onStartDateChange">
              <view class="date-picker">
                <text :class="{ placeholder: !filterData.startDate }">{{
                  filterData.startDate || '开始日期'
                }}</text>
              </view>
            </Picker>
            <text class="date-separator">至</text>
            <Picker mode="date" :value="filterData.endDate" @change="onEndDateChange">
              <view class="date-picker">
                <text :class="{ placeholder: !filterData.endDate }">{{
                  filterData.endDate || '结束日期'
                }}</text>
              </view>
            </Picker>
          </view>
        </FormItem>
      </view>
    </Modal>

    <!-- 用户操作菜单 -->
    <ActionSheet
      v-model="showActionSheet"
      :actions="actionSheetActions"
      @select="handleActionSelect"
    />
  </PageContainer>
</template>

<script>
import { useUserStore } from '@/store'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import Button from '@/components/Common/Button.vue'
import Input from '@/components/Form/Input.vue'
import Loading from '@/components/Common/Loading.vue'
import Empty from '@/components/Common/Empty.vue'
import Pagination from '@/components/Common/Pagination.vue'
import Modal from '@/components/Common/Modal.vue'
import FormItem from '@/components/Form/FormItem.vue'
import Picker from '@/components/Form/Picker.vue'
import ActionSheet from '@/components/Common/ActionSheet.vue'

export default {
  name: 'UserManagement',

  components: {
    PageContainer,
    Card,
    Button,
    Input,
    Loading,
    Empty,
    Pagination,
    Modal,
    FormItem,
    Picker,
    ActionSheet
  },

  setup() {
    const userStore = useUserStore()

    return {
      userStore
    }
  },

  data() {
    return {
      loading: false,
      exporting: false,
      searchKeyword: '',
      userList: [],

      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },

      showFilterModal: false,
      filterData: {
        status: '',
        role: '',
        department: '',
        startDate: '',
        endDate: ''
      },

      statusOptions: [
        { label: '全部状态', value: '' },
        { label: '正常', value: 'active' },
        { label: '禁用', value: 'disabled' },
        { label: '待激活', value: 'pending' }
      ],

      roleOptions: [
        { label: '全部角色', value: '' },
        { label: '系统管理员', value: 'admin' },
        { label: '评估师', value: 'assessor' },
        { label: '护理员', value: 'caregiver' },
        { label: '医生', value: 'doctor' },
        { label: '普通用户', value: 'user' }
      ],

      showActionSheet: false,
      selectedUser: null,
      actionSheetActions: []
    }
  },

  computed: {
    currentUser() {
      return this.userStore.userInfo
    },

    filterStatusIndex() {
      return this.statusOptions.findIndex(item => item.value === this.filterData.status)
    },

    filterRoleIndex() {
      return this.roleOptions.findIndex(item => item.value === this.filterData.role)
    }
  },

  onLoad() {
    this.loadUserList()
  },

  onPullDownRefresh() {
    this.loadUserList().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.pagination.current * this.pagination.pageSize < this.pagination.total) {
      this.pagination.current++
      this.loadUserList(true)
    }
  },

  methods: {
    async loadUserList(append = false) {
      try {
        this.loading = !append

        const params = {
          page: this.pagination.current,
          pageSize: this.pagination.pageSize,
          keyword: this.searchKeyword,
          ...this.filterData
        }

        const result = await this.userStore.getUserList(params)

        if (append) {
          this.userList = [...this.userList, ...result.list]
        } else {
          this.userList = result.list
        }

        this.pagination.total = result.total
      } catch (error) {
        console.error('加载用户列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      this.pagination.current = 1
      this.loadUserList()
    },

    handleClear() {
      this.searchKeyword = ''
      this.pagination.current = 1
      this.loadUserList()
    },

    handlePageChange(page) {
      this.pagination.current = page
      this.loadUserList()
    },

    onFilterStatusChange(e) {
      const index = e.detail.value
      this.filterData.status = this.statusOptions[index].value
    },

    onFilterRoleChange(e) {
      const index = e.detail.value
      this.filterData.role = this.roleOptions[index].value
    },

    onStartDateChange(e) {
      this.filterData.startDate = e.detail.value
    },

    onEndDateChange(e) {
      this.filterData.endDate = e.detail.value
    },

    applyFilter() {
      this.showFilterModal = false
      this.pagination.current = 1
      this.loadUserList()
    },

    resetFilter() {
      this.filterData = {
        status: '',
        role: '',
        department: '',
        startDate: '',
        endDate: ''
      }
      this.showFilterModal = false
      this.pagination.current = 1
      this.loadUserList()
    },

    getStatusLabel(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.label : status
    },

    getRoleLabel(role) {
      const option = this.roleOptions.find(item => item.value === role)
      return option ? option.label : role
    },

    formatUserStatus(status) {
      const statusMap = {
        active: '正常',
        disabled: '禁用',
        pending: '待激活'
      }
      return statusMap[status] || status
    },

    formatUserRole(role) {
      const roleMap = {
        admin: '系统管理员',
        assessor: '评估师',
        caregiver: '护理员',
        doctor: '医生',
        user: '普通用户'
      }
      return roleMap[role] || role
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    },

    addUser() {
      uni.navigateTo({
        url: '/pages/user/create/index'
      })
    },

    editUser(userId) {
      uni.navigateTo({
        url: `/pages/user/create/index?id=${userId}`
      })
    },

    viewUserDetail(userId) {
      uni.navigateTo({
        url: `/pages/user/detail/index?id=${userId}`
      })
    },

    showUserActions(user) {
      this.selectedUser = user

      this.actionSheetActions = [
        {
          text: '查看详情',
          value: 'detail'
        },
        {
          text: '编辑用户',
          value: 'edit'
        }
      ]

      // 根据用户状态添加不同操作
      if (user.status === 'active') {
        this.actionSheetActions.push({
          text: '禁用用户',
          value: 'disable',
          type: 'warn'
        })
      } else if (user.status === 'disabled') {
        this.actionSheetActions.push({
          text: '启用用户',
          value: 'enable'
        })
      }

      this.actionSheetActions.push(
        {
          text: '重置密码',
          value: 'resetPassword'
        },
        {
          text: '删除用户',
          value: 'delete',
          type: 'danger'
        }
      )

      this.showActionSheet = true
    },

    async handleActionSelect(action) {
      const user = this.selectedUser

      switch (action.value) {
        case 'detail':
          this.viewUserDetail(user.id)
          break

        case 'edit':
          this.editUser(user.id)
          break

        case 'disable':
          await this.handleUpdateUserStatus(user.id, 'disabled')
          break

        case 'enable':
          await this.handleUpdateUserStatus(user.id, 'active')
          break

        case 'resetPassword':
          await this.handleResetPassword(user.id)
          break

        case 'delete':
          await this.handleDeleteUser(user.id)
          break
      }
    },

    async handleUpdateUserStatus(userId, status) {
      const statusText = status === 'active' ? '启用' : '禁用'

      uni.showModal({
        title: '确认操作',
        content: `确定要${statusText}该用户吗？`,
        success: async res => {
          if (res.confirm) {
            try {
              await this.userStore.updateUserStatus({ userId, status })

              uni.showToast({
                title: `${statusText}成功`,
                icon: 'success'
              })

              this.loadUserList()
            } catch (error) {
              console.error(`${statusText}用户失败:`, error)
              uni.showToast({
                title: `${statusText}失败`,
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleResetPassword(userId) {
      uni.showModal({
        title: '确认重置',
        content: '确定要重置该用户的密码吗？重置后将发送新密码到用户手机。',
        success: async res => {
          if (res.confirm) {
            try {
              await this.resetUserPassword(userId)

              uni.showToast({
                title: '密码重置成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('重置密码失败:', error)
              uni.showToast({
                title: '重置失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleDeleteUser(userId) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该用户吗？删除后无法恢复。',
        success: async res => {
          if (res.confirm) {
            try {
              await this.deleteUser(userId)

              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })

              this.loadUserList()
            } catch (error) {
              console.error('删除用户失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async exportUsers() {
      try {
        this.exporting = true

        const params = {
          keyword: this.searchKeyword,
          ...this.filterData
        }

        await this.exportUserList(params)

        uni.showToast({
          title: '导出成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('导出用户失败:', error)
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      } finally {
        this.exporting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.search-section {
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-bottom: 1px solid $border-color-light;

  .search-bar {
    display: flex;
    gap: $spacing-sm;
    align-items: center;
  }
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-bottom: 1px solid $border-color-light;

  .total-count {
    font-size: $font-size-sm;
    color: $text-color-secondary;
  }

  .action-buttons {
    display: flex;
    gap: $spacing-sm;
  }
}

.user-list {
  padding: $spacing-md;

  .user-items {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
  }
}

.user-card {
  .user-content {
    display: flex;
    gap: $spacing-md;

    .user-avatar {
      width: 60px;
      height: 60px;
      flex-shrink: 0;

      image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      .avatar-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $color-primary;
        color: white;
        border-radius: 50%;
        font-size: $font-size-lg;
        font-weight: 600;
      }
    }

    .user-info {
      flex: 1;

      .user-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-xs;

        .user-name {
          font-size: $font-size-lg;
          font-weight: 600;
          color: $text-color-primary;
        }

        .user-status {
          .status-badge {
            font-size: $font-size-xs;
            padding: 2px 8px;
            border-radius: $border-radius-xs;

            &.active {
              background-color: $color-success-light;
              color: $color-success;
            }

            &.disabled {
              background-color: $color-danger-light;
              color: $color-danger;
            }

            &.pending {
              background-color: $color-warning-light;
              color: $color-warning;
            }
          }
        }
      }

      .user-details {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-md;
        margin-bottom: $spacing-xs;

        .user-detail {
          font-size: $font-size-sm;
          color: $text-color-secondary;
        }
      }

      .user-contact {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-md;
        margin-bottom: $spacing-xs;

        .contact-info {
          font-size: $font-size-sm;
          color: $text-color-secondary;
        }
      }

      .user-meta {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-md;

        .meta-info {
          font-size: $font-size-xs;
          color: $text-color-placeholder;
        }
      }
    }

    .user-actions {
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;
      align-self: flex-start;
    }
  }
}

.filter-content {
  .picker-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-sm $spacing-md;
    background-color: $bg-color-light;
    border: 1px solid $border-color-light;
    border-radius: $border-radius-sm;

    text {
      font-size: $font-size-base;

      &.placeholder {
        color: $text-color-placeholder;
      }
    }

    .picker-arrow {
      color: $text-color-secondary;
      transform: rotate(90deg);
    }
  }

  .date-range {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    .date-picker {
      flex: 1;
      padding: $spacing-sm $spacing-md;
      background-color: $bg-color-light;
      border: 1px solid $border-color-light;
      border-radius: $border-radius-sm;

      text {
        font-size: $font-size-base;

        &.placeholder {
          color: $text-color-placeholder;
        }
      }
    }

    .date-separator {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-content {
    flex-direction: column;

    .user-avatar {
      align-self: center;
    }

    .user-actions {
      flex-direction: row;
      justify-content: center;
    }
  }

  .user-details,
  .user-contact,
  .user-meta {
    flex-direction: column;
    gap: $spacing-xs;
  }

  .action-bar {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;
  }

  .date-range {
    flex-direction: column;

    .date-separator {
      text-align: center;
    }
  }
}
</style>
