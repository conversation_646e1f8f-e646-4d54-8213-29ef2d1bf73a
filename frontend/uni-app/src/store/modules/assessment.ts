import { defineStore } from 'pinia'

// 评估相关类型定义
export interface Assessment {
  id: string | number | null
  elderlyId: string | number
  elderlyName: string
  scaleId: string | number
  scaleName: string
  status: 'in_progress' | 'completed' | 'paused'
  startTime: string
  endTime?: string
  assessorId?: string | number | null
  assessorName?: string | null
  score?: number
  createTime?: string
}

export interface AssessmentQuestion {
  id: string | number
  title: string
  type: 'single' | 'multiple' | 'text' | 'score'
  options?: Array<{
    id: string | number
    text: string
    value: any
    score?: number
  }>
  required?: boolean
  description?: string
}

export interface AssessmentSection {
  id: string | number
  title: string
  description?: string
  questions: AssessmentQuestion[]
}

export interface Scale {
  id: string | number
  name: string
  description?: string
  sections: AssessmentSection[]
}

export interface AssessmentProgress {
  currentSectionIndex: number
  currentQuestionIndex: number
  totalSections: number
  totalQuestions: number
  completedQuestions: number
  answers: Record<string, any>
  startTime: string | null
  lastSaveTime: string | null
}

export interface AssessmentResult {
  totalScore: number
  sectionScores: Array<{
    sectionId: string | number
    score: number
    maxScore: number
  }>
  level: string
  suggestions: string[]
}

export interface AssessmentStats {
  totalCount: number
  completedCount: number
  inProgressCount: number
  averageScore: number
  averageDuration?: number
}

// Store 状态类型定义
interface AssessmentState {
  currentAssessment: Assessment | null
  assessmentList: Assessment[]
  scaleList: Scale[]
  currentScale: Scale | null
  assessmentProgress: AssessmentProgress
  assessmentResult: AssessmentResult | null
  assessmentHistory: Assessment[]
  assessmentStats: AssessmentStats
}

export const useAssessmentStore = defineStore('assessment', {
  state: (): AssessmentState => ({
    currentAssessment: null,
    assessmentList: [],
    scaleList: [],
    currentScale: null,
    assessmentProgress: {
      currentSectionIndex: 0,
      currentQuestionIndex: 0,
      totalSections: 0,
      totalQuestions: 0,
      completedQuestions: 0,
      answers: {},
      startTime: null,
      lastSaveTime: null
    },
    assessmentResult: null,
    assessmentHistory: [],
    assessmentStats: {
      totalCount: 0,
      completedCount: 0,
      inProgressCount: 0,
      averageScore: 0,
      averageDuration: 0
    }
  }),

  getters: {
    // 当前评估信息
    currentAssessment: (state): Assessment | null => state.currentAssessment,

    // 当前量表
    currentScale: (state): Scale | null => state.currentScale,

    // 评估进度
    assessmentProgress: (state): AssessmentProgress => state.assessmentProgress,

    // 评估结果
    assessmentResult: (state): AssessmentResult | null => state.assessmentResult,

    // 评估列表
    assessmentList: (state): Assessment[] => state.assessmentList,

    // 量表列表
    scaleList: (state): Scale[] => state.scaleList,

    // 评估统计
    assessmentStats: (state): AssessmentStats => state.assessmentStats,

    // 当前题目
    currentQuestion: (state): AssessmentQuestion | null => {
      if (!state.currentScale || !state.currentScale.sections) {
        return null
      }

      const { currentSectionIndex, currentQuestionIndex } = state.assessmentProgress
      const currentSection = state.currentScale.sections[currentSectionIndex]

      if (!currentSection || !currentSection.questions) {
        return null
      }

      return currentSection.questions[currentQuestionIndex]
    },

    // 当前章节
    currentSection: (state): AssessmentSection | null => {
      if (!state.currentScale || !state.currentScale.sections) {
        return null
      }

      const { currentSectionIndex } = state.assessmentProgress
      return state.currentScale.sections[currentSectionIndex]
    },

    // 评估完成百分比
    completionPercentage: (state): number => {
      const { totalQuestions, completedQuestions } = state.assessmentProgress
      if (totalQuestions === 0) return 0
      return Math.round((completedQuestions / totalQuestions) * 100)
    },

    // 是否为最后一题
    isLastQuestion: (state): boolean => {
      if (!state.currentScale || !state.currentScale.sections) {
        return false
      }

      const { currentSectionIndex, currentQuestionIndex } = state.assessmentProgress
      const isLastSection = currentSectionIndex === state.currentScale.sections.length - 1
      const currentSection = state.currentScale.sections[currentSectionIndex]
      const isLastQuestionInSection = currentQuestionIndex === currentSection.questions.length - 1

      return isLastSection && isLastQuestionInSection
    },

    // 是否为第一题
    isFirstQuestion: (state): boolean => {
      const { currentSectionIndex, currentQuestionIndex } = state.assessmentProgress
      return currentSectionIndex === 0 && currentQuestionIndex === 0
    },

    // 获取指定题目的答案
    getAnswer: (state) => (questionId: string): any => {
      return state.assessmentProgress.answers[questionId]
    },

    // 检查是否有未保存的更改
    hasUnsavedChanges: (): boolean => {
      // 这里可以添加逻辑来检查是否有未保存的更改
      return false
    }
  },

  actions: {
    // 设置当前评估
    setCurrentAssessment(assessment: Assessment | null) {
      this.currentAssessment = assessment
    },

    // 设置评估列表
    setAssessmentList(list: Assessment[]) {
      this.assessmentList = list
    },

    // 设置量表列表
    setScaleList(list: Scale[]) {
      this.scaleList = list
    },

    // 设置当前量表
    setCurrentScale(scale: Scale | null) {
      this.currentScale = scale
    },

    // 设置评估进度
    setAssessmentProgress(progress: Partial<AssessmentProgress>) {
      this.assessmentProgress = { ...this.assessmentProgress, ...progress }
    },

    // 设置评估结果
    setAssessmentResult(result: AssessmentResult | null) {
      this.assessmentResult = result
    },

    // 设置评估统计
    setAssessmentStats(stats: Partial<AssessmentStats>) {
      this.assessmentStats = { ...this.assessmentStats, ...stats }
    },

    // 获取评估列表
    async getAssessmentList(params: any = {}) {
      // 这里应该调用API获取评估列表
      // const response = await getAssessmentList(params)
      // this.setAssessmentList(response.data.list)
      // return response

      // 临时模拟数据
      const mockData = {
        list: [],
        total: 0
      }
      this.setAssessmentList(mockData.list)
      return { data: mockData }
    },

    // 获取量表列表
    async getScaleList(params: any = {}) {
      // 这里应该调用API获取量表列表
      // const response = await getScaleList(params)
      // this.setScaleList(response.data.list)
      // return response

      // 临时模拟数据
      const mockData = {
        list: [],
        total: 0
      }
      this.setScaleList(mockData.list)
      return { data: mockData }
    },

    // 获取量表详情
    async getScaleDetail(scaleId: string | number) {
      // 这里应该调用API获取量表详情
      // const response = await getScaleDetail(scaleId)
      // this.setCurrentScale(response.data)
      // return response

      // 临时返回空数据
      return { data: null }
    },

    // 开始评估
    startAssessment({ scale, elderly }: { scale: Scale; elderly: any }) {
      this.setCurrentScale(scale)
      this.setAssessmentProgress({
        currentSectionIndex: 0,
        currentQuestionIndex: 0,
        totalSections: scale.sections?.length || 0,
        totalQuestions:
          scale.sections?.reduce(
            (total, section) => total + (section.questions?.length || 0),
            0
          ) || 0,
        completedQuestions: 0,
        answers: {},
        startTime: new Date().toISOString(),
        lastSaveTime: new Date().toISOString()
      })

      this.setCurrentAssessment({
        id: null,
        elderlyId: elderly.id,
        elderlyName: elderly.name,
        scaleId: scale.id,
        scaleName: scale.name,
        status: 'in_progress',
        startTime: new Date().toISOString(),
        assessorId: null,
        assessorName: null
      })
    },

    // 更新答案
    updateAnswer({ questionId, answer }: { questionId: string; answer: any }) {
      this.assessmentProgress.answers[questionId] = answer

      // 更新完成的题目数量
      this.assessmentProgress.completedQuestions = Object.keys(
        this.assessmentProgress.answers
      ).length

      // 更新最后保存时间
      this.assessmentProgress.lastSaveTime = new Date().toISOString()
    },

    // 保存答案
    async saveAnswer({ questionId, answer }: { questionId: string; answer: any }) {
      this.updateAnswer({ questionId, answer })

      // 自动保存到服务器
      try {
        // 这里应该调用API保存答案
        // await saveAssessmentAnswer({
        //   assessmentId: this.currentAssessment.id,
        //   questionId,
        //   answer
        // })
      } catch (error) {
        console.error('保存答案失败:', error)
      }
    },

    // 更新当前题目索引
    updateQuestionIndex({ sectionIndex, questionIndex }: { sectionIndex: number; questionIndex: number }) {
      this.assessmentProgress.currentSectionIndex = sectionIndex
      this.assessmentProgress.currentQuestionIndex = questionIndex
    },

    // 下一题
    nextQuestion() {
      const { currentSectionIndex, currentQuestionIndex } = this.assessmentProgress
      const currentSection = this.currentScale!.sections[currentSectionIndex]

      if (currentQuestionIndex < currentSection.questions.length - 1) {
        // 同一章节的下一题
        this.updateQuestionIndex({
          sectionIndex: currentSectionIndex,
          questionIndex: currentQuestionIndex + 1
        })
      } else if (currentSectionIndex < this.currentScale!.sections.length - 1) {
        // 下一章节的第一题
        this.updateQuestionIndex({
          sectionIndex: currentSectionIndex + 1,
          questionIndex: 0
        })
      }
    },

    // 上一题
    previousQuestion() {
      const { currentSectionIndex, currentQuestionIndex } = this.assessmentProgress

      if (currentQuestionIndex > 0) {
        // 同一章节的上一题
        this.updateQuestionIndex({
          sectionIndex: currentSectionIndex,
          questionIndex: currentQuestionIndex - 1
        })
      } else if (currentSectionIndex > 0) {
        // 上一章节的最后一题
        const previousSection = this.currentScale!.sections[currentSectionIndex - 1]
        this.updateQuestionIndex({
          sectionIndex: currentSectionIndex - 1,
          questionIndex: previousSection.questions.length - 1
        })
      }
    },

    // 跳转到指定题目
    goToQuestion({ sectionIndex, questionIndex }: { sectionIndex: number; questionIndex: number }) {
      this.updateQuestionIndex({ sectionIndex, questionIndex })
    },

    // 提交评估
    async submitAssessment() {
      // 这里应该调用API提交评估
      // const response = await submitAssessment({
      //   assessmentId: this.currentAssessment.id,
      //   answers: this.assessmentProgress.answers
      // })
      // this.setAssessmentResult(response.data)
      // return response

      // 临时模拟提交成功
      const mockResult: AssessmentResult = {
        totalScore: 0,
        sectionScores: [],
        level: 'mild',
        suggestions: []
      }
      this.setAssessmentResult(mockResult)
      return { data: mockResult }
    },

    // 暂停评估
    async pauseAssessment() {
      // 这里应该调用API暂停评估
      // await pauseAssessment(this.currentAssessment.id)

      // 更新本地状态
      if (this.currentAssessment) {
        const updatedAssessment = {
          ...this.currentAssessment,
          status: 'paused' as const
        }
        this.setCurrentAssessment(updatedAssessment)
      }
    },

    // 恢复评估
    async resumeAssessment(assessmentId: string | number) {
      // 这里应该调用API恢复评估
      // const response = await resumeAssessment(assessmentId)
      // this.setCurrentAssessment(response.data.assessment)
      // this.setCurrentScale(response.data.scale)
      // this.setAssessmentProgress(response.data.progress)
      // return response

      // 临时返回空数据
      return { data: null }
    },

    // 删除评估
    async deleteAssessment(assessmentId: string | number) {
      // 这里应该调用API删除评估
      // await deleteAssessment(assessmentId)

      // 删除本地记录
      const index = this.assessmentList.findIndex(item => item.id === assessmentId)
      if (index !== -1) {
        this.assessmentList.splice(index, 1)
      }
    },

    // 获取评估统计
    async getAssessmentStats() {
      // 这里应该调用API获取评估统计
      // const response = await getAssessmentStats()
      // this.setAssessmentStats(response.data)
      // return response

      // 临时模拟数据
      const mockStats: AssessmentStats = {
        totalCount: 0,
        completedCount: 0,
        inProgressCount: 0,
        averageScore: 0,
        averageDuration: 0
      }
      this.setAssessmentStats(mockStats)
      return { data: mockStats }
    },

    // 重置评估状态
    resetAssessment() {
      this.setCurrentAssessment(null)
      this.setCurrentScale(null)
      this.setAssessmentProgress({
        currentSectionIndex: 0,
        currentQuestionIndex: 0,
        totalSections: 0,
        totalQuestions: 0,
        completedQuestions: 0,
        answers: {},
        startTime: null,
        lastSaveTime: null
      })
      this.setAssessmentResult(null)
    },

    // 获取评估统计数据（用于首页显示）
    async getAssessmentStatistics() {
      try {
        // 调用真实API
        const { getAssessmentStatistics } = await import('@/api/assessment')
        const response = await getAssessmentStatistics()
        
        if (response.success) {
          this.setAssessmentStats({
            totalCount: response.data.total || 0,
            completedCount: response.data.completed || 0,
            inProgressCount: response.data.inProgress || 0,
            averageScore: response.data.averageScore || 0
          })
          return response
        } else {
          throw new Error(response.message || '获取评估统计失败')
        }
      } catch (error) {
        console.error('获取评估统计数据失败:', error)
        const defaultStats = { total: 0, completed: 0, inProgress: 0, averageScore: 0 }
        this.setAssessmentStats({
          totalCount: 0,
          completedCount: 0,
          inProgressCount: 0,
          averageScore: 0
        })
        return { data: defaultStats }
      }
    },

    // 获取最近评估记录
    async getRecentAssessments(params: any = {}) {
      try {
        // 调用真实API
        const { getRecentAssessments } = await import('@/api/assessment')
        const response = await getRecentAssessments(params)
        
        if (response.success) {
          this.setAssessmentList(response.data.list || [])
          return response
        } else {
          throw new Error(response.message || '获取最近评估记录失败')
        }
      } catch (error) {
        console.error('获取最近评估记录失败:', error)
        const defaultData = { list: [], total: 0 }
        this.setAssessmentList([])
        return { data: defaultData }
      }
    },

    // 添加评估记录
    addAssessmentRecord(assessment: Assessment) {
      this.assessmentList.unshift(assessment)
    },

    // 更新评估记录
    updateAssessmentRecord(assessment: Assessment) {
      const index = this.assessmentList.findIndex(item => item.id === assessment.id)
      if (index !== -1) {
        this.assessmentList.splice(index, 1, assessment)
      }
    }
  }
})