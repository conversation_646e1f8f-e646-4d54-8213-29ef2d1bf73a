import { defineStore } from 'pinia'

// 应用配置类型定义
export interface AppConfig {
  name: string
  version: string
  description: string
}

// 上传配置类型定义
export interface UploadConfig {
  maxSize: number
  allowedTypes: string[]
  allowedExtensions: string[]
}

// 缓存配置类型定义
export interface CacheConfig {
  expireTime: number
  maxSize: number
}

// 系统设置类型定义
export interface SystemSettings {
  theme: 'light' | 'dark'
  language: string
  fontSize: 'small' | 'normal' | 'large'
  debug: boolean
  timeout: number
  pageSize: number
  upload: UploadConfig
  cache: CacheConfig
}

// 评估配置类型定义
export interface AssessmentConfig {
  autoSaveInterval: number
  timeout: number
  allowPause: boolean
  allowSkip: boolean
}

// 老人照片配置类型定义
export interface ElderlyPhotoConfig {
  required: boolean
  maxSize: number
  allowedTypes: string[]
}

// 老人管理配置类型定义
export interface ElderlyConfig {
  photo: ElderlyPhotoConfig
  requiredFields: string[]
}

// 报告配置类型定义
export interface ReportConfig {
  formats: string[]
  defaultFormat: string
  includeCharts: boolean
  includeSuggestions: boolean
}

// 业务配置类型定义
export interface BusinessConfig {
  assessment: AssessmentConfig
  elderly: ElderlyConfig
  report: ReportConfig
}

// 字典项类型定义
export interface DictItem {
  label: string
  value: string
  color?: string
}

// 字典数据类型定义
export interface Dictionaries {
  gender: DictItem[]
  assessmentStatus: DictItem[]
  assessmentLevel: DictItem[]
  userRoles: DictItem[]
}

// Store 状态类型定义
interface ConfigState {
  baseURL: string
  appConfig: AppConfig
  systemSettings: SystemSettings
  businessConfig: BusinessConfig
  dictionaries: Dictionaries
}

export const useConfigStore = defineStore('config', {
  state: (): ConfigState => ({
    baseURL: process.env.NODE_ENV === 'development' ? 'http://localhost:8181/api' : '/api',
    appConfig: {
      name: '智慧养老评估平台',
      version: '1.0.0',
      description: '专业的养老评估管理系统'
    },
    systemSettings: {
      theme: 'light',
      language: 'zh-CN',
      fontSize: 'normal',
      debug: process.env.NODE_ENV === 'development',
      timeout: 10000,
      pageSize: 20,
      upload: {
        maxSize: 10 * 1024 * 1024, // 10MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
        allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
      },
      cache: {
        expireTime: 30 * 60 * 1000, // 30分钟
        maxSize: 100
      }
    },
    businessConfig: {
      assessment: {
        autoSaveInterval: 30,
        timeout: 60,
        allowPause: true,
        allowSkip: false
      },
      elderly: {
        photo: {
          required: true,
          maxSize: 2 * 1024 * 1024, // 2MB
          allowedTypes: ['image/jpeg', 'image/png']
        },
        requiredFields: ['name', 'gender', 'birthDate', 'idCard', 'phone']
      },
      report: {
        formats: ['pdf', 'word', 'excel'],
        defaultFormat: 'pdf',
        includeCharts: true,
        includeSuggestions: true
      }
    },
    dictionaries: {
      gender: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ],
      assessmentStatus: [
        { label: '未开始', value: 'not_started', color: '#909399' },
        { label: '进行中', value: 'in_progress', color: '#E6A23C' },
        { label: '已完成', value: 'completed', color: '#67C23A' },
        { label: '已暂停', value: 'paused', color: '#F56C6C' }
      ],
      assessmentLevel: [
        { label: '轻度', value: 'mild', color: '#67C23A' },
        { label: '中度', value: 'moderate', color: '#E6A23C' },
        { label: '重度', value: 'severe', color: '#F56C6C' }
      ],
      userRoles: [
        { label: '管理员', value: 'admin' },
        { label: '评估师', value: 'assessor' },
        { label: '护理员', value: 'caregiver' },
        { label: '医生', value: 'doctor' }
      ]
    }
  }),

  // 持久化配置
  persist: {
    storage: {
      getItem: (key: string) => uni.getStorageSync(key),
      setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      removeItem: (key: string) => uni.removeStorageSync(key)
    },
    paths: ['systemSettings', 'businessConfig'] // 只持久化系统设置和业务配置
  },

  getters: {
    // 获取API基础URL
    apiBaseURL: (state): string => state.baseURL,

    // 获取应用信息
    appInfo: (state): AppConfig => state.appConfig,

    // 获取系统设置
    systemSettings: (state): SystemSettings => state.systemSettings,

    // 获取业务配置
    businessConfig: (state): BusinessConfig => state.businessConfig,

    // 获取字典数据
    dictionaries: (state): Dictionaries => state.dictionaries,

    // 获取指定字典
    getDictionary: (state) => (key: keyof Dictionaries): DictItem[] => {
      return state.dictionaries[key] || []
    },

    // 获取字典标签
    getDictLabel: (state) => (dictKey: keyof Dictionaries, value: string): string => {
      const dict = state.dictionaries[dictKey]
      if (!dict) return value
      const item = dict.find(item => item.value === value)
      return item ? item.label : value
    },

    // 获取字典颜色
    getDictColor: (state) => (dictKey: keyof Dictionaries, value: string): string => {
      const dict = state.dictionaries[dictKey]
      if (!dict) return ''
      const item = dict.find(item => item.value === value)
      return item ? item.color || '' : ''
    },

    // 是否为调试模式
    isDebug: (state): boolean => state.systemSettings.debug,

    // 获取当前主题
    currentTheme: (state): string => state.systemSettings.theme,

    // 获取当前语言
    currentLanguage: (state): string => state.systemSettings.language,

    // 获取当前字体大小
    currentFontSize: (state): string => state.systemSettings.fontSize,

    // 获取请求超时时间
    requestTimeout: (state): number => state.systemSettings.timeout,

    // 获取分页大小
    pageSize: (state): number => state.systemSettings.pageSize,

    // 获取上传配置
    uploadConfig: (state): UploadConfig => state.systemSettings.upload,

    // 获取缓存配置
    cacheConfig: (state): CacheConfig => state.systemSettings.cache
  },

  actions: {
    // 设置基础URL
    setBaseURL(url: string) {
      this.baseURL = url
    },

    // 初始化配置
    initConfig() {
      // 从本地存储恢复配置
      const savedSystemSettings = uni.getStorageSync('systemSettings')
      if (savedSystemSettings) {
        this.systemSettings = { ...this.systemSettings, ...savedSystemSettings }
      }

      const savedBusinessConfig = uni.getStorageSync('businessConfig')
      if (savedBusinessConfig) {
        this.businessConfig = { ...this.businessConfig, ...savedBusinessConfig }
      }
    },

    // 更新系统设置
    updateSystemSetting<K extends keyof SystemSettings>(key: K, value: SystemSettings[K]) {
      this.systemSettings[key] = value
      // 持久化存储
      uni.setStorageSync('systemSettings', this.systemSettings)
    },

    // 更新业务配置
    updateBusinessConfig<
      M extends keyof BusinessConfig,
      K extends keyof BusinessConfig[M]
    >(module: M, key: K, value: BusinessConfig[M][K]) {
      if (this.businessConfig[module]) {
        this.businessConfig[module][key] = value
        // 持久化存储
        uni.setStorageSync('businessConfig', this.businessConfig)
      }
    },

    // 更新字典数据
    updateDictionary<K extends keyof Dictionaries>(key: K, data: Dictionaries[K]) {
      this.dictionaries[key] = data
    },

    // 重置配置
    resetConfig() {
      uni.removeStorageSync('systemSettings')
      uni.removeStorageSync('businessConfig')
      this.initConfig()
    },

    // 切换主题
    toggleTheme() {
      const newTheme = this.systemSettings.theme === 'light' ? 'dark' : 'light'
      this.updateSystemSetting('theme', newTheme)
    },

    // 设置语言
    setLanguage(language: string) {
      this.updateSystemSetting('language', language)
    },

    // 设置字体大小
    setFontSize(fontSize: 'small' | 'normal' | 'large') {
      this.updateSystemSetting('fontSize', fontSize)
    }
  }
})