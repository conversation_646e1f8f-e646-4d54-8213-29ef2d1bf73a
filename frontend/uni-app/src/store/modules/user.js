import { login, logout, getUserInfo } from '../../api/auth'

const state = {
  token: uni.getStorageSync('token') || '',
  userInfo: uni.getStorageSync('userInfo') || null,
  permissions: [],
  roles: [],
  list: [], // 用户列表
  total: 0, // 用户总数
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0
  },
  loading: false
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    if (token) {
      uni.setStorageSync('token', token)
    } else {
      uni.removeStorageSync('token')
    }
  },

  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    if (userInfo) {
      uni.setStorageSync('userInfo', userInfo)
    } else {
      uni.removeStorageSync('userInfo')
    }
  },

  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions
  },

  SET_ROLES(state, roles) {
    state.roles = roles
  },

  SET_USER_LIST(state, list) {
    state.list = list
  },

  SET_USER_TOTAL(state, total) {
    state.total = total
    state.pagination.total = total
  },

  SET_USER_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },

  SET_USER_LOADING(state, loading) {
    state.loading = loading
  }
}

const actions = {
  // 设置Token (供登录页面直接使用)
  setToken({ commit }, token) {
    commit('SET_TOKEN', token)
  },

  // 设置用户信息 (供登录页面直接使用)
  setUserInfo({ commit }, userInfo) {
    commit('SET_USER_INFO', userInfo)
  },

  // 用户登录
  async login({ commit }, loginForm) {
    const response = await login(loginForm)
    const { token, userInfo } = response.data

    commit('SET_TOKEN', token)
    commit('SET_USER_INFO', userInfo)

    return response
  },

  // 获取用户信息
  async getUserInfo({ commit, state }) {
    try {
      if (!state.token) {
        throw new Error('Token不存在')
      }

      const response = await getUserInfo()
      const { userInfo, permissions, roles } = response.data

      commit('SET_USER_INFO', userInfo)
      commit('SET_PERMISSIONS', permissions)
      commit('SET_ROLES', roles)

      return response
    } catch (error) {
      // token无效，清除登录状态
      commit('SET_TOKEN', '')
      commit('SET_USER_INFO', null)
      commit('SET_PERMISSIONS', [])
      commit('SET_ROLES', [])
      throw error
    }
  },

  // 用户登出
  async logout({ commit }) {
    try {
      await logout()
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      // 清除本地状态
      commit('SET_TOKEN', '')
      commit('SET_USER_INFO', null)
      commit('SET_PERMISSIONS', [])
      commit('SET_ROLES', [])

      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/login/index'
      })
    }
  },

  // 验证token
  async validateToken({ dispatch, state }) {
    if (!state.token) {
      return false
    }

    try {
      await dispatch('getUserInfo')
      return true
    } catch (error) {
      return false
    }
  },

  // 重置用户状态
  resetUserState({ commit }) {
    commit('SET_TOKEN', '')
    commit('SET_USER_INFO', null)
    commit('SET_PERMISSIONS', [])
    commit('SET_ROLES', [])
  },

  // 获取用户列表
  async getUserList({ commit, state }, params = {}) {
    try {
      commit('SET_USER_LOADING', true)

      const requestParams = {
        page: state.pagination.page,
        pageSize: state.pagination.pageSize,
        ...params
      }

      // 模拟API调用，实际应该调用真实API
      const mockData = {
        list: [
          {
            id: 1,
            username: 'admin',
            nickname: '管理员',
            email: '<EMAIL>',
            phone: '138****1234',
            avatar: '',
            status: 'active',
            roles: ['admin'],
            createTime: '2024-01-15 10:30:00'
          },
          {
            id: 2,
            username: 'nurse01',
            nickname: '护理员001',
            email: '<EMAIL>',
            phone: '139****5678',
            avatar: '',
            status: 'active',
            roles: ['nurse'],
            createTime: '2024-01-16 14:20:00'
          }
        ],
        total: 2,
        page: requestParams.page,
        pageSize: requestParams.pageSize
      }

      commit('SET_USER_LIST', mockData.list)
      commit('SET_USER_TOTAL', mockData.total)
      commit('SET_USER_PAGINATION', {
        page: mockData.page,
        pageSize: mockData.pageSize
      })

      return mockData
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    } finally {
      commit('SET_USER_LOADING', false)
    }
  }
}

const getters = {
  // 是否已登录
  isLoggedIn: state => !!state.token,

  // 用户ID
  userId: state => state.userInfo?.id,

  // 用户名
  username: state => state.userInfo?.username,

  // 用户昵称
  nickname: state => state.userInfo?.nickname || state.userInfo?.username,

  // 用户头像
  avatar: state => state.userInfo?.avatar,

  // 用户角色
  userRoles: state => state.roles,

  // 用户权限
  userPermissions: state => state.permissions,

  // 检查是否有指定权限
  hasPermission: state => permission => {
    return state.permissions.includes(permission)
  },

  // 检查是否有指定角色
  hasRole: state => role => {
    return state.roles.includes(role)
  },

  // 检查是否有任一权限
  hasAnyPermission: state => permissions => {
    return permissions.some(permission => state.permissions.includes(permission))
  },

  // 检查是否有任一角色
  hasAnyRole: state => roles => {
    return roles.some(role => state.roles.includes(role))
  },

  // 用户列表
  userList: state => state.list,

  // 用户总数
  userTotal: state => state.total,

  // 分页信息
  userPagination: state => state.pagination,

  // 是否加载中
  userLoading: state => state.loading
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
