import { request } from '@/utils/request'

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/api/user/list',
    method: 'GET',
    params
  })
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: `/api/user/${id}`,
    method: 'GET'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/api/user',
    method: 'POST',
    data
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/api/user/${id}`,
    method: 'PUT',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/api/user/${id}`,
    method: 'DELETE'
  })
}

// 重置用户密码
export function resetUserPassword(id, data) {
  return request({
    url: `/api/user/${id}/password/reset`,
    method: 'POST',
    data
  })
}

// 更新用户状态
export function updateUserStatus(id, status) {
  return request({
    url: `/api/user/${id}/status`,
    method: 'PUT',
    data: { status }
  })
}

// 导出用户列表
export function exportUserList(params) {
  return request({
    url: '/api/user/export',
    method: 'GET',
    params,
    responseType: 'blob'
  })
}

// 获取用户统计
export function getUserStats(id) {
  return request({
    url: `/api/user/${id}/stats`,
    method: 'GET'
  })
}

// 获取用户日志
export function getUserLogs(id, params) {
  return request({
    url: `/api/user/${id}/logs`,
    method: 'GET',
    params
  })
}