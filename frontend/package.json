{"name": "assessment-platform", "version": "1.0.0", "description": "智能评估平台前端 Monorepo", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "npm run dev --workspaces", "dev:website": "npm run dev --workspace=packages/website", "dev:admin": "npm run dev --workspace=packages/admin", "dev:mobile": "npm run dev --workspace=packages/mobile", "build": "npm run build --workspaces", "build:website": "npm run build --workspace=packages/website", "build:admin": "npm run build --workspace=packages/admin", "build:mobile": "npm run build --workspace=packages/mobile", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "test": "npm run test --workspaces", "type-check": "npm run type-check --workspaces", "clean": "rm -rf node_modules packages/*/node_modules packages/*/dist", "install:all": "npm install && npm run install --workspaces"}, "devDependencies": {"@types/node": "^20.11.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.4", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.21.1", "prettier": "^3.2.5", "sass": "^1.71.0", "typescript": "^5.3.3", "vite": "^5.4.19", "vue": "^3.4.25", "vue-router": "^4.3.2", "vue-tsc": "^2.2.10"}, "dependencies": {"@vueuse/core": "^10.9.0", "axios": "^1.6.8", "dayjs": "^1.11.10", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^3.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}