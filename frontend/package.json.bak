{"name": "assessment-frontend", "version": "1.0.0", "description": "智慧养老评估平台前端应用", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "format": "prettier --write src/", "format:check": "prettier --check src/", "type-check": "vue-tsc --noEmit", "test:unit": "vitest", "test:coverage": "vitest --coverage", "quality:check": "npm run lint:check && npm run format:check && npm run type-check && npm run test:unit", "quality:fix": "npm run lint && npm run format"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.9.0", "axios": "^1.6.8", "dayjs": "^1.11.10", "echarts": "^5.5.0", "element-plus": "^2.6.1", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-echarts": "^6.6.0", "vue-router": "^4.3.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-vue": "^5.2.4", "@vitest/coverage-v8": "^1.6.1", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.5", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-security": "^2.1.1", "eslint-plugin-vue": "^9.22.0", "jsdom": "^24.0.0", "prettier": "^3.2.5", "sass": "^1.72.0", "terser": "^5.42.0", "typescript": "~5.4.0", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.19", "vitest": "^1.6.1", "vue-tsc": "^2.0.6"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "overrides": {"esbuild": "^0.25.5"}}