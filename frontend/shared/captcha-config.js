/**
 * 滑动验证码共享配置
 * 两个前端项目共用此配置文件
 */

export const CAPTCHA_CONFIG = {
  // 图片尺寸配置
  IMAGE_WIDTH: 310,
  IMAGE_HEIGHT: 155,
  BLOCK_WIDTH: 47,
  
  // 滑块配置
  SLIDER_HEIGHT: 40,
  SLIDER_BUTTON_SIZE: 40,
  
  // 文本配置
  TEXTS: {
    SLIDE_TEXT: '向右滑动完成验证',
    SUCCESS_TEXT: '验证成功',
    LOADING_TEXT: '验证中...',
    ERROR_TEXT: '验证失败，请重试',
    NETWORK_ERROR: '网络异常，请重试',
    VERIFIED_STATUS: '滑动验证码已通过',
    UNVERIFIED_STATUS: '请完成下方滑动验证码'
  },
  
  // API配置
  API: {
    GET_CAPTCHA: '/api/captcha/get',
    CHECK_CAPTCHA: '/api/captcha/check',
    CAPTCHA_TYPE: 'blockPuzzle'
  },
  
  // 验证配置
  VERIFICATION: {
    MAX_SLIDE_DISTANCE_RATIO: 0.87, // 最大滑动距离比例 (310-40)/310
    Y_COORDINATE: 5, // 固定Y坐标
    TIP_DISPLAY_DURATION: 3000 // 提示显示时长
  },
  
  // Note: Styling configs removed - using backend native appearance
  // Colors and styles are now controlled by backend Java code
};

/**
 * 计算最大滑动距离
 * @param {number} containerWidth 容器宽度
 * @param {number} sliderWidth 滑块宽度  
 * @returns {number} 最大滑动距离
 */
export function getMaxSlideDistance(containerWidth, sliderWidth) {
  return containerWidth - sliderWidth;
}

/**
 * 格式化验证数据
 * @param {string} token 验证码token
 * @param {number} x X坐标
 * @param {string} secretKey 密钥
 * @returns {object} 格式化的验证数据
 */
export function formatVerifyData(token, x, secretKey) {
  return {
    captchaType: CAPTCHA_CONFIG.API.CAPTCHA_TYPE,
    token,
    pointJson: JSON.stringify({
      x: Math.round(x),
      y: CAPTCHA_CONFIG.VERIFICATION.Y_COORDINATE
    }),
    verification: secretKey
  };
}

/**
 * 单位转换工具
 */
export const UNITS = {
  // px转rpx (uni-app用)
  pxToRpx: (px) => px * 2,
  // rpx转px
  rpxToPx: (rpx) => rpx / 2
};