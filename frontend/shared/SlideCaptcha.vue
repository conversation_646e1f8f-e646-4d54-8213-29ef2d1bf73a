<template>
  <div class="slide-captcha" :data-env="isUniApp ? 'uniapp' : 'admin'">
    <!-- 验证码状态提示 -->
    <div class="captcha-status" v-if="verified">
      <span class="status-icon">✅</span>
      <span class="status-text">{{ config?.TEXTS?.VERIFIED_STATUS || '滑动验证码已通过' }}</span>
    </div>
    <div class="captcha-status warning" v-else>
      <span class="status-icon">🔒</span>
      <span class="status-text">{{ config?.TEXTS?.UNVERIFIED_STATUS || '请完成下方滑动验证码' }}</span>
    </div>

    <!-- 内嵌滑动验证码 -->
    <div v-if="!verified && captchaData?.originalImageBase64" class="inline-captcha">
      <!-- 背景图片容器 -->
      <div
        class="captcha-image-panel"
        :style="getImagePanelStyle()"
      >
        <!-- 背景图片 -->
        <img
          :src="'data:image/png;base64,' + (captchaData?.originalImageBase64 || '')"
          class="captcha-bg-image"
          draggable="false"
        />

        <!-- 目标区域提示 (仅在未验证时显示) -->
        <div
          v-if="captchaData?.jigsawImageBase64 && !verifySuccess"
          class="captcha-target-hint"
          :style="getTargetHintStyle()"
        />

        <!-- 滑块图片 -->
        <div
          v-if="captchaData?.jigsawImageBase64"
          class="captcha-block"
          :style="getBlockStyle()"
        >
          <img
            :src="'data:image/png;base64,' + (captchaData?.jigsawImageBase64 || '')"
            class="captcha-block-image"
            draggable="false"
          />
        </div>

        <!-- 刷新按钮 -->
        <div
          class="captcha-refresh"
          @click="refreshCaptcha"
        >
          <span class="refresh-icon">🔄</span>
        </div>

        <!-- 提示信息 -->
        <transition name="tip-fade">
          <div v-if="tipMessage" class="captcha-tip" :class="verifySuccess ? 'tip-success' : 'tip-error'">
            {{ tipMessage }}
          </div>
        </transition>
      </div>

      <!-- 滑动条 -->
      <div class="captcha-slider" :style="getSliderStyle()">
        <!-- 滑动轨道 -->
        <div class="slider-track">
          <div class="slider-track-bg">
            <span class="slider-text">{{ sliderText }}</span>
          </div>

          <!-- 已滑动区域 -->
          <div
            class="slider-fill"
            :style="getSliderFillStyle()"
          >
            <span class="slider-text">{{ finishText }}</span>
          </div>
        </div>

        <!-- 滑块 -->
        <div
          class="slider-button"
          :style="getSliderButtonStyle()"
          @mousedown="handleMouseDown"
          @touchstart="handleTouchStart"
        >
          <span class="slider-icon" :class="verifySuccess ? 'icon-success' : 'icon-normal'">
            {{ verifySuccess ? '✓' : '➤' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'
import { CAPTCHA_CONFIG, formatVerifyData, UNITS } from './captcha-config.js'
import { addGlobalDragListeners, getEventCoordinates } from './eventOptimizer.js'

// 环境检测工具
const detectEnvironment = () => {
  // 检测是否为uni-app环境
  const isUniApp = typeof uni !== 'undefined'
  // 检测Vue版本 (Vue 3有getCurrentInstance)
  const isVue3 = typeof getCurrentInstance !== 'undefined'

  return {
    isUniApp,
    isVue3,
    platform: isUniApp ? 'uni-app' : 'admin'
  }
}

export default {
  name: 'SlideCaptcha',

  props: {
    // API接口函数
    getCaptcha: {
      type: Function,
      required: true
    },
    checkCaptcha: {
      type: Function,
      required: true
    }
  },

  emits: ['verified', 'error'],

  setup(props, { emit }) {
    // 检测当前环境
    const env = detectEnvironment()

    // 如果是Vue 3环境，使用Composition API
    if (env.isVue3) {
      // 环境信息
      const isUniApp = ref(env.isUniApp)
      const platform = ref(env.platform)
      const config = ref(CAPTCHA_CONFIG)

      // 验证码相关状态
      const captchaToken = ref('')
      const captchaVerification = ref('')
      const verified = ref(false)

      // 验证码数据
      const captchaData = reactive({
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      })

      // 计算属性：是否为admin环境
      const isAdmin = computed(() => !isUniApp.value)

      // 图片尺寸配置（根据环境自动调整）
      const imgWidth = computed(() =>
        isAdmin.value ? CAPTCHA_CONFIG.IMAGE_WIDTH : UNITS.pxToRpx(CAPTCHA_CONFIG.IMAGE_WIDTH)
      )
      const imgHeight = computed(() =>
        isAdmin.value ? CAPTCHA_CONFIG.IMAGE_HEIGHT : UNITS.pxToRpx(CAPTCHA_CONFIG.IMAGE_HEIGHT)
      )
      const blockWidth = computed(() =>
        isAdmin.value ? CAPTCHA_CONFIG.BLOCK_WIDTH : UNITS.pxToRpx(CAPTCHA_CONFIG.BLOCK_WIDTH)
      )

      // 滑块状态
      const sliderLeft = ref(0)
      const blockLeft = ref(0)
      const isMoving = ref(false)
      const startX = ref(0)
      const verifySuccess = ref(false)
      
      // 事件监听器清理函数
      const removeGlobalListeners = ref(null)

      // 文本提示
      const sliderText = ref(CAPTCHA_CONFIG.TEXTS.SLIDE_TEXT)
      const finishText = ref(CAPTCHA_CONFIG.TEXTS.SUCCESS_TEXT)
      const tipMessage = ref('')

      // 返回Vue 3的响应式数据和方法
      return {
        // 环境信息
        isUniApp,
        platform,
        config,
        isAdmin,

        // 验证码相关状态
        captchaToken,
        captchaVerification,
        verified,
        captchaData,

        // 图片尺寸
        imgWidth,
        imgHeight,
        blockWidth,

        // 滑块状态
        sliderLeft,
        blockLeft,
        isMoving,
        startX,
        verifySuccess,

        // 文本提示
        sliderText,
        finishText,
        tipMessage,
        
        // 事件清理
        removeGlobalListeners
      }
    }

    // Vue 2兼容性返回null，使用data()和methods
    return null
  },

  data() {
    // Vue 2环境的数据
    const env = detectEnvironment()
    const isAdmin = !env.isUniApp

    return {
      // 环境信息
      isUniApp: env.isUniApp,
      isVue3: env.isVue3,
      platform: env.platform,

      // 共享配置
      config: CAPTCHA_CONFIG,

      // 验证码相关状态
      captchaToken: '',
      captchaVerification: '',
      verified: false,

      // 验证码数据
      captchaData: {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      },

      // 图片尺寸配置（根据环境自动调整）
      imgWidth: isAdmin ? CAPTCHA_CONFIG.IMAGE_WIDTH : UNITS.pxToRpx(CAPTCHA_CONFIG.IMAGE_WIDTH),
      imgHeight: isAdmin ? CAPTCHA_CONFIG.IMAGE_HEIGHT : UNITS.pxToRpx(CAPTCHA_CONFIG.IMAGE_HEIGHT),
      blockWidth: isAdmin ? CAPTCHA_CONFIG.BLOCK_WIDTH : UNITS.pxToRpx(CAPTCHA_CONFIG.BLOCK_WIDTH),

      // 滑块状态
      sliderLeft: 0,
      blockLeft: 0,
      isMoving: false,
      startX: 0,
      verifySuccess: false,
      
      // 事件监听器清理函数
      removeGlobalListeners: null,

      // 文本提示
      sliderText: CAPTCHA_CONFIG.TEXTS.SLIDE_TEXT,
      finishText: CAPTCHA_CONFIG.TEXTS.SUCCESS_TEXT,
      tipMessage: ''
    }
  },

  computed: {
    // 计算属性：是否为admin环境
    isAdmin() {
      return !this.isUniApp
    }
  },

  mounted() {
    // 组件挂载后立即初始化验证码
    this.initCaptcha()
  },

  methods: {
    // 获取样式的通用方法
    getUnit(value) {
      return this.isAdmin ? value + 'px' : value + 'rpx'
    },

    getImagePanelStyle() {
      return {
        width: this.getUnit(this.imgWidth),
        height: this.getUnit(this.imgHeight)
      }
    },

    getTargetHintStyle() {
      return {
        left: this.getUnit(Math.round((this.imgWidth - this.blockWidth) * 0.75)),
        top: this.getUnit(this.isAdmin ? 20 : 40),
        width: this.getUnit(this.blockWidth),
        height: this.getUnit(this.imgHeight - (this.isAdmin ? 40 : 80))
      }
    },

    getBlockStyle() {
      return {
        left: this.getUnit(this.blockLeft),
        top: this.getUnit(0),
        width: this.getUnit(this.blockWidth),
        height: this.getUnit(this.imgHeight)
      }
    },

    getSliderStyle() {
      return {
        width: this.getUnit(this.imgWidth),
        height: this.getUnit(this.isAdmin ? 40 : 80)
      }
    },

    getSliderFillStyle() {
      return {
        width: this.getUnit(this.sliderLeft),
        transition: this.isMoving ? 'none' : 'all 0.3s ease'
      }
    },

    getSliderButtonStyle() {
      const buttonSize = this.isAdmin ? 40 : 80
      return {
        left: this.getUnit(this.sliderLeft),
        width: this.getUnit(buttonSize),
        height: this.getUnit(buttonSize),
        transition: this.isMoving ? 'none' : 'all 0.3s ease'
      }
    },

    // 初始化验证码 - 集成真实API
    async initCaptcha() {
      console.log('🚀 开始初始化验证码...', { platform: this.platform })

      try {
        const response = await this.getCaptcha()

        // 处理不同环境的响应格式
        let captchaData = null
        if (this.isAdmin) {
          // Admin环境：response.data.data
          captchaData = response?.data?.data || response?.data
        } else {
          // Uni-app环境：response.data 或 response
          captchaData = response?.data || response
        }

        if (captchaData && captchaData.originalImageBase64) {
          console.log('✅ 验证码数据加载成功:', {
            token: captchaData.token,
            hasOriginalImage: !!captchaData.originalImageBase64,
            hasJigsawImage: !!captchaData.jigsawImageBase64
          })

          Object.assign(this.captchaData, captchaData)
          this.resetSlider()
        } else {
          throw new Error('验证码数据格式错误')
        }
      } catch (error) {
        console.error('❌ 获取验证码失败:', error)
        this.showTip(this.config?.TEXTS?.NETWORK_ERROR || '验证码加载失败', false)
        this.$emit('error', error.message || '验证码加载失败')
      }
    },

    // 刷新验证码
    async refreshCaptcha() {
      this.resetCaptchaState()
      await this.initCaptcha()
    },

    // 重置验证码状态
    resetCaptchaState() {
      Object.assign(this.captchaData, {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      })
      this.resetSlider()
      this.tipMessage = ''
      this.verifySuccess = false
      this.verified = false
    },

    // 重置滑块状态
    resetSlider() {
      this.sliderLeft = 0
      this.blockLeft = 0
      this.isMoving = false
      this.verifySuccess = false
    },

    // 鼠标按下 (仅适用于admin环境)
    handleMouseDown(e) {
      if (this.verifySuccess || !this.isAdmin) return

      this.isMoving = true
      const coords = getEventCoordinates(e)
      this.startX = coords.x
      this.tipMessage = ''

      console.log('🖱️ 鼠标按下:', { platform: this.platform, startX: this.startX })

      // 清理之前的监听器
      if (this.removeGlobalListeners) {
        this.removeGlobalListeners()
      }

      // 使用优化的事件监听器
      this.removeGlobalListeners = addGlobalDragListeners({
        mousemove: this.handleMouseMove,
        mouseup: this.handleMouseUp
      })

      // 防止文本选择
      e.preventDefault()
    },

    // 鼠标移动
    handleMouseMove(e) {
      if (!this.isMoving || this.verifySuccess || !this.isAdmin) return

      const coords = getEventCoordinates(e)
      const deltaX = coords.x - this.startX
      this.updateSliderPosition(deltaX)
    },

    // 鼠标松开
    async handleMouseUp() {
      if (!this.isMoving || this.verifySuccess || !this.isAdmin) return

      this.isMoving = false
      console.log('🖱️ 鼠标松开:', { platform: this.platform })

      // 移除全局事件监听
      if (this.removeGlobalListeners) {
        this.removeGlobalListeners()
        this.removeGlobalListeners = null
      }

      // 验证滑动距离
      await this.verifyCaptchaPosition()
    },

    // 触摸开始 (兼容admin和uni-app环境)
    handleTouchStart(e) {
      if (this.verifySuccess) return

      this.isMoving = true
      this.tipMessage = ''

      // 获取触摸起始位置，兼容不同平台
      const coords = getEventCoordinates(e)
      this.startX = coords.x

      if (this.isAdmin) {
        // Admin环境：标准Web触摸事件
        // 清理之前的监听器
        if (this.removeGlobalListeners) {
          this.removeGlobalListeners()
        }

        // 使用优化的事件监听器
        this.removeGlobalListeners = addGlobalDragListeners({
          touchmove: this.handleTouchMove,
          touchend: this.handleTouchEnd
        })
        
        e.preventDefault()
      }

      console.log('👆 触摸开始:', { platform: this.platform, startX: this.startX })
    },

    // 触摸移动
    handleTouchMove(e) {
      if (!this.isMoving || this.verifySuccess) return

      const coords = getEventCoordinates(e)
      const deltaX = coords.x - this.startX
      
      if (this.isAdmin) {
        // Admin环境：阻止默认滚动行为
        e.preventDefault()
      }

      this.updateSliderPosition(deltaX)
    },

    // 触摸结束
    async handleTouchEnd() {
      if (!this.isMoving || this.verifySuccess) return

      this.isMoving = false
      console.log('👆 触摸结束:', { platform: this.platform })

      if (this.isAdmin) {
        // 移除全局事件监听
        if (this.removeGlobalListeners) {
          this.removeGlobalListeners()
          this.removeGlobalListeners = null
        }
      }

      // 验证滑动距离
      await this.verifyCaptchaPosition()
    },

    // 更新滑块位置
    updateSliderPosition(deltaX) {
      // 根据环境调整deltaX
      // Admin环境：直接使用px值
      // Uni-app环境：需要将px转换为rpx (1px = 2rpx)
      const adjustedDelta = this.isAdmin ? deltaX : deltaX * 2

      // 计算滑块位置，限制在有效范围内
      const buttonSize = this.isAdmin ?
        (this.config?.SLIDER_BUTTON_SIZE || 40) :
        UNITS.pxToRpx(this.config?.SLIDER_BUTTON_SIZE || 40)
      const maxSlideDistance = this.imgWidth - buttonSize
      const newSliderLeft = Math.max(0, Math.min(adjustedDelta, maxSlideDistance))

      this.sliderLeft = newSliderLeft
      this.blockLeft = newSliderLeft
    },

    // 验证验证码位置
    async verifyCaptchaPosition() {
      try {
        // 将滑动距离转换为像素坐标
        const pixelX = this.isAdmin ? Math.round(this.sliderLeft) : Math.round(this.sliderLeft / 2)

        const verifyData = formatVerifyData(
          this.captchaData.token,
          pixelX,
          this.captchaData.secretKey
        )

        console.log('🔍 验证数据:', {
          platform: this.platform,
          pixelX,
          verifyData
        })

        const response = await this.checkCaptcha(verifyData)

        // 处理不同环境的响应格式
        let responseData = null
        if (this.isAdmin) {
          // Admin环境：response.data.data 或 response.data
          responseData = response?.data?.data || response?.data || response
        } else {
          // Uni-app环境：response.data 或 response
          responseData = response?.data || response
        }

        console.log('📋 验证响应:', {
          platform: this.platform,
          responseData,
          success: responseData?.success,
          result: responseData?.data?.result || responseData?.result
        })

        // 检查验证结果
        const isSuccess = responseData?.success && (responseData?.data?.result || responseData?.result)

        if (isSuccess) {
          this.verifySuccess = true
          this.verified = true
          this.captchaToken = this.captchaData.token
          this.captchaVerification = this.captchaData.secretKey
          this.showTip(this.config?.TEXTS?.SUCCESS_TEXT || '验证成功', true)

          console.log('✅ 验证成功:', {
            token: this.captchaToken,
            verification: this.captchaVerification
          })

          // 触发验证成功事件
          this.$emit('verified', {
            token: this.captchaToken,
            verification: this.captchaVerification
          })
        } else {
          console.log('❌ 验证失败')
          this.showTip(this.config?.TEXTS?.ERROR_TEXT || '验证失败，请重试', false)
          this.resetSlider()
        }
      } catch (error) {
        console.error('❌ 验证过程出错:', error)
        this.showTip(this.config?.TEXTS?.ERROR_TEXT || '验证失败，请重试', false)
        this.resetSlider()
        this.$emit('error', error.message || '验证失败，请重试')
      }
    },

    // 显示提示信息
    showTip(message, isSuccess) {
      this.tipMessage = message
      this.verifySuccess = isSuccess

      console.log('💬 显示提示:', { message, isSuccess, platform: this.platform })

      // 根据配置时间清除提示
      setTimeout(() => {
        if (!isSuccess) {
          this.tipMessage = ''
        }
      }, this.config?.VERIFICATION?.TIP_DISPLAY_DURATION || 3000)
    },

    // 重置验证码 (外部调用)
    reset() {
      console.log('🔄 重置验证码:', { platform: this.platform })
      this.resetCaptchaState()
      this.initCaptcha()
    },

    // 获取验证状态 (外部调用)
    getVerificationData() {
      return {
        verified: this.verified,
        token: this.captchaToken,
        verification: this.captchaVerification
      }
    }
  }
}
</script>

<style scoped>
/* minimal basic layout styles only - let backend control appearance */
.slide-captcha {
  width: 100%;
}

.inline-captcha {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.captcha-image-panel {
  position: relative;
}

.captcha-bg-image {
  display: block;
  width: 100%;
  height: 100%;
}

.captcha-block {
  position: absolute;
  top: 0;
  z-index: 2;
}

.captcha-block-image {
  display: block;
  width: 100%;
  height: 100%;
}

.captcha-refresh {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 3;
  cursor: pointer;
}

.captcha-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  white-space: nowrap;
}

.captcha-slider {
  width: 100%;
  position: relative;
}

.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-text {
  user-select: none;
  pointer-events: none;
}

.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  user-select: none;
}

.slider-icon {
  transition: all 0.3s ease;
}
</style>