/**
 * 事件监听器优化工具
 * 解决 passive event listener 警告，提升页面响应性能
 */

/**
 * 检查浏览器是否支持 passive 事件监听器
 */
let supportsPassive = false
try {
  const opts = Object.defineProperty({}, 'passive', {
    get() {
      supportsPassive = true
      return false
    }
  })
  window.addEventListener('testPassive', null, opts)
  window.removeEventListener('testPassive', null, opts)
} catch (e) {
  // 不支持 passive
}

/**
 * 优化的事件监听器添加函数
 * @param {Element} element - 目标元素
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理函数
 * @param {Object|boolean} options - 事件选项
 * @returns {Function} 移除事件监听器的函数
 */
export function addOptimizedEventListener(element, event, handler, options = {}) {
  // 默认的被动事件类型
  const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchend']
  
  // 需要阻止默认行为的事件类型（不能使用passive）
  const nonPassiveEvents = ['touchmove']
  
  let finalOptions = options
  
  if (supportsPassive) {
    if (typeof options === 'boolean') {
      finalOptions = { capture: options }
    } else if (typeof options === 'object') {
      finalOptions = { ...options }
    } else {
      finalOptions = {}
    }
    
    // 根据事件类型自动设置passive选项
    if (nonPassiveEvents.includes(event)) {
      // 对于需要preventDefault的事件，明确设置passive为false
      finalOptions.passive = false
    } else if (passiveEvents.includes(event)) {
      // 对于滚动相关事件，默认使用passive
      if (finalOptions.passive === undefined) {
        finalOptions.passive = true
      }
    }
  }
  
  element.addEventListener(event, handler, finalOptions)
  
  // 返回移除监听器的函数
  return () => {
    element.removeEventListener(event, handler, finalOptions)
  }
}

/**
 * 为滑动验证码组件优化的事件监听器
 * @param {Element} element - 目标元素
 * @param {Object} handlers - 事件处理函数对象
 * @returns {Function} 移除所有事件监听器的函数
 */
export function addCaptchaEventListeners(element, handlers) {
  const removeListeners = []
  
  // 鼠标事件（桌面端）
  if (handlers.mousedown) {
    removeListeners.push(
      addOptimizedEventListener(element, 'mousedown', handlers.mousedown, { passive: false })
    )
  }
  
  // 触摸事件（移动端）
  if (handlers.touchstart) {
    removeListeners.push(
      addOptimizedEventListener(element, 'touchstart', handlers.touchstart, { passive: false })
    )
  }
  
  // 返回清理函数
  return () => {
    removeListeners.forEach(remove => remove())
  }
}

/**
 * 为文档添加全局事件监听器（用于拖拽操作）
 * @param {Object} handlers - 事件处理函数对象
 * @returns {Function} 移除所有事件监听器的函数
 */
export function addGlobalDragListeners(handlers) {
  const removeListeners = []
  
  // 鼠标移动和释放事件
  if (handlers.mousemove) {
    removeListeners.push(
      addOptimizedEventListener(document, 'mousemove', handlers.mousemove, { passive: true })
    )
  }
  
  if (handlers.mouseup) {
    removeListeners.push(
      addOptimizedEventListener(document, 'mouseup', handlers.mouseup, { passive: true })
    )
  }
  
  // 触摸移动和结束事件
  if (handlers.touchmove) {
    removeListeners.push(
      addOptimizedEventListener(document, 'touchmove', handlers.touchmove, { passive: false })
    )
  }
  
  if (handlers.touchend) {
    removeListeners.push(
      addOptimizedEventListener(document, 'touchend', handlers.touchend, { passive: true })
    )
  }
  
  // 返回清理函数
  return () => {
    removeListeners.forEach(remove => remove())
  }
}

/**
 * 节流函数 - 限制函数执行频率
 * @param {Function} func - 要节流的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 防抖函数 - 延迟执行函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func(...args)
  }
}

/**
 * 检测是否为触摸设备
 * @returns {boolean} 是否为触摸设备
 */
export function isTouchDevice() {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0
}

/**
 * 获取触摸或鼠标事件的坐标
 * @param {Event} event - 事件对象
 * @returns {Object} 坐标对象 {x, y}
 */
export function getEventCoordinates(event) {
  if (event.touches && event.touches[0]) {
    return {
      x: event.touches[0].clientX,
      y: event.touches[0].clientY
    }
  } else if (event.changedTouches && event.changedTouches[0]) {
    return {
      x: event.changedTouches[0].clientX,
      y: event.changedTouches[0].clientY
    }
  } else {
    return {
      x: event.clientX || 0,
      y: event.clientY || 0
    }
  }
}

export default {
  addOptimizedEventListener,
  addCaptchaEventListeners,
  addGlobalDragListeners,
  throttle,
  debounce,
  isTouchDevice,
  getEventCoordinates,
  supportsPassive
}