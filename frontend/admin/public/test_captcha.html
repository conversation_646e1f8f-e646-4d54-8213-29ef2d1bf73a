<\!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>验证码图片测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .test-section { border: 1px solid #ccc; padding: 15px; margin: 15px 0; }
        .image-container { margin: 10px 0; }
        .captcha-image { border: 1px solid #red; max-width: 100%; }
        .info { background: #f0f0f0; padding: 10px; font-family: monospace; font-size: 12px; word-break: break-all; }
        button { padding: 10px 20px; font-size: 16px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>验证码图片显示测试</h1>
        
        <button onclick="loadCaptcha()">重新加载验证码</button>
        
        <div class="test-section">
            <h3>背景图片 (310x155)</h3>
            <div class="image-container">
                <img id="bgImage" class="captcha-image" style="width: 310px; height: 155px;" />
            </div>
            <div class="info" id="bgInfo">等待加载...</div>
        </div>
        
        <div class="test-section">
            <h3>滑块图片 (47x155)</h3>
            <div class="image-container">
                <img id="jigsawImage" class="captcha-image" style="width: 47px; height: 155px;" />
            </div>
            <div class="info" id="jigsawInfo">等待加载...</div>
        </div>
        
        <div class="test-section">
            <h3>错误信息</h3>
            <div class="info" id="errorInfo" style="color: red;"></div>
        </div>
    </div>

    <script>
        async function loadCaptcha() {
            try {
                document.getElementById('errorInfo').textContent = '';
                document.getElementById('bgInfo').textContent = '正在加载...';
                document.getElementById('jigsawInfo').textContent = '正在加载...';
                
                const response = await fetch('http://localhost:8181/api/captcha/get');
                const result = await response.json();
                
                console.log('API响应:', result);
                
                if (result.success && result.data) {
                    const data = result.data;
                    
                    // 测试背景图片
                    if (data.originalImageBase64) {
                        const bgSrc = 'data:image/png;base64,' + data.originalImageBase64;
                        const bgImg = document.getElementById('bgImage');
                        
                        bgImg.onload = function() {
                            console.log('✅ 背景图片加载成功');
                            document.getElementById('bgInfo').innerHTML = 
                                \`✅ 加载成功<br>Base64长度: \${data.originalImageBase64.length}<br>Token: \${data.token}\`;
                        };
                        
                        bgImg.onerror = function() {
                            console.error('❌ 背景图片加载失败');
                            document.getElementById('errorInfo').textContent = '❌ 背景图片加载失败！Base64数据可能损坏。';
                        };
                        
                        bgImg.src = bgSrc;
                    } else {
                        document.getElementById('errorInfo').textContent = '❌ 未收到背景图片数据';
                    }
                    
                    // 测试滑块图片
                    if (data.jigsawImageBase64) {
                        const jigsawSrc = 'data:image/png;base64,' + data.jigsawImageBase64;
                        const jigsawImg = document.getElementById('jigsawImage');
                        
                        jigsawImg.onload = function() {
                            console.log('✅ 滑块图片加载成功');
                            document.getElementById('jigsawInfo').innerHTML = 
                                \`✅ 加载成功<br>Base64长度: \${data.jigsawImageBase64.length}\`;
                        };
                        
                        jigsawImg.onerror = function() {
                            console.error('❌ 滑块图片加载失败');
                            document.getElementById('errorInfo').textContent += '❌ 滑块图片加载失败！';
                        };
                        
                        jigsawImg.src = jigsawSrc;
                    } else {
                        document.getElementById('errorInfo').textContent += '❌ 未收到滑块图片数据';
                    }
                } else {
                    document.getElementById('errorInfo').textContent = '❌ 获取验证码失败: ' + (result.message || '未知错误');
                }
            } catch (error) {
                console.error('请求失败:', error);
                document.getElementById('errorInfo').textContent = '❌ 请求失败: ' + error.message;
            }
        }
        
        // 页面加载时自动加载验证码
        window.addEventListener('load', loadCaptcha);
    </script>
</body>
</html>
EOF < /dev/null