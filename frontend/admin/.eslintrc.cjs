module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    parser: '@typescript-eslint/parser'
  },
  extends: [
    'eslint:recommended',
    'plugin:vue/vue3-recommended',
    'plugin:@typescript-eslint/recommended',
    'prettier'
  ],
  plugins: [
    '@typescript-eslint'
  ],
  rules: {
    // 允许未使用的变量（TypeScript编译时会处理）
    '@typescript-eslint/no-unused-vars': 'off',
    'no-unused-vars': 'off',
    
    // 允许console语句
    'no-console': 'off',
    
    // 允许any类型
    '@typescript-eslint/no-explicit-any': 'off',
    
    // 允许空函数
    '@typescript-eslint/no-empty-function': 'off',
    
    // Vue相关规则
    'vue/multi-word-component-names': 'off',
    'vue/no-unused-components': 'warn',
    'vue/no-unused-vars': 'off',
    'vue/attributes-order': 'off',
    'vue/no-v-html': 'off',
    'vue/require-default-prop': 'off',
    'vue/first-attribute-linebreak': 'off',
    '@typescript-eslint/ban-types': 'off',
    '@typescript-eslint/ban-ts-comment': 'off',
    'no-case-declarations': 'off',
    'no-useless-escape': 'off'
  },
  overrides: [
    {
      files: ['*.vue'],
      parser: 'vue-eslint-parser',
      parserOptions: {
        parser: '@typescript-eslint/parser',
      },
    },
  ],
  globals: {
    // 添加Element Plus全局组件
    ElMessage: 'readonly',
    ElMessageBox: 'readonly',
    ElNotification: 'readonly',
    NodeJS: 'readonly' // 添加NodeJS全局变量
  }
};