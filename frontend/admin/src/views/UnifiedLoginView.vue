<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="max-w-md w-full bg-white rounded-2xl shadow-xl p-8">
      <div class="text-center mb-8">
        <div class="mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">智能评估平台</h2>
        <p class="text-gray-600 mt-2">安全登录系统</p>
      </div>

      <!-- 登录类型切换 -->
      <div class="mb-6">
        <div class="flex bg-gray-100 rounded-lg p-1">
          <button
            @click="loginType = 'INDIVIDUAL'"
            :class="[
              'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors',
              loginType === 'INDIVIDUAL' 
                ? 'bg-white text-blue-600 shadow-sm' 
                : 'text-gray-600 hover:text-gray-800'
            ]"
          >
            👤 个人用户
          </button>
          <button
            @click="loginType = 'INSTITUTIONAL'"
            :class="[
              'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors',
              loginType === 'INSTITUTIONAL' 
                ? 'bg-white text-blue-600 shadow-sm' 
                : 'text-gray-600 hover:text-gray-800'
            ]"
          >
            🏢 机构用户
          </button>
        </div>
      </div>

      <!-- 登录表单 -->
      <el-form :model="loginForm" :rules="currentRules" ref="loginFormRef" @submit.prevent="handleLogin" class="space-y-4">
        
        <!-- 个人用户登录 -->
        <template v-if="loginType === 'INDIVIDUAL'">
          <el-form-item prop="identifier">
            <label class="block text-sm font-medium text-gray-700 mb-2">手机号或邮箱</label>
            <el-input
              v-model="loginForm.identifier"
              placeholder="请输入手机号或邮箱，如：13800138000"
              size="large"
              prefix-icon="User"
              clearable
              @input="handleIdentifierInput"
            />
            <div v-if="identifierHint" class="text-xs mt-1" :class="{
              'text-green-600': identifierHint.startsWith('✓'),
              'text-blue-600': identifierHint.startsWith('💡'),
              'text-red-600': identifierHint.startsWith('⚠️')
            }">
              {{ identifierHint }}
            </div>
            <div v-else class="text-xs text-gray-500 mt-1">
              支持手机号：13800138000 或邮箱：<EMAIL>
            </div>
          </el-form-item>
        </template>

        <!-- 机构用户登录 -->
        <template v-else>
          <el-form-item prop="username">
            <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名，如：zhangsan.001"
              size="large"
              prefix-icon="User"
              clearable
            />
            <div class="text-xs text-gray-500 mt-1">
              格式：姓名拼音.工号 (如：zhangsan.001)
            </div>
          </el-form-item>
          
          <el-form-item prop="tenantCode">
            <label class="block text-sm font-medium text-gray-700 mb-2">机构代码</label>
            <el-input
              v-model="loginForm.tenantCode"
              placeholder="请输入机构代码，如：SH02YL01"
              size="large"
              prefix-icon="Building"
              clearable
              style="text-transform: uppercase"
              @input="handleTenantCodeInput"
            />
            <div v-if="tenantCodeHint" class="text-xs mt-1" :class="{
              'text-green-600': tenantCodeHint.startsWith('✓'),
              'text-blue-600': tenantCodeHint.startsWith('💡'),
              'text-red-600': tenantCodeHint.startsWith('⚠️')
            }">
              {{ tenantCodeHint }}
            </div>
            <div v-else class="text-xs text-gray-500 mt-1">
              <div>省级：SH01MZ (上海民政厅) · 市级：SH02MZ (浦东民政局)</div>
              <div>机构：SH02YL01 (浦东阳光养老院) · 系统：SYSTEM</div>
            </div>
          </el-form-item>
        </template>
        
        <el-form-item prop="password">
          <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-button
          type="primary"
          size="large"
          :loading="loading"
          @click="handleLogin"
          class="w-full"
        >
          登录
        </el-button>
      </el-form>

      <!-- 个人用户注册链接 -->
      <div v-if="loginType === 'INDIVIDUAL'" class="mt-4 text-center">
        <span class="text-sm text-gray-600">还没有账号？</span>
        <el-button link @click="showRegisterDialog = true" class="text-sm">
          立即注册
        </el-button>
      </div>

      <!-- 帮助链接 -->
      <div class="mt-6 text-center">
        <el-button link @click="showHelpDialog = true" class="text-sm text-gray-500">
          登录遇到问题？
        </el-button>
      </div>
    </div>

    <!-- 个人用户注册对话框 -->
    <el-dialog v-model="showRegisterDialog" title="个人用户注册" width="400px">
      <el-form :model="registerForm" :rules="registerRules" ref="registerFormRef" class="space-y-4">
        <el-form-item prop="email">
          <label class="block text-sm font-medium text-gray-700 mb-2">邮箱地址</label>
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱地址"
            size="large"
            prefix-icon="Message"
            @blur="checkEmailAvailability"
          />
          <div v-if="emailCheckResult" class="text-xs mt-1" :class="{
            'text-green-600': emailCheckResult.available,
            'text-red-600': !emailCheckResult.available
          }">
            {{ emailCheckResult.message }}
          </div>
        </el-form-item>
        
        <el-form-item prop="phone">
          <label class="block text-sm font-medium text-gray-700 mb-2">手机号（可选）</label>
          <el-input
            v-model="registerForm.phone"
            placeholder="手机号（可选，未来功能）"
            size="large"
            prefix-icon="Phone"
            maxlength="11"
            disabled
          />
          <div class="text-xs text-gray-500 mt-1">
            开发阶段暂不支持手机号注册
          </div>
        </el-form-item>
        
        <el-form-item prop="name">
          <label class="block text-sm font-medium text-gray-700 mb-2">姓名</label>
          <el-input
            v-model="registerForm.name"
            placeholder="请输入真实姓名"
            size="large"
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码（至少6位）"
            size="large"
            prefix-icon="Lock"
            show-password
          />
        </el-form-item>
        
        <el-form-item prop="confirmPassword">
          <label class="block text-sm font-medium text-gray-700 mb-2">确认密码</label>
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="flex space-x-2">
          <el-button @click="showRegisterDialog = false">取消</el-button>
          <el-button type="primary" :loading="registerLoading" @click="handleRegister">
            注册
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 帮助对话框 -->
    <el-dialog v-model="showHelpDialog" title="登录帮助" width="500px">
      <div class="space-y-4 text-sm">
        <div>
          <h4 class="font-medium text-gray-900 mb-2">个人用户登录：</h4>
          <ul class="list-disc list-inside text-gray-600 space-y-1">
            <li>手机号：13800138000（需要先注册）</li>
            <li>邮箱：<EMAIL></li>
            <li>支持找回密码和密码重置</li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 mb-2">机构用户登录：</h4>
          <ul class="list-disc list-inside text-gray-600 space-y-1">
            <li>用户名格式：姓名拼音.工号（如：zhangsan.001）</li>
            <li>机构代码：由机构管理员提供（如：SH02YL01）</li>
            <li>账号由机构管理员创建和管理</li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 mb-2">常见问题：</h4>
          <ul class="list-disc list-inside text-gray-600 space-y-1">
            <li>个人用户忘记密码：使用手机号/邮箱找回</li>
            <li>机构用户忘记密码：联系机构管理员重置</li>
            <li>注册遇到问题：检查手机号是否已被使用</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showHelpDialog = false">知道了</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import request from '@/utils/request';

const router = useRouter();
const loading = ref(false);
const registerLoading = ref(false);
const loginFormRef = ref();
const registerFormRef = ref();
const showHelpDialog = ref(false);
const showRegisterDialog = ref(false);
// const countdown = ref(0); // 暂时未使用，保留用于验证码倒计时功能

// 登录类型
const loginType = ref<'INDIVIDUAL' | 'INSTITUTIONAL'>('INDIVIDUAL');

// 提示信息
const identifierHint = ref('');
const tenantCodeHint = ref('');

// 登录表单
const loginForm = reactive({
  identifier: '',  // 个人用户：手机号或邮箱
  username: '',    // 机构用户：用户名
  tenantCode: '',  // 机构用户：机构代码
  password: '',
});

// 注册表单
const registerForm = reactive({
  email: '',
  phone: '',
  verificationCode: '',
  name: '',
  password: '',
  confirmPassword: '',
});

// 邮箱检查结果
interface EmailCheckResult {
  available: boolean;
  message: string;
}
const emailCheckResult = ref<EmailCheckResult | null>(null);

// 验证规则
const individualRules = {
  identifier: [
    { required: true, message: '请输入手机号或邮箱', trigger: 'blur' },
    { pattern: /^(1[3-9]\d{9}|[^\s@]+@[^\s@]+\.[^\s@]+)$/, message: '请输入正确的手机号或邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
};

const institutionalRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { pattern: /^[a-zA-Z]+\.[a-zA-Z0-9]+$/, message: '用户名格式：姓名拼音.工号（如：zhangsan.001）', trigger: 'blur' }
  ],
  tenantCode: [
    { required: true, message: '请输入机构代码', trigger: 'blur' },
    { min: 3, message: '机构代码至少3位', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
};

const registerRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, message: '姓名至少2个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { 
      validator: (_rule: any, value: string, callback: Function) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ],
};

// 当前使用的验证规则
const currentRules = computed(() => {
  return loginType.value === 'INDIVIDUAL' ? individualRules : institutionalRules;
});

// 处理个人用户标识符输入
const handleIdentifierInput = (value: string) => {
  if (!value) {
    identifierHint.value = '';
    return;
  }
  
  // 手机号验证
  if (/^1[3-9]\d{9}$/.test(value)) {
    identifierHint.value = '✓ 手机号格式正确';
  } else if (/^1[3-9]\d{0,8}$/.test(value)) {
    identifierHint.value = '💡 请继续输入手机号';
  }
  // 邮箱验证
  else if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    identifierHint.value = '✓ 邮箱格式正确';
  } else if (value.includes('@')) {
    identifierHint.value = '💡 请输入完整的邮箱地址';
  } else {
    identifierHint.value = '⚠️ 请输入正确的手机号或邮箱格式';
  }
};

// 处理机构代码输入（复用之前的逻辑）
const handleTenantCodeInput = (value: string) => {
  // 这里可以复用之前实现的机构代码验证逻辑
  const upperValue = value.toUpperCase();
  loginForm.tenantCode = upperValue;
  
  // 简化的提示逻辑
  if (upperValue.length >= 3) {
    tenantCodeHint.value = `💡 机构代码: ${upperValue}`;
  } else {
    tenantCodeHint.value = '';
  }
};

// 检查邮箱可用性
const checkEmailAvailability = async () => {
  if (!registerForm.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(registerForm.email)) {
    emailCheckResult.value = null;
    return;
  }
  
  try {
    const response = await request({
      url: '/api/auth/check-email',
      method: 'get',
      params: { email: registerForm.email }
    });
    
    const result = response.data || response;
    emailCheckResult.value = {
      available: result.available,
      message: result.message
    };
    
  } catch (error: any) {
    emailCheckResult.value = {
      available: false,
      message: '检查失败，请稍后重试'
    };
  }
};

// 发送验证码（保留但简化）
// const sendVerificationCode = async () => {
//   ElMessage.info('开发阶段暂不支持短信验证码');
// };

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return;
  
  try {
    const valid = await registerFormRef.value.validate();
    if (!valid) return;
    
    // 检查邮箱可用性
    if (emailCheckResult.value && !emailCheckResult.value.available) {
      ElMessage.error('邮箱已被注册，请更换邮箱');
      return;
    }
    
    registerLoading.value = true;
    
    const response = await request({
      url: '/api/auth/register',
      method: 'post',
      data: {
        email: registerForm.email,
        phone: registerForm.phone || null,
        name: registerForm.name,
        password: registerForm.password,
        serviceType: 'FREE'
      }
    });
    
    const result = response.data || response;
    ElMessage.success(result.message || '注册成功！请登录');
    showRegisterDialog.value = false;
    
    // 自动填充登录信息
    loginForm.identifier = registerForm.email;
    
    // 清空注册表单
    Object.keys(registerForm).forEach(key => {
      (registerForm as any)[key] = '';
    });
    emailCheckResult.value = null;
    
  } catch (error: any) {
    ElMessage.error(error.response?.data?.message || '注册失败');
  } finally {
    registerLoading.value = false;
  }
};

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    const valid = await loginFormRef.value.validate();
    if (!valid) return;

    loading.value = true;

    // 构建请求数据
    const requestData = loginType.value === 'INDIVIDUAL' 
      ? {
          identifier: loginForm.identifier,
          password: loginForm.password,
          loginType: 'INDIVIDUAL'
        }
      : {
          username: loginForm.username,
          tenantCode: loginForm.tenantCode,
          password: loginForm.password,
          loginType: 'INSTITUTIONAL'
        };

    const response = await request({
      url: '/api/auth/login',
      method: 'post',
      data: requestData,
    });

    const userData = response.data || response;
    
    if (userData.accessToken) {
      localStorage.setItem('token', userData.accessToken);
      localStorage.setItem('refreshToken', userData.refreshToken || '');
      localStorage.setItem('user', JSON.stringify(userData));

      ElMessage.success(`登录成功！欢迎 ${userData.displayName || userData.username}`);
      router.push('/');
    } else {
      ElMessage.error(userData.message || '登录失败');
    }
  } catch (error: any) {
    console.error('Login error:', error);
    ElMessage.error(error.response?.data?.message || '登录失败，请检查账号密码');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* 保持与之前相同的样式 */
</style>