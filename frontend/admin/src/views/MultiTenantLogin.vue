<template>
  <div class="login-container">
    <div class="login-form">
      <div class="login-header">
        <h1>智能评估平台</h1>
        <p>多租户管理系统</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form-content"
        label-width="0"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="tenantCode">
          <el-input
            v-model="loginForm.tenantCode"
            placeholder="租户代码"
            size="large"
            prefix-icon="Office"
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            size="large"
            prefix-icon="User"
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            size="large"
            prefix-icon="Lock"
            show-password
            :disabled="loading"
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            :loading="loading"
            size="large"
            type="primary"
            style="width: 100%"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="demo-accounts" v-if="showDemoAccounts">
        <el-divider>演示账号</el-divider>
        <div class="demo-account-list">
          <div 
            v-for="account in demoAccounts" 
            :key="account.tenantCode + account.username"
            class="demo-account"
            @click="fillDemoAccount(account)"
          >
            <div class="demo-account-info">
              <span class="tenant-code">{{ account.tenantCode }}</span>
              <span class="username">{{ account.username }}</span>
              <span class="role">{{ account.role }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="login-footer">
        <el-button
          type="text"
          size="small"
          @click="toggleDemoAccounts"
        >
          {{ showDemoAccounts ? '隐藏' : '显示' }}演示账号
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElForm } from 'element-plus';
import { multiTenantAuthApi } from '@/api/multiTenantAdapter';
import tenantContext from '@/utils/tenantContext';

const router = useRouter();
const loginFormRef = ref<InstanceType<typeof ElForm>>();

// 表单数据
const loginForm = reactive({
  tenantCode: '',
  username: '',
  password: ''
});

// 验证规则
const loginRules = {
  tenantCode: [
    { required: true, message: '请输入租户代码', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
};

// 状态控制
const loading = ref(false);
const showDemoAccounts = ref(false);

// 演示账号
const demoAccounts = [
  {
    tenantCode: 'DEMO_HOSPITAL',
    username: 'admin',
    password: 'password123',
    role: '管理员'
  },
  {
    tenantCode: 'DEMO_HOSPITAL',
    username: 'assessor01',
    password: 'password123',
    role: '评估员'
  },
  {
    tenantCode: 'DEMO_NURSING',
    username: 'admin',
    password: 'password123',
    role: '管理员'
  },
  {
    tenantCode: 'DEMO_NURSING',
    username: 'reviewer01',
    password: 'password123',
    role: '审核员'
  }
];

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    // 验证表单
    await loginFormRef.value.validate();
    
    loading.value = true;

    // 调用多租户登录API
    const response = await multiTenantAuthApi.login({
      tenantCode: loginForm.tenantCode,
      username: loginForm.username,
      password: loginForm.password
    });

    const { data } = response;
    
    if (data.success) {
      const { token, user, tenant, membership } = data.data;

      // 设置租户上下文
      tenantContext.setToken(token);
      tenantContext.setTenant(tenant);
      tenantContext.setUser({
        ...user,
        role: membership.role as 'ADMIN' | 'ASSESSOR' | 'REVIEWER' | 'SUPERVISOR' | 'VIEWER',
        permissions: membership.permissions
      });

      ElMessage.success(`欢迎您，${user.fullName || user.username}！`);
      
      // 跳转到主页
      router.push('/');
    } else {
      ElMessage.error(data.message || '登录失败');
    }
  } catch (error: any) {
    console.error('Login error:', error);
    const errorMessage = error.response?.data?.message || error.message || '登录失败';
    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

// 填充演示账号
const fillDemoAccount = (account: any) => {
  loginForm.tenantCode = account.tenantCode;
  loginForm.username = account.username;
  loginForm.password = account.password;
};

// 切换演示账号显示
const toggleDemoAccounts = () => {
  showDemoAccounts.value = !showDemoAccounts.value;
};

// 检查是否已登录
onMounted(() => {
  if (tenantContext.isAuthenticated()) {
    router.push('/');
  }
});
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #303133;
  font-size: 28px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.login-header p {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

.login-form-content {
  margin-bottom: 20px;
}

.demo-accounts {
  margin-top: 20px;
}

.demo-account-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.demo-account {
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.demo-account:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.demo-account-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.tenant-code {
  color: #409eff;
  font-weight: 500;
}

.username {
  color: #303133;
}

.role {
  color: #909399;
  font-size: 11px;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-form {
    width: 90%;
    padding: 20px;
  }
  
  .login-header h1 {
    font-size: 24px;
  }
}
</style>