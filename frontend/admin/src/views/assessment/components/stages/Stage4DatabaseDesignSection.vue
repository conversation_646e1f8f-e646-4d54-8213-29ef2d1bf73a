<template>
  <el-card class="stage-card stage4-database-design" :class="{ disabled: !enabled }">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ completed: isCompleted, active: enabled }">
          <el-icon v-if="isCompleted"><Check /></el-icon>
          <span v-else>4</span>
        </div>
        <div class="stage-title">
          <h3>数据库结构设计</h3>
          <span class="stage-description">设计和编辑数据库表结构</span>
        </div>
        <div class="stage-actions">
          <el-tag v-if="isCompleted" type="success">设计完成</el-tag>
          <el-tag v-else-if="!enabled" type="info">等待分析</el-tag>
          <el-tag v-else type="warning">设计中</el-tag>
        </div>
      </div>
    </template>

    <div class="design-content" :class="{ disabled: !enabled }">
      <!-- 数据库结构编辑器 -->
      <div v-if="enabled && databaseStructure" class="structure-editor">
        <!-- 表基本信息 -->
        <div class="table-info-section">
          <div class="section-header">
            <div class="flex items-center">
              <CircleStackIcon class="h-5 w-5 mr-2 text-blue-600" />
              <span class="font-semibold text-gray-700">表基本信息</span>
            </div>
            <div class="info-actions">
              <el-button size="small" @click="loadAIRecommendation" type="info">
                <CpuChipIcon class="h-4 w-4 mr-1" />
                应用AI建议
              </el-button>
            </div>
          </div>
          
          <el-row :gutter="16" class="table-form">
            <el-col :span="8">
              <el-input
                v-model="databaseStructure.tableName"
                @input="onStructureChange"
                placeholder="表名"
              >
                <template #prepend>表名</template>
              </el-input>
            </el-col>
            <el-col :span="16">
              <el-input
                v-model="databaseStructure.tableComment"
                @input="onStructureChange"
                placeholder="表说明"
              >
                <template #prepend>说明</template>
              </el-input>
            </el-col>
          </el-row>
        </div>

        <!-- 字段列表 -->
        <div class="fields-section">
          <div class="section-header">
            <div class="flex items-center">
              <ClipboardDocumentListIcon class="h-5 w-5 mr-2 text-green-600" />
              <span class="font-semibold text-gray-700">
                字段列表 ({{ databaseStructure.fields.length }}个)
              </span>
            </div>
            <div class="fields-actions">
              <el-button size="small" @click="addField" type="primary">
                <PlusIcon class="h-4 w-4 mr-1" />
                添加字段
              </el-button>
              <el-button size="small" @click="addCommonFields" type="info">
                <Squares2X2Icon class="h-4 w-4 mr-1" />
                添加通用字段
              </el-button>
            </div>
          </div>

          <div class="fields-table-wrapper">
            <el-table
              :data="databaseStructure.fields"
              border
              stripe
              size="small"
              class="fields-table"
            >
              <el-table-column prop="name" label="字段名" width="150">
                <template #default="{ row, $index }">
                  <el-input
                    v-model="row.name"
                    @input="() => onFieldChange($index)"
                    size="small"
                    placeholder="字段名"
                  />
                </template>
              </el-table-column>

              <el-table-column prop="type" label="类型" width="120">
                <template #default="{ row, $index }">
                  <el-select
                    v-model="row.type"
                    @change="() => onFieldChange($index)"
                    size="small"
                    placeholder="类型"
                  >
                    <el-option
                      v-for="type in fieldTypes"
                      :key="type.value"
                      :label="type.label"
                      :value="type.value"
                    />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column prop="length" label="长度" width="80">
                <template #default="{ row, $index }">
                  <el-input
                    v-model="row.length"
                    @input="() => onFieldChange($index)"
                    size="small"
                    placeholder="长度"
                    v-if="needsLength(row.type)"
                  />
                </template>
              </el-table-column>

              <el-table-column prop="nullable" label="可空" width="60">
                <template #default="{ row, $index }">
                  <el-checkbox
                    v-model="row.nullable"
                    @change="() => onFieldChange($index)"
                  />
                </template>
              </el-table-column>

              <el-table-column prop="primaryKey" label="主键" width="60">
                <template #default="{ row, $index }">
                  <el-checkbox
                    v-model="row.primaryKey"
                    @change="() => onFieldChange($index)"
                  />
                </template>
              </el-table-column>

              <el-table-column prop="defaultValue" label="默认值" width="100">
                <template #default="{ row, $index }">
                  <el-input
                    v-model="row.defaultValue"
                    @input="() => onFieldChange($index)"
                    size="small"
                    placeholder="默认值"
                  />
                </template>
              </el-table-column>

              <el-table-column prop="comment" label="说明" min-width="150">
                <template #default="{ row, $index }">
                  <el-input
                    v-model="row.comment"
                    @input="() => onFieldChange($index)"
                    size="small"
                    placeholder="字段说明"
                  />
                </template>
              </el-table-column>

              <el-table-column label="操作" width="100">
                <template #default="{ row, $index }">
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeField($index)"
                    :disabled="databaseStructure.fields.length <= 1"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 索引设计 -->
        <div class="indexes-section">
          <div class="section-header">
            <div class="flex items-center">
              <MagnifyingGlassIcon class="h-5 w-5 mr-2 text-purple-600" />
              <span class="font-semibold text-gray-700">索引设计</span>
            </div>
            <div class="indexes-actions">
              <el-button size="small" @click="addIndex" type="primary">
                <PlusIcon class="h-4 w-4 mr-1" />
                添加索引
              </el-button>
              <el-button size="small" @click="autoCreateIndexes" type="info">
                <BoltIcon class="h-4 w-4 mr-1" />
                智能索引
              </el-button>
            </div>
          </div>

          <div v-if="databaseStructure.indexes && databaseStructure.indexes.length > 0" class="indexes-list">
            <div
              v-for="(index, idx) in databaseStructure.indexes"
              :key="idx"
              class="index-item"
            >
              <el-input
                v-model="index.name"
                placeholder="索引名称"
                size="small"
                style="width: 200px; margin-right: 12px;"
              />
              <el-select
                v-model="index.fields"
                multiple
                placeholder="选择字段"
                size="small"
                style="width: 300px; margin-right: 12px;"
              >
                <el-option
                  v-for="field in databaseStructure.fields"
                  :key="field.name"
                  :label="field.name"
                  :value="field.name"
                />
              </el-select>
              <el-select
                v-model="index.type"
                placeholder="索引类型"
                size="small"
                style="width: 120px; margin-right: 12px;"
              >
                <el-option label="普通索引" value="INDEX" />
                <el-option label="唯一索引" value="UNIQUE" />
              </el-select>
              <el-button
                size="small"
                type="danger"
                @click="removeIndex(idx)"
              >
                删除
              </el-button>
            </div>
          </div>
          <div v-else class="no-indexes">
            <span class="text-gray-500">暂无索引</span>
          </div>
        </div>

        <!-- 结构预览和操作 -->
        <div class="structure-preview">
          <div class="section-header">
            <div class="flex items-center">
              <DocumentTextIcon class="h-5 w-5 mr-2 text-orange-600" />
              <span class="font-semibold text-gray-700">结构预览</span>
            </div>
            <div class="preview-actions">
              <el-button size="small" @click="generateSQL" type="primary">
                <ArrowPathIcon class="h-4 w-4 mr-1" />
                生成SQL
              </el-button>
              <el-button
                size="small"
                @click="validateStructure"
                type="info"
              >
                <CheckCircleIcon class="h-4 w-4 mr-1" />
                验证结构
              </el-button>
            </div>
          </div>

          <div class="preview-content">
            <div class="structure-summary">
              <div class="summary-item">
                <span class="summary-label">表名:</span>
                <span class="summary-value">{{ databaseStructure.tableName || '未设置' }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">字段数:</span>
                <span class="summary-value">{{ databaseStructure.fields.length }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">主键:</span>
                <span class="summary-value">
                  {{ getPrimaryKeyFields() || '未设置' }}
                </span>
              </div>
              <div class="summary-item">
                <span class="summary-label">索引数:</span>
                <span class="summary-value">{{ databaseStructure.indexes?.length || 0 }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 确认按钮 -->
        <div class="confirm-section">
          <el-button
            type="success"
            size="large"
            @click="confirmStructure"
            :disabled="!isStructureValid"
            class="confirm-button"
          >
            <CheckCircleIcon class="h-5 w-5 mr-2" />
            确认数据库结构设计
          </el-button>
        </div>
      </div>

      <!-- 禁用状态 -->
      <div v-else-if="!enabled" class="disabled-state">
        <el-empty
          :image-size="120"
          description="请先完成AI智能分析"
        >
          <template #image>
            <CircleStackIcon class="h-24 w-24 text-gray-400" />
          </template>
        </el-empty>
      </div>

      <!-- 等待AI分析结果 -->
      <div v-else class="waiting-state">
        <el-empty
          :image-size="120"
          description="等待AI分析结果"
        >
          <template #image>
            <CpuChipIcon class="h-24 w-24 text-gray-400" />
          </template>
        </el-empty>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Check } from '@element-plus/icons-vue';
import {
  CircleStackIcon,
  ClipboardDocumentListIcon,
  PlusIcon,
  Squares2X2Icon,
  MagnifyingGlassIcon,
  BoltIcon,
  DocumentTextIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  CpuChipIcon,
} from '@heroicons/vue/24/outline';

// Types
interface DatabaseField {
  name: string;
  type: string;
  length?: string;
  nullable: boolean;
  primaryKey: boolean;
  defaultValue?: string;
  comment: string;
}

interface DatabaseIndex {
  name: string;
  fields: string[];
  type: 'INDEX' | 'UNIQUE';
}

interface DatabaseStructure {
  tableName: string;
  tableComment: string;
  fields: DatabaseField[];
  indexes?: DatabaseIndex[];
}

interface AnalysisResult {
  structure: any;
  recommendations: string[];
  confidence: number;
  timestamp: Date;
}

// Props
interface Props {
  enabled?: boolean;
  analysisResult?: AnalysisResult | null;
}

const props = withDefaults(defineProps<Props>(), {
  enabled: false,
  analysisResult: null,
});

// Emits
const emit = defineEmits<{
  'stage-complete': [result: DatabaseStructure];
}>();

// Refs
const isCompleted = ref(false);

// Database structure
const databaseStructure = reactive<DatabaseStructure>({
  tableName: '',
  tableComment: '',
  fields: [
    {
      name: 'id',
      type: 'BIGINT',
      nullable: false,
      primaryKey: true,
      comment: '主键ID',
    },
  ],
  indexes: [],
});

// Field types configuration
const fieldTypes = [
  { value: 'VARCHAR', label: 'VARCHAR' },
  { value: 'TEXT', label: 'TEXT' },
  { value: 'INT', label: 'INT' },
  { value: 'BIGINT', label: 'BIGINT' },
  { value: 'DECIMAL', label: 'DECIMAL' },
  { value: 'DATETIME', label: 'DATETIME' },
  { value: 'DATE', label: 'DATE' },
  { value: 'TIME', label: 'TIME' },
  { value: 'BOOLEAN', label: 'BOOLEAN' },
  { value: 'JSON', label: 'JSON' },
];

// Computed
const isStructureValid = computed(() => {
  return (
    databaseStructure.tableName &&
    databaseStructure.fields.length > 0 &&
    databaseStructure.fields.some(field => field.primaryKey)
  );
});

// Watch for analysis result changes
watch(() => props.analysisResult, (newResult) => {
  if (newResult?.structure) {
    loadStructureFromAnalysis(newResult.structure);
  }
}, { immediate: true });

// Methods
const loadStructureFromAnalysis = (structure: any) => {
  if (structure.tableName) {
    databaseStructure.tableName = structure.tableName;
  }
  if (structure.tableComment) {
    databaseStructure.tableComment = structure.tableComment;
  }
  if (structure.fields && Array.isArray(structure.fields)) {
    databaseStructure.fields = structure.fields.map((field: any) => ({
      name: field.name || '',
      type: field.type || 'VARCHAR',
      length: field.length || '',
      nullable: field.nullable !== false,
      primaryKey: field.primaryKey === true,
      defaultValue: field.defaultValue || '',
      comment: field.comment || '',
    }));
  }
  
  ElMessage.success('已加载AI分析的结构建议');
};

const loadAIRecommendation = () => {
  if (!props.analysisResult?.structure) {
    ElMessage.warning('没有可用的AI分析结果');
    return;
  }
  
  ElMessageBox.confirm(
    '这将覆盖当前的结构设计，是否继续？',
    '加载AI建议',
    {
      confirmButtonText: '确认加载',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    loadStructureFromAnalysis(props.analysisResult!.structure);
  }).catch(() => {});
};

const addField = () => {
  const newField: DatabaseField = {
    name: '',
    type: 'VARCHAR',
    length: '255',
    nullable: true,
    primaryKey: false,
    defaultValue: '',
    comment: '',
  };
  
  databaseStructure.fields.push(newField);
  onStructureChange();
};

const addCommonFields = () => {
  const commonFields: DatabaseField[] = [
    {
      name: 'created_at',
      type: 'DATETIME',
      nullable: false,
      primaryKey: false,
      defaultValue: 'CURRENT_TIMESTAMP',
      comment: '创建时间',
    },
    {
      name: 'updated_at',
      type: 'DATETIME',
      nullable: false,
      primaryKey: false,
      defaultValue: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
      comment: '更新时间',
    },
    {
      name: 'created_by',
      type: 'BIGINT',
      nullable: false,
      primaryKey: false,
      comment: '创建人ID',
    },
    {
      name: 'status',
      type: 'VARCHAR',
      length: '20',
      nullable: false,
      primaryKey: false,
      defaultValue: 'ACTIVE',
      comment: '状态',
    },
  ];
  
  // 检查是否已存在这些字段
  const existingFieldNames = databaseStructure.fields.map(f => f.name);
  const fieldsToAdd = commonFields.filter(f => !existingFieldNames.includes(f.name));
  
  if (fieldsToAdd.length === 0) {
    ElMessage.info('所有通用字段已存在');
    return;
  }
  
  databaseStructure.fields.push(...fieldsToAdd);
  onStructureChange();
  ElMessage.success(`已添加 ${fieldsToAdd.length} 个通用字段`);
};

const removeField = (index: number) => {
  if (databaseStructure.fields.length <= 1) {
    ElMessage.warning('至少需要保留一个字段');
    return;
  }
  
  databaseStructure.fields.splice(index, 1);
  onStructureChange();
};

const addIndex = () => {
  if (!databaseStructure.indexes) {
    databaseStructure.indexes = [];
  }
  
  const newIndex: DatabaseIndex = {
    name: `idx_${databaseStructure.tableName}_${databaseStructure.indexes.length + 1}`,
    fields: [],
    type: 'INDEX',
  };
  
  databaseStructure.indexes.push(newIndex);
};

const removeIndex = (index: number) => {
  if (databaseStructure.indexes) {
    databaseStructure.indexes.splice(index, 1);
  }
};

const autoCreateIndexes = () => {
  if (!databaseStructure.indexes) {
    databaseStructure.indexes = [];
  }
  
  // 为外键字段自动创建索引
  const foreignKeyFields = databaseStructure.fields.filter(field => 
    field.name.endsWith('_id') && !field.primaryKey
  );
  
  const existingIndexFields = databaseStructure.indexes.flatMap(idx => idx.fields);
  
  foreignKeyFields.forEach(field => {
    if (!existingIndexFields.includes(field.name)) {
      databaseStructure.indexes!.push({
        name: `idx_${databaseStructure.tableName}_${field.name}`,
        fields: [field.name],
        type: 'INDEX',
      });
    }
  });
  
  ElMessage.success(`已自动创建 ${foreignKeyFields.length} 个索引`);
};

const generateSQL = () => {
  // 生成CREATE TABLE语句的逻辑
  ElMessage.info('SQL生成功能将在下一阶段实现');
};

const validateStructure = () => {
  const errors: string[] = [];
  
  if (!databaseStructure.tableName) {
    errors.push('表名不能为空');
  }
  
  if (databaseStructure.fields.length === 0) {
    errors.push('至少需要一个字段');
  }
  
  const primaryKeys = databaseStructure.fields.filter(f => f.primaryKey);
  if (primaryKeys.length === 0) {
    errors.push('至少需要一个主键字段');
  }
  
  const fieldNames = databaseStructure.fields.map(f => f.name);
  const duplicateFields = fieldNames.filter((name, index) => 
    name && fieldNames.indexOf(name) !== index
  );
  if (duplicateFields.length > 0) {
    errors.push(`存在重复的字段名: ${duplicateFields.join(', ')}`);
  }
  
  if (errors.length > 0) {
    ElMessage.error(`验证失败: ${errors.join('; ')}`);
  } else {
    ElMessage.success('结构验证通过');
  }
};

const confirmStructure = () => {
  if (!isStructureValid.value) {
    ElMessage.warning('请完善数据库结构设计');
    return;
  }
  
  // 先进行验证
  validateStructure();
  
  ElMessageBox.confirm(
    `确认数据库结构设计：
    
表名: ${databaseStructure.tableName}
表说明: ${databaseStructure.tableComment}
字段数: ${databaseStructure.fields.length}
索引数: ${databaseStructure.indexes?.length || 0}

确认后将进入SQL生成阶段。`,
    '确认结构设计',
    {
      confirmButtonText: '确认设计',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    isCompleted.value = true;
    emit('stage-complete', { ...databaseStructure });
    ElMessage.success('数据库结构设计已确认');
  }).catch(() => {});
};

const onStructureChange = () => {
  // 结构变更时的处理逻辑
};

const onFieldChange = (index: number) => {
  // 字段变更时的处理逻辑
  onStructureChange();
};

// Utility methods
const needsLength = (type: string) => {
  return ['VARCHAR', 'CHAR', 'DECIMAL'].includes(type);
};

const getPrimaryKeyFields = () => {
  const primaryKeys = databaseStructure.fields
    .filter(field => field.primaryKey)
    .map(field => field.name);
  return primaryKeys.length > 0 ? primaryKeys.join(', ') : null;
};
</script>

<style scoped>
.stage-card {
  height: 100%;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stage-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stage-card.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.stage-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stage-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  background: #f3f4f6;
  color: #6b7280;
  transition: all 0.3s ease;
}

.stage-number.active {
  background: #3b82f6;
  color: white;
}

.stage-number.completed {
  background: #10b981;
  color: white;
}

.stage-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.stage-description {
  font-size: 14px;
  color: #6b7280;
}

.design-content {
  padding: 8px 0;
  max-height: 800px;
  overflow-y: auto;
}

.design-content.disabled {
  opacity: 0.5;
}

.structure-editor {
  space-y: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.table-info-section,
.fields-section,
.indexes-section,
.structure-preview {
  margin-bottom: 24px;
}

.table-form {
  margin-top: 16px;
}

.fields-table-wrapper {
  max-height: 400px;
  overflow-y: auto;
}

.fields-table {
  width: 100%;
}

.indexes-list {
  space-y: 12px;
}

.index-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.no-indexes {
  text-align: center;
  padding: 20px;
  background: #f9fafb;
  border-radius: 6px;
}

.preview-content {
  background: #f8fafc;
  border-radius: 6px;
  padding: 16px;
}

.structure-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.summary-label {
  font-weight: 500;
  color: #6b7280;
}

.summary-value {
  font-weight: 600;
  color: #1f2937;
}

.confirm-section {
  text-align: center;
  padding: 24px 0;
  border-top: 1px solid #e5e7eb;
}

.confirm-button {
  width: 100%;
  max-width: 300px;
}

.disabled-state,
.waiting-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.info-actions,
.fields-actions,
.indexes-actions,
.preview-actions {
  display: flex;
  gap: 8px;
}
</style>