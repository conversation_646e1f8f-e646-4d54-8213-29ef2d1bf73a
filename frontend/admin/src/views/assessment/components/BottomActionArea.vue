<template>
  <div class="space-y-4">
    <!-- 最近文件列表 -->
    <el-card class="card">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <ClipboardDocumentListIcon class="h-5 w-5 mr-2 text-primary-700" />
            <span class="text-xl font-bold text-primary-700">最近文件</span>
          </div>
          <div class="space-x-2">
            <el-button
              size="small"
              @click="$emit('refresh-recent')"
              :loading="loadingRecent"
            >
              <div class="flex items-center">
                <ArrowPathIcon class="h-4 w-4 mr-1" />
                <span>刷新</span>
              </div>
            </el-button>
            <el-button size="small" @click="$emit('open-chat')" type="primary">
              <div class="flex items-center">
                <ChatBubbleLeftRightIcon class="h-4 w-4 mr-1" />
                <span>AI对话</span>
              </div>
            </el-button>
          </div>
        </div>
      </template>

      <RecentFilesList
        :recent-scales="recentScales"
        :loading="loadingRecent"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @load-scale="$emit('load-scale', $event)"
        @delete-scale="$emit('delete-scale', $event)"
        @page-change="$emit('page-change', $event)"
      />
    </el-card>

    <!-- AI聊天对话框 -->
    <AIChatDialog
      v-if="showChatDialog"
      :visible="showChatDialog"
      :messages="chatMessages"
      :streaming="chatStreaming"
      @close="$emit('close-chat')"
      @send-message="$emit('send-message', $event)"
      @clear-chat="$emit('clear-chat')"
    />
  </div>
</template>

<script setup lang="ts">
import RecentFilesList from './RecentFilesList.vue';
// @ts-ignore - Vue组件类型声明待完善
import AIChatDialog from './AIChatDialog.vue';
import { ClipboardDocumentListIcon, ArrowPathIcon, ChatBubbleLeftRightIcon } from '@heroicons/vue/24/outline';

// Props
interface Props {
  recentScales: any[];
  loadingRecent: boolean;
  currentPage: number;
  pageSize: number;
  total: number;
  showChatDialog: boolean;
  chatMessages: any[];
  chatStreaming: boolean;
}

defineProps<Props>();

// Emits
defineEmits<{
  'refresh-recent': [];
  'open-chat': [];
  'close-chat': [];
  'load-scale': [scale: any];
  'delete-scale': [id: string];
  'page-change': [page: number];
  'send-message': [message: string];
  'clear-chat': [];
}>();
</script>
