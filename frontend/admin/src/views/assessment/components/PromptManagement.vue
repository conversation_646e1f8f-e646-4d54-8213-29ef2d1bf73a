<template>
  <el-card>
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <CpuChipIcon class="h-5 w-5 mr-2 text-primary-700" />
          <span class="text-xl font-bold text-primary-700">AI提示词管理</span>
        </div>
        <div class="space-x-2">
          <el-button size="small" @click="resetToDefault">
            <div class="flex items-center">
              <ArrowPathIcon class="h-4 w-4 mr-1" />
              <span>重置默认</span>
            </div>
          </el-button>
          <el-button size="small" @click="$emit('save-prompt')" type="primary">
            <div class="flex items-center">
              <ArchiveBoxIcon class="h-4 w-4 mr-1" />
              <span>保存提示词</span>
            </div>
          </el-button>
        </div>
      </div>
    </template>

    <!-- 预设提示词选择 -->
    <div class="mb-4">
      <el-select
        :model-value="selectedPromptType"
        placeholder="选择预设提示词"
        @change="applyPresetPrompt"
        class="w-full"
      >
        <el-option
          v-for="preset in promptPresets"
          :key="preset.key"
          :label="preset.name"
          :value="preset.key"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <component :is="getIconComponent(preset.icon)" class="h-4 w-4 mr-2 text-primary-600" />
              <span>{{ preset.name }}</span>
            </div>
            <span class="text-xs text-gray-500">{{ preset.description }}</span>
          </div>
        </el-option>
      </el-select>
    </div>

    <!-- 自定义提示词编辑器 -->
    <div class="space-y-3">
      <div class="flex items-center justify-between">
        <label class="text-sm font-medium text-primary-700">自定义提示词</label>
        <span class="text-xs text-gray-500"
          >{{ customPrompt.length }} 字符</span
        >
      </div>

      <el-input
        :model-value="customPrompt"
        type="textarea"
        :rows="8"
        placeholder="输入您的自定义AI提示词..."
        @input="$emit('update:prompt', $event)"
        class="font-mono text-sm"
      />

      <!-- 提示词预览 -->
      <div
        class="bg-gray-50 p-3 rounded-lg text-xs text-gray-600 leading-relaxed"
      >
        <div class="flex items-center font-medium mb-2">
          <LightBulbIcon class="h-4 w-4 mr-1 text-accent" />
          <span>提示词使用说明：</span>
        </div>
        <ul class="space-y-1">
          <li>• 使用 {content} 占位符表示文档内容</li>
          <li>• 保持提示词简洁明确，避免过于复杂的指令</li>
          <li>• 建议使用中文提示词获得更好的中文输出效果</li>
          <li>• 可以指定输出格式（如JSON、Markdown等）</li>
        </ul>
      </div>
    </div>

    <!-- 提示词历史记录 -->
    <div class="mt-4" v-if="promptHistory.length > 0">
      <el-divider content-position="left">
        <span class="text-sm text-gray-600">历史记录</span>
      </el-divider>

      <div class="space-y-2 max-h-32 overflow-y-auto">
        <div
          v-for="(history, index) in promptHistory"
          :key="index"
          class="p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100 transition-colors"
          @click="applyHistoryPrompt(history)"
        >
          <div class="text-xs text-gray-600 mb-1">
            {{ formatDate(history.timestamp) }}
          </div>
          <div class="text-sm text-primary-700 truncate">
            {{ history.prompt.substring(0, 100) }}...
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { verifiedPrompts } from '../../../data/verified-prompts.ts';
import {
  CpuChipIcon,
  ArrowPathIcon,
  ArchiveBoxIcon,
  LightBulbIcon,
  CircleStackIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  SparklesIcon,
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  customPrompt: string;
}

defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:prompt': [prompt: string];
  'save-prompt': [];
}>();

// 响应式数据
const selectedPromptType = ref('');
const promptHistory = ref<any[]>([]);

// 预设提示词配置
const promptPresets = [
  {
    key: 'database_design',
    name: '数据库设计专用',
    description: '分析文档并设计数据库结构',
    prompt: verifiedPrompts.databaseDesign,
    icon: 'CircleStackIcon',
  },
  {
    key: 'content_analysis',
    name: '内容分析专用',
    description: '深度分析文档内容结构',
    prompt: verifiedPrompts.contentAnalysis,
    icon: 'ChartBarIcon',
  },
  {
    key: 'field_extraction',
    name: '字段提取专用',
    description: '提取表单字段和属性',
    prompt: verifiedPrompts.fieldExtraction,
    icon: 'MagnifyingGlassIcon',
  },
  {
    key: 'universal',
    name: '通用智能分析',
    description: '适用于各种文档的通用分析',
    prompt: verifiedPrompts.universal,
    icon: 'SparklesIcon',
  },
];

// 图标组件映射
const iconComponents = {
  CircleStackIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  SparklesIcon,
};

// 获取图标组件
const getIconComponent = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents];
};

// Methods
const applyPresetPrompt = () => {
  const preset = promptPresets.find(p => p.key === selectedPromptType.value);
  if (preset) {
    emit('update:prompt', preset.prompt);
    saveToHistory(preset.prompt);
  }
};

const resetToDefault = () => {
  const defaultPrompt = verifiedPrompts.universal;
  emit('update:prompt', defaultPrompt);
  selectedPromptType.value = 'universal';
  saveToHistory(defaultPrompt);
};

const applyHistoryPrompt = (history: any) => {
  emit('update:prompt', history.prompt);
};

const saveToHistory = (prompt: string) => {
  const newHistory = {
    prompt,
    timestamp: new Date().toISOString(),
  };

  promptHistory.value.unshift(newHistory);

  // 限制历史记录数量
  if (promptHistory.value.length > 10) {
    promptHistory.value = promptHistory.value.slice(0, 10);
  }

  // 保存到本地存储
  localStorage.setItem('promptHistory', JSON.stringify(promptHistory.value));
};

const loadHistoryFromStorage = () => {
  const stored = localStorage.getItem('promptHistory');
  if (stored) {
    try {
      promptHistory.value = JSON.parse(stored);
    } catch (error) {
      console.error('加载提示词历史失败:', error);
    }
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 生命周期
onMounted(() => {
  loadHistoryFromStorage();
});
</script>
