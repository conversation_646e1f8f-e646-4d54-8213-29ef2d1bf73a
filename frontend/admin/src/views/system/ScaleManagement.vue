<template>
  <div class="scale-management">
    <div class="page-header">
      <h1>量表管理</h1>
      <p>管理系统中的所有评估量表</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleCreate"
          v-if="canManageScales"
        >
          新增量表
        </el-button>
        <el-button
          icon="Refresh"
          @click="refreshData"
        >
          刷新
        </el-button>
        <el-button
          icon="TrendCharts"
          @click="showStats"
        >
          统计信息
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索量表名称或代码"
          style="width: 250px; margin-right: 10px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="categoryFilter"
          placeholder="分类筛选"
          style="width: 150px; margin-right: 10px"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部分类" value="" />
          <el-option 
            v-for="category in categories" 
            :key="category" 
            :label="category" 
            :value="category" 
          />
        </el-select>

        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          style="width: 120px; margin-right: 10px"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部状态" value="" />
          <el-option label="活跃" value="ACTIVE" />
          <el-option label="非活跃" value="INACTIVE" />
          <el-option label="已弃用" value="DEPRECATED" />
          <el-option label="审核中" value="UNDER_REVIEW" />
        </el-select>

        <el-select
          v-model="visibilityFilter"
          placeholder="可见性筛选"
          style="width: 120px"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部可见性" value="" />
          <el-option label="公开" value="PUBLIC" />
          <el-option label="私有" value="PRIVATE" />
          <el-option label="付费" value="PREMIUM" />
        </el-select>
      </div>
    </div>

    <!-- 量表列表 -->
    <el-table
      :data="scaleList"
      v-loading="loading"
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column type="expand">
        <template #default="{ row }">
          <div class="scale-detail">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <label>量表代码:</label>
                  <span>{{ row.code }}</span>
                </div>
                <div class="info-item">
                  <label>版本:</label>
                  <span>{{ row.version }}</span>
                </div>
                <div class="info-item">
                  <label>分类:</label>
                  <span>{{ row.category }}</span>
                </div>
                <div class="info-item">
                  <label>发布者:</label>
                  <span>{{ getPublisherTypeText(row.publisherType) }}</span>
                </div>
                <div class="info-item">
                  <label>使用次数:</label>
                  <span>{{ row.usageCount }}</span>
                </div>
                <div class="info-item">
                  <label>评分:</label>
                  <span>{{ row.rating }} ({{ row.ratingCount }}次评价)</span>
                </div>
              </div>
            </div>
            
            <div class="detail-section" v-if="row.industryTags && row.industryTags.length > 0">
              <h4>行业标签</h4>
              <div class="tag-list">
                <el-tag 
                  v-for="tag in row.industryTags" 
                  :key="tag"
                  size="small"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>

            <div class="detail-section" v-if="row.keywords && row.keywords.length > 0">
              <h4>关键词</h4>
              <div class="tag-list">
                <el-tag 
                  v-for="keyword in row.keywords" 
                  :key="keyword"
                  type="info"
                  size="small"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ keyword }}
                </el-tag>
              </div>
            </div>

            <div class="detail-section">
              <h4>时间信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <label>创建时间:</label>
                  <span>{{ formatDateTime(row.createdAt) }}</span>
                </div>
                <div class="info-item">
                  <label>更新时间:</label>
                  <span>{{ formatDateTime(row.updatedAt) }}</span>
                </div>
                <div class="info-item" v-if="row.publishedAt">
                  <label>发布时间:</label>
                  <span>{{ formatDateTime(row.publishedAt) }}</span>
                </div>
                <div class="info-item" v-if="row.deprecatedAt">
                  <label>弃用时间:</label>
                  <span>{{ formatDateTime(row.deprecatedAt) }}</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="name"
        label="量表名称"
        width="200"
        sortable="custom"
        show-overflow-tooltip
      />
      
      <el-table-column
        prop="code"
        label="量表代码"
        width="150"
        sortable="custom"
      />

      <el-table-column
        prop="version"
        label="版本"
        width="80"
        align="center"
      />

      <el-table-column
        prop="category"
        label="分类"
        width="120"
        align="center"
      />

      <el-table-column
        prop="publisherType"
        label="发布者"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getPublisherTypeTagType(row.publisherType)"
            size="small"
          >
            {{ getPublisherTypeText(row.publisherType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="visibility"
        label="可见性"
        width="90"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getVisibilityTagType(row.visibility)"
            size="small"
          >
            {{ getVisibilityText(row.visibility) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="status"
        label="状态"
        width="90"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getStatusTagType(row.status)"
            size="small"
          >
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="usageCount"
        label="使用次数"
        width="100"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-badge 
            :value="row.usageCount" 
            :type="row.usageCount > 100 ? 'success' : 'primary'"
          />
        </template>
      </el-table-column>

      <el-table-column
        prop="isOfficial"
        label="官方"
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-icon 
            v-if="row.isOfficial" 
            color="#67C23A"
            size="16"
          >
            <Check />
          </el-icon>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="updatedAt"
        label="更新时间"
        width="150"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.updatedAt) }}
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="200"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          
          <el-button
            type="primary"
            link
            size="small"
            @click="handleEdit(row)"
            v-if="canManageScales"
          >
            编辑
          </el-button>

          <el-dropdown
            @command="(command: string) => handleDropdownCommand(command, row)"
            v-if="canManageScales"
          >
            <el-button type="primary" link size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-if="row.status !== 'ACTIVE'"
                  :command="`publish-${row.id}`"
                >
                  发布量表
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="row.status === 'ACTIVE'"
                  :command="`deprecate-${row.id}`"
                >
                  弃用量表
                </el-dropdown-item>
                <el-dropdown-item
                  :command="`delete-${row.id}`"
                  divided
                >
                  删除量表
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handlePageSizeChange"
      @current-change="handlePageChange"
      style="margin-top: 20px; justify-content: center"
    />

    <!-- 量表详情/编辑对话框 -->
    <scale-form-dialog
      v-model:visible="formDialogVisible"
      :scale="currentScale"
      :mode="formMode"
      :categories="categories"
      @success="handleFormSuccess"
    />

    <!-- 统计信息对话框 -->
    <scale-stats-dialog
      v-model:visible="statsDialogVisible"
      @refresh="refreshData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Plus, Refresh, TrendCharts, Check, ArrowDown } from '@element-plus/icons-vue';
import { systemScaleApi, type AssessmentScale } from '@/api/multiTenantAdapter';
import tenantContext from '@/utils/tenantContext';
import ScaleFormDialog from './components/ScaleFormDialog.vue';
import ScaleStatsDialog from './components/ScaleStatsDialog.vue';

// 数据状态
const scaleList = ref<AssessmentScale[]>([]);
const categories = ref<string[]>([]);
const loading = ref(false);
const searchQuery = ref('');
const categoryFilter = ref('');
const statusFilter = ref('');
const visibilityFilter = ref('');

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
});

// 排序
const sortField = ref('');
const sortOrder = ref('');

// 对话框状态
const formDialogVisible = ref(false);
const formMode = ref<'create' | 'edit' | 'view'>('create');
const currentScale = ref(null);

const statsDialogVisible = ref(false);

// 权限控制
const canManageScales = computed(() => {
  return tenantContext.isAdmin() || tenantContext.hasPermission('SCALE_MANAGE');
});

// 获取量表列表
const fetchScaleList = async () => {
  try {
    loading.value = true;
    
    const params = {
      page: pagination.current - 1,
      size: pagination.size,
      search: searchQuery.value || undefined,
      category: categoryFilter.value || undefined,
      status: (statusFilter.value || undefined) as AssessmentScale['status'],
      visibility: (visibilityFilter.value || undefined) as AssessmentScale['visibility'],
      sortField: sortField.value || undefined,
      sortOrder: (sortOrder.value || undefined) as 'ASC' | 'DESC' | undefined
    };

    const response = await systemScaleApi.getScales(params);
    const { data } = response;

    if (data.success) {
      scaleList.value = data.data.content || [];
      pagination.total = data.data.totalElements || 0;
    } else {
      ElMessage.error(data.message || '获取量表列表失败');
    }
  } catch (error: any) {
    console.error('获取量表列表失败:', error);
    ElMessage.error(error.message || '获取量表列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await systemScaleApi.getCategories();
    const { data } = response;
    
    // getCategories直接返回string[]，无需success检查
    categories.value = data || [];
  } catch (error) {
    console.warn('获取分类列表失败:', error);
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchScaleList();
};

// 排序处理
const handleSortChange = ({ prop, order }: any) => {
  sortField.value = prop || '';
  sortOrder.value = order === 'ascending' ? 'ASC' : order === 'descending' ? 'DESC' : '';
  fetchScaleList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchScaleList();
};

const handlePageSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchScaleList();
};

// 刷新数据
const refreshData = () => {
  fetchScaleList();
  fetchCategories();
};

// 操作处理
const handleCreate = () => {
  currentScale.value = null;
  formMode.value = 'create';
  formDialogVisible.value = true;
};

const handleView = (scale: any) => {
  currentScale.value = scale;
  formMode.value = 'view';
  formDialogVisible.value = true;
};

const handleEdit = (scale: any) => {
  currentScale.value = scale;
  formMode.value = 'edit';
  formDialogVisible.value = true;
};

const showStats = () => {
  statsDialogVisible.value = true;
};

// 下拉菜单操作
const handleDropdownCommand = async (command: string, scale: any) => {
  const [action, id] = command.split('-');
  
  try {
    switch (action) {
      case 'publish':
        await handlePublishScale(scale);
        break;
      case 'deprecate':
        await handleDeprecateScale(scale);
        break;
      case 'delete':
        await handleDeleteScale(scale);
        break;
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败');
  }
};

// 发布量表
const handlePublishScale = async (scale: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要发布量表 "${scale.name}" 吗？`,
      '确认发布',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await systemScaleApi.publishScale(scale.id);
    const { data } = response;
    
    if (data.success) {
      ElMessage.success('量表发布成功');
      fetchScaleList();
    } else {
      ElMessage.error(data.message || '发布失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '发布失败');
    }
  }
};

// 弃用量表
const handleDeprecateScale = async (scale: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要弃用量表 "${scale.name}" 吗？弃用后将不能再使用。`,
      '确认弃用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await systemScaleApi.deprecateScale(scale.id);
    const { data } = response;
    
    if (data.success) {
      ElMessage.success('量表弃用成功');
      fetchScaleList();
    } else {
      ElMessage.error(data.message || '弃用失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '弃用失败');
    }
  }
};

// 删除量表
const handleDeleteScale = async (scale: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除量表 "${scale.name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await systemScaleApi.deleteScale(scale.id);
    const { data } = response;
    
    if (data.success) {
      ElMessage.success('量表删除成功');
      fetchScaleList();
    } else {
      ElMessage.error(data.message || '删除失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败');
    }
  }
};

// 表单操作成功
const handleFormSuccess = () => {
  formDialogVisible.value = false;
  fetchScaleList();
};

// 工具函数
const getPublisherTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'PLATFORM': 'danger',
    'TENANT': 'primary',
    'PARTNER': 'warning'
  };
  return typeMap[type] || 'info';
};

const getPublisherTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'PLATFORM': '平台官方',
    'TENANT': '租户',
    'PARTNER': '合作伙伴'
  };
  return textMap[type] || type;
};

const getVisibilityTagType = (visibility: string) => {
  const typeMap: Record<string, string> = {
    'PUBLIC': 'success',
    'PRIVATE': 'info',
    'PREMIUM': 'warning'
  };
  return typeMap[visibility] || 'info';
};

const getVisibilityText = (visibility: string) => {
  const textMap: Record<string, string> = {
    'PUBLIC': '公开',
    'PRIVATE': '私有',
    'PREMIUM': '付费'
  };
  return textMap[visibility] || visibility;
};

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'ACTIVE': 'success',
    'INACTIVE': 'info',
    'DEPRECATED': 'danger',
    'UNDER_REVIEW': 'warning'
  };
  return typeMap[status] || 'info';
};

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'ACTIVE': '活跃',
    'INACTIVE': '非活跃',
    'DEPRECATED': '已弃用',
    'UNDER_REVIEW': '审核中'
  };
  return textMap[status] || status;
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 初始化
onMounted(() => {
  fetchScaleList();
  fetchCategories();
});
</script>

<style scoped>
.scale-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 5px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.scale-detail {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  margin: 10px 0;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .toolbar-right {
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>