<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="`批量${approved ? '通过' : '拒绝'} - ${records.length}条记录`"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="batch-review-form">
      <!-- 记录列表 -->
      <el-card shadow="never" class="records-card">
        <template #header>
          <div class="card-header">
            <span>待审核记录</span>
            <el-tag :type="approved ? 'success' : 'danger'">
              {{ approved ? '通过' : '拒绝' }}
            </el-tag>
          </div>
        </template>
        
        <div class="records-list">
          <div 
            v-for="record in records" 
            :key="record.record.id"
            class="record-item"
          >
            <div class="record-info">
              <span class="record-number">{{ record.record.recordNumber }}</span>
              <span class="tenant-name">{{ record.tenantInfo?.name || '-' }}</span>
              <span class="scale-name">{{ record.scaleInfo?.name || '-' }}</span>
            </div>
            <el-tag size="small">{{ getStatusText(record.record.status) }}</el-tag>
          </div>
        </div>
      </el-card>

      <!-- 审核表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="review-form"
      >
        <el-form-item 
          :label="approved ? '通过备注' : '拒绝原因'" 
          prop="reviewNotes"
        >
          <el-input
            v-model="formData.reviewNotes"
            type="textarea"
            :rows="4"
            :placeholder="approved ? '请输入批量审核通过的备注信息（可选）' : '请输入批量审核拒绝的原因'"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="审核说明">
          <el-alert
            :title="getAlertMessage()"
            :type="approved ? 'success' : 'warning'"
            show-icon
            :closable="false"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          :type="approved ? 'success' : 'danger'"
          @click="handleSubmit"
          :loading="submitting"
        >
          确认{{ approved ? '通过' : '拒绝' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { systemAssessmentApi } from '@/api/multiTenantAdapter';
import tenantContext from '@/utils/tenantContext';

interface Props {
  visible: boolean;
  records: any[];
  approved: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  records: () => [],
  approved: true
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用和状态
const formRef = ref();
const submitting = ref(false);

// 表单数据
const formData = reactive({
  reviewNotes: ''
});

// 表单验证规则
const formRules = computed(() => ({
  reviewNotes: [
    { 
      required: !props.approved, 
      message: '批量拒绝时必须填写拒绝原因', 
      trigger: 'blur' 
    },
    { 
      min: props.approved ? 0 : 10, 
      message: '拒绝原因至少需要10个字符', 
      trigger: 'blur' 
    }
  ]
}));

// 监听对话框显示
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      initFormData();
    });
  }
});

// 初始化表单数据
const initFormData = () => {
  formData.reviewNotes = '';
  
  // 清除验证状态
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 获取提示信息
const getAlertMessage = () => {
  const action = props.approved ? '通过' : '拒绝';
  return `您即将批量${action} ${props.records.length} 条评估记录，此操作不可撤销，请确认。`;
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    submitting.value = true;

    // 准备提交数据
    const submitData = {
      recordIds: props.records.map(record => record.record.id),
      approved: props.approved,
      reviewNotes: formData.reviewNotes.trim(),
      reviewerId: tenantContext.getCurrentUserId() as string
    };

    const response = await systemAssessmentApi.batchReviewAssessments(submitData);
    const { data } = response;
    
    if (data.success) {
      const action = props.approved ? '通过' : '拒绝';
      ElMessage.success(`批量审核${action}成功`);
      emit('success');
    } else {
      ElMessage.error(data.message || '批量审核失败');
    }
  } catch (error: any) {
    console.error('批量审核失败:', error);
    ElMessage.error(error.message || '批量审核失败');
  } finally {
    submitting.value = false;
  }
};

// 工具函数
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'DRAFT': '草稿',
    'SUBMITTED': '已提交',
    'REVIEWED': '已审核',
    'APPROVED': '已批准',
    'ARCHIVED': '已归档',
    'REJECTED': '已拒绝'
  };
  return textMap[status] || status;
};
</script>

<style scoped>
.batch-review-form {
  max-height: 60vh;
  overflow-y: auto;
}

.records-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.records-list {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.record-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.record-number {
  font-weight: 500;
  color: #303133;
}

.tenant-name,
.scale-name {
  font-size: 12px;
  color: #909399;
}

.review-form {
  margin: 20px 0;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 20px auto;
  }
  
  .record-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>