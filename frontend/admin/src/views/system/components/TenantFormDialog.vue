<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="租户代码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入租户代码"
              :disabled="mode === 'edit'"
              maxlength="20"
              show-word-limit
            />
            <div class="form-tip" v-if="mode === 'create'">
              租户代码创建后不可修改，建议使用英文简称
            </div>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formData.status"
              placeholder="请选择状态"
              style="width: 100%"
            >
              <el-option label="活跃" value="ACTIVE" />
              <el-option label="暂停" value="SUSPENDED" />
              <el-option label="禁用" value="DISABLED" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="租户名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入租户名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="描述信息" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入租户描述信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系邮箱" prop="contactEmail">
            <el-input
              v-model="formData.contactEmail"
              placeholder="请输入联系邮箱"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input
              v-model="formData.contactPhone"
              placeholder="请输入联系电话"
              maxlength="20"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="联系地址" prop="address">
        <el-input
          v-model="formData.address"
          type="textarea"
          :rows="2"
          placeholder="请输入联系地址"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订阅计划" prop="subscriptionPlan">
            <el-select
              v-model="formData.subscriptionPlan"
              placeholder="请选择订阅计划"
              style="width: 100%"
            >
              <el-option label="基础版" value="BASIC" />
              <el-option label="标准版" value="STANDARD" />
              <el-option label="高级版" value="PREMIUM" />
              <el-option label="企业版" value="ENTERPRISE" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="订阅到期" prop="subscriptionExpiresAt">
            <el-date-picker
              v-model="formData.subscriptionExpiresAt"
              type="datetime"
              placeholder="选择到期时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="Logo链接" prop="logoUrl">
        <el-input
          v-model="formData.logoUrl"
          placeholder="请输入Logo图片链接"
          maxlength="500"
        />
        <div class="form-tip">
          可选：租户的Logo图片URL地址
        </div>
      </el-form-item>

      <!-- Logo预览 -->
      <el-form-item v-if="formData.logoUrl" label="">
        <div class="logo-preview">
          <img 
            :src="formData.logoUrl" 
            alt="Logo预览"
            @error="handleImageError"
            @load="handleImageLoad"
          />
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ mode === 'create' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { ElMessage, ElForm } from 'element-plus';
import { tenantManageApi } from '@/api/multiTenantAdapter';

// Props
interface Props {
  visible: boolean;
  tenant?: any;
  mode: 'create' | 'edit' | 'view';
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  tenant: null,
  mode: 'create'
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  success: [];
}>();

// 响应式数据
const formRef = ref<InstanceType<typeof ElForm>>();
const submitting = ref(false);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titleMap = {
    create: '新增租户',
    edit: '编辑租户',
    view: '查看租户'
  };
  return titleMap[props.mode];
});

// 表单数据
const formData = ref({
  code: '',
  name: '',
  description: '',
  logoUrl: '',
  contactEmail: '',
  contactPhone: '',
  address: '',
  status: 'ACTIVE' as 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING' | 'DISABLED',
  subscriptionPlan: 'BASIC',
  subscriptionExpiresAt: ''
});

// 表单验证规则
const formRules = {
  code: [
    { required: true, message: '请输入租户代码', trigger: 'blur' },
    { 
      pattern: /^[A-Z0-9_]+$/, 
      message: '租户代码只能包含大写字母、数字和下划线', 
      trigger: 'blur' 
    },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入租户名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  subscriptionPlan: [
    { required: true, message: '请选择订阅计划', trigger: 'change' }
  ],
  contactEmail: [
    { 
      type: 'email', 
      message: '请输入正确的邮箱地址', 
      trigger: ['blur', 'change'] 
    }
  ],
  contactPhone: [
    { 
      pattern: /^1[3-9]\d{9}$/, 
      message: '请输入正确的手机号码', 
      trigger: 'blur' 
    }
  ]
};

// 重置表单
const resetForm = () => {
  formData.value = {
    code: '',
    name: '',
    description: '',
    logoUrl: '',
    contactEmail: '',
    contactPhone: '',
    address: '',
    status: 'ACTIVE',
    subscriptionPlan: 'BASIC',
    subscriptionExpiresAt: ''
  };
  
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 填充表单数据
const fillFormData = (tenant: any) => {
  if (tenant) {
    formData.value = {
      code: tenant.code || '',
      name: tenant.name || '',
      description: tenant.description || '',
      logoUrl: tenant.logoUrl || '',
      contactEmail: tenant.contactEmail || '',
      contactPhone: tenant.contactPhone || '',
      address: tenant.address || '',
      status: tenant.status || 'ACTIVE',
      subscriptionPlan: tenant.subscriptionPlan || 'BASIC',
      subscriptionExpiresAt: tenant.subscriptionExpiresAt || ''
    };
  } else {
    resetForm();
  }
};

// 监听租户数据变化
watch(
  () => props.tenant,
  (newTenant) => {
    fillFormData(newTenant);
  },
  { immediate: true }
);

// 监听对话框打开
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      fillFormData(props.tenant);
    }
  }
);

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    // 验证表单
    await formRef.value.validate();
    
    submitting.value = true;

    // 准备提交数据
    const submitData: any = { ...formData.value };
    
    // 如果没有设置过期时间，设置为一年后
    if (!submitData.subscriptionExpiresAt) {
      const expiresAt = new Date();
      expiresAt.setFullYear(expiresAt.getFullYear() + 1);
      submitData.subscriptionExpiresAt = expiresAt.toISOString().slice(0, 19);
    }

    let response;
    if (props.mode === 'create') {
      response = await tenantManageApi.createTenant(submitData);
    } else {
      response = await tenantManageApi.updateTenant(props.tenant.id, submitData);
    }

    const { data }: { data: any } = response;
    if (data.success) {
      ElMessage.success(
        props.mode === 'create' ? '租户创建成功' : '租户更新成功'
      );
      emit('success');
    } else {
      ElMessage.error(data.message || '操作失败');
    }
  } catch (error: any) {
    console.error('提交失败:', error);
    ElMessage.error(error.message || '操作失败');
  } finally {
    submitting.value = false;
  }
};

// 图片加载错误处理
const handleImageError = () => {
  ElMessage.warning('Logo图片加载失败，请检查链接是否正确');
};

// 图片加载成功处理
const handleImageLoad = () => {
  // 可以在这里添加图片加载成功的处理逻辑
};
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.logo-preview {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 10px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
}

.logo-preview img {
  max-width: 120px;
  max-height: 60px;
  object-fit: contain;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表单项间距调整 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  .el-col {
    margin-bottom: 10px;
  }
}
</style>