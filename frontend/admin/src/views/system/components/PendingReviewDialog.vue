<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="待审核记录"
    width="80%"
    :close-on-click-modal="false"
  >
    <div class="pending-review-container" v-loading="loading">
      <!-- 筛选工具栏 -->
      <div class="toolbar">
        <el-select
          v-model="selectedTenantId"
          placeholder="选择租户"
          style="width: 200px"
          clearable
          @change="fetchPendingRecords"
        >
          <el-option label="全部租户" value="" />
          <el-option 
            v-for="tenant in tenants" 
            :key="tenant.id" 
            :label="tenant.name" 
            :value="tenant.id" 
          />
        </el-select>
        
        <el-button @click="fetchPendingRecords" icon="Refresh">刷新</el-button>
      </div>

      <!-- 待审核记录列表 -->
      <el-table
        :data="pendingRecords"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column
          prop="record.recordNumber"
          label="记录编号"
          width="150"
        />
        
        <el-table-column
          label="租户"
          width="120"
        >
          <template #default="{ row }">
            {{ row.tenantInfo?.name || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          label="量表"
          width="150"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.scaleInfo?.name || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          label="评估员"
          width="120"
        >
          <template #default="{ row }">
            {{ row.assessorInfo?.fullName || row.assessorInfo?.username || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="record.assessmentDate"
          label="评估时间"
          width="160"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.record.assessmentDate) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="record.totalScore"
          label="总分"
          width="80"
          align="center"
        >
          <template #default="{ row }">
            {{ row.record.totalScore || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          label="等待时长"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            {{ getWaitingTime(row.record.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column
          label="优先级"
          width="80"
          align="center"
        >
          <template #default="{ row }">
            <el-tag 
              :type="getPriorityTagType(row.record.assessmentType)"
              size="small"
            >
              {{ getPriorityText(row.record.assessmentType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="150"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="success"
              link
              size="small"
              @click="handleQuickApprove(row)"
            >
              快速通过
            </el-button>
            <el-button
              type="primary"
              link
              size="small"
              @click="handleDetailReview(row)"
            >
              详细审核
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedRecords.length > 0">
        <div class="selected-info">
          已选择 {{ selectedRecords.length }} 条记录
        </div>
        <div class="action-buttons">
          <el-button 
            type="success" 
            @click="handleBatchApprove"
          >
            批量通过
          </el-button>
          <el-button 
            type="danger" 
            @click="handleBatchReject"
          >
            批量拒绝
          </el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty 
        v-if="!loading && pendingRecords.length === 0"
        description="暂无待审核记录"
        :image-size="120"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh" :loading="loading">
          刷新数据
        </el-button>
      </div>
    </template>

    <!-- 详细审核对话框 -->
    <review-dialog
      v-model:visible="reviewDialogVisible"
      :assessment="currentAssessment"
      :mode="reviewMode"
      @success="handleReviewSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import { systemAssessmentApi, tenantManageApi } from '@/api/multiTenantAdapter';
import tenantContext from '@/utils/tenantContext';
import ReviewDialog from './ReviewDialog.vue';

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible', 'refresh']);

// 数据状态
const loading = ref(false);
const pendingRecords = ref<any[]>([]);
const tenants = ref<any[]>([]);
const selectedRecords = ref<any[]>([]);
const selectedTenantId = ref('');

// 对话框状态
const reviewDialogVisible = ref(false);
const currentAssessment = ref(null);
const reviewMode = ref<'approve' | 'reject'>('approve');

// 获取待审核记录
const fetchPendingRecords = async () => {
  try {
    loading.value = true;
    
    const response = await systemAssessmentApi.getPendingReviewRecords(selectedTenantId.value);
    const { data }: { data: any } = response;
    
    if (data.success) {
      pendingRecords.value = data.data || [];
    } else {
      ElMessage.error(data.message || '获取待审核记录失败');
    }
  } catch (error: any) {
    console.error('获取待审核记录失败:', error);
    ElMessage.error(error.message || '获取待审核记录失败');
  } finally {
    loading.value = false;
  }
};

// 获取租户列表
const fetchTenants = async () => {
  try {
    const response = await tenantManageApi.getTenants({ size: 1000 });
    const { data }: { data: any } = response;
    
    if (data.success) {
      tenants.value = data.data.content || [];
    }
  } catch (error) {
    console.warn('获取租户列表失败:', error);
  }
};

// 选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedRecords.value = selection;
};

// 快速通过
const handleQuickApprove = async (assessment: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要快速通过评估记录 "${assessment.record.recordNumber}" 吗？`,
      '确认快速通过',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }
    );

    const submitData = {
      approved: true,
      reviewNotes: '快速审核通过',
      reviewerId: tenantContext.getCurrentUserId() as string
    };

    const response = await systemAssessmentApi.reviewAssessment(assessment.record.id, submitData);
    const { data } = response;
    
    if (data.success) {
      ElMessage.success('快速通过成功');
      fetchPendingRecords();
      emit('refresh');
    } else {
      ElMessage.error(data.message || '快速通过失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '快速通过失败');
    }
  }
};

// 详细审核
const handleDetailReview = (assessment: any) => {
  currentAssessment.value = assessment;
  reviewMode.value = 'approve';
  reviewDialogVisible.value = true;
};

// 批量通过
const handleBatchApprove = () => {
  // 这里可以打开批量审核对话框或直接执行
  ElMessage.info('批量审核功能开发中');
};

// 批量拒绝
const handleBatchReject = () => {
  // 这里可以打开批量审核对话框或直接执行
  ElMessage.info('批量审核功能开发中');
};

// 审核成功回调
const handleReviewSuccess = () => {
  reviewDialogVisible.value = false;
  fetchPendingRecords();
  emit('refresh');
};

// 刷新数据
const handleRefresh = () => {
  fetchPendingRecords();
  emit('refresh');
};

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
};

// 工具函数
const getWaitingTime = (createdAt: string) => {
  if (!createdAt) return '-';
  
  const now = new Date();
  const created = new Date(createdAt);
  const diffHours = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60));
  
  if (diffHours < 1) return '< 1小时';
  if (diffHours < 24) return `${diffHours}小时`;
  return `${Math.floor(diffHours / 24)}天`;
};

const getPriorityTagType = (assessmentType: string) => {
  const typeMap: Record<string, string> = {
    'EMERGENCY': 'danger',
    'REASSESSMENT': 'warning',
    'FOLLOWUP': 'success',
    'REGULAR': 'info'
  };
  return typeMap[assessmentType] || 'info';
};

const getPriorityText = (assessmentType: string) => {
  const textMap: Record<string, string> = {
    'EMERGENCY': '紧急',
    'REASSESSMENT': '高',
    'FOLLOWUP': '中',
    'REGULAR': '普通'
  };
  return textMap[assessmentType] || '普通';
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 监听对话框显示
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    fetchPendingRecords();
    fetchTenants();
  }
});
</script>

<style scoped>
.pending-review-container {
  min-height: 400px;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 12px 16px;
  background: #e8f4fd;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.selected-info {
  color: #409EFF;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 20px auto;
  }
  
  .batch-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .action-buttons {
    justify-content: center;
  }
}
</style>