<template>
  <el-card class="upload-status-tracker" v-if="showTracker">
    <template #header>
      <div class="status-header">
        <div class="flex items-center">
          <component 
            :is="getStatusIcon()" 
            class="h-5 w-5 mr-2"
            :class="getStatusIconClass()"
          />
          <span class="font-semibold">上传状态追踪</span>
        </div>
        <el-button size="small" text @click="closeTracker">
          <XMarkIcon class="h-4 w-4" />
        </el-button>
      </div>
    </template>

    <div class="status-content">
      <!-- 实时状态显示 -->
      <div class="status-display">
        <el-tag 
          :type="getStatusTagType()" 
          size="large"
          class="w-full justify-between mb-3"
        >
          <div class="flex items-center">
            <component 
              :is="getStatusIcon()" 
              class="h-4 w-4 mr-2"
              :class="{ 'animate-spin': isProcessing }"
            />
            <span>{{ getStatusText() }}</span>
          </div>
          <span>{{ getProgress() }}%</span>
        </el-tag>
      </div>

      <!-- 进度条 -->
      <div v-if="uploadStatus !== 'idle'" class="progress-section mb-4">
        <el-progress
          :percentage="progressPercentage"
          :stroke-width="8"
          :show-text="false"
          :status="getProgressStatus()"
          :color="getProgressColor()"
        />
        <div class="progress-info mt-2">
          <div class="text-sm text-gray-600">{{ currentMessage }}</div>
          <div class="text-xs text-gray-500">
            {{ formatTime(elapsedTime) }} / 预计 {{ formatTime(estimatedTime) }}
          </div>
        </div>
      </div>

      <!-- 阶段详情 -->
      <div class="stages-section">
        <div class="stages-title text-sm font-medium text-gray-700 mb-2">处理阶段</div>
        <div class="stages-list">
          <div 
            v-for="stage in stages" 
            :key="stage.id"
            class="stage-item"
            :class="{ 
              'stage-active': stage.status === 'active',
              'stage-completed': stage.status === 'completed',
              'stage-error': stage.status === 'error'
            }"
          >
            <div class="stage-indicator">
              <CheckCircleIcon 
                v-if="stage.status === 'completed'" 
                class="h-4 w-4 text-green-500" 
              />
              <ExclamationCircleIcon 
                v-else-if="stage.status === 'error'" 
                class="h-4 w-4 text-red-500" 
              />
              <ClockIcon 
                v-else-if="stage.status === 'active'" 
                class="h-4 w-4 text-blue-500 animate-spin" 
              />
              <div 
                v-else 
                class="w-4 h-4 rounded-full border-2 border-gray-300"
              ></div>
            </div>
            <div class="stage-content">
              <div class="stage-name">{{ stage.name }}</div>
              <div class="stage-description">{{ stage.description }}</div>
              <div v-if="stage.duration" class="stage-duration">
                {{ formatTime(stage.duration) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 服务状态 -->
      <div class="services-section mt-4">
        <div class="services-title text-sm font-medium text-gray-700 mb-2">服务状态</div>
        <div class="services-grid">
          <div class="service-item">
            <el-tag 
              :type="doclingStatus === 'online' ? 'success' : 'danger'"
              size="small"
            >
              <CpuChipIcon class="h-3 w-3 mr-1" />
              Docling: {{ getServiceStatusText(doclingStatus) }}
            </el-tag>
          </div>
          <div class="service-item">
            <el-tag 
              :type="aiStatus === 'online' ? 'success' : 'danger'"
              size="small"
            >
              <CogIcon class="h-3 w-3 mr-1" />
              AI: {{ getServiceStatusText(aiStatus) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 错误详情 -->
      <div v-if="errorDetails" class="error-section mt-4">
        <el-alert
          :title="errorDetails.title"
          :description="errorDetails.message"
          type="error"
          :closable="false"
          show-icon
        />
        <div v-if="errorDetails.suggestions?.length" class="error-suggestions mt-2">
          <div class="text-sm font-medium text-gray-700 mb-1">建议解决方案：</div>
          <ul class="text-sm text-gray-600">
            <li v-for="suggestion in errorDetails.suggestions" :key="suggestion">
              • {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section mt-4">
        <el-button-group class="w-full">
          <el-button 
            v-if="uploadStatus === 'error'" 
            type="primary" 
            @click="retryUpload"
            size="small"
          >
            <ArrowPathIcon class="h-4 w-4 mr-1" />
            重试上传
          </el-button>
          <el-button 
            v-if="uploadStatus === 'uploading'" 
            type="danger" 
            @click="cancelUpload"
            size="small"
          >
            <XMarkIcon class="h-4 w-4 mr-1" />
            取消上传
          </el-button>
          <el-button 
            type="info" 
            @click="copyLogs"
            size="small"
          >
            <ClipboardDocumentIcon class="h-4 w-4 mr-1" />
            复制日志
          </el-button>
        </el-button-group>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
  XMarkIcon,
  ArrowPathIcon,
  ClipboardDocumentIcon,
  CpuChipIcon,
  CogIcon,
  CloudArrowUpIcon,
  DocumentTextIcon,
} from '@heroicons/vue/24/outline';

// Types
type UploadStatus = 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
type StageStatus = 'pending' | 'active' | 'completed' | 'error';
type ServiceStatus = 'online' | 'offline' | 'checking';

interface Stage {
  id: string;
  name: string;
  description: string;
  status: StageStatus;
  startTime?: Date;
  endTime?: Date;
  duration?: number;
}

interface ErrorDetails {
  title: string;
  message: string;
  code?: string;
  suggestions?: string[];
}

// Props
interface Props {
  visible?: boolean;
  uploadStatus?: UploadStatus;
  progress?: number;
  currentMessage?: string;
  doclingStatus?: ServiceStatus;
  aiStatus?: ServiceStatus;
  errorDetails?: ErrorDetails | null;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  uploadStatus: 'idle',
  progress: 0,
  currentMessage: '准备上传...',
  doclingStatus: 'offline',
  aiStatus: 'offline',
  errorDetails: null,
});

// Emits
const emit = defineEmits<{
  'close': [];
  'retry': [];
  'cancel': [];
}>();

// Reactive data
const showTracker = ref(false);
const startTime = ref<Date | null>(null);
const elapsedTime = ref(0);
const estimatedTime = ref(0);
const progressPercentage = ref(0);
const timer = ref<NodeJS.Timeout | null>(null);

// Default stages
const stages = ref<Stage[]>([
  {
    id: 'upload',
    name: '文件上传',
    description: '上传文件到服务器',
    status: 'pending'
  },
  {
    id: 'validation',
    name: '文件验证',
    description: '验证文件格式和大小',
    status: 'pending'
  },
  {
    id: 'parsing',
    name: 'Docling解析',
    description: '使用Docling AI解析文档结构',
    status: 'pending'
  },
  {
    id: 'processing',
    name: '内容处理',
    description: '生成Markdown格式内容',
    status: 'pending'
  },
  {
    id: 'completion',
    name: '处理完成',
    description: '文档处理完成，准备展示',
    status: 'pending'
  }
]);

// Computed
const isProcessing = computed(() => {
  return ['uploading', 'processing'].includes(props.uploadStatus);
});

// Methods - 定义所有函数
const startTimer = () => {
  if (timer.value) {
    clearInterval(timer.value);
  }
  
  timer.value = setInterval(() => {
    if (startTime.value) {
      elapsedTime.value = Date.now() - startTime.value.getTime();
      estimatedTime.value = Math.max(60000, elapsedTime.value * 2);
    }
  }, 1000);
};

const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

const updateStagesBasedOnStatus = (status: UploadStatus) => {
  const now = new Date();
  
  switch (status) {
    case 'idle':
      stages.value.forEach(stage => {
        stage.status = 'pending';
        stage.startTime = undefined;
        stage.endTime = undefined;
        stage.duration = undefined;
      });
      stopTimer();
      break;
    case 'uploading':
      updateStageStatus('upload', 'active', now);
      break;
    case 'processing':
      updateStageStatus('upload', 'completed');
      updateStageStatus('validation', 'completed');
      updateStageStatus('parsing', 'active', now);
      break;
    case 'completed':
      stages.value.forEach(stage => {
        if (stage.status !== 'error') {
          updateStageStatus(stage.id, 'completed');
        }
      });
      stopTimer();
      break;
    case 'error':
      const activeStage = stages.value.find(s => s.status === 'active');
      if (activeStage) {
        updateStageStatus(activeStage.id, 'error');
      }
      stopTimer();
      break;
  }
};

const updateStageStatus = (stageId: string, status: StageStatus, startTime?: Date) => {
  const stage = stages.value.find(s => s.id === stageId);
  if (stage) {
    if (status === 'active' && startTime) {
      stage.startTime = startTime;
    } else if (status === 'completed' && stage.startTime) {
      stage.endTime = new Date();
      stage.duration = stage.endTime.getTime() - stage.startTime.getTime();
    }
    stage.status = status;
  }
};

const updateProgress = () => {
  if (props.uploadStatus === 'completed') {
    progressPercentage.value = 100;
  } else if (props.uploadStatus === 'error') {
    // Keep current progress on error
  } else if (props.progress > 0) {
    progressPercentage.value = props.progress;
  }
};

const getStatusIcon = () => {
  switch (props.uploadStatus) {
    case 'uploading':
    case 'processing':
      return CloudArrowUpIcon;
    case 'completed':
      return CheckCircleIcon;
    case 'error':
      return ExclamationCircleIcon;
    default:
      return DocumentTextIcon;
  }
};

const getStatusIconClass = () => {
  switch (props.uploadStatus) {
    case 'uploading':
    case 'processing':
      return 'text-blue-500 animate-pulse';
    case 'completed':
      return 'text-green-500';
    case 'error':
      return 'text-red-500';
    default:
      return 'text-gray-500';
  }
};

const getStatusTagType = () => {
  switch (props.uploadStatus) {
    case 'uploading':
    case 'processing':
      return 'warning';
    case 'completed':
      return 'success';
    case 'error':
      return 'danger';
    default:
      return 'info';
  }
};

const getStatusText = () => {
  switch (props.uploadStatus) {
    case 'uploading':
      return '文件上传中';
    case 'processing':
      return 'AI解析处理中';
    case 'completed':
      return '处理完成';
    case 'error':
      return '处理失败';
    default:
      return '等待上传';
  }
};

const getProgress = () => {
  return Math.round(progressPercentage.value);
};

const getProgressStatus = () => {
  switch (props.uploadStatus) {
    case 'completed':
      return 'success';
    case 'error':
      return 'exception';
    default:
      return undefined;
  }
};

const getProgressColor = () => {
  if (props.uploadStatus === 'error') {
    return '#f56565';
  } else if (props.uploadStatus === 'completed') {
    return '#48bb78';
  }
  return '#3182ce';
};

const getServiceStatusText = (status: ServiceStatus) => {
  switch (status) {
    case 'online':
      return '在线';
    case 'offline':
      return '离线';
    case 'checking':
      return '检查中';
    default:
      return '未知';
  }
};

const formatTime = (milliseconds: number) => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`;
  } else {
    return `${remainingSeconds}秒`;
  }
};

const closeTracker = () => {
  showTracker.value = false;
  stopTimer();
  emit('close');
};

const retryUpload = () => {
  startTime.value = new Date();
  elapsedTime.value = 0;
  progressPercentage.value = 0;
  stages.value.forEach(stage => {
    stage.status = 'pending';
    stage.startTime = undefined;
    stage.endTime = undefined;
    stage.duration = undefined;
  });
  
  startTimer();
  emit('retry');
};

const cancelUpload = () => {
  stopTimer();
  emit('cancel');
};

const copyLogs = () => {
  const logs = [
    `上传状态追踪日志`,
    `时间: ${new Date().toLocaleString()}`,
    `状态: ${getStatusText()}`,
    `进度: ${getProgress()}%`,
    `耗时: ${formatTime(elapsedTime.value)}`,
    ``,
    `阶段详情:`,
    ...stages.value.map(stage => 
      `- ${stage.name}: ${stage.status} ${stage.duration ? `(${formatTime(stage.duration)})` : ''}`
    ),
    ``,
    `服务状态:`,
    `- Docling: ${getServiceStatusText(props.doclingStatus)}`,
    `- AI服务: ${getServiceStatusText(props.aiStatus)}`,
  ];
  
  if (props.errorDetails) {
    logs.push('', '错误详情:', `- ${props.errorDetails.title}`, `- ${props.errorDetails.message}`);
  }
  
  navigator.clipboard.writeText(logs.join('\n'));
  ElMessage.success('日志已复制到剪贴板');
};

// Watch for props changes
watch(() => props.visible, (newVal) => {
  showTracker.value = newVal;
  if (newVal && !startTime.value) {
    startTime.value = new Date();
    startTimer();
  } else if (!newVal) {
    stopTimer();
    startTime.value = null;
    elapsedTime.value = 0;
  }
}, { immediate: true });

watch(() => props.uploadStatus, (newStatus) => {
  updateStagesBasedOnStatus(newStatus);
  updateProgress();
});

watch(() => props.progress, (newProgress) => {
  progressPercentage.value = newProgress;
});

// Cleanup
onUnmounted(() => {
  stopTimer();
});
</script>

<style scoped>
.upload-status-tracker {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 2000;
  border: 2px solid #3b82f6;
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.2);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  padding: 4px 0;
}

.status-display {
  margin-bottom: 12px;
}

.progress-section {
  background: #f8fafc;
  border-radius: 6px;
  padding: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stages-section {
  background: #f9fafb;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.stages-title {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 4px;
}

.stages-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stage-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.stage-item.stage-active {
  background: #dbeafe;
  border-left: 3px solid #3b82f6;
}

.stage-item.stage-completed {
  background: #d1fae5;
  border-left: 3px solid #10b981;
}

.stage-item.stage-error {
  background: #fee2e2;
  border-left: 3px solid #ef4444;
}

.stage-indicator {
  margin-top: 2px;
}

.stage-content {
  flex: 1;
}

.stage-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.stage-description {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.stage-duration {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 2px;
}

.services-section {
  background: #f3f4f6;
  border-radius: 6px;
  padding: 12px;
}

.services-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.service-item {
  display: flex;
  justify-content: center;
}

.error-section {
  background: #fef2f2;
  border-radius: 6px;
  padding: 12px;
}

.error-suggestions ul {
  list-style: none;
  padding: 0;
  margin: 4px 0 0 0;
}

.actions-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: .5; }
}
</style>