<template>
  <div class="base-table">
    <!-- 表格头部工具栏 -->
    <div v-if="$slots.toolbar || title" class="base-table-toolbar">
      <div class="base-table-title">
        <h3 v-if="title">{{ title }}</h3>
      </div>
      <div class="base-table-actions">
        <slot name="toolbar"></slot>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="base-table-container">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="data"
        :columns="columns"
        :stripe="stripe"
        :border="border"
        :size="size"
        :fit="fit"
        :show-header="showHeader"
        :highlight-current-row="highlightCurrentRow"
        :row-class-name="rowClassName"
        :row-style="rowStyle"
        :cell-class-name="cellClassName"
        :cell-style="cellStyle"
        :header-row-class-name="headerRowClassName"
        :header-row-style="headerRowStyle"
        :header-cell-class-name="headerCellClassName"
        :header-cell-style="headerCellStyle"
        :row-key="rowKey"
        :empty-text="emptyText"
        :default-expand-all="defaultExpandAll"
        :expand-row-keys="expandRowKeys"
        :default-sort="defaultSort"
        :tooltip-effect="tooltipEffect"
        :show-summary="showSummary"
        :sum-text="sumText"
        :summary-method="summaryMethod"
        :span-method="spanMethod"
        :select-on-indeterminate="selectOnIndeterminate"
        :indent="indent"
        :lazy="lazy"
        :load="load"
        :tree-props="treeProps"
        :table-layout="tableLayout"
        :scrollbar-always-on="scrollbarAlwaysOn"
        :flexible="flexible"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @selection-change="handleSelectionChange"
        @cell-mouse-enter="handleCellMouseEnter"
        @cell-mouse-leave="handleCellMouseLeave"
        @cell-click="handleCellClick"
        @cell-dblclick="handleCellDblclick"
        @cell-contextmenu="handleCellContextmenu"
        @row-click="handleRowClick"
        @row-contextmenu="handleRowContextmenu"
        @row-dblclick="handleRowDblclick"
        @header-click="handleHeaderClick"
        @header-contextmenu="handleHeaderContextmenu"
        @sort-change="handleSortChange"
        @filter-change="handleFilterChange"
        @current-change="handleCurrentChange"
        @header-dragend="handleHeaderDragend"
        @expand-change="handleExpandChange"
      >
        <!-- 动态列渲染 (当提供columns时) -->
        <template v-if="columns" v-for="column in columns" :key="column.prop || column.type">
          <!-- 选择列 -->
          <el-table-column
            v-if="column.type === 'selection'"
            type="selection"
            :width="column.width || 55"
            :fixed="column.fixed"
            :selectable="column.selectable"
          />
          
          <!-- 索引列 -->
          <el-table-column
            v-else-if="column.type === 'index'"
            type="index"
            :label="column.label || '#'"
            :width="column.width || 55"
            :fixed="column.fixed"
            :index="column.index"
          />
          
          <!-- 展开列 -->
          <el-table-column
            v-else-if="column.type === 'expand'"
            type="expand"
            :width="column.width || 55"
            :fixed="column.fixed"
          >
            <template #default="scope">
              <slot name="expand" :row="scope.row" :$index="scope.$index"></slot>
            </template>
          </el-table-column>
          
          <!-- 普通列 -->
          <el-table-column
            v-else
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
            :render-header="column.renderHeader"
            :sortable="column.sortable"
            :sort-method="column.sortMethod"
            :sort-by="column.sortBy"
            :sort-orders="column.sortOrders"
            :resizable="column.resizable"
            :formatter="column.formatter"
            :show-overflow-tooltip="column.showOverflowTooltip"
            :align="column.align"
            :header-align="column.headerAlign"
            :class-name="column.className"
            :label-class-name="column.labelClassName"
            :filters="column.filters"
            :filter-placement="column.filterPlacement"
            :filter-multiple="column.filterMultiple"
            :filter-method="column.filterMethod"
            :filtered-value="column.filteredValue"
          >
            <template #default="scope" v-if="column.slot">
              <slot
                :name="column.slot"
                :row="scope.row"
                :column="scope.column"
                :$index="scope.$index"
                :value="column.prop ? scope.row[column.prop] : undefined"
              ></slot>
            </template>
            <template #header="scope" v-if="column.headerSlot">
              <slot
                :name="column.headerSlot"
                :column="scope.column"
                :$index="scope.$index"
              ></slot>
            </template>
          </el-table-column>
        </template>
        
        <!-- Slot方式的列渲染 (当不提供columns时) -->
        <slot v-if="!columns"></slot>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div v-if="showPagination" class="base-table-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        :background="paginationBackground"
        :small="paginationSmall"
        :hide-on-single-page="hideOnSinglePage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { TableInstance } from 'element-plus'

// 列配置接口
interface TableColumn {
  type?: 'selection' | 'index' | 'expand'
  prop?: string
  label?: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  renderHeader?: Function
  sortable?: boolean | 'custom'
  sortMethod?: Function
  sortBy?: string | string[] | Function
  sortOrders?: Array<'ascending' | 'descending' | null>
  resizable?: boolean
  formatter?: Function
  showOverflowTooltip?: boolean
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  className?: string
  labelClassName?: string
  selectable?: Function
  reserveSelection?: boolean
  filters?: Array<{ text: string; value: any }>
  filterPlacement?: string
  filterMultiple?: boolean
  filterMethod?: Function
  filteredValue?: any[]
  index?: number | Function
  slot?: string
  headerSlot?: string
}

interface Props {
  // 数据源
  data: any[]
  // 列配置 (使用slot方式时可选)
  columns?: TableColumn[]
  // 表格标题
  title?: string
  // 加载状态
  loading?: boolean
  // 是否显示斑马纹
  stripe?: boolean
  // 是否显示边框
  border?: boolean
  // 表格尺寸
  size?: 'large' | 'default' | 'small'
  // 列宽是否自撑开
  fit?: boolean
  // 是否显示表头
  showHeader?: boolean
  // 是否高亮当前行
  highlightCurrentRow?: boolean
  // 行类名
  rowClassName?: string | Function
  // 行样式
  rowStyle?: object | Function
  // 单元格类名
  cellClassName?: string | Function
  // 单元格样式
  cellStyle?: object | Function
  // 表头行类名
  headerRowClassName?: string | Function
  // 表头行样式
  headerRowStyle?: object | Function
  // 表头单元格类名
  headerCellClassName?: string | Function
  // 表头单元格样式
  headerCellStyle?: object | Function
  // 行键
  rowKey?: string | Function
  // 空数据文本
  emptyText?: string
  // 默认展开所有行
  defaultExpandAll?: boolean
  // 展开行的keys
  expandRowKeys?: any[]
  // 默认排序
  defaultSort?: object
  // tooltip效果
  tooltipEffect?: 'dark' | 'light'
  // 是否显示合计行
  showSummary?: boolean
  // 合计行标题文本
  sumText?: string
  // 合计方法
  summaryMethod?: Function
  // 合并方法
  spanMethod?: Function
  // 在多选时，仅半选时全选
  selectOnIndeterminate?: boolean
  // 树形数据缩进
  indent?: number
  // 懒加载
  lazy?: boolean
  // 加载函数
  load?: Function
  // 树形属性配置
  treeProps?: object
  // 表格布局方式
  tableLayout?: 'fixed' | 'auto'
  // 滚动条总是显示
  scrollbarAlwaysOn?: boolean
  // 灵活高度
  flexible?: boolean
  
  // 分页相关
  showPagination?: boolean
  total?: number
  currentPage?: number
  pageSize?: number
  pageSizes?: number[]
  paginationLayout?: string
  paginationBackground?: boolean
  paginationSmall?: boolean
  hideOnSinglePage?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  columns: undefined,
  loading: false,
  stripe: false,
  border: true,
  size: 'default',
  fit: true,
  showHeader: true,
  highlightCurrentRow: false,
  emptyText: '暂无数据',
  defaultExpandAll: false,
  tooltipEffect: 'dark',
  showSummary: false,
  sumText: '合计',
  selectOnIndeterminate: true,
  indent: 16,
  lazy: false,
  tableLayout: 'fixed',
  scrollbarAlwaysOn: false,
  flexible: false,
  showPagination: false,
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: () => [10, 20, 50, 100],
  paginationLayout: 'total, sizes, prev, pager, next, jumper',
  paginationBackground: true,
  paginationSmall: false,
  hideOnSinglePage: false
})

const emit = defineEmits<{
  // 表格事件
  'select': [selection: any[], row: any]
  'select-all': [selection: any[]]
  'selection-change': [selection: any[]]
  'cell-mouse-enter': [row: any, column: any, cell: any, event: Event]
  'cell-mouse-leave': [row: any, column: any, cell: any, event: Event]
  'cell-click': [row: any, column: any, cell: any, event: Event]
  'cell-dblclick': [row: any, column: any, cell: any, event: Event]
  'cell-contextmenu': [row: any, column: any, cell: any, event: Event]
  'row-click': [row: any, column: any, event: Event]
  'row-contextmenu': [row: any, column: any, event: Event]
  'row-dblclick': [row: any, column: any, event: Event]
  'header-click': [column: any, event: Event]
  'header-contextmenu': [column: any, event: Event]
  'sort-change': [{ column: any; prop: string; order: string | null }]
  'filter-change': [filters: any]
  'current-change': [currentRow: any, oldCurrentRow: any]
  'header-dragend': [newWidth: number, oldWidth: number, column: any, event: Event]
  'expand-change': [row: any, expandedRows: any[]]
  
  // 分页事件
  'size-change': [size: number]
  'page-change': [page: number]
}>()

// 表格引用
const tableRef = ref<TableInstance>()

// 分页状态
const currentPage = ref(props.currentPage)
const pageSize = ref(props.pageSize)

// 事件处理器
const handleSelect = (selection: any[], row: any) => emit('select', selection, row)
const handleSelectAll = (selection: any[]) => emit('select-all', selection)
const handleSelectionChange = (selection: any[]) => emit('selection-change', selection)
const handleCellMouseEnter = (row: any, column: any, cell: any, event: Event) => emit('cell-mouse-enter', row, column, cell, event)
const handleCellMouseLeave = (row: any, column: any, cell: any, event: Event) => emit('cell-mouse-leave', row, column, cell, event)
const handleCellClick = (row: any, column: any, cell: any, event: Event) => emit('cell-click', row, column, cell, event)
const handleCellDblclick = (row: any, column: any, cell: any, event: Event) => emit('cell-dblclick', row, column, cell, event)
const handleCellContextmenu = (row: any, column: any, cell: any, event: Event) => emit('cell-contextmenu', row, column, cell, event)
const handleRowClick = (row: any, column: any, event: Event) => emit('row-click', row, column, event)
const handleRowContextmenu = (row: any, column: any, event: Event) => emit('row-contextmenu', row, column, event)
const handleRowDblclick = (row: any, column: any, event: Event) => emit('row-dblclick', row, column, event)
const handleHeaderClick = (column: any, event: Event) => emit('header-click', column, event)
const handleHeaderContextmenu = (column: any, event: Event) => emit('header-contextmenu', column, event)
const handleSortChange = (params: { column: any; prop: string; order: string | null }) => emit('sort-change', params)
const handleFilterChange = (filters: any) => emit('filter-change', filters)
const handleCurrentChange = (currentRow: any, oldCurrentRow: any) => emit('current-change', currentRow, oldCurrentRow)
const handleHeaderDragend = (newWidth: number, oldWidth: number, column: any, event: Event) => emit('header-dragend', newWidth, oldWidth, column, event)
const handleExpandChange = (row: any, expandedRows: any[]) => emit('expand-change', row, expandedRows)

// 分页事件处理器
const handleSizeChange = (size: number) => {
  pageSize.value = size
  emit('size-change', size)
}

const handleCurrentPageChange = (page: number) => {
  currentPage.value = page
  emit('page-change', page)
}

// 暴露表格实例方法
defineExpose({
  tableRef,
  clearSelection: () => tableRef.value?.clearSelection(),
  getSelectionRows: () => tableRef.value?.getSelectionRows(),
  toggleRowSelection: (row: any, selected?: boolean) => tableRef.value?.toggleRowSelection(row, selected),
  toggleAllSelection: () => tableRef.value?.toggleAllSelection(),
  toggleRowExpansion: (row: any, expanded?: boolean) => tableRef.value?.toggleRowExpansion(row, expanded),
  setCurrentRow: (row: any) => tableRef.value?.setCurrentRow(row),
  clearSort: () => tableRef.value?.clearSort(),
  clearFilter: (columnKeys?: string[]) => tableRef.value?.clearFilter(columnKeys),
  doLayout: () => tableRef.value?.doLayout(),
  sort: (prop: string, order: string) => tableRef.value?.sort(prop, order),
  scrollTo: (options: any) => tableRef.value?.scrollTo(options),
  setScrollTop: (top: number) => tableRef.value?.setScrollTop(top),
  setScrollLeft: (left: number) => tableRef.value?.setScrollLeft(left)
})
</script>

<style scoped>
.base-table {
  background: #fff;
}

.base-table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.base-table-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.base-table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.base-table-container {
  min-height: 200px;
}

.base-table-pagination {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

:deep(.el-table__empty-block) {
  min-height: 200px;
}
</style>