<template>
  <el-card
    :class="[
      'base-card',
      { 'is-loading': loading, 'has-error': error }
    ]"
    :shadow="shadow"
    :body-style="bodyStyle"
  >
    <!-- 头部插槽 -->
    <template v-if="$slots.header || title || $slots.actions" #header>
      <div class="base-card-header">
        <div class="base-card-header-title">
          <slot name="header">
            <h3 v-if="title" class="base-card-title">{{ title }}</h3>
          </slot>
        </div>
        <div v-if="$slots.actions" class="base-card-header-actions">
          <slot name="actions"></slot>
        </div>
      </div>
    </template>

    <!-- 加载状态 -->
    <div v-if="loading" class="base-card-loading">
      <el-skeleton :rows="skeletonRows" animated />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="base-card-error">
      <el-empty :description="errorText || '加载失败'">
        <el-button v-if="onRetry" type="primary" @click="onRetry">
          重试
        </el-button>
      </el-empty>
    </div>

    <!-- 内容区域 -->
    <div v-else class="base-card-content">
      <slot></slot>
    </div>

    <!-- 底部插槽 -->
    <div v-if="$slots.footer" class="base-card-footer">
      <slot name="footer"></slot>
    </div>
  </el-card>
</template>

<script setup lang="ts">
interface Props {
  // 标题
  title?: string
  // 加载状态
  loading?: boolean
  // 错误状态
  error?: boolean
  // 错误文本
  errorText?: string
  // 重试函数
  onRetry?: () => void
  // 阴影效果
  shadow?: 'always' | 'hover' | 'never'
  // 内容区域样式
  bodyStyle?: Record<string, any>
  // 骨架屏行数
  skeletonRows?: number
}

withDefaults(defineProps<Props>(), {
  loading: false,
  error: false,
  shadow: 'always',
  skeletonRows: 3
})
</script>

<style scoped>
.base-card {
  margin-bottom: 16px;
  transition: all 0.3s;
}

.base-card.is-loading {
  pointer-events: none;
}

.base-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.base-card-header-title {
  flex: 1;
}

.base-card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.base-card-header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.base-card-content {
  min-height: 50px;
}

.base-card-loading {
  padding: 20px 0;
}

.base-card-error {
  padding: 40px 0;
  text-align: center;
}

.base-card-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}
</style>