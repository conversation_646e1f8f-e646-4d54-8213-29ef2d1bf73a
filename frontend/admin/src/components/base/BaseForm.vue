<template>
  <el-form
    ref="formRef"
    :model="modelValue"
    :rules="rules"
    :inline="inline"
    :label-position="labelPosition"
    :label-width="labelWidth"
    :label-suffix="labelSuffix"
    :hide-required-asterisk="hideRequiredAsterisk"
    :show-message="showMessage"
    :inline-message="inlineMessage"
    :status-icon="statusIcon"
    :validate-on-rule-change="validateOnRuleChange"
    :size="size"
    :disabled="disabled"
    :scroll-to-error="scrollToError"
    :require-asterisk-position="requireAsteriskPosition"
    :class="['base-form', customClass]"
    @validate="handleValidate"
  >
    <!-- 表单项渲染 -->
    <template v-for="field in fields" :key="field.prop">
      <el-form-item
        :prop="field.prop"
        :label="field.label"
        :label-width="field.labelWidth"
        :required="field.required"
        :rules="field.rules"
        :error="field.error"
        :show-message="field.showMessage !== false"
        :inline-message="field.inlineMessage"
        :size="field.size || size"
        :for="field.for"
        :validate-status="field.validateStatus"
        :class="field.className"
      >
        <!-- 输入框 -->
        <el-input
          v-if="field.type === 'input'"
          v-model="modelValue[field.prop]"
          :type="field.inputType || 'text'"
          :maxlength="field.maxlength"
          :minlength="field.minlength"
          :show-word-limit="field.showWordLimit"
          :placeholder="field.placeholder"
          :clearable="field.clearable !== false"
          :disabled="field.disabled || disabled"
          :size="field.size || size"
          :prefix-icon="field.prefixIcon"
          :suffix-icon="field.suffixIcon"
          :rows="field.rows"
          :autosize="field.autosize"
          :autocomplete="field.autocomplete"
          :readonly="field.readonly"
          :max="field.max"
          :min="field.min"
          :step="field.step"
          :resize="field.resize"
          :autofocus="field.autofocus"
          :form="field.form"
          :label="field.inputLabel"
          :tabindex="field.tabindex"
          :validate-event="field.validateEvent !== false"
          :input-style="field.inputStyle"
          @blur="field.onBlur"
          @focus="field.onFocus"
          @change="field.onChange"
          @input="field.onInput"
          @clear="field.onClear"
        />

        <!-- 文本域 -->
        <el-input
          v-else-if="field.type === 'textarea'"
          v-model="modelValue[field.prop]"
          type="textarea"
          :rows="field.rows || 3"
          :autosize="field.autosize"
          :placeholder="field.placeholder"
          :disabled="field.disabled || disabled"
          :maxlength="field.maxlength"
          :show-word-limit="field.showWordLimit"
          :resize="field.resize || 'both'"
          :readonly="field.readonly"
          :autofocus="field.autofocus"
          @blur="field.onBlur"
          @focus="field.onFocus"
          @change="field.onChange"
          @input="field.onInput"
        />

        <!-- 选择器 -->
        <el-select
          v-else-if="field.type === 'select'"
          v-model="modelValue[field.prop]"
          :multiple="field.multiple"
          :disabled="field.disabled || disabled"
          :value-key="field.valueKey"
          :size="field.size || size"
          :clearable="field.clearable !== false"
          :collapse-tags="field.collapseTags"
          :collapse-tags-tooltip="field.collapseTagsTooltip"
          :multiple-limit="field.multipleLimit"
          :name="field.name"
          :autocomplete="field.autocomplete"
          :placeholder="field.placeholder"
          :filterable="field.filterable"
          :allow-create="field.allowCreate"
          :filter-method="field.filterMethod"
          :remote="field.remote"
          :remote-method="field.remoteMethod"
          :loading="field.loading"
          :loading-text="field.loadingText"
          :no-match-text="field.noMatchText"
          :no-data-text="field.noDataText"
          :popper-class="field.popperClass"
          :reserve-keyword="field.reserveKeyword"
          :default-first-option="field.defaultFirstOption"
          :teleported="field.teleported !== false"
          :persistent="field.persistent"
          :automatic-dropdown="field.automaticDropdown"
          :clear-icon="field.clearIcon"
          :fit-input-width="field.fitInputWidth"
          :suffix-icon="field.suffixIcon"
          :tag-type="field.tagType"
          :validate-event="field.validateEvent !== false"
          @change="field.onChange"
          @visible-change="field.onVisibleChange"
          @remove-tag="field.onRemoveTag"
          @clear="field.onClear"
          @blur="field.onBlur"
          @focus="field.onFocus"
        >
          <el-option
            v-for="option in field.options"
            :key="option[field.optionValue || 'value']"
            :label="option[field.optionLabel || 'label']"
            :value="option[field.optionValue || 'value']"
            :disabled="option.disabled"
          />
        </el-select>

        <!-- 日期选择器 -->
        <el-date-picker
          v-else-if="field.type === 'date'"
          v-model="modelValue[field.prop]"
          :type="field.dateType || 'date'"
          :readonly="field.readonly"
          :disabled="field.disabled || disabled"
          :editable="field.editable !== false"
          :clearable="field.clearable !== false"
          :size="field.size || size"
          :placeholder="field.placeholder"
          :start-placeholder="field.startPlaceholder"
          :end-placeholder="field.endPlaceholder"
          :time-arrow-control="field.timeArrowControl"
          :format="field.format"
          :value-format="field.valueFormat"
          :range-separator="field.rangeSeparator"
          :default-value="field.defaultValue"
          :default-time="field.defaultTime"
          :name="field.name"
          :unlink-panels="field.unlinkPanels"
          :prefix-icon="field.prefixIcon"
          :clear-icon="field.clearIcon"
          :validate-event="field.validateEvent !== false"
          :disabled-date="field.disabledDate"
          :shortcuts="field.shortcuts"
          :cell-class-name="field.cellClassName"
          @change="field.onChange"
          @blur="field.onBlur"
          @focus="field.onFocus"
          @calendar-change="field.onCalendarChange"
          @panel-change="field.onPanelChange"
          @visible-change="field.onVisibleChange"
        />

        <!-- 时间选择器 -->
        <el-time-picker
          v-else-if="field.type === 'time'"
          v-model="modelValue[field.prop]"
          :readonly="field.readonly"
          :disabled="field.disabled || disabled"
          :editable="field.editable !== false"
          :clearable="field.clearable !== false"
          :size="field.size || size"
          :placeholder="field.placeholder"
          :start-placeholder="field.startPlaceholder"
          :end-placeholder="field.endPlaceholder"
          :is-range="field.isRange"
          :arrow-control="field.arrowControl"
          :range-separator="field.rangeSeparator"
          :format="field.format"
          :default-value="field.defaultValue"
          :name="field.name"
          :prefix-icon="field.prefixIcon"
          :clear-icon="field.clearIcon"
          :disabled-hours="field.disabledHours"
          :disabled-minutes="field.disabledMinutes"
          :disabled-seconds="field.disabledSeconds"
          @change="field.onChange"
          @blur="field.onBlur"
          @focus="field.onFocus"
          @visible-change="field.onVisibleChange"
        />

        <!-- 数字输入器 -->
        <el-input-number
          v-else-if="field.type === 'number'"
          v-model="modelValue[field.prop]"
          :min="field.min"
          :max="field.max"
          :step="field.step || 1"
          :step-strictly="field.stepStrictly"
          :precision="field.precision"
          :size="field.size || size"
          :readonly="field.readonly"
          :disabled="field.disabled || disabled"
          :controls="field.controls !== false"
          :controls-position="field.controlsPosition"
          :name="field.name"
          :label="field.inputLabel"
          :placeholder="field.placeholder"
          :id="field.id"
          :value-on-clear="field.valueOnClear"
          :validate-event="field.validateEvent !== false"
          @change="field.onChange"
          @blur="field.onBlur"
          @focus="field.onFocus"
        />

        <!-- 开关 -->
        <el-switch
          v-else-if="field.type === 'switch'"
          v-model="modelValue[field.prop]"
          :disabled="field.disabled || disabled"
          :width="field.width"
          :inline-prompt="field.inlinePrompt"
          :active-icon="field.activeIcon"
          :inactive-icon="field.inactiveIcon"
          :active-text="field.activeText"
          :inactive-text="field.inactiveText"
          :active-value="field.activeValue !== undefined ? field.activeValue : true"
          :inactive-value="field.inactiveValue !== undefined ? field.inactiveValue : false"
          :active-color="field.activeColor"
          :inactive-color="field.inactiveColor"
          :border-color="field.borderColor"
          :name="field.name"
          :validate-event="field.validateEvent !== false"
          :before-change="field.beforeChange"
          :id="field.id"
          :size="field.size || size"
          :tabindex="field.tabindex"
          @change="field.onChange"
        />

        <!-- 单选框组 -->
        <el-radio-group
          v-else-if="field.type === 'radio'"
          v-model="modelValue[field.prop]"
          :size="field.size || size"
          :disabled="field.disabled || disabled"
          :text-color="field.textColor"
          :fill="field.fill"
          :validate-event="field.validateEvent !== false"
          :id="field.id"
          :name="field.name"
          @change="field.onChange"
        >
          <el-radio
            v-for="option in field.options"
            :key="option[field.optionValue || 'value']"
            :label="option[field.optionValue || 'value']"
            :disabled="option.disabled"
            :border="field.border"
          >
            {{ option[field.optionLabel || 'label'] }}
          </el-radio>
        </el-radio-group>

        <!-- 多选框组 -->
        <el-checkbox-group
          v-else-if="field.type === 'checkbox'"
          v-model="modelValue[field.prop]"
          :size="field.size || size"
          :disabled="field.disabled || disabled"
          :min="field.min"
          :max="field.max"
          :text-color="field.textColor"
          :fill="field.fill"
          :tag="field.tag"
          :validate-event="field.validateEvent !== false"
          :id="field.id"
          @change="field.onChange"
        >
          <el-checkbox
            v-for="option in field.options"
            :key="option[field.optionValue || 'value']"
            :label="option[field.optionValue || 'value']"
            :true-label="option.trueLabel"
            :false-label="option.falseLabel"
            :disabled="option.disabled"
            :border="field.border"
            :name="option.name"
            :checked="option.checked"
            :indeterminate="option.indeterminate"
          >
            {{ option[field.optionLabel || 'label'] }}
          </el-checkbox>
        </el-checkbox-group>

        <!-- 评分 -->
        <el-rate
          v-else-if="field.type === 'rate'"
          v-model="modelValue[field.prop]"
          :max="field.max || 5"
          :disabled="field.disabled || disabled"
          :allow-half="field.allowHalf"
          :low-threshold="field.lowThreshold || 2"
          :high-threshold="field.highThreshold || 4"
          :colors="field.colors"
          :void-color="field.voidColor"
          :disabled-void-color="field.disabledVoidColor"
          :icon-classes="field.iconClasses"
          :void-icon-class="field.voidIconClass"
          :disabled-void-icon-class="field.disabledVoidIconClass"
          :show-text="field.showText"
          :show-score="field.showScore"
          :text-color="field.textColor"
          :texts="field.texts"
          :score-template="field.scoreTemplate"
          :size="field.size || size"
          :clearable="field.clearable"
          @change="field.onChange"
        />

        <!-- 滑块 -->
        <el-slider
          v-else-if="field.type === 'slider'"
          v-model="modelValue[field.prop]"
          :min="field.min || 0"
          :max="field.max || 100"
          :disabled="field.disabled || disabled"
          :step="field.step || 1"
          :show-input="field.showInput"
          :show-input-controls="field.showInputControls !== false"
          :input-size="field.inputSize || size"
          :show-stops="field.showStops"
          :show-tooltip="field.showTooltip !== false"
          :format-tooltip="field.formatTooltip"
          :range="field.range"
          :vertical="field.vertical"
          :height="field.height"
          :label="field.inputLabel"
          :debounce="field.debounce || 300"
          :tooltip-class="field.tooltipClass"
          :marks="field.marks"
          :validate-event="field.validateEvent !== false"
          @change="field.onChange"
          @input="field.onInput"
        />

        <!-- 自定义插槽 -->
        <slot
          v-else-if="field.type === 'slot'"
          :name="field.slotName"
          :field="field"
          :value="modelValue[field.prop]"
          :disabled="field.disabled || disabled"
        ></slot>

        <!-- 默认文本显示 -->
        <span v-else class="form-text">
          {{ modelValue[field.prop] || field.placeholder || '-' }}
        </span>

        <!-- 字段说明 -->
        <div v-if="field.help" class="field-help">
          {{ field.help }}
        </div>
      </el-form-item>
    </template>

    <!-- 表单操作按钮 -->
    <el-form-item v-if="showActions" class="form-actions">
      <slot name="actions">
        <el-button v-if="showReset" @click="handleReset">
          {{ resetText }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ submitText }}
        </el-button>
      </slot>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'

// 字段配置接口
interface FormField {
  // 基础属性
  prop: string
  label?: string
  type: 'input' | 'textarea' | 'select' | 'date' | 'time' | 'number' | 'switch' | 'radio' | 'checkbox' | 'rate' | 'slider' | 'slot' | 'text'
  required?: boolean
  rules?: any[]
  
  // 通用属性
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  size?: 'large' | 'default' | 'small'
  help?: string
  className?: string
  
  // Input 相关
  inputType?: string
  maxlength?: number
  minlength?: number
  showWordLimit?: boolean
  clearable?: boolean
  prefixIcon?: string
  suffixIcon?: string
  rows?: number
  autosize?: boolean | object
  autocomplete?: string
  autofocus?: boolean
  
  // Select 相关
  options?: any[]
  optionLabel?: string
  optionValue?: string
  multiple?: boolean
  filterable?: boolean
  remote?: boolean
  remoteMethod?: Function
  loading?: boolean
  
  // Date/Time 相关
  dateType?: string
  format?: string
  valueFormat?: string
  
  // Number 相关
  min?: number
  max?: number
  step?: number
  precision?: number
  
  // Switch 相关
  activeText?: string
  inactiveText?: string
  activeValue?: any
  inactiveValue?: any
  
  // 事件处理
  onChange?: Function
  onInput?: Function
  onFocus?: Function
  onBlur?: Function
  onClear?: Function
  
  // 插槽相关
  slotName?: string
  
  [key: string]: any
}

interface Props {
  // 表单数据（v-model）
  modelValue: Record<string, any>
  // 字段配置
  fields: FormField[]
  // 验证规则
  rules?: FormRules
  // 行内表单
  inline?: boolean
  // 标签位置
  labelPosition?: 'left' | 'right' | 'top'
  // 标签宽度
  labelWidth?: string | number
  // 标签后缀
  labelSuffix?: string
  // 隐藏必填星号
  hideRequiredAsterisk?: boolean
  // 是否显示校验错误信息
  showMessage?: boolean
  // 是否以行内形式展示校验信息
  inlineMessage?: boolean
  // 是否在输入框中显示校验结果反馈图标
  statusIcon?: boolean
  // 是否在 rules 属性改变后立即触发一次验证
  validateOnRuleChange?: boolean
  // 表单内组件的尺寸
  size?: 'large' | 'default' | 'small'
  // 是否禁用该表单内的所有组件
  disabled?: boolean
  // 当校验失败时，滚动到第一个错误表单项
  scrollToError?: boolean
  // 必填星号的位置
  requireAsteriskPosition?: 'left' | 'right'
  // 自定义类名
  customClass?: string
  
  // 操作按钮相关
  showActions?: boolean
  showReset?: boolean
  submitText?: string
  resetText?: string
  loading?: boolean
  
  // 提交/重置回调
  onSubmit?: (valid: boolean, data: Record<string, any>) => void
  onReset?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  inline: false,
  labelPosition: 'right',
  labelWidth: '100px',
  hideRequiredAsterisk: false,
  showMessage: true,
  inlineMessage: false,
  statusIcon: false,
  validateOnRuleChange: true,
  size: 'default',
  disabled: false,
  scrollToError: false,
  requireAsteriskPosition: 'left',
  showActions: true,
  showReset: true,
  submitText: '提交',
  resetText: '重置',
  loading: false
})

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
  'validate': [prop: string, isValid: boolean, message: string]
  'submit': [valid: boolean, data: Record<string, any>]
  'reset': []
}>()

// 表单引用
const formRef = ref<FormInstance>()

// 表单验证事件
const handleValidate = (prop: string, isValid: boolean, message: string) => {
  emit('validate', prop, isValid, message)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (props.onSubmit) {
      props.onSubmit(valid, props.modelValue)
    }
    emit('submit', valid, props.modelValue)
  } catch (error) {
    console.log('表单验证失败:', error)
    if (props.onSubmit) {
      props.onSubmit(false, props.modelValue)
    }
    emit('submit', false, props.modelValue)
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  if (props.onReset) {
    props.onReset()
  }
  emit('reset')
}

// 暴露表单实例方法
defineExpose({
  formRef,
  validate: () => formRef.value?.validate(),
  validateField: (props: string | string[]) => formRef.value?.validateField(props),
  resetFields: () => formRef.value?.resetFields(),
  scrollToField: (prop: string) => formRef.value?.scrollToField(prop),
  clearValidate: (props?: string | string[]) => formRef.value?.clearValidate(props)
})
</script>

<style scoped>
.base-form {
  background: #fff;
}

.form-actions {
  margin-top: 24px;
  text-align: center;
}

.form-actions :deep(.el-form-item__content) {
  justify-content: center;
}

.field-help {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  margin-top: 4px;
}

.form-text {
  color: #606266;
  min-height: 32px;
  line-height: 32px;
  display: inline-block;
}

/* 行内表单样式 */
.base-form.el-form--inline .el-form-item {
  margin-right: 16px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .base-form {
    padding: 16px;
  }
  
  .base-form :deep(.el-form-item__label) {
    width: 100px !important;
  }
  
  .form-actions {
    text-align: center;
  }
}
</style>