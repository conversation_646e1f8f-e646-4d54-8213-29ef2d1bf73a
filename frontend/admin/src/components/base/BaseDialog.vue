<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :fullscreen="fullscreen"
    :top="top"
    :modal="modal"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :draggable="draggable"
    :before-close="handleBeforeClose"
    :destroy-on-close="destroyOnClose"
    :class="['base-dialog', customClass]"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
  >
    <!-- 对话框内容 -->
    <div v-loading="loading" class="base-dialog-body">
      <slot></slot>
    </div>

    <!-- 对话框底部 -->
    <template #footer>
      <div class="base-dialog-footer">
        <slot name="footer">
          <el-button @click="handleCancel">{{ cancelText }}</el-button>
          <el-button
            type="primary"
            :loading="confirmLoading"
            :disabled="confirmDisabled"
            @click="handleConfirm"
          >
            {{ confirmText }}
          </el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  // 是否显示对话框（v-model）
  modelValue: boolean
  // 对话框标题
  title?: string
  // 对话框宽度
  width?: string | number
  // 是否全屏
  fullscreen?: boolean
  // 距离顶部的距离
  top?: string
  // 是否显示遮罩
  modal?: boolean
  // 是否插入到body
  appendToBody?: boolean
  // 是否锁定body滚动
  lockScroll?: boolean
  // 是否可以通过点击遮罩关闭
  closeOnClickModal?: boolean
  // 是否可以通过ESC关闭
  closeOnPressEscape?: boolean
  // 是否显示关闭按钮
  showClose?: boolean
  // 是否可拖拽
  draggable?: boolean
  // 关闭时是否销毁内容
  destroyOnClose?: boolean
  // 自定义类名
  customClass?: string
  // 内容加载状态
  loading?: boolean
  // 确认按钮文本
  confirmText?: string
  // 取消按钮文本
  cancelText?: string
  // 确认按钮加载状态
  confirmLoading?: boolean
  // 确认按钮禁用状态
  confirmDisabled?: boolean
  // 关闭前的钩子函数
  beforeClose?: (done: () => void) => void
  // 确认回调
  onConfirm?: () => void | Promise<void>
  // 取消回调
  onCancel?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  width: '50%',
  fullscreen: false,
  top: '15vh',
  modal: true,
  appendToBody: false,
  lockScroll: true,
  closeOnClickModal: true,
  closeOnPressEscape: true,
  showClose: true,
  draggable: false,
  destroyOnClose: false,
  loading: false,
  confirmText: '确定',
  cancelText: '取消',
  confirmLoading: false,
  confirmDisabled: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'open': []
  'opened': []
  'close': []
  'closed': []
  'confirm': []
  'cancel': []
}>()

// 计算属性：对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 处理关闭前的逻辑
const handleBeforeClose = (done: () => void) => {
  if (props.beforeClose) {
    props.beforeClose(done)
  } else {
    done()
  }
}

// 处理取消
const handleCancel = () => {
  if (props.onCancel) {
    props.onCancel()
  }
  emit('cancel')
  visible.value = false
}

// 处理确认
const handleConfirm = async () => {
  if (props.onConfirm) {
    try {
      await props.onConfirm()
      emit('confirm')
      visible.value = false
    } catch (error: any) {
      console.error('确认操作失败:', error)
      ElMessage.error(error.message || '操作失败')
    }
  } else {
    emit('confirm')
    visible.value = false
  }
}

// 生命周期钩子
const handleOpen = () => emit('open')
const handleOpened = () => emit('opened')
const handleClose = () => emit('close')
const handleClosed = () => emit('closed')
</script>

<style scoped>
.base-dialog {
  border-radius: 8px;
}

.base-dialog-body {
  min-height: 100px;
}

.base-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebeef5;
  padding: 20px;
  margin-right: 0;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 15px 20px;
}
</style>