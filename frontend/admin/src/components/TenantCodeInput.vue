<template>
  <div class="tenant-code-input">
    <el-input
      v-model="localValue"
      placeholder="请输入机构代码，如：PLATFORM"
      size="large"
      prefix-icon="Building"
      clearable
      style="text-transform: uppercase"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      :loading="loading"
    />
    
    <!-- 下拉建议列表 -->
    <div v-if="showSuggestions && suggestions.length > 0" class="suggestions-dropdown">
      <div
        v-for="suggestion in suggestions"
        :key="suggestion.code"
        class="suggestion-item"
        @click="selectSuggestion(suggestion)"
      >
        <div class="suggestion-code">{{ suggestion.code }}</div>
        <div class="suggestion-name">{{ suggestion.name }}</div>
        <div class="suggestion-level">{{ suggestion.level }}</div>
      </div>
    </div>
    
    <!-- 状态提示 -->
    <div v-if="hint" class="hint-message" :class="{
      'hint-success': hint.startsWith('✓'),
      'hint-info': hint.startsWith('💡'),
      'hint-error': hint.startsWith('⚠️')
    }">
      {{ hint }}
    </div>
    
    <!-- 没有建议时的帮助信息 -->
    <div v-else-if="!loading && localValue.length === 0" class="hint-message hint-help">
      <div>系统：PLATFORM · 政府：SH_HQ (上海) · 医疗：HOSP_RJ (瑞金医院)</div>
      <div>养老：CARE_CXM (椿萱茂) · 保险：INS_ZGRS (中国人寿)</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { getTenantSuggestions, validateTenantCode, type TenantInfo } from '@/api/tenant';

// Props
interface Props {
  modelValue: string;
  placeholder?: string;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入机构代码',
  disabled: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'validated': [tenant: TenantInfo | null];
}>();

// 响应式数据
const localValue = ref(props.modelValue);
const loading = ref(false);
const showSuggestions = ref(false);
const suggestions = ref<TenantInfo[]>([]);
const hint = ref('');
const focused = ref(false);

// 防抖定时器
let debounceTimer: NodeJS.Timeout | null = null;
let validationTimer: NodeJS.Timeout | null = null;

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  localValue.value = newValue;
});

// 监听本地值变化
watch(localValue, (newValue) => {
  emit('update:modelValue', newValue);
});

// 处理输入
const handleInput = (value: string) => {
  const upperValue = value.toUpperCase();
  localValue.value = upperValue;
  
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  if (validationTimer) {
    clearTimeout(validationTimer);
  }
  
  // 重置状态
  hint.value = '';
  emit('validated', null);
  
  if (upperValue.length >= 2) {
    // 搜索建议（防抖300ms）
    debounceTimer = setTimeout(() => {
      searchSuggestions(upperValue);
    }, 300);
    
    // 精确验证（防抖800ms）
    validationTimer = setTimeout(() => {
      validateInput(upperValue);
    }, 800);
  } else {
    showSuggestions.value = false;
    suggestions.value = [];
  }
};

// 搜索建议
const searchSuggestions = async (query: string) => {
  if (!focused.value) return;
  
  try {
    loading.value = true;
    const results = await getTenantSuggestions(query, 8);
    
    if (focused.value) {
      suggestions.value = results;
      showSuggestions.value = results.length > 0;
    }
  } catch (error) {
    console.warn('搜索租户建议失败:', error);
    suggestions.value = [];
    showSuggestions.value = false;
  } finally {
    loading.value = false;
  }
};

// 验证输入
const validateInput = async (tenantCode: string) => {
  try {
    const tenant = await validateTenantCode(tenantCode);
    hint.value = `✓ ${tenant.name} (${tenant.level})`;
    emit('validated', tenant);
  } catch (error) {
    // 如果精确匹配失败，检查是否有建议
    if (suggestions.value.length > 0) {
      const exactMatch = suggestions.value.find(s => s.code === tenantCode);
      if (exactMatch) {
        hint.value = `✓ ${exactMatch.name} (${exactMatch.level})`;
        emit('validated', exactMatch);
      } else {
        hint.value = `💡 您是否想输入: ${suggestions.value.slice(0, 3).map(s => s.code).join(', ')}`;
      }
    } else {
      hint.value = '⚠️ 机构代码不存在，请检查输入';
      emit('validated', null);
    }
  }
};

// 选择建议
const selectSuggestion = (suggestion: TenantInfo) => {
  localValue.value = suggestion.code;
  hint.value = `✓ ${suggestion.name} (${suggestion.level})`;
  showSuggestions.value = false;
  emit('validated', suggestion);
};

// 获得焦点
const handleFocus = () => {
  focused.value = true;
  if (localValue.value.length >= 2) {
    searchSuggestions(localValue.value);
  }
};

// 失去焦点
const handleBlur = () => {
  focused.value = false;
  // 延迟隐藏建议，允许点击选择
  setTimeout(() => {
    if (!focused.value) {
      showSuggestions.value = false;
    }
  }, 200);
};
</script>

<style scoped>
.tenant-code-input {
  position: relative;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid #f5f7fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background-color: #f5f7fa;
}

.suggestion-code {
  font-weight: bold;
  color: #409eff;
  min-width: 100px;
}

.suggestion-name {
  flex: 1;
  color: #606266;
}

.suggestion-level {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 12px;
}

.hint-message {
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

.hint-success {
  color: #67c23a;
}

.hint-info {
  color: #409eff;
}

.hint-error {
  color: #f56c6c;
}

.hint-help {
  color: #909399;
}
</style>