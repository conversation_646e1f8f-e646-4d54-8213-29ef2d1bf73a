<template>
  <div class="role-based-dashboard">
    <!-- 用户角色信息卡片 -->
    <div class="user-role-card mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div :class="`role-badge role-${userRole.color}`">
            {{ userRole.name }}
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-semibold text-gray-800">{{ currentUser.firstName }} {{ currentUser.lastName }}</h3>
            <p class="text-sm text-gray-600">{{ userRole.description }}</p>
          </div>
        </div>
        <div class="text-right">
          <p class="text-sm text-gray-500">当前机构</p>
          <p class="font-medium text-gray-800">
            {{ currentUser.tenantName || '系统管理' }}
          </p>
        </div>
      </div>
    </div>

    <!-- 系统总管理员仪表板 -->
    <div v-if="isSuperAdmin" class="super-admin-dashboard">
      <h2 class="text-xl font-bold text-red-600 mb-4 flex items-center">
        🔧 系统总管理员控制台
      </h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="stat-card bg-red-50 border-red-200">
          <div class="stat-icon text-red-500">🏢</div>
          <div class="stat-content">
            <div class="stat-title">租户总数</div>
            <div class="stat-value text-red-600">12</div>
          </div>
        </div>
        
        <div class="stat-card bg-blue-50 border-blue-200">
          <div class="stat-icon text-blue-500">👥</div>
          <div class="stat-content">
            <div class="stat-title">用户总数</div>
            <div class="stat-value text-blue-600">156</div>
          </div>
        </div>
        
        <div class="stat-card bg-green-50 border-green-200">
          <div class="stat-icon text-green-500">📋</div>
          <div class="stat-content">
            <div class="stat-title">评估总数</div>
            <div class="stat-value text-green-600">2,345</div>
          </div>
        </div>
        
        <div class="stat-card bg-purple-50 border-purple-200">
          <div class="stat-icon text-purple-500">⚖️</div>
          <div class="stat-content">
            <div class="stat-title">量表总数</div>
            <div class="stat-value text-purple-600">28</div>
          </div>
        </div>
      </div>

      <div class="admin-actions">
        <h3 class="text-lg font-semibold mb-3">系统管理功能</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="action-card" @click="navigateTo('tenant-manage')">
            <div class="action-icon">🏢</div>
            <div class="action-content">
              <h4>租户管理</h4>
              <p>创建、配置和管理所有租户</p>
            </div>
          </div>
          
          <div class="action-card" @click="navigateTo('system-users')">
            <div class="action-icon">👥</div>
            <div class="action-content">
              <h4>用户管理</h4>
              <p>管理平台所有用户账户</p>
            </div>
          </div>
          
          <div class="action-card" @click="navigateTo('system-scales')">
            <div class="action-icon">⚖️</div>
            <div class="action-content">
              <h4>量表管理</h4>
              <p>管理系统级评估量表</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 机构管理员仪表板 -->
    <div v-else-if="isTenantAdmin" class="tenant-admin-dashboard">
      <h2 class="text-xl font-bold text-blue-600 mb-4 flex items-center">
        🏥 机构管理控制台
      </h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <div class="stat-card bg-blue-50 border-blue-200">
          <div class="stat-icon text-blue-500">👥</div>
          <div class="stat-content">
            <div class="stat-title">机构用户</div>
            <div class="stat-value text-blue-600">24</div>
          </div>
        </div>
        
        <div class="stat-card bg-green-50 border-green-200">
          <div class="stat-icon text-green-500">📋</div>
          <div class="stat-content">
            <div class="stat-title">本月评估</div>
            <div class="stat-value text-green-600">156</div>
          </div>
        </div>
        
        <div class="stat-card bg-orange-50 border-orange-200">
          <div class="stat-icon text-orange-500">⏳</div>
          <div class="stat-content">
            <div class="stat-title">待审核</div>
            <div class="stat-value text-orange-600">8</div>
          </div>
        </div>
      </div>

      <div class="admin-actions">
        <h3 class="text-lg font-semibold mb-3">机构管理功能</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-if="hasPermission('USER_MANAGE')" class="action-card" @click="navigateTo('user-manage')">
            <div class="action-icon">👥</div>
            <div class="action-content">
              <h4>用户管理</h4>
              <p>管理机构内用户账户</p>
            </div>
          </div>
          
          <div v-if="hasPermission('ASSESSMENT_ALL')" class="action-card" @click="navigateTo('assessment-manage')">
            <div class="action-icon">📋</div>
            <div class="action-content">
              <h4>评估管理</h4>
              <p>查看和管理评估记录</p>
            </div>
          </div>
          
          <div v-if="hasPermission('REPORT_ALL')" class="action-card" @click="navigateTo('report-manage')">
            <div class="action-icon">📊</div>
            <div class="action-content">
              <h4>数据报告</h4>
              <p>查看统计报告和数据</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 普通用户仪表板 -->
    <div v-else class="user-dashboard">
      <h2 class="text-xl font-bold text-gray-600 mb-4">工作台</h2>
      
      <div class="user-actions">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div v-if="hasPermission('ASSESSMENT_CREATE')" class="action-card" @click="navigateTo('assessment-create')">
            <div class="action-icon">➕</div>
            <div class="action-content">
              <h4>新建评估</h4>
              <p>创建新的评估记录</p>
            </div>
          </div>
          
          <div v-if="hasPermission('ASSESSMENT_READ')" class="action-card" @click="navigateTo('assessment-view')">
            <div class="action-icon">📋</div>
            <div class="action-content">
              <h4>我的评估</h4>
              <p>查看我的评估记录</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限不足提示 -->
    <div v-if="!currentUser" class="no-permission">
      <div class="text-center py-12">
        <div class="text-6xl mb-4">🔒</div>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">权限不足</h3>
        <p class="text-gray-500">您没有访问此页面的权限，请联系管理员</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { 
  getCurrentUser, 
  isSuperAdmin as checkIsSuperAdmin, 
  isTenantAdmin as checkIsTenantAdmin,
  hasPermission as checkHasPermission,
  getUserRole 
} from '@/utils/permission'

const router = useRouter()

// 计算属性
const currentUser = computed(() => getCurrentUser())
const isSuperAdmin = computed(() => checkIsSuperAdmin())
const isTenantAdmin = computed(() => checkIsTenantAdmin())
const userRole = computed(() => getUserRole())

// 权限检查函数
const hasPermission = (permission) => {
  return checkHasPermission(permission)
}

// 导航函数
const navigateTo = (routeName) => {
  router.push({ name: routeName })
}
</script>

<style scoped>
/* 用户角色卡片 */
.user-role-card {
  @apply bg-white rounded-lg shadow-sm border p-6;
}

.role-badge {
  @apply px-3 py-1 rounded-full text-sm font-medium;
}

.role-red {
  @apply bg-red-100 text-red-800;
}

.role-blue {
  @apply bg-blue-100 text-blue-800;
}

.role-gray {
  @apply bg-gray-100 text-gray-800;
}

/* 统计卡片 */
.stat-card {
  @apply bg-white rounded-lg border p-4 flex items-center;
}

.stat-icon {
  @apply text-2xl mr-4;
}

.stat-content .stat-title {
  @apply text-sm text-gray-600;
}

.stat-content .stat-value {
  @apply text-2xl font-bold;
}

/* 功能卡片 */
.action-card {
  @apply bg-white rounded-lg shadow-sm border p-4 cursor-pointer transition-all duration-200 hover:shadow-md hover:border-blue-300 flex items-start;
}

.action-icon {
  @apply text-2xl mr-4 mt-1;
}

.action-content h4 {
  @apply font-semibold text-gray-800 mb-1;
}

.action-content p {
  @apply text-sm text-gray-600;
}

/* 权限不足 */
.no-permission {
  @apply bg-white rounded-lg shadow-sm border;
}
</style>