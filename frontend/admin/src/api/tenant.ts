import request from '@/utils/request';

export interface TenantInfo {
  code: string;
  name: string;
  industry: string;
  subscriptionPlan: string;
  level: string;
}

/**
 * 获取所有活跃租户信息（仅用于小规模场景）
 * @deprecated 建议使用 getTenantSuggestions 替代
 */
export async function getTenantList(): Promise<TenantInfo[]> {
  const response = await request({
    url: '/api/public/tenants/info',
    method: 'get',
  });
  return response.data;
}

/**
 * 搜索租户建议（推荐用于大规模场景）
 * @param query 搜索关键词
 * @param limit 返回结果数量限制
 */
export async function getTenantSuggestions(query: string = '', limit: number = 10): Promise<TenantInfo[]> {
  const response = await request({
    url: '/api/public/tenants/suggestions',
    method: 'get',
    params: { query, limit }
  });
  return response.data;
}

/**
 * 快速验证租户代码
 * @param tenantCode 租户代码
 */
export async function validateTenantCode(tenantCode: string): Promise<TenantInfo> {
  const response = await request({
    url: `/api/public/tenants/validate/${tenantCode}`,
    method: 'get',
  });
  return response.data;
}

/**
 * 检查租户代码是否存在（保持向后兼容）
 * @deprecated 建议使用 validateTenantCode 替代
 */
export async function checkTenantCode(tenantCode: string): Promise<TenantInfo> {
  return validateTenantCode(tenantCode);
}