/**
 * 多租户API适配器
 * 将现有的单租户API调用适配为多租户API调用
 * 保持现有组件接口不变，实现平滑迁移
 */

import request from '@/utils/request';
import tenantContext from '@/utils/tenantContext';
import type { TenantInfo, UserInfo } from '@/utils/tenantContext';
import type { AxiosResponse } from 'axios';

// ========================= 通用类型定义 =========================

// 通用API响应包装器
interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data: T;
}

interface PaginationParams {
  page?: number;
  size?: number;
}

interface PaginatedResponse<T> {
  success: boolean;
  message?: string;
  data: {
    content: T[];
    totalElements: number;
    totalPages: number;
    size: number;
    number: number;
    first: boolean;
    last: boolean;
    empty: boolean;
  };
}

// ========================= 认证相关类型 =========================

interface LoginRequest {
  tenantCode: string;
  username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  message?: string;
  data: {
    token: string;
    refreshToken: string;
    user: UserInfo;
    tenant: TenantInfo;
    membership: {
      role: string;
      permissions: string[];
    };
    expiresIn: number;
  };
}

interface RefreshResponse {
  token: string;
  expiresIn: number;
}

// ========================= 量表相关类型 =========================

export interface AssessmentScale {
  id: string;
  code: string;
  name: string;
  category: string;
  version: string;
  description?: string;
  formSchema: Record<string, any>;
  scoringRules: Record<string, any>;
  validationRules?: Record<string, any>;
  reportTemplate?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DEPRECATED' | 'UNDER_REVIEW';
  visibility: 'PUBLIC' | 'PRIVATE' | 'PREMIUM';
  publisherType: 'PLATFORM' | 'TENANT' | 'PARTNER';
  publisherId?: string;
  isOfficial: boolean;
  isVerified: boolean;
  usageCount: number;
  rating?: number;
  ratingCount: number;
  price?: number;
  keywords?: string[];
  industryTags?: string[];
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  deprecatedAt?: string;
}

interface ScaleListParams extends PaginationParams {
  category?: string;
  search?: string;
  status?: AssessmentScale['status'];
  visibility?: AssessmentScale['visibility'];
  publisherType?: AssessmentScale['publisherType'];
  sortField?: string;
  sortOrder?: 'ASC' | 'DESC';
}

interface ScaleUsageStats {
  usageCount: number;
  lastUsed: string | null;
}

// ========================= 评估记录相关类型 =========================

interface AssessmentRecord {
  id: string;
  recordNumber: string;
  tenantId: string;
  subjectId: string;
  scaleId: string;
  assessorId: string;
  reviewerId?: string;
  assessmentType: 'REGULAR' | 'EMERGENCY' | 'FOLLOWUP' | 'REASSESSMENT';
  scaleType: 'GLOBAL' | 'CUSTOM';
  status: 'DRAFT' | 'SUBMITTED' | 'REVIEWED' | 'APPROVED' | 'REJECTED' | 'ARCHIVED';
  assessmentDate: string;
  formData: Record<string, any>;
  scoreData?: Record<string, any>;
  resultDetails?: Record<string, any>;
  totalScore?: number;
  resultLevel?: string;
  completenessScore?: number;
  qualityScore?: number;
  workflowStage?: string;
  reviewNotes?: string;
  reviewedAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

interface RecordListParams extends PaginationParams {
  subjectId?: string;
  scaleId?: string;
  assessorId?: string;
  status?: AssessmentRecord['status'];
  startDate?: string;
  endDate?: string;
  sortField?: string;
  sortOrder?: 'ASC' | 'DESC';
}

// 系统级评估记录查询参数
interface SystemAssessmentListParams extends PaginationParams {
  tenantId?: string;
  status?: AssessmentRecord['status'];
  startDate?: string;
  endDate?: string;
  sortField?: string;
  sortOrder?: 'ASC' | 'DESC';
}

interface CreateRecordRequest {
  subjectId?: string;
  elderlyId?: string;
  scaleId: string;
  assessorId?: string;
  formData: Record<string, any>;
}

interface AssessmentResult {
  totalScore: number;
  resultLevel: string;
  formData: Record<string, any>;
}

// ========================= 评估对象相关类型 =========================

interface AssessmentSubject {
  id: string;
  tenantId: string;
  name: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  birthDate?: string;
  idNumber?: string;
  phone?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  currentCareLevel?: string;
  medicalInsuranceNumber?: string;
  address?: string;
  medicalHistory?: string;
  medications?: string;
  allergies?: string;
  careNotes?: string;
  customFields?: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

interface SubjectListParams extends PaginationParams {
  search?: string;
  isActive?: boolean;
}

interface CreateSubjectRequest {
  name: string;
  gender: AssessmentSubject['gender'];
  birthDate?: string;
  idNumber?: string;
  contactPhone?: string;
  contactAddress?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  medicalHistory?: string;
  personalInfo?: Record<string, any>;
}

// ========================= 租户管理相关类型 =========================

interface TenantListParams extends PaginationParams {
  search?: string;
  status?: TenantInfo['status'];
  sortField?: string;
  sortOrder?: 'ASC' | 'DESC';
}

// ========================= 系统统计相关类型 =========================

interface SystemOverview {
  totalTenants: number;
  activeTenants: number;
  totalUsers: number;
  activeUsers: number;
  totalAssessments: number;
  pendingReviews: number;
  systemLoad: number;
  storageUsage: number;
}

interface TenantStats {
  totalTenants: number;
  activeTenants: number;
  trialTenants: number;
  expiringSoon: number;
  newThisMonth: number;
}

interface UserStats {
  totalUsers: number;
  activeUsers: number;
  newThisMonth: number;
  averageSessionTime: number;
}

interface AssessmentStats {
  totalAssessments: number;
  completedToday: number;
  pendingReview: number;
  averageCompletionTime: number;
  popularScales: Array<{
    scaleId: string;
    scaleName: string;
    usageCount: number;
  }>;
}

interface ScaleStats {
  totalScales: number;
  publicScales: number;
  premiumScales: number;
  averageRating: number;
}

interface SystemPerformance {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkLoad: number;
  responseTime: number;
  uptime: number;
}

interface RecentActivity {
  id: string;
  type: string;
  description: string;
  userId: string;
  userName: string;
  tenantId?: string;
  tenantName?: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

// ========================= API实现 =========================

/**
 * 多租户认证API
 */
export const multiTenantAuthApi = {
  // 多租户登录
  login: (data: LoginRequest): Promise<AxiosResponse<LoginResponse>> => {
    return request({
      url: '/api/multi-tenant/auth/login',
      method: 'post',
      data
    });
  },

  // 退出登录
  logout: (): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: '/api/multi-tenant/auth/logout',
      method: 'post'
    });
  },

  // 刷新令牌
  refresh: (): Promise<AxiosResponse<RefreshResponse>> => {
    return request({
      url: '/api/multi-tenant/auth/refresh',
      method: 'post'
    });
  },

  // 获取用户信息
  getProfile: (): Promise<AxiosResponse<UserInfo>> => {
    return request({
      url: '/api/multi-tenant/auth/profile',
      method: 'get'
    });
  }
};

/**
 * 多租户量表管理API适配器
 * 适配原有的 assessmentScaleApi
 */
export const createScaleApiAdapter = () => {
  return {
    // 获取量表列表 - 适配为获取全局公开量表
    getScales: (params: ScaleListParams = {}): Promise<AxiosResponse<PaginatedResponse<AssessmentScale>>> => {
      return request({
        url: '/api/multi-tenant/scales/public',
        method: 'get',
        params: {
          page: params.page || 0,
          size: params.size || 20,
          category: params.category,
          search: params.search
        }
      });
    },

    // 获取量表详情
    getScale: (id: string): Promise<AxiosResponse<AssessmentScale>> => {
      return request({
        url: `/api/multi-tenant/scales/${id}`,
        method: 'get'
      });
    },

    // 创建量表 - 需要管理员权限，暂时返回错误提示
    createScale: (_data: Partial<AssessmentScale>): Promise<never> => {
      return Promise.reject(new Error('创建量表功能需要系统管理员权限'));
    },

    // 更新量表 - 需要管理员权限，暂时返回错误提示
    updateScale: (_id: string, _data: Partial<AssessmentScale>): Promise<never> => {
      return Promise.reject(new Error('更新量表功能需要系统管理员权限'));
    },

    // 删除量表 - 需要管理员权限，暂时返回错误提示
    deleteScale: (_id: string): Promise<never> => {
      return Promise.reject(new Error('删除量表功能需要系统管理员权限'));
    },

    // 激活/停用量表 - 需要管理员权限，暂时返回错误提示
    toggleScale: (_id: string, _isActive: boolean): Promise<never> => {
      return Promise.reject(new Error('量表状态管理功能需要系统管理员权限'));
    },

    // 获取量表使用统计 - 记录使用次数
    getUsageStats: (id: string): Promise<AxiosResponse<ScaleUsageStats>> => {
      // 记录使用次数
      request({
        url: `/api/multi-tenant/scales/${id}/usage`,
        method: 'post'
      }).catch(e => console.warn('Failed to record scale usage:', e));
      
      // 返回模拟的统计数据
      return Promise.resolve({
        data: {
          usageCount: 0,
          lastUsed: null
        }
      } as AxiosResponse<ScaleUsageStats>);
    }
  };
};

/**
 * 多租户评估记录API适配器
 * 适配原有的 assessmentRecordApi
 */
export const createRecordApiAdapter = () => {
  const getTenantId = (): string => {
    const tenantId = tenantContext.getCurrentTenantId();
    if (!tenantId) {
      throw new Error('未设置当前租户，请先登录');
    }
    return tenantId;
  };

  return {
    // 获取评估记录列表
    getRecords: (params: RecordListParams = {}): Promise<AxiosResponse<PaginatedResponse<AssessmentRecord>>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments`,
        method: 'get',
        params: {
          page: params.page || 0,
          size: params.size || 20,
          subjectId: params.subjectId,
          scaleId: params.scaleId,
          assessorId: params.assessorId,
          status: params.status
        }
      });
    },

    // 获取评估记录详情
    getRecord: (id: string): Promise<AxiosResponse<AssessmentRecord>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${id}`,
        method: 'get'
      });
    },

    // 创建评估记录
    createRecord: (data: CreateRecordRequest): Promise<AxiosResponse<AssessmentRecord>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments`,
        method: 'post',
        data: {
          elderlyId: data.subjectId || data.elderlyId, // 兼容字段名
          scaleId: data.scaleId,
          assessorId: data.assessorId || tenantContext.getCurrentUserId(),
          formData: data.formData
        }
      });
    },

    // 更新评估记录
    updateRecord: (id: string, data: Partial<AssessmentRecord>): Promise<AxiosResponse<AssessmentRecord>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${id}`,
        method: 'put',
        data
      });
    },

    // 提交评估记录
    submitRecord: (id: string): Promise<AxiosResponse<ApiResponse<null>>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${id}/submit`,
        method: 'post'
      });
    },

    // 获取评估结果
    getResults: (recordId: string): Promise<AxiosResponse<AssessmentResult>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${recordId}`,
        method: 'get'
      }).then((response: AxiosResponse<AssessmentRecord>) => ({
        ...response,
        data: {
          totalScore: response.data.totalScore || 0,
          resultLevel: response.data.resultLevel || '',
          formData: response.data.formData
        }
      }));
    },

    // 导出评估报告
    exportReport: (recordId: string, format: string = 'pdf'): Promise<AxiosResponse<Blob>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${recordId}/export`,
        method: 'get',
        params: { format },
        responseType: 'blob'
      });
    }
  };
};

/**
 * 多租户评估对象API适配器
 * 适配原有的 elderlyApi
 */
export const createSubjectApiAdapter = () => {
  const getTenantId = (): string => {
    const tenantId = tenantContext.getCurrentTenantId();
    if (!tenantId) {
      throw new Error('未设置当前租户，请先登录');
    }
    return tenantId;
  };

  return {
    // 获取评估对象列表
    getElderly: (params: SubjectListParams = {}): Promise<AxiosResponse<PaginatedResponse<AssessmentSubject>>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects`,
        method: 'get',
        params: {
          page: params.page || 0,
          size: params.size || 20,
          search: params.search,
          isActive: params.isActive
        }
      });
    },

    // 获取评估对象详情
    getElderlyById: (id: string): Promise<AxiosResponse<AssessmentSubject>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects/${id}`,
        method: 'get'
      });
    },

    // 创建评估对象档案
    createElderly: (data: CreateSubjectRequest): Promise<AxiosResponse<AssessmentSubject>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects`,
        method: 'post',
        data: {
          name: data.name,
          gender: data.gender,
          birthDate: data.birthDate,
          idNumber: data.idNumber,
          contactPhone: data.contactPhone,
          contactAddress: data.contactAddress,
          emergencyContactName: data.emergencyContactName,
          emergencyContactPhone: data.emergencyContactPhone,
          medicalHistory: data.medicalHistory,
          personalInfo: data.personalInfo || {}
        }
      });
    },

    // 更新评估对象信息
    updateElderly: (id: string, data: Partial<AssessmentSubject>): Promise<AxiosResponse<AssessmentSubject>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects/${id}`,
        method: 'put',
        data
      });
    },

    // 删除评估对象档案
    deleteElderly: (id: string): Promise<AxiosResponse<ApiResponse<null>>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects/${id}`,
        method: 'delete'
      });
    },

    // 获取评估对象的评估历史
    getAssessmentHistory: (elderlyId: string): Promise<AxiosResponse<AssessmentRecord[]>> => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects/${elderlyId}/history`,
        method: 'get'
      });
    }
  };
};

/**
 * 租户管理API
 */
export const tenantManageApi = {
  // 获取租户列表（仅系统管理员）
  getTenants: (params: TenantListParams = {}): Promise<AxiosResponse<PaginatedResponse<TenantInfo>>> => {
    return request({
      url: '/api/system/tenants',
      method: 'get',
      params: {
        page: params.page || 0,
        size: params.size || 20,
        search: params.search,
        status: params.status,
        sortField: params.sortField,
        sortOrder: params.sortOrder
      }
    });
  },

  // 获取租户详情
  getTenant: (id: string): Promise<AxiosResponse<TenantInfo>> => {
    return request({
      url: `/api/system/tenants/${id}`,
      method: 'get'
    });
  },

  // 创建租户
  createTenant: (data: Partial<TenantInfo>): Promise<AxiosResponse<TenantInfo>> => {
    return request({
      url: '/api/system/tenants',
      method: 'post',
      data
    });
  },

  // 更新租户信息
  updateTenant: (id: string, data: Partial<TenantInfo>): Promise<AxiosResponse<TenantInfo>> => {
    return request({
      url: `/api/system/tenants/${id}`,
      method: 'put',
      data
    });
  },

  // 启用/禁用租户
  toggleTenant: (id: string, isActive: boolean): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: `/api/system/tenants/${id}/toggle`,
      method: 'patch',
      data: { isActive }
    });
  }
};

/**
 * 系统量表管理API
 */
export const systemScaleApi = {
  // 获取量表列表
  getScales: (params: ScaleListParams = {}): Promise<AxiosResponse<PaginatedResponse<AssessmentScale>>> => {
    return request({
      url: '/api/system/scales',
      method: 'get',
      params: {
        page: params.page || 0,
        size: params.size || 20,
        search: params.search,
        category: params.category,
        status: params.status,
        visibility: params.visibility,
        publisherType: params.publisherType,
        sortField: params.sortField,
        sortOrder: params.sortOrder
      }
    });
  },

  // 获取量表详情
  getScale: (id: string): Promise<AxiosResponse<AssessmentScale>> => {
    return request({
      url: `/api/system/scales/${id}`,
      method: 'get'
    });
  },

  // 创建量表
  createScale: (data: Partial<AssessmentScale>): Promise<AxiosResponse<AssessmentScale>> => {
    return request({
      url: '/api/system/scales',
      method: 'post',
      data
    });
  },

  // 更新量表
  updateScale: (id: string, data: Partial<AssessmentScale>): Promise<AxiosResponse<AssessmentScale>> => {
    return request({
      url: `/api/system/scales/${id}`,
      method: 'put',
      data
    });
  },

  // 删除量表
  deleteScale: (id: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: `/api/system/scales/${id}`,
      method: 'delete'
    });
  },

  // 发布量表
  publishScale: (id: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: `/api/system/scales/${id}/publish`,
      method: 'post'
    });
  },

  // 弃用量表
  deprecateScale: (id: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: `/api/system/scales/${id}/deprecate`,
      method: 'post'
    });
  },

  // 获取量表统计
  getScaleStats: (): Promise<AxiosResponse<ScaleStats>> => {
    return request({
      url: '/api/system/scales/stats',
      method: 'get'
    });
  },

  // 获取量表分类
  getCategories: (): Promise<AxiosResponse<string[]>> => {
    return request({
      url: '/api/system/scales/categories',
      method: 'get'
    });
  }
};

/**
 * 系统评估记录管理API
 */
export const systemAssessmentApi = {
  // 获取评估记录列表
  getAssessments: (params: SystemAssessmentListParams = {}): Promise<AxiosResponse<PaginatedResponse<AssessmentRecord>>> => {
    return request({
      url: '/api/system/assessments',
      method: 'get',
      params: {
        page: params.page || 0,
        size: params.size || 20,
        tenantId: params.tenantId,
        status: params.status,
        startDate: params.startDate,
        endDate: params.endDate,
        sortField: params.sortField,
        sortOrder: params.sortOrder
      }
    });
  },

  // 获取评估记录详情
  getAssessment: (id: string): Promise<AxiosResponse<AssessmentRecord>> => {
    return request({
      url: `/api/system/assessments/${id}`,
      method: 'get'
    });
  },

  // 审核评估记录
  reviewAssessment: (id: string, data: {
    approved: boolean;
    reviewNotes: string;
    reviewerId: string;
  }): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: `/api/system/assessments/${id}/review`,
      method: 'post',
      data: {
        approved: data.approved,
        reviewNotes: data.reviewNotes,
        reviewerId: data.reviewerId
      }
    });
  },

  // 批量审核评估记录
  batchReviewAssessments: (data: {
    recordIds: string[];
    approved: boolean;
    reviewNotes: string;
    reviewerId: string;
  }): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: '/api/system/assessments/batch-review',
      method: 'post',
      data: {
        recordIds: data.recordIds,
        approved: data.approved,
        reviewNotes: data.reviewNotes,
        reviewerId: data.reviewerId
      }
    });
  },

  // 归档评估记录
  archiveAssessment: (id: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: `/api/system/assessments/${id}/archive`,
      method: 'post'
    });
  },

  // 删除评估记录
  deleteAssessment: (id: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: `/api/system/assessments/${id}`,
      method: 'delete'
    });
  },

  // 获取评估统计
  getAssessmentStats: (params: {
    tenantId?: string;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<AxiosResponse<AssessmentStats>> => {
    return request({
      url: '/api/system/assessments/stats',
      method: 'get',
      params: {
        tenantId: params.tenantId,
        startDate: params.startDate,
        endDate: params.endDate
      }
    });
  },

  // 获取待审核记录
  getPendingReviewRecords: (tenantId?: string): Promise<AxiosResponse<AssessmentRecord[]>> => {
    return request({
      url: '/api/system/assessments/pending-review',
      method: 'get',
      params: { tenantId }
    });
  }
};

/**
 * 平台用户管理API
 */
export const platformUserApi = {
  // 获取平台用户列表
  getUsers: (params: {
    page?: number;
    size?: number;
    search?: string;
    tenantId?: string;
    role?: string;
    sortField?: string;
    sortOrder?: 'ASC' | 'DESC';
  } = {}): Promise<AxiosResponse<PaginatedResponse<UserInfo>>> => {
    return request({
      url: '/api/system/users',
      method: 'get',
      params: {
        page: params.page || 0,
        size: params.size || 20,
        search: params.search,
        tenantId: params.tenantId,
        role: params.role,
        sortField: params.sortField,
        sortOrder: params.sortOrder
      }
    });
  },

  // 获取用户详情
  getUser: (id: string): Promise<AxiosResponse<UserInfo>> => {
    return request({
      url: `/api/system/users/${id}`,
      method: 'get'
    });
  },

  // 创建用户
  createUser: (data: Partial<UserInfo>): Promise<AxiosResponse<UserInfo>> => {
    return request({
      url: '/api/system/users',
      method: 'post',
      data
    });
  },

  // 更新用户信息
  updateUser: (id: string, data: Partial<UserInfo>): Promise<AxiosResponse<UserInfo>> => {
    return request({
      url: `/api/system/users/${id}`,
      method: 'put',
      data
    });
  },

  // 重置用户密码
  resetPassword: (id: string): Promise<AxiosResponse<{ newPassword: string }>> => {
    return request({
      url: `/api/system/users/${id}/reset-password`,
      method: 'post'
    });
  },

  // 管理租户用户关系
  manageTenantMembership: (userId: string, tenantId: string, data: {
    role: UserInfo['tenantRole'];
    permissions: string[];
    isActive: boolean;
  }): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: `/api/system/users/${userId}/tenants/${tenantId}`,
      method: 'put',
      data: {
        role: data.role,
        permissions: data.permissions,
        isActive: data.isActive
      }
    });
  },

  // 移除租户用户关系
  removeTenantMembership: (userId: string, tenantId: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return request({
      url: `/api/system/users/${userId}/tenants/${tenantId}`,
      method: 'delete'
    });
  },

  // 获取用户统计
  getUserStats: (): Promise<AxiosResponse<UserStats>> => {
    return request({
      url: '/api/system/users/stats',
      method: 'get'
    });
  }
};

/**
 * 系统监控面板API
 */
export const systemDashboardApi = {
  // 获取系统概览
  getSystemOverview: (): Promise<AxiosResponse<SystemOverview>> => {
    return request({
      url: '/api/system/dashboard/overview',
      method: 'get'
    });
  },

  // 获取租户统计
  getTenantStats: (): Promise<AxiosResponse<TenantStats>> => {
    return request({
      url: '/api/system/dashboard/tenant-stats',
      method: 'get'
    });
  },

  // 获取用户统计
  getUserStats: (): Promise<AxiosResponse<UserStats>> => {
    return request({
      url: '/api/system/dashboard/user-stats',
      method: 'get'
    });
  },

  // 获取评估统计
  getAssessmentStats: (): Promise<AxiosResponse<AssessmentStats>> => {
    return request({
      url: '/api/system/dashboard/assessment-stats',
      method: 'get'
    });
  },

  // 获取量表统计
  getScaleStats: (): Promise<AxiosResponse<ScaleStats>> => {
    return request({
      url: '/api/system/dashboard/scale-stats',
      method: 'get'
    });
  },

  // 获取系统性能
  getSystemPerformance: (): Promise<AxiosResponse<SystemPerformance>> => {
    return request({
      url: '/api/system/dashboard/performance',
      method: 'get'
    });
  },

  // 获取实时活动
  getRecentActivities: (limit: number = 50): Promise<AxiosResponse<RecentActivity[]>> => {
    return request({
      url: '/api/system/dashboard/activities',
      method: 'get',
      params: { limit }
    });
  }
};

/**
 * 创建兼容的API实例
 * 这些实例保持与原有API相同的接口，但内部使用多租户逻辑
 */
export const createCompatibleApis = () => {
  return {
    assessmentScaleApi: createScaleApiAdapter(),
    assessmentRecordApi: createRecordApiAdapter(),
    elderlyApi: createSubjectApiAdapter()
  };
};

// 导出所有API
export default {
  multiTenantAuthApi,
  tenantManageApi,
  systemScaleApi,
  systemAssessmentApi,
  systemDashboardApi,
  platformUserApi,
  createScaleApiAdapter,
  createRecordApiAdapter,
  createSubjectApiAdapter,
  createCompatibleApis
};