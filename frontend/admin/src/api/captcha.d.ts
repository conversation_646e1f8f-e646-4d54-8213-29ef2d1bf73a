declare module '@/api/captcha' {
  export interface CaptchaResponse {
    success: boolean
    data: {
      token: string
      originalImageBase64: string
      jigsawImageBase64: string
      secretKey: string
      result?: boolean
    }
    message?: string
  }

  export interface CheckCaptchaRequest {
    captchaType: string
    token: string
    pointJson: string
    verification: string
  }

  export interface CheckCaptchaResponse {
    success: boolean
    data: {
      result: boolean
    }
    message?: string
  }

  export function getCaptcha(): Promise<CaptchaResponse>
  export function checkCaptcha(data: CheckCaptchaRequest): Promise<CheckCaptchaResponse>
  export function verifyCaptcha(data: any): Promise<any>
}