import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

// 创建 Pinia 实例
const pinia = createPinia()

// 添加持久化插件
pinia.use(createPersistedState({
  // 全局配置
  storage: localStorage,
  key: (id) => `pinia-${id}`,
  // 序列化配置
  serializer: {
    serialize: JSON.stringify,
    deserialize: JSON.parse
  }
}))

export default pinia

// 导出所有 store
export { useAssessmentStore } from './modules/assessment'
export { useElderlyStore } from './modules/elderly'
export { useScaleStore } from './modules/scale'

// 导出类型
export type { Assessment, AssessmentQuestion, AssessmentResult } from '@/types/assessment'
export type { Elderly, FamilyMember, HealthRecord } from './modules/elderly'
export type { Scale, ScaleDimension, ScaleQuestion } from './modules/scale'