import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'

// 量表相关类型定义
export interface Scale {
  id: string
  name: string
  description?: string
  category: string
  version: string
  status: 'active' | 'inactive' | 'draft'
  dimensions: ScaleDimension[]
  questionCount: number
  maxScore: number
  timeLimit?: number
  passingScore?: number
  instructions?: string
  createTime: string
  updateTime?: string
  creatorId?: string
  creatorName?: string
  tenantId?: string
  tags?: string[]
  isDefault?: boolean
  usageCount?: number
}

export interface ScaleDimension {
  id: string
  name: string
  description?: string
  weight: number
  questionCount: number
  maxScore: number
  order: number
}

export interface ScaleQuestion {
  id: string
  scaleId: string
  dimensionId?: string
  questionText: string
  questionType: 'single' | 'multiple' | 'text' | 'score' | 'yesno'
  options?: QuestionOption[]
  required: boolean
  order: number
  score?: number
  maxScore?: number
  weight?: number
  description?: string
  imageUrl?: string
  videoUrl?: string
  audioUrl?: string
}

export interface QuestionOption {
  id: string
  questionId: string
  optionText: string
  optionValue: string | number
  score: number
  order: number
  isCorrect?: boolean
  imageUrl?: string
}

export interface ScaleTemplate {
  id: string
  name: string
  description?: string
  category: string
  content: any
  isPublic: boolean
  downloadCount: number
  rating: number
  createTime: string
  creatorName?: string
}

interface ScaleState {
  scales: Scale[]
  currentScale: Scale | null
  scaleQuestions: ScaleQuestion[]
  scaleTemplates: ScaleTemplate[]
  selectedScaleIds: string[]
  loading: boolean
  error: string | null
  pagination: {
    current: number
    pageSize: number
    total: number
  }
  filters: {
    keyword: string
    category: string
    status: string
    version: string
    creatorId: string
    startDate: string
    endDate: string
    tags: string[]
  }
  stats: any | null
  cache: Map<string, { data: any; timestamp: number }>
}

export const useScaleStore = defineStore('scale', {
  state: (): ScaleState => ({
    scales: [],
    currentScale: null,
    scaleQuestions: [],
    scaleTemplates: [],
    selectedScaleIds: [],
    loading: false,
    error: null,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    },
    filters: {
      keyword: '',
      category: '',
      status: '',
      version: '',
      creatorId: '',
      startDate: '',
      endDate: '',
      tags: []
    },
    stats: null,
    cache: new Map()
  }),

  getters: {
    filteredScales(state): Scale[] {
      let filtered = state.scales
      
      if (state.filters.keyword) {
        const keyword = state.filters.keyword.toLowerCase()
        filtered = filtered.filter(scale => 
          scale.name.toLowerCase().includes(keyword) ||
          scale.description?.toLowerCase().includes(keyword) ||
          scale.category.toLowerCase().includes(keyword)
        )
      }
      
      if (state.filters.category) {
        filtered = filtered.filter(scale => scale.category === state.filters.category)
      }
      
      if (state.filters.status) {
        filtered = filtered.filter(scale => scale.status === state.filters.status)
      }
      
      if (state.filters.version) {
        filtered = filtered.filter(scale => scale.version === state.filters.version)
      }
      
      if (state.filters.creatorId) {
        filtered = filtered.filter(scale => scale.creatorId === state.filters.creatorId)
      }
      
      if (state.filters.tags.length > 0) {
        filtered = filtered.filter(scale => 
          state.filters.tags.some(tag => scale.tags?.includes(tag))
        )
      }
      
      if (state.filters.startDate && state.filters.endDate) {
        filtered = filtered.filter(scale => {
          const createTime = new Date(scale.createTime)
          return createTime >= new Date(state.filters.startDate) && 
                 createTime <= new Date(state.filters.endDate)
        })
      }
      
      return filtered
    },

    scaleCategories(state): string[] {
      const categories = new Set(state.scales.map(scale => scale.category))
      return Array.from(categories)
    },

    scaleVersions(state): string[] {
      const versions = new Set(state.scales.map(scale => scale.version))
      return Array.from(versions)
    },

    allScaleTags(state): string[] {
      const allTags = new Set<string>()
      state.scales.forEach(scale => {
        scale.tags?.forEach(tag => allTags.add(tag))
      })
      return Array.from(allTags)
    },

    scalesByCategory(state): Record<string, Scale[]> {
      const result: Record<string, Scale[]> = {}
      state.scales.forEach(scale => {
        if (!result[scale.category]) {
          result[scale.category] = []
        }
        result[scale.category].push(scale)
      })
      return result
    },

    scalesByStatus(state): Record<string, number> {
      const result = { active: 0, inactive: 0, draft: 0 }
      state.scales.forEach(scale => {
        result[scale.status]++
      })
      return result
    },

    totalScales(state): number {
      return state.scales.length
    },

    activeScales(state): number {
      return state.scales.filter(scale => scale.status === 'active').length
    }
  },

  actions: {
    // 缓存工具方法
    getCache(key: string) {
      const CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存
      const cached = this.cache.get(key)
      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        return cached.data
      }
      this.cache.delete(key)
      return null
    },

    setCache(key: string, data: any) {
      this.cache.set(key, { data, timestamp: Date.now() })
    },

    // 获取量表列表
    async fetchScales(params: any = {}) {
      const cacheKey = `scales_${JSON.stringify(params)}`
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.scales = cachedData.data
        this.pagination = cachedData.pagination
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const mockData: Scale[] = [
          {
            id: '1',
            name: 'Barthel日常生活活动能力指数',
            description: '评估老年人日常生活活动能力的标准量表',
            category: '日常生活能力',
            version: 'v2.0',
            status: 'active',
            dimensions: [
              {
                id: '1',
                name: '进食',
                description: '自主进食能力',
                weight: 10,
                questionCount: 1,
                maxScore: 10,
                order: 1
              },
              {
                id: '2',
                name: '洗澡',
                description: '自主洗澡能力',
                weight: 5,
                questionCount: 1,
                maxScore: 5,
                order: 2
              }
            ],
            questionCount: 10,
            maxScore: 100,
            timeLimit: 30,
            passingScore: 60,
            instructions: '请根据老人的实际情况选择最符合的选项',
            createTime: '2023-01-15T08:00:00.000Z',
            creatorId: 'user1',
            creatorName: '系统管理员',
            tenantId: 'tenant1',
            tags: ['ADL', '日常生活', '能力评估'],
            isDefault: true,
            usageCount: 150
          },
          {
            id: '2',
            name: 'MMSE简易智能精神状态检查量表',
            description: '筛查认知障碍的经典量表',
            category: '认知功能',
            version: 'v1.5',
            status: 'active',
            dimensions: [
              {
                id: '3',
                name: '定向力',
                description: '时间和地点定向',
                weight: 10,
                questionCount: 10,
                maxScore: 10,
                order: 1
              },
              {
                id: '4',
                name: '记忆力',
                description: '即时和延迟回忆',
                weight: 6,
                questionCount: 6,
                maxScore: 6,
                order: 2
              }
            ],
            questionCount: 30,
            maxScore: 30,
            timeLimit: 20,
            passingScore: 24,
            instructions: '测试过程中请保持安静的环境',
            createTime: '2023-02-10T08:00:00.000Z',
            creatorId: 'user2',
            creatorName: '医生张三',
            tenantId: 'tenant1',
            tags: ['MMSE', '认知', '智能检查'],
            isDefault: true,
            usageCount: 200
          }
        ]
        
        this.scales = mockData
        this.pagination = {
          current: 1,
          pageSize: 10,
          total: mockData.length
        }
        
        this.setCache(cacheKey, { data: this.scales, pagination: this.pagination })
        return { data: mockData, pagination: this.pagination }
      } catch (err: any) {
        this.error = err.message || '获取量表列表失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 获取量表详情
    async fetchScaleById(id: string) {
      const cacheKey = `scale_${id}`
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.currentScale = cachedData
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        const scale = this.scales.find(item => item.id === id)
        if (scale) {
          this.currentScale = scale
          this.setCache(cacheKey, scale)
          return scale
        }
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        throw new Error('量表不存在')
      } catch (err: any) {
        this.error = err.message || '获取量表详情失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 获取量表问题
    async fetchScaleQuestions(scaleId: string) {
      const cacheKey = `scale_questions_${scaleId}`
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.scaleQuestions = cachedData
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        
        const mockQuestions: ScaleQuestion[] = [
          {
            id: '1',
            scaleId,
            dimensionId: '1',
            questionText: '进食情况如何？',
            questionType: 'single',
            options: [
              {
                id: '1',
                questionId: '1',
                optionText: '完全独立',
                optionValue: 'independent',
                score: 10,
                order: 1
              },
              {
                id: '2',
                questionId: '1',
                optionText: '需要帮助',
                optionValue: 'assisted',
                score: 5,
                order: 2
              },
              {
                id: '3',
                questionId: '1',
                optionText: '完全依赖',
                optionValue: 'dependent',
                score: 0,
                order: 3
              }
            ],
            required: true,
            order: 1,
            maxScore: 10,
            weight: 1
          }
        ]
        
        this.scaleQuestions = mockQuestions
        this.setCache(cacheKey, mockQuestions)
        return mockQuestions
      } catch (err: any) {
        this.error = err.message || '获取量表问题失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 创建量表
    async createScale(data: Partial<Scale>) {
      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const newScale: Scale = {
          id: Date.now().toString(),
          name: data.name || '',
          description: data.description,
          category: data.category || '',
          version: data.version || 'v1.0',
          status: data.status || 'draft',
          dimensions: data.dimensions || [],
          questionCount: data.questionCount || 0,
          maxScore: data.maxScore || 0,
          timeLimit: data.timeLimit,
          passingScore: data.passingScore,
          instructions: data.instructions,
          createTime: new Date().toISOString(),
          creatorId: 'current_user',
          creatorName: '当前用户',
          tenantId: 'tenant1',
          tags: data.tags || [],
          isDefault: false,
          usageCount: 0,
          ...data
        }
        
        this.scales.unshift(newScale)
        this.pagination.total += 1
        
        // 清理缓存
        this.cache.clear()
        
        ElMessage.success('创建量表成功')
        return newScale
      } catch (err: any) {
        this.error = err.message || '创建量表失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 更新量表
    async updateScale(id: string, data: Partial<Scale>) {
      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const index = this.scales.findIndex(item => item.id === id)
        if (index !== -1) {
          this.scales[index] = {
            ...this.scales[index],
            ...data,
            updateTime: new Date().toISOString()
          }
        }
        
        if (this.currentScale?.id === id) {
          this.currentScale = {
            ...this.currentScale,
            ...data,
            updateTime: new Date().toISOString()
          }
        }
        
        // 清理缓存
        this.cache.clear()
        
        ElMessage.success('更新量表成功')
        return this.scales[index]
      } catch (err: any) {
        this.error = err.message || '更新量表失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 删除量表
    async deleteScale(id: string) {
      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        
        const index = this.scales.findIndex(item => item.id === id)
        if (index !== -1) {
          this.scales.splice(index, 1)
          this.pagination.total -= 1
        }
        
        if (this.currentScale?.id === id) {
          this.currentScale = null
        }
        
        // 清理缓存
        this.cache.clear()
        
        ElMessage.success('删除量表成功')
        return true
      } catch (err: any) {
        this.error = err.message || '删除量表失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 复制量表
    async duplicateScale(id: string, newName?: string) {
      this.loading = true
      this.error = null
      
      try {
        const sourceScale = this.scales.find(item => item.id === id)
        if (!sourceScale) {
          throw new Error('源量表不存在')
        }
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const duplicatedScale: Scale = {
          ...sourceScale,
          id: Date.now().toString(),
          name: newName || `${sourceScale.name} (副本)`,
          version: 'v1.0',
          status: 'draft',
          createTime: new Date().toISOString(),
          updateTime: undefined,
          usageCount: 0,
          isDefault: false
        }
        
        this.scales.unshift(duplicatedScale)
        this.pagination.total += 1
        
        // 清理缓存
        this.cache.clear()
        
        ElMessage.success('复制量表成功')
        return duplicatedScale
      } catch (err: any) {
        this.error = err.message || '复制量表失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 获取量表模板
    async fetchScaleTemplates() {
      const cacheKey = 'scale_templates'
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.scaleTemplates = cachedData
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        
        const mockTemplates: ScaleTemplate[] = [
          {
            id: '1',
            name: 'Barthel量表模板',
            description: '标准的Barthel日常生活活动能力评估模板',
            category: '日常生活能力',
            content: {},
            isPublic: true,
            downloadCount: 1500,
            rating: 4.8,
            createTime: '2023-01-01T00:00:00.000Z',
            creatorName: '官方'
          },
          {
            id: '2',
            name: 'MMSE量表模板',
            description: '标准的简易智能精神状态检查模板',
            category: '认知功能',
            content: {},
            isPublic: true,
            downloadCount: 2000,
            rating: 4.9,
            createTime: '2023-01-01T00:00:00.000Z',
            creatorName: '官方'
          }
        ]
        
        this.scaleTemplates = mockTemplates
        this.setCache(cacheKey, mockTemplates)
        return mockTemplates
      } catch (err: any) {
        this.error = err.message || '获取量表模板失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 设置过滤条件
    setFilters(newFilters: Partial<ScaleState['filters']>) {
      this.filters = { ...this.filters, ...newFilters }
    },

    // 重置过滤条件
    resetFilters() {
      this.filters = {
        keyword: '',
        category: '',
        status: '',
        version: '',
        creatorId: '',
        startDate: '',
        endDate: '',
        tags: []
      }
    },

    // 设置分页
    setPagination(newPagination: Partial<ScaleState['pagination']>) {
      this.pagination = { ...this.pagination, ...newPagination }
    },

    // 设置选中的量表ID
    setSelectedScaleIds(ids: string[]) {
      this.selectedScaleIds = ids
    },

    // 设置当前量表
    setCurrentScale(scale: Scale | null) {
      this.currentScale = scale
    },

    // 获取统计数据
    async fetchStats() {
      const cacheKey = 'scale_stats'
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.stats = cachedData
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        // 调用统计API
        const { systemDashboardApi } = await import('@/api/multiTenantAdapter')
        const response = await systemDashboardApi.getScaleStats()
        
        if (response.data) {
          this.stats = response.data
          this.setCache(cacheKey, response.data)
          return response.data
        } else {
          throw new Error('获取量表统计失败')
        }
      } catch (err: any) {
        const errorMsg = err.message || '获取量表统计失败'
        this.error = errorMsg
        ElMessage.error(errorMsg)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 清理缓存
    clearCache() {
      this.cache.clear()
    }
  }
})