import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'

// 老人信息类型定义
export interface Elderly {
  id: string
  name: string
  gender: 'male' | 'female'
  age: number
  birthday: string
  idCard: string
  phone?: string
  address?: string
  emergencyContact?: string
  emergencyPhone?: string
  medicalHistory?: string[]
  allergies?: string[]
  medications?: string[]
  careLevel?: string
  admissionDate?: string
  roomNumber?: string
  bedNumber?: string
  status: 'active' | 'inactive' | 'discharged'
  profileImage?: string
  notes?: string
  familyMembers?: FamilyMember[]
  healthRecords?: HealthRecord[]
  createTime: string
  updateTime?: string
  tenantId?: string
}

export interface FamilyMember {
  id: string
  elderlyId: string
  name: string
  relationship: string
  phone?: string
  address?: string
  emergencyContact: boolean
  notes?: string
}

export interface HealthRecord {
  id: string
  elderlyId: string
  recordType: 'checkup' | 'diagnosis' | 'treatment' | 'medication' | 'assessment'
  title: string
  description: string
  recordDate: string
  doctorName?: string
  department?: string
  attachments?: string[]
  tags?: string[]
}

interface ElderlyState {
  elderlyList: Elderly[]
  currentElderly: Elderly | null
  selectedElderlyIds: string[]
  loading: boolean
  error: string | null
  pagination: {
    current: number
    pageSize: number
    total: number
  }
  filters: {
    keyword: string
    gender: string
    ageRange: [number, number] | null
    careLevel: string
    status: string
    roomNumber: string
    startDate: string
    endDate: string
  }
  cache: Map<string, { data: any; timestamp: number }>
}

export const useElderlyStore = defineStore('elderly', {
  state: (): ElderlyState => ({
    elderlyList: [],
    currentElderly: null,
    selectedElderlyIds: [],
    loading: false,
    error: null,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    },
    filters: {
      keyword: '',
      gender: '',
      ageRange: null,
      careLevel: '',
      status: '',
      roomNumber: '',
      startDate: '',
      endDate: ''
    },
    cache: new Map()
  }),

  getters: {
    filteredElderlyList(state): Elderly[] {
      let filtered = state.elderlyList
      
      if (state.filters.keyword) {
        const keyword = state.filters.keyword.toLowerCase()
        filtered = filtered.filter(elderly => 
          elderly.name.toLowerCase().includes(keyword) ||
          elderly.idCard.includes(keyword) ||
          elderly.phone?.includes(keyword) ||
          elderly.roomNumber?.includes(keyword)
        )
      }
      
      if (state.filters.gender) {
        filtered = filtered.filter(elderly => elderly.gender === state.filters.gender)
      }
      
      if (state.filters.ageRange) {
        const [minAge, maxAge] = state.filters.ageRange
        filtered = filtered.filter(elderly => 
          elderly.age >= minAge && elderly.age <= maxAge
        )
      }
      
      if (state.filters.careLevel) {
        filtered = filtered.filter(elderly => elderly.careLevel === state.filters.careLevel)
      }
      
      if (state.filters.status) {
        filtered = filtered.filter(elderly => elderly.status === state.filters.status)
      }
      
      if (state.filters.roomNumber) {
        filtered = filtered.filter(elderly => 
          elderly.roomNumber?.includes(state.filters.roomNumber)
        )
      }
      
      if (state.filters.startDate && state.filters.endDate) {
        filtered = filtered.filter(elderly => {
          const admissionDate = new Date(elderly.admissionDate || '')
          return admissionDate >= new Date(state.filters.startDate) && 
                 admissionDate <= new Date(state.filters.endDate)
        })
      }
      
      return filtered
    },

    elderlyByGender(state): Record<string, number> {
      const stats = { male: 0, female: 0 }
      state.elderlyList.forEach(elderly => {
        stats[elderly.gender]++
      })
      return stats
    },

    elderlyByAgeGroup(state): Record<string, number> {
      const ageGroups = {
        '60-70': 0,
        '70-80': 0,
        '80-90': 0,
        '90+': 0
      }
      
      state.elderlyList.forEach(elderly => {
        if (elderly.age >= 60 && elderly.age < 70) ageGroups['60-70']++
        else if (elderly.age >= 70 && elderly.age < 80) ageGroups['70-80']++
        else if (elderly.age >= 80 && elderly.age < 90) ageGroups['80-90']++
        else if (elderly.age >= 90) ageGroups['90+']++
      })
      
      return ageGroups
    },

    elderlybyCareLevel(state): Record<string, number> {
      const careLevels: Record<string, number> = {}
      state.elderlyList.forEach(elderly => {
        const level = elderly.careLevel || '未分级'
        careLevels[level] = (careLevels[level] || 0) + 1
      })
      return careLevels
    },

    totalElderly(state): number {
      return state.elderlyList.length
    },

    activeElderly(state): number {
      return state.elderlyList.filter(elderly => elderly.status === 'active').length
    }
  },

  actions: {
    // 缓存工具方法
    getCache(key: string) {
      const CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存
      const cached = this.cache.get(key)
      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        return cached.data
      }
      this.cache.delete(key)
      return null
    },

    setCache(key: string, data: any) {
      this.cache.set(key, { data, timestamp: Date.now() })
    },

    // 获取老人列表
    async fetchElderlyList(params: any = {}) {
      const cacheKey = `elderly_list_${JSON.stringify(params)}`
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.elderlyList = cachedData.data
        this.pagination = cachedData.pagination
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用 - 实际项目中替换为真实API
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const mockData = [
          {
            id: '1',
            name: '张老太',
            gender: 'female' as const,
            age: 82,
            birthday: '1942-03-15',
            idCard: '110101194203150001',
            phone: '13800138001',
            address: '北京市东城区王府井大街1号',
            emergencyContact: '张小明',
            emergencyPhone: '13900139001',
            careLevel: '三级护理',
            admissionDate: '2023-01-15',
            roomNumber: 'A101',
            bedNumber: '01',
            status: 'active' as const,
            createTime: '2023-01-15T08:00:00.000Z',
            tenantId: 'tenant1'
          },
          {
            id: '2',
            name: '李爷爷',
            gender: 'male' as const,
            age: 75,
            birthday: '1949-06-20',
            idCard: '110101194906200002',
            phone: '13800138002',
            address: '北京市西城区西单大街2号',
            emergencyContact: '李小华',
            emergencyPhone: '13900139002',
            careLevel: '二级护理',
            admissionDate: '2023-02-10',
            roomNumber: 'B201',
            bedNumber: '02',
            status: 'active' as const,
            createTime: '2023-02-10T08:00:00.000Z',
            tenantId: 'tenant1'
          }
        ]
        
        this.elderlyList = mockData
        this.pagination = {
          current: 1,
          pageSize: 10,
          total: mockData.length
        }
        
        this.setCache(cacheKey, { data: this.elderlyList, pagination: this.pagination })
        return { data: mockData, pagination: this.pagination }
      } catch (err: any) {
        this.error = err.message || '获取老人列表失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 获取老人详情
    async fetchElderlyById(id: string) {
      const cacheKey = `elderly_${id}`
      const cachedData = this.getCache(cacheKey)
      
      if (cachedData) {
        this.currentElderly = cachedData
        return cachedData
      }

      this.loading = true
      this.error = null
      
      try {
        // 从列表中查找或模拟API调用
        const elderly = this.elderlyList.find(item => item.id === id)
        if (elderly) {
          this.currentElderly = elderly
          this.setCache(cacheKey, elderly)
          return elderly
        }
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        throw new Error('老人信息不存在')
      } catch (err: any) {
        this.error = err.message || '获取老人详情失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 创建老人信息
    async createElderly(data: Partial<Elderly>) {
      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const newElderly: Elderly = {
          id: Date.now().toString(),
          name: data.name || '',
          gender: data.gender || 'male',
          age: data.age || 0,
          birthday: data.birthday || '',
          idCard: data.idCard || '',
          phone: data.phone,
          address: data.address,
          emergencyContact: data.emergencyContact,
          emergencyPhone: data.emergencyPhone,
          careLevel: data.careLevel,
          admissionDate: data.admissionDate,
          roomNumber: data.roomNumber,
          bedNumber: data.bedNumber,
          status: data.status || 'active',
          createTime: new Date().toISOString(),
          tenantId: 'tenant1',
          ...data
        }
        
        this.elderlyList.unshift(newElderly)
        this.pagination.total += 1
        
        // 清理缓存
        this.cache.clear()
        
        ElMessage.success('创建老人信息成功')
        return newElderly
      } catch (err: any) {
        this.error = err.message || '创建老人信息失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 更新老人信息
    async updateElderly(id: string, data: Partial<Elderly>) {
      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const index = this.elderlyList.findIndex(item => item.id === id)
        if (index !== -1) {
          this.elderlyList[index] = {
            ...this.elderlyList[index],
            ...data,
            updateTime: new Date().toISOString()
          }
        }
        
        if (this.currentElderly?.id === id) {
          this.currentElderly = {
            ...this.currentElderly,
            ...data,
            updateTime: new Date().toISOString()
          }
        }
        
        // 清理缓存
        this.cache.clear()
        
        ElMessage.success('更新老人信息成功')
        return this.elderlyList[index]
      } catch (err: any) {
        this.error = err.message || '更新老人信息失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 删除老人信息
    async deleteElderly(id: string) {
      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        
        const index = this.elderlyList.findIndex(item => item.id === id)
        if (index !== -1) {
          this.elderlyList.splice(index, 1)
          this.pagination.total -= 1
        }
        
        if (this.currentElderly?.id === id) {
          this.currentElderly = null
        }
        
        // 清理缓存
        this.cache.clear()
        
        ElMessage.success('删除老人信息成功')
        return true
      } catch (err: any) {
        this.error = err.message || '删除老人信息失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 批量删除老人信息
    async batchDeleteElderly(ids: string[]) {
      this.loading = true
      this.error = null
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        ids.forEach(id => {
          const index = this.elderlyList.findIndex(item => item.id === id)
          if (index !== -1) {
            this.elderlyList.splice(index, 1)
            this.pagination.total -= 1
          }
        })
        
        if (this.currentElderly && ids.includes(this.currentElderly.id)) {
          this.currentElderly = null
        }
        
        // 清空选中状态
        this.selectedElderlyIds = []
        
        // 清理缓存
        this.cache.clear()
        
        ElMessage.success(`成功删除 ${ids.length} 条老人信息`)
        return true
      } catch (err: any) {
        this.error = err.message || '批量删除失败'
        ElMessage.error(this.error)
        throw err
      } finally {
        this.loading = false
      }
    },

    // 设置过滤条件
    setFilters(newFilters: Partial<ElderlyState['filters']>) {
      this.filters = { ...this.filters, ...newFilters }
    },

    // 重置过滤条件
    resetFilters() {
      this.filters = {
        keyword: '',
        gender: '',
        ageRange: null,
        careLevel: '',
        status: '',
        roomNumber: '',
        startDate: '',
        endDate: ''
      }
    },

    // 设置分页
    setPagination(newPagination: Partial<ElderlyState['pagination']>) {
      this.pagination = { ...this.pagination, ...newPagination }
    },

    // 设置选中的老人ID
    setSelectedElderlyIds(ids: string[]) {
      this.selectedElderlyIds = ids
    },

    // 添加选中的老人ID
    addSelectedElderlyId(id: string) {
      if (!this.selectedElderlyIds.includes(id)) {
        this.selectedElderlyIds.push(id)
      }
    },

    // 移除选中的老人ID
    removeSelectedElderlyId(id: string) {
      const index = this.selectedElderlyIds.indexOf(id)
      if (index > -1) {
        this.selectedElderlyIds.splice(index, 1)
      }
    },

    // 清空选中状态
    clearSelectedElderly() {
      this.selectedElderlyIds = []
    },

    // 设置当前老人
    setCurrentElderly(elderly: Elderly | null) {
      this.currentElderly = elderly
    },

    // 清理缓存
    clearCache() {
      this.cache.clear()
    }
  }
})