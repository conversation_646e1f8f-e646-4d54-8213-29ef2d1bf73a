import { ref, computed, readonly } from 'vue'
import { ElMessage } from 'element-plus'

// 请求状态类型
interface RequestState<T = any> {
  data: T | null
  loading: boolean
  error: string | null
}

// 缓存管理器
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  get<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data as T
  }

  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  has(key: string): boolean {
    const cached = this.cache.get(key)
    if (!cached) return false
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return false
    }
    
    return true
  }

  // 批量删除匹配模式的缓存
  deletePattern(pattern: RegExp): void {
    const keysToDelete: string[] = []
    this.cache.forEach((_, key) => {
      if (pattern.test(key)) {
        keysToDelete.push(key)
      }
    })
    keysToDelete.forEach(key => this.cache.delete(key))
  }
}

// 全局缓存实例
const globalCache = new CacheManager()

// 防抖管理器
class DebounceManager {
  private timers = new Map<string, NodeJS.Timeout>()

  debounce<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    delay: number = 300
  ): (...args: Parameters<T>) => Promise<ReturnType<T>> {
    return (...args: Parameters<T>): Promise<ReturnType<T>> => {
      return new Promise((resolve, reject) => {
        // 清除之前的定时器
        const existingTimer = this.timers.get(key)
        if (existingTimer) {
          clearTimeout(existingTimer)
        }

        // 设置新的定时器
        const timer = setTimeout(async () => {
          try {
            const result = await fn(...args)
            resolve(result)
          } catch (error) {
            reject(error)
          } finally {
            this.timers.delete(key)
          }
        }, delay)

        this.timers.set(key, timer)
      })
    }
  }

  cancel(key: string): void {
    const timer = this.timers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(key)
    }
  }

  cancelAll(): void {
    this.timers.forEach((timer) => clearTimeout(timer))
    this.timers.clear()
  }
}

// 全局防抖实例
const globalDebounce = new DebounceManager()

// 节流管理器
class ThrottleManager {
  private lastCallTimes = new Map<string, number>()

  throttle<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    delay: number = 1000
  ): (...args: Parameters<T>) => ReturnType<T> | undefined {
    return (...args: Parameters<T>): ReturnType<T> | undefined => {
      const now = Date.now()
      const lastCallTime = this.lastCallTimes.get(key) || 0

      if (now - lastCallTime >= delay) {
        this.lastCallTimes.set(key, now)
        return fn(...args)
      }

      return undefined
    }
  }

  reset(key: string): void {
    this.lastCallTimes.delete(key)
  }

  resetAll(): void {
    this.lastCallTimes.clear()
  }
}

// 全局节流实例
const globalThrottle = new ThrottleManager()

// 请求选项
interface UseRequestOptions {
  // 是否启用缓存
  cache?: boolean
  // 缓存键
  cacheKey?: string
  // 缓存时间（毫秒）
  cacheTTL?: number
  // 是否启用防抖
  debounce?: boolean
  // 防抖延迟（毫秒）
  debounceDelay?: number
  // 防抖键
  debounceKey?: string
  // 是否启用节流
  throttle?: boolean
  // 节流延迟（毫秒）
  throttleDelay?: number
  // 节流键
  throttleKey?: string
  // 错误处理
  onError?: (error: any) => void
  // 成功处理
  onSuccess?: (data: any) => void
  // 是否显示错误消息
  showErrorMessage?: boolean
  // 是否显示成功消息
  showSuccessMessage?: boolean
  // 成功消息文本
  successMessage?: string
  // 是否在组件卸载时取消请求
  cancelOnUnmount?: boolean
}

// useRequest composable
export function useRequest<T = any>(
  requestFn: (...args: any[]) => Promise<T>,
  options: UseRequestOptions = {}
) {
  const {
    cache = false,
    cacheKey,
    cacheTTL = 5 * 60 * 1000,
    debounce = false,
    debounceDelay = 300,
    debounceKey,
    throttle = false,
    throttleDelay = 1000,
    throttleKey,
    onError,
    onSuccess,
    showErrorMessage = true,
    showSuccessMessage = false,
    successMessage,
    cancelOnUnmount = true
  } = options

  // 响应式状态
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isIdle = computed(() => !loading.value && !error.value)
  const isError = computed(() => !!error.value)
  const isSuccess = computed(() => !loading.value && !error.value && data.value !== null)

  // 请求取消控制
  let requestController: AbortController | null = null

  // 执行请求的核心方法
  const executeRequest = async (...args: any[]): Promise<T | null> => {
    // 检查缓存
    if (cache && cacheKey) {
      const cachedData = globalCache.get<T>(cacheKey)
      if (cachedData) {
        data.value = cachedData
        return cachedData
      }
    }

    // 取消之前的请求
    if (requestController) {
      requestController.abort()
    }

    // 创建新的请求控制器
    requestController = new AbortController()

    loading.value = true
    error.value = null

    try {
      const result = await requestFn(...args)
      
      // 检查请求是否被取消
      if (requestController.signal.aborted) {
        return null
      }

      data.value = result

      // 缓存结果
      if (cache && cacheKey) {
        globalCache.set(cacheKey, result, cacheTTL)
      }

      // 成功回调
      if (onSuccess) {
        onSuccess(result)
      }

      // 显示成功消息
      if (showSuccessMessage && successMessage) {
        ElMessage.success(successMessage)
      }

      return result
    } catch (err: any) {
      // 检查请求是否被取消
      if (requestController.signal.aborted) {
        return null
      }

      const errorMessage = err.message || '请求失败'
      error.value = errorMessage

      // 错误回调
      if (onError) {
        onError(err)
      }

      // 显示错误消息
      if (showErrorMessage) {
        ElMessage.error(errorMessage)
      }

      throw err
    } finally {
      loading.value = false
      requestController = null
    }
  }

  // 创建包装后的请求方法
  let wrappedRequest = executeRequest

  // 应用防抖
  if (debounce) {
    const key = debounceKey || `request_${Date.now()}`
    wrappedRequest = globalDebounce.debounce(key, executeRequest, debounceDelay)
  }

  // 应用节流
  if (throttle) {
    const key = throttleKey || `request_${Date.now()}`
    const throttledFn = globalThrottle.throttle(key, executeRequest, throttleDelay)
    const originalWrapped = wrappedRequest
    wrappedRequest = (...args: any[]) => {
      const result = throttledFn(...args)
      return result || originalWrapped(...args)
    }
  }

  // 执行请求
  const execute = async (...args: any[]): Promise<T | null> => {
    return wrappedRequest(...args)
  }

  // 取消请求
  const cancel = () => {
    if (requestController) {
      requestController.abort()
      requestController = null
    }
    
    if (debounce && debounceKey) {
      globalDebounce.cancel(debounceKey)
    }
  }

  // 重置状态
  const reset = () => {
    data.value = null
    loading.value = false
    error.value = null
    cancel()
  }

  // 刷新（重新执行最后一次请求）
  const refresh = async (...args: any[]): Promise<T | null> => {
    // 清除缓存
    if (cache && cacheKey) {
      globalCache.delete(cacheKey)
    }
    return execute(...args)
  }

  // 组件卸载时清理
  if (cancelOnUnmount) {
    // Vue 3的onUnmounted需要在setup中调用
    // 这里返回清理函数，由调用者决定何时调用
  }

  return {
    // 状态
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    
    // 计算属性
    isIdle,
    isError,
    isSuccess,
    
    // 方法
    execute,
    cancel,
    reset,
    refresh,
    
    // 工具方法
    clearCache: () => cache && cacheKey && globalCache.delete(cacheKey),
    
    // 清理函数（供组件卸载时调用）
    cleanup: cancel
  }
}

// 批量请求 composable
export function useBatchRequest<T = any>(
  requests: Array<(...args: any[]) => Promise<T>>,
  options: Omit<UseRequestOptions, 'cacheKey' | 'debounceKey' | 'throttleKey'> & {
    cacheKeys?: string[]
    debounceKeys?: string[]
    throttleKeys?: string[]
  } = {}
) {
  const results = ref<(T | null)[]>(new Array(requests.length).fill(null))
  const loadingStates = ref<boolean[]>(new Array(requests.length).fill(false))
  const errorStates = ref<(string | null)[]>(new Array(requests.length).fill(null))

  const loading = computed(() => loadingStates.value.some(state => state))
  const allCompleted = computed(() => loadingStates.value.every(state => !state))
  const hasErrors = computed(() => errorStates.value.some(error => error !== null))

  const executeAll = async (argsArray: any[][] = []): Promise<(T | null)[]> => {
    const promises = requests.map((request, index) => {
      const requestOptions: UseRequestOptions = {
        ...options,
        cacheKey: options.cacheKeys?.[index],
        debounceKey: options.debounceKeys?.[index],
        throttleKey: options.throttleKeys?.[index],
        showErrorMessage: false, // 统一处理错误消息
        showSuccessMessage: false // 统一处理成功消息
      }

      const { execute } = useRequest(request, requestOptions)
      const args = argsArray[index] || []
      
      return execute(...args).then(result => {
        results.value[index] = result
        return result
      }).catch(error => {
        errorStates.value[index] = error.message
        return null
      })
    })

    try {
      const allResults = await Promise.allSettled(promises)
      
      if (options.showSuccessMessage && !hasErrors.value) {
        ElMessage.success('批量操作完成')
      }
      
      if (options.showErrorMessage && hasErrors.value) {
        ElMessage.error('部分操作失败')
      }
      
      return results.value
    } catch (error) {
      if (options.showErrorMessage) {
        ElMessage.error('批量操作失败')
      }
      throw error
    }
  }

  const reset = () => {
    results.value.fill(null)
    loadingStates.value.fill(false)
    errorStates.value.fill(null)
  }

  return {
    results: readonly(results),
    loadingStates: readonly(loadingStates),
    errorStates: readonly(errorStates),
    loading,
    allCompleted,
    hasErrors,
    executeAll,
    reset
  }
}

// 导出工具类
export { CacheManager, DebounceManager, ThrottleManager, globalCache, globalDebounce, globalThrottle }