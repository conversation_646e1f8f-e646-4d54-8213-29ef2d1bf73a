/**
 * 权限管理工具
 * 处理系统总管理员和机构管理员的权限判断
 */

// 类型定义
interface UserInfo {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: 'ADMIN' | 'ASSESSOR' | 'REVIEWER' | 'SUPERVISOR' | 'VIEWER';
  tenantRole: 'ADMIN' | 'ASSESSOR' | 'REVIEWER' | 'SUPERVISOR' | 'VIEWER';
  tenantName?: string;
  permissions: string[];
  isSuperAdmin?: boolean;
  platformRole?: 'ADMIN' | 'USER';
}

interface RoleInfo {
  type: 'super_admin' | 'tenant_admin' | 'user';
  name: string;
  description: string;
  color: 'red' | 'blue' | 'gray';
}

interface MenuItem {
  name: string;
  route?: string;
  icon?: string;
  children?: MenuItem[];
}

/**
 * 获取当前用户信息
 */
export function getCurrentUser(): UserInfo | null {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) as UserInfo : null;
  } catch (error) {
    console.error('Failed to parse user info:', error);
    return null;
  }
}

/**
 * 检查是否为系统总管理员
 */
export function isSuperAdmin(): boolean {
  const user = getCurrentUser();
  return user?.isSuperAdmin === true || user?.platformRole === 'ADMIN';
}

/**
 * 检查是否为机构管理员
 */
export function isTenantAdmin(): boolean {
  const user = getCurrentUser();
  return user?.tenantRole === 'ADMIN' && !isSuperAdmin();
}

/**
 * 检查是否有特定权限
 */
export function hasPermission(permission: string): boolean {
  const user = getCurrentUser();
  if (!user) return false;
  
  // 超级管理员拥有所有权限
  if (isSuperAdmin()) {
    return true;
  }
  
  // 检查用户权限列表
  if (user.permissions && Array.isArray(user.permissions)) {
    return user.permissions.includes(permission) || user.permissions.includes('ALL');
  }
  
  return false;
}

/**
 * 检查是否有多个权限中的任意一个
 */
export function hasAnyPermission(permissions: string[]): boolean {
  if (!Array.isArray(permissions)) return false;
  return permissions.some(permission => hasPermission(permission));
}

/**
 * 检查是否拥有所有指定权限
 */
export function hasAllPermissions(permissions: string[]): boolean {
  if (!Array.isArray(permissions)) return false;
  return permissions.every(permission => hasPermission(permission));
}

/**
 * 获取用户角色信息
 */
export function getUserRole(): RoleInfo | null {
  const user = getCurrentUser();
  if (!user) return null;
  
  if (isSuperAdmin()) {
    return {
      type: 'super_admin',
      name: '系统总管理员',
      description: '拥有平台最高权限，可管理所有租户和用户',
      color: 'red'
    };
  }
  
  if (isTenantAdmin()) {
    return {
      type: 'tenant_admin',
      name: '机构管理员',
      description: `管理 ${user.tenantName || '当前机构'} 的所有事务`,
      color: 'blue'
    };
  }
  
  return {
    type: 'user',
    name: user.tenantRole || '普通用户',
    description: '基础权限用户',
    color: 'gray'
  };
}

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 系统级权限
  SYSTEM_MANAGE: 'SYSTEM_MANAGE',
  TENANT_MANAGE: 'TENANT_MANAGE',
  USER_GLOBAL_MANAGE: 'USER_GLOBAL_MANAGE',
  
  // 租户级权限  
  USER_MANAGE: 'USER_MANAGE',
  SCALE_MANAGE: 'SCALE_MANAGE',
  ASSESSMENT_ALL: 'ASSESSMENT_ALL',
  ASSESSMENT_CREATE: 'ASSESSMENT_CREATE',
  ASSESSMENT_READ: 'ASSESSMENT_READ',
  ASSESSMENT_UPDATE: 'ASSESSMENT_UPDATE',
  ASSESSMENT_SUBMIT: 'ASSESSMENT_SUBMIT',
  ASSESSMENT_REVIEW: 'ASSESSMENT_REVIEW',
  REPORT_ALL: 'REPORT_ALL',
  REPORT_READ: 'REPORT_READ',
  TENANT_CONFIG: 'TENANT_CONFIG',
  USER_READ: 'USER_READ',
  
  // 特殊权限
  ALL: 'ALL'
} as const;

export type PermissionType = typeof PERMISSIONS[keyof typeof PERMISSIONS];

/**
 * 角色权限映射
 */
export const ROLE_PERMISSIONS: Record<string, string[]> = {
  SUPER_ADMIN: [PERMISSIONS.ALL],
  TENANT_ADMIN: [
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.SCALE_MANAGE,
    PERMISSIONS.ASSESSMENT_ALL,
    PERMISSIONS.REPORT_ALL,
    PERMISSIONS.TENANT_CONFIG
  ],
  SUPERVISOR: [
    PERMISSIONS.ASSESSMENT_READ,
    PERMISSIONS.ASSESSMENT_REVIEW,
    PERMISSIONS.REPORT_READ,
    PERMISSIONS.USER_READ
  ],
  ASSESSOR: [
    PERMISSIONS.ASSESSMENT_CREATE,
    PERMISSIONS.ASSESSMENT_READ,
    PERMISSIONS.ASSESSMENT_UPDATE,
    PERMISSIONS.ASSESSMENT_SUBMIT
  ],
  REVIEWER: [
    PERMISSIONS.ASSESSMENT_READ,
    PERMISSIONS.ASSESSMENT_REVIEW,
    PERMISSIONS.REPORT_READ
  ],
  VIEWER: [
    PERMISSIONS.ASSESSMENT_READ,
    PERMISSIONS.REPORT_READ
  ]
};

/**
 * 检查当前用户是否可以访问指定路由
 */
export function canAccessRoute(routeName: string): boolean {
  const user = getCurrentUser();
  if (!user) return false;
  
  // 超级管理员可以访问所有路由
  if (isSuperAdmin()) return true;
  
  // 路由权限映射
  const routePermissions: Record<string, () => boolean> = {
    // 系统管理路由（仅超级管理员）
    'system-dashboard': () => isSuperAdmin(),
    'tenant-manage': () => isSuperAdmin(),
    'system-users': () => isSuperAdmin(),
    'system-scales': () => isSuperAdmin(),
    
    // 机构管理路由
    'tenant-dashboard': () => isTenantAdmin() || isSuperAdmin(),
    'user-manage': () => hasPermission(PERMISSIONS.USER_MANAGE),
    'scale-manage': () => hasPermission(PERMISSIONS.SCALE_MANAGE),
    'assessment-manage': () => hasPermission(PERMISSIONS.ASSESSMENT_ALL),
    'report-manage': () => hasPermission(PERMISSIONS.REPORT_ALL),
    'tenant-config': () => hasPermission(PERMISSIONS.TENANT_CONFIG),
    
    // 评估相关路由
    'assessment-create': () => hasPermission(PERMISSIONS.ASSESSMENT_CREATE),
    'assessment-review': () => hasPermission(PERMISSIONS.ASSESSMENT_REVIEW),
    'assessment-view': () => hasPermission(PERMISSIONS.ASSESSMENT_READ),
    
    // 报告路由
    'report-view': () => hasPermission(PERMISSIONS.REPORT_READ),
    'report-export': () => hasPermission(PERMISSIONS.REPORT_ALL)
  };
  
  const checkFunction = routePermissions[routeName];
  return checkFunction ? checkFunction() : false;
}

/**
 * 获取当前用户可访问的功能菜单
 */
export function getAccessibleMenus(): MenuItem[] {
  const user = getCurrentUser();
  if (!user) return [];
  
  const menus: MenuItem[] = [];
  
  if (isSuperAdmin()) {
    // 系统总管理员菜单
    menus.push(
      {
        name: '系统管理',
        icon: '🔧',
        children: [
          { name: '系统概览', route: 'system-dashboard' },
          { name: '租户管理', route: 'tenant-manage' },
          { name: '用户管理', route: 'system-users' },
          { name: '量表管理', route: 'system-scales' },
          { name: '系统配置', route: 'system-config' }
        ]
      }
    );
  }
  
  if (isTenantAdmin() || isSuperAdmin()) {
    // 机构管理员菜单
    menus.push(
      {
        name: '机构管理',
        icon: '🏥',
        children: [
          { name: '机构概览', route: 'tenant-dashboard' },
          ...(hasPermission(PERMISSIONS.USER_MANAGE) ? [{ name: '用户管理', route: 'user-manage' }] : []),
          ...(hasPermission(PERMISSIONS.SCALE_MANAGE) ? [{ name: '量表管理', route: 'scale-manage' }] : []),
          ...(hasPermission(PERMISSIONS.ASSESSMENT_ALL) ? [{ name: '评估管理', route: 'assessment-manage' }] : []),
          ...(hasPermission(PERMISSIONS.REPORT_ALL) ? [{ name: '报告管理', route: 'report-manage' }] : []),
          ...(hasPermission(PERMISSIONS.TENANT_CONFIG) ? [{ name: '机构配置', route: 'tenant-config' }] : [])
        ]
      }
    );
  }
  
  // 评估功能菜单
  if (hasAnyPermission([PERMISSIONS.ASSESSMENT_CREATE, PERMISSIONS.ASSESSMENT_READ, PERMISSIONS.ASSESSMENT_REVIEW])) {
    menus.push(
      {
        name: '评估中心',
        icon: '📋',
        children: [
          ...(hasPermission(PERMISSIONS.ASSESSMENT_CREATE) ? [{ name: '新建评估', route: 'assessment-create' }] : []),
          ...(hasPermission(PERMISSIONS.ASSESSMENT_READ) ? [{ name: '评估记录', route: 'assessment-view' }] : []),
          ...(hasPermission(PERMISSIONS.ASSESSMENT_REVIEW) ? [{ name: '评估审核', route: 'assessment-review' }] : [])
        ]
      }
    );
  }
  
  // 报告功能菜单
  if (hasAnyPermission([PERMISSIONS.REPORT_READ, PERMISSIONS.REPORT_ALL])) {
    menus.push(
      {
        name: '数据报告',
        icon: '📊',
        children: [
          { name: '数据统计', route: 'report-view' },
          ...(hasPermission(PERMISSIONS.REPORT_ALL) ? [{ name: '报告导出', route: 'report-export' }] : [])
        ]
      }
    );
  }
  
  return menus;
}

export default {
  getCurrentUser,
  isSuperAdmin,
  isTenantAdmin,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getUserRole,
  canAccessRoute,
  getAccessibleMenus,
  PERMISSIONS,
  ROLE_PERMISSIONS
};