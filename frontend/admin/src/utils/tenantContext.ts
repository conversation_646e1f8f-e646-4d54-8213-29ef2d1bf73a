/**
 * 多租户上下文管理器
 * 管理当前租户信息和上下文状态
 */

// 类型定义
export interface TenantInfo {
  id: string;
  code: string;
  name: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING' | 'DISABLED';
  subscriptionPlan: 'BASIC' | 'STANDARD' | 'PREMIUM' | 'ENTERPRISE';
  subscriptionStatus: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'SUSPENDED';
  maxUsers: number;
  maxMonthlyAssessments: number;
  maxCustomScales: number;
  maxStorageMb: number;
  isTrial: boolean;
  subscriptionExpiresAt?: string;
  trialEndDate?: string;
  contactEmail?: string;
  contactPerson?: string;
  contactPhone?: string;
  logoUrl?: string;
  address?: string;
  description?: string;
  industry?: string;
  brandingConfig?: Record<string, any>;
  customConfig?: Record<string, any>;
}

export interface UserInfo {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: 'ADMIN' | 'ASSESSOR' | 'REVIEWER' | 'SUPERVISOR' | 'VIEWER';
  tenantRole: 'ADMIN' | 'ASSESSOR' | 'REVIEWER' | 'SUPERVISOR' | 'VIEWER';
  permissions: string[];
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'SUSPENDED';
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatarUrl?: string;
  department?: string;
  professionalTitle?: string;
  licenseNumber?: string;
  displayName?: string;
  joinedAt?: string;
  lastActiveAt?: string;
}

export type TenantContextEvent = 'tenantChanged' | 'userChanged' | 'tokenChanged' | 'cleared';

export type TenantContextListener = (event: TenantContextEvent, data?: any) => void;

class TenantContext {
  private currentTenant: TenantInfo | null = null;
  private currentUser: UserInfo | null = null;
  private token: string | null = null;
  private listeners: Set<TenantContextListener> = new Set();

  /**
   * 设置当前租户信息
   */
  setTenant(tenantInfo: TenantInfo | null): void {
    this.currentTenant = tenantInfo;
    this.notifyListeners('tenantChanged', tenantInfo);
    
    // 持久化到localStorage
    if (tenantInfo) {
      localStorage.setItem('currentTenant', JSON.stringify(tenantInfo));
    } else {
      localStorage.removeItem('currentTenant');
    }
  }

  /**
   * 获取当前租户信息
   */
  getCurrentTenant(): TenantInfo | null {
    if (!this.currentTenant) {
      const stored = localStorage.getItem('currentTenant');
      if (stored) {
        try {
          this.currentTenant = JSON.parse(stored) as TenantInfo;
        } catch (e) {
          console.warn('Failed to parse stored tenant info:', e);
          localStorage.removeItem('currentTenant');
        }
      }
    }
    return this.currentTenant;
  }

  /**
   * 获取当前租户ID
   */
  getCurrentTenantId(): string | null {
    const tenant = this.getCurrentTenant();
    return tenant ? tenant.id : null;
  }

  /**
   * 获取当前租户代码
   */
  getCurrentTenantCode(): string | null {
    const tenant = this.getCurrentTenant();
    return tenant ? tenant.code : null;
  }

  /**
   * 设置当前用户信息
   */
  setUser(userInfo: UserInfo | null): void {
    this.currentUser = userInfo;
    this.notifyListeners('userChanged', userInfo);
    
    // 持久化到localStorage
    if (userInfo) {
      localStorage.setItem('currentUser', JSON.stringify(userInfo));
    } else {
      localStorage.removeItem('currentUser');
    }
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): UserInfo | null {
    if (!this.currentUser) {
      const stored = localStorage.getItem('currentUser');
      if (stored) {
        try {
          this.currentUser = JSON.parse(stored) as UserInfo;
        } catch (e) {
          console.warn('Failed to parse stored user info:', e);
          localStorage.removeItem('currentUser');
        }
      }
    }
    return this.currentUser;
  }

  /**
   * 获取当前用户ID
   */
  getCurrentUserId(): string | null {
    const user = this.getCurrentUser();
    return user ? user.id : null;
  }

  /**
   * 设置认证令牌
   */
  setToken(token: string | null): void {
    this.token = token;
    this.notifyListeners('tokenChanged', token);
    
    // 持久化到localStorage
    if (token) {
      localStorage.setItem('authToken', token);
    } else {
      localStorage.removeItem('authToken');
    }
  }

  /**
   * 获取认证令牌
   */
  getToken(): string | null {
    if (!this.token) {
      this.token = localStorage.getItem('authToken');
    }
    return this.token;
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    return !!(this.getToken() && this.getCurrentTenant() && this.getCurrentUser());
  }

  /**
   * 检查用户是否有指定权限
   */
  hasPermission(permission: string): boolean {
    const user = this.getCurrentUser();
    if (!user || !user.permissions) return false;
    
    return user.permissions.includes(permission) || user.role === 'ADMIN';
  }

  /**
   * 检查用户角色
   */
  hasRole(role: UserInfo['role']): boolean {
    const user = this.getCurrentUser();
    return user ? user.role === role : false;
  }

  /**
   * 是否为管理员
   */
  isAdmin(): boolean {
    return this.hasRole('ADMIN');
  }

  /**
   * 清除所有信息（退出登录）
   */
  clear(): void {
    this.currentTenant = null;
    this.currentUser = null;
    this.token = null;
    
    localStorage.removeItem('currentTenant');
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
    
    this.notifyListeners('cleared');
  }

  /**
   * 添加监听器
   */
  addListener(callback: TenantContextListener): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  /**
   * 通知监听器
   */
  private notifyListeners(event: TenantContextEvent, data?: any): void {
    this.listeners.forEach(callback => {
      try {
        callback(event, data);
      } catch (e) {
        console.error('Error in tenant context listener:', e);
      }
    });
  }

  /**
   * 生成带租户前缀的API路径
   */
  getTenantApiPath(path: string): string {
    const tenantId = this.getCurrentTenantId();
    if (!tenantId) {
      throw new Error('No current tenant set');
    }
    
    // 如果path已经包含租户路径，直接返回
    if (path.includes('/tenants/')) {
      return path;
    }
    
    // 添加租户前缀
    return `/api/tenants/${tenantId}${path}`;
  }

  /**
   * 生成多租户通用API路径
   */
  getMultiTenantApiPath(path: string): string {
    if (path.startsWith('/api/multi-tenant/')) {
      return path;
    }
    return `/api/multi-tenant${path}`;
  }
}

// 创建全局实例
const tenantContext = new TenantContext();

// 导出实例和类
export default tenantContext;
export { TenantContext };