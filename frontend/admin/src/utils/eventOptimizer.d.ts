/**
 * 事件监听器优化工具类型声明
 */

export interface EventCoordinates {
  x: number;
  y: number;
}

export interface EventHandlers {
  mousedown?: (event: MouseEvent) => void;
  mousemove?: (event: MouseEvent) => void;
  mouseup?: (event: MouseEvent) => void;
  touchstart?: (event: TouchEvent) => void;
  touchmove?: (event: TouchEvent) => void;
  touchend?: (event: TouchEvent) => void;
}

export function addOptimizedEventListener(
  element: Element | Document,
  event: string,
  handler: EventListener,
  options?: AddEventListenerOptions | boolean
): () => void;

export function addCaptchaEventListeners(
  element: Element,
  handlers: EventHandlers
): () => void;

export function addGlobalDragListeners(
  handlers: EventHandlers
): () => void;

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): T;

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): T;

export function isTouchDevice(): boolean;

export function getEventCoordinates(event: MouseEvent | TouchEvent): EventCoordinates;

export const supportsPassive: boolean;

declare const _default: {
  addOptimizedEventListener: typeof addOptimizedEventListener;
  addCaptchaEventListeners: typeof addCaptchaEventListeners;
  addGlobalDragListeners: typeof addGlobalDragListeners;
  throttle: typeof throttle;
  debounce: typeof debounce;
  isTouchDevice: typeof isTouchDevice;
  getEventCoordinates: typeof getEventCoordinates;
  supportsPassive: typeof supportsPassive;
};

export default _default;