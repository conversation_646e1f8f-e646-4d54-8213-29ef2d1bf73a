/**
 * Vue组件类型声明
 * 解决Vue文件的TypeScript类型推断问题
 */

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 解决特定组件导入问题
declare module '@/components/*' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/views/*' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '../views/*' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '../views/assessment/*' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module './components/*' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '../../../shared/*' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 具体的shared组件声明
declare module '@shared/SlideCaptcha.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '../../../shared/SlideCaptcha.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Data module declarations
declare module '@/data/verified-prompts' {
  export const verifiedPrompts: any;
}

// 具体的views组件声明
declare module '../views/LayoutView.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '../views/assessment/ScaleManagement.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '../views/assessment/RecordManagement.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module './components/AIChatDialog.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Element Plus 全局组件类型声明
declare global {
  const ElMessage: any;
  const ElMessageBox: any;
  const ElNotification: any;
  const ElLoading: any;
  
  // Element Plus 图标组件
  const Plus: any;
  const Check: any;
  const Close: any;
  const Clock: any;
  const Refresh: any;
  const TrendCharts: any;
  
  // Vue 相关全局类型
  interface Window {
    // 添加任何需要的window属性
  }
}

// 解决 Element Plus 表单验证规则类型问题
declare module 'element-plus' {
  interface FormItemRule {
    type?: string;
    required?: boolean;
    message?: string;
    trigger?: string | string[];
    pattern?: RegExp;
    min?: number;
    max?: number;
    validator?: (rule: any, value: any, callback: any) => void;
  }
}

export {}