// 评估相关类型定义

export interface Assessment {
  id: string
  title: string
  description?: string
  category: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  createTime?: string
  updateTime?: string
  creatorId?: string
  creatorName?: string
  elderlyId?: string
  elderlyName?: string
  totalScore?: number
  maxScore?: number
  progress?: number
  duration?: number
  completedAt?: string
  resultId?: string
  scaleId?: string
  scaleName?: string
  tenantId?: string
}

export interface AssessmentQuestion {
  id: string
  assessmentId: string
  scaleId: string
  questionText: string
  questionType: 'single' | 'multiple' | 'text' | 'score' | 'yesno'
  options?: AssessmentOption[]
  required: boolean
  order: number
  score?: number
  maxScore?: number
  weight?: number
  dimension?: string
  category?: string
  description?: string
  imageUrl?: string
  videoUrl?: string
  audioUrl?: string
}

export interface AssessmentOption {
  id: string
  questionId: string
  optionText: string
  optionValue: string | number
  score: number
  order: number
  isCorrect?: boolean
  imageUrl?: string
}

export interface AssessmentAnswer {
  id: string
  assessmentId: string
  questionId: string
  answerValue: any
  answerText?: string
  score?: number
  isCorrect?: boolean
  answerTime?: string
  duration?: number
}

export interface AssessmentResult {
  id: string
  assessmentId: string
  elderlyId: string
  elderlyName?: string
  totalScore: number
  maxScore: number
  percentage: number
  level: string
  levelDescription?: string
  dimensions: AssessmentDimension[]
  suggestions: string[]
  riskFactors: string[]
  strengths: string[]
  weaknesses: string[]
  recommendations: string[]
  createTime: string
  completedAt: string
  duration: number
  answerCount: number
  correctCount: number
  tenantId?: string
}

export interface AssessmentDimension {
  name: string
  description?: string
  score: number
  maxScore: number
  percentage: number
  level: string
  questions: number
  correctAnswers: number
}

export interface AssessmentScale {
  id: string
  name: string
  description?: string
  category: string
  version: string
  status: 'active' | 'inactive' | 'draft'
  dimensions: string[]
  questionCount: number
  maxScore: number
  timeLimit?: number
  passingScore?: number
  instructions?: string
  createTime: string
  updateTime?: string
  creatorId?: string
  tenantId?: string
}

export interface AssessmentTemplate {
  id: string
  name: string
  description?: string
  category: string
  scaleIds: string[]
  isDefault: boolean
  configuration: Record<string, any>
  createTime: string
  updateTime?: string
  tenantId?: string
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  code?: number
  timestamp?: string
}

export interface PaginatedResponse<T = any> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 请求参数类型
export interface AssessmentListParams {
  page?: number
  pageSize?: number
  status?: string
  category?: string
  keyword?: string
  elderlyId?: string
  scaleId?: string
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface CreateAssessmentParams {
  title: string
  description?: string
  category: string
  elderlyId: string
  scaleId: string
  instructions?: string
  timeLimit?: number
}

export interface UpdateAssessmentParams {
  title?: string
  description?: string
  category?: string
  status?: Assessment['status']
  instructions?: string
  timeLimit?: number
}

export interface SubmitAnswersParams {
  assessmentId: string
  answers: Record<string, any>
  duration?: number
  completedAt?: string
}