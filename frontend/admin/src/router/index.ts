/// <reference types="vite/client" />
import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        title: '用户登录',
        hideInMenu: true,
      },
    },
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: '/assessment',
      name: 'assessment',
      // @ts-ignore - Vue 组件类型声明待完善
      component: () => import('../views/LayoutView.vue'),
      meta: {
        requiresAuth: true,
      },
      children: [
        {
          path: 'pdf-upload',
          name: 'pdf-upload',
          component: () => import('../views/assessment/PdfUploadNew.vue'),
          meta: {
            title: '量表上传并数字化',
            icon: 'Upload',
            requiresAuth: true,
          },
        },
        {
          path: 'scales',
          name: 'scales',
          // @ts-ignore - Vue 组件类型声明待完善
          component: () => import('../views/assessment/ScaleManagement.vue'),
          meta: {
            title: '量表管理',
            icon: 'DocumentChecked',
            requiresAuth: true,
          },
        },
        {
          path: 'records',
          name: 'records',
          // @ts-ignore - Vue 组件类型声明待完善
          component: () => import('../views/assessment/RecordManagement.vue'),
          meta: {
            title: '评估记录',
            icon: 'Notebook',
            requiresAuth: true,
          },
        },
      ],
    },
    {
      path: '/system',
      name: 'system',
      // @ts-ignore - Vue 组件类型声明待完善
      component: () => import('../views/LayoutView.vue'),
      meta: {
        requiresAuth: true,
        title: '系统管理',
        icon: 'Setting',
      },
      children: [
        {
          path: 'tenants',
          name: 'system-tenants',
          component: () => import('../views/system/TenantManagement.vue'),
          meta: {
            title: '租户管理',
            icon: 'OfficeBuilding',
            requiresAuth: true,
          },
        },
        {
          path: 'users',
          name: 'system-users',
          component: () => import('../views/system/UserManagement.vue'),
          meta: {
            title: '用户管理',
            icon: 'User',
            requiresAuth: true,
          },
        },
        {
          path: 'scales',
          name: 'system-scales',
          component: () => import('../views/system/ScaleManagement.vue'),
          meta: {
            title: '量表管理',
            icon: 'DocumentChecked',
            requiresAuth: true,
          },
        },
        {
          path: 'assessments',
          name: 'system-assessments',
          component: () => import('../views/system/AssessmentManagement.vue'),
          meta: {
            title: '评估记录管理',
            icon: 'Notebook',
            requiresAuth: true,
          },
        },
        {
          path: 'dashboard',
          name: 'system-dashboard',
          component: () => import('../views/system/SystemDashboard.vue'),
          meta: {
            title: '系统监控面板',
            icon: 'Monitor',
            requiresAuth: true,
          },
        },
      ],
    },
  ],
});

// 清理认证信息的函数
const clearAuthData = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  localStorage.removeItem('refreshToken');
  sessionStorage.clear();
  console.log('🧹 认证信息已清理');
};

// 路由守卫 - 简化版本，确保登录保护正常工作
router.beforeEach((to, _from, next) => {
  console.log(`🔐 路由守卫检查: ${to.path}`);

  const token = localStorage.getItem('token');
  console.log(`🎫 Token状态: ${token ? '存在' : '不存在'}`);

  // 检查是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  console.log(`🔒 页面是否需要认证: ${requiresAuth}`);

  // 如果页面需要认证但没有token，跳转到登录页
  if (requiresAuth && !token) {
    console.log('❌ 需要认证但未找到token，强制跳转到登录页');
    next({ path: '/login' });
    return;
  }

  // 如果有token，验证格式
  if (requiresAuth && token) {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;

      if (payload.exp && payload.exp < currentTime) {
        console.log('⏱️ Token已过期，清除并跳转到登录页');
        clearAuthData();
        next({ path: '/login' });
        return;
      }
    } catch (error) {
      console.log('🚫 Token格式无效，清除并跳转到登录页', error);
      clearAuthData();
      next({ path: '/login' });
      return;
    }
  }

  // 如果已登录访问登录页，重定向到首页
  if (to.path === '/login' && token) {
    console.log('✅ 已登录用户访问登录页，重定向到首页');
    next({ path: '/' });
    return;
  }

  console.log('✅ 路由守卫通过，允许访问');
  next();
});

export default router;
