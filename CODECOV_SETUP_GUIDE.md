# 🔑 CODECOV_TOKEN 配置指南

## 快速配置步骤

### 1. 获取 Codecov Token
1. 访问 [Codecov](https://app.codecov.io/)
2. 使用 GitHub 账号登录
3. 搜索并添加仓库: `changxiaoyangbrain/assessment`
4. 进入仓库设置页面
5. 找到 **General** → **Upload Token**
6. 复制 Repository Upload Token

### 2. 配置 GitHub Secrets
1. 访问 GitHub 仓库设置页面:
   ```
   https://github.com/changxiaoyangbrain/assessment/settings/secrets/actions
   ```

2. 点击 **"New repository secret"**

3. 配置参数:
   - **Name**: `CODECOV_TOKEN`
   - **Secret**: 粘贴从 Codecov 复制的 Token
   - ⚠️ 注意：不要包含 `CODECOV_TOKEN=` 前缀

4. 点击 **"Add secret"** 保存

### 3. 验证配置
运行 GitHub Actions 测试:
```
https://github.com/changxiaoyangbrain/assessment/actions/workflows/test-secrets.yml
```

## 🎯 配置完成后的效果

✅ **立即生效：**
- 自动生成测试覆盖率报告
- 覆盖率数据上传到 Codecov
- PR 中显示覆盖率变化
- 配置完整性评分提升至 60/100

✅ **长期收益：**
- 实时覆盖率监控和趋势分析
- 质量门禁和覆盖率要求
- 团队代码质量可视化
- CI/CD 流水线质量保证

## 🔍 故障排除

### 问题：Token 无效
- 确认 Token 完整复制（包含所有字符）
- 确认仓库在 Codecov 中已正确添加
- 检查 GitHub Secrets 名称是否为 `CODECOV_TOKEN`

### 问题：上传失败
- 确认仓库权限设置
- 检查 GitHub Actions 工作流语法
- 查看 Actions 日志详细错误信息

### 问题：公开仓库选择
对于公开仓库，Codecov 2025版支持：
- **Token 方式**：最安全，推荐使用
- **Tokenless 方式**：简化配置，但功能受限
- **OIDC 方式**：企业级认证，高级功能

## 📞 获取帮助

- 📚 Codecov 官方文档: https://docs.codecov.com/
- 🎬 配置视频教程: https://about.codecov.io/blog/
- 💬 社区支持: https://github.com/codecov/feedback

---

⚡ **快速链接**
- [GitHub Secrets 设置](https://github.com/changxiaoyangbrain/assessment/settings/secrets/actions)
- [Codecov 登录](https://app.codecov.io/)
- [验证测试](https://github.com/changxiaoyangbrain/assessment/actions/workflows/test-secrets.yml)