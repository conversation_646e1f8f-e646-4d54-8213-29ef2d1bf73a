# IDE
.idea/
.vscode/
*.iml
*.iws
*.ipr
.DS_Store

# 编译输出
target/
build/
dist/
out/
*.class
*.jar
*.war

# 日志
logs/
*.log

# 环境配置
.env
.env.local
.env.*.local

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-store/

# 前端构建 (Monorepo架构)
frontend/packages/*/dist/
frontend/packages/*/coverage/
frontend/packages/mobile/unpackage/
frontend/packages/*/node_modules/
frontend/backup/

# 备份文件
*.bak
*.backup
*.deprecated.*

# TypeScript构建信息
*.tsbuildinfo

# 测试覆盖率
coverage/
*.lcov
.nyc_output/

# 数据目录
data/
backup/
docker/postgres-data/
database-backups/

# 第三方库
anji-captcha/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv
pip-log.txt

# 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini

# 临时文件
*.tmp
*.temp
*.swp
*~

# 证书和密钥
*.pem
*.key
*.crt
nginx/ssl/

# GitHub Actions secrets
.secrets
.env.secrets
.env.prod
*.p12
ssl/

# Docker运行时目录
docker/input/
docker/output/
docker/models/

# 开发调试文件
dev-debug.log

# 任务文件
# tasks.json
# tasks/