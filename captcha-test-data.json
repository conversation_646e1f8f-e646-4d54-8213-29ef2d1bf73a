{"test_scenarios": [{"name": "验证码生成测试", "method": "GET", "url": "http://localhost:8181/api/captcha/get", "headers": {"Accept": "application/json"}, "expected_response": {"success": true, "data": {"token": "string", "originalImageBase64": "string", "jigsawImageBase64": "string", "secretKey": "string", "result": false}}}, {"name": "验证码校验测试", "method": "POST", "url": "http://localhost:8181/api/captcha/check", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "body": {"captchaType": "blockPuzzle", "token": "{{token_from_generate}}", "pointJson": "{\"x\":120,\"y\":5}", "verification": "{{secret_key_from_generate}}"}, "expected_response": {"success": true, "data": {"result": "boolean", "message": "string"}}}, {"name": "验证码二次验证测试", "method": "POST", "url": "http://localhost:8181/api/captcha/verify", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "body": {"captchaType": "blockPuzzle", "token": "{{token_from_generate}}", "pointJson": "{\"x\":120,\"y\":5}", "verification": "{{secret_key_from_generate}}"}, "expected_response": {"success": true, "data": {"result": "boolean", "message": "string"}}}], "test_data_examples": {"valid_slide_position": {"x": 120, "y": 5}, "invalid_slide_position": {"x": 50, "y": 5}, "edge_case_positions": [{"x": 0, "y": 5}, {"x": 310, "y": 5}, {"x": 155, "y": 5}]}, "component_integration_tests": {"uni_app": {"component_path": "frontend/uni-app/src/components/AjCaptcha/index.vue", "api_path": "frontend/uni-app/src/api/captcha.js", "login_integration": "frontend/uni-app/src/pages/login/index.vue"}, "vue3_admin": {"component_path": "frontend/admin/src/components/AjCaptcha.vue", "api_path": "frontend/admin/src/api/captcha.js", "login_integration": "frontend/admin/src/views/LoginView.vue"}}, "backend_implementation": {"service": "backend/src/main/java/com/assessment/service/SimpleCaptchaService.java", "controller": "backend/src/main/java/com/assessment/controller/CaptchaController.java", "dto": "backend/src/main/java/com/assessment/dto/CaptchaVerifyRequest.java", "response": "backend/src/main/java/com/assessment/common/response/ApiResponse.java"}, "configuration": {"redis_key_prefix": "simple_captcha:", "expire_time": "5 minutes", "tolerance": "5 pixels", "image_size": {"width": 310, "height": 155, "piece_width": 47, "piece_height": 47}}}