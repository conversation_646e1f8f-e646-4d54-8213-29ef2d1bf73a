<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台最终测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .test-section { padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; border-radius: 4px; }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .captcha-display { margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        button { padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        img { max-width: 100%; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏁 管理后台验证码最终测试</h1>
        <p>重构后的LoginView.vue验证码功能测试</p>
        
        <div class="status success">
            <strong>✅ 重构完成:</strong>
            <ul style="margin: 5px 0; padding-left: 20px;">
                <li>直接使用共享组件 SharedSlideCaptcha</li>
                <li>移除中间包装器避免props传递问题</li>
                <li>添加调试信息显示验证状态</li>
                <li>修复组件导入路径</li>
            </ul>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <h3>🖥️ 管理后台登录页面</h3>
                <iframe src="http://localhost:5274/login" title="管理后台登录页面"></iframe>
            </div>
            
            <div class="test-section">
                <h3>📱 uni-app登录页面</h3>
                <iframe src="http://localhost:5273/#/pages/login/index" title="uni-app登录页面"></iframe>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 直接API测试</h3>
            <button onclick="testCaptchaAPI()">🔄 测试验证码API</button>
            <div id="captcha-result" class="captcha-display">
                <p>点击按钮测试验证码API...</p>
            </div>
        </div>
        
        <div class="status info">
            <h4>📊 测试检查清单:</h4>
            <ul style="margin: 5px 0; padding-left: 20px;">
                <li>✅ 管理后台登录页面能正常加载</li>
                <li>🔍 验证码组件是否在管理后台中显示</li>
                <li>🎨 验证码是否显示彩色效果（天蓝色渐变背景）</li>
                <li>✅ uni-app验证码仍然正常工作</li>
                <li>✅ 验证码API直接调用正常</li>
            </ul>
        </div>
        
        <div style="margin: 20px 0; font-size: 12px; color: #666;">
            测试时间: <span id="timestamp"></span>
        </div>
    </div>

    <script>
        // 显示时间戳
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
        
        async function testCaptchaAPI() {
            const resultDiv = document.getElementById('captcha-result');
            resultDiv.innerHTML = '<p>🔄 正在测试验证码API...</p>';
            
            try {
                const response = await fetch('http://localhost:8181/api/captcha/get', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data) {
                    const { originalImageBase64, jigsawImageBase64, y, token } = data.data;
                    
                    resultDiv.innerHTML = `
                        <div style="margin: 15px 0;">
                            <h4>🖼️ 背景图片（应为彩色）：</h4>
                            <img src="data:image/png;base64,${originalImageBase64}" 
                                 style="border: 2px solid #007bff; max-width: 100%; background: #f0f0f0;" 
                                 alt="验证码背景"/>
                            <p style="font-size: 12px; color: #666; margin: 5px 0;">
                                ✅ 检查背景是否为<strong>天蓝色渐变</strong>，带有<strong>白色网格线</strong>
                            </p>
                        </div>
                        <div style="margin: 15px 0;">
                            <h4>🧩 拼图滑块：</h4>
                            <img src="data:image/png;base64,${jigsawImageBase64}" 
                                 style="border: 2px solid #28a745; background: #f8f9fa;" 
                                 alt="验证码滑块"/>
                            <p style="font-size: 12px; color: #666; margin: 5px 0;">
                                ✅ 检查滑块是否有<strong>柔和的渐变填充</strong>
                            </p>
                        </div>
                        <div style="margin: 15px 0; padding: 10px; background: #e9ecef; border-radius: 4px; font-size: 12px;">
                            <p><strong>📍 技术参数:</strong></p>
                            <p>Y坐标: ${y} | Token: ${token.substring(0, 20)}...</p>
                            <p style="color: green;">✅ API测试成功！彩色验证码正常生成</p>
                        </div>
                    `;
                } else {
                    throw new Error('API返回数据格式错误');
                }
                
            } catch (error) {
                console.error('验证码API测试失败:', error);
                resultDiv.innerHTML = `<p style="color: red;">❌ API测试失败: ${error.message}</p>`;
            }
        }

        // 检查iframe加载状态
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach((iframe, index) => {
            iframe.onload = function() {
                console.log(`✅ iframe ${index + 1} 加载成功`);
            };
            iframe.onerror = function() {
                console.error(`❌ iframe ${index + 1} 加载失败`);
            };
        });
        
        // 自动测试API
        setTimeout(() => {
            testCaptchaAPI();
        }, 2000);
    </script>
</body>
</html>