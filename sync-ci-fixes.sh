#!/bin/bash

# 同步 CI/CD 自动修复代码的脚本
echo "🔄 同步 CI/CD 自动修复的代码"
echo "=================================="

# 1. 获取远程最新信息
echo "📡 获取远程仓库最新信息..."
git fetch origin

# 2. 检查是否有新的提交
echo "🔍 检查远程是否有新提交..."
BEHIND=$(git rev-list --count HEAD..origin/main)
AHEAD=$(git rev-list --count origin/main..HEAD)

if [ "$BEHIND" -gt 0 ]; then
    echo "✨ 发现 $BEHIND 个新提交需要拉取"
    
    # 3. 显示即将拉取的提交
    echo "📋 即将拉取的提交:"
    git log --oneline HEAD..origin/main
    
    # 4. 询问是否继续
    echo ""
    read -p "是否要拉取这些更改？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 5. 拉取更改
        echo "⬇️ 拉取远程更改..."
        git pull origin main
        
        echo "✅ 同步完成！"
        echo "📊 当前本地状态:"
        git log --oneline -5
    else
        echo "❌ 操作已取消"
        exit 0
    fi
    
elif [ "$AHEAD" -gt 0 ]; then
    echo "⚠️ 本地领先远程 $AHEAD 个提交"
    echo "📋 本地未推送的提交:"
    git log --oneline origin/main..HEAD
    echo "💡 建议先推送本地更改: git push origin main"
    
else
    echo "✅ 本地和远程已同步，无需拉取"
fi

# 6. 显示当前状态
echo ""
echo "📊 当前分支状态:"
git status --porcelain
if [ $? -eq 0 ] && [ -z "$(git status --porcelain)" ]; then
    echo "✅ 工作目录干净"
else
    echo "⚠️ 有未提交的更改"
fi

# 7. 显示最新的 CI/CD 工作流状态
echo ""
echo "🤖 最新 CI/CD 工作流状态:"
if command -v gh &> /dev/null; then
    gh run list --limit 3 --json status,conclusion,displayTitle,createdAt | jq -r '.[] | "\(.status) - \(.displayTitle) (\(.createdAt[:10]))"'
else
    echo "💡 安装 GitHub CLI (gh) 可查看工作流状态"
fi