<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试中文背景图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .chinese-bg {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .other-bg {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🀄 快速测试中文背景图</h1>
        <p>点击按钮多次测试，直到出现中文字符背景图</p>
        
        <button class="test-button" onclick="testCaptcha()">🔄 获取验证码</button>
        <button class="test-button" onclick="testMultiple()">🎲 连续测试10次</button>
        
        <div id="result"></div>
        <div id="stats">
            <p>总测试: <span id="total">0</span> | 中文背景: <span id="chinese">0</span> | 其他背景: <span id="other">0</span></p>
        </div>
    </div>

    <script>
        let stats = { total: 0, chinese: 0, other: 0 };
        
        async function testCaptcha() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 测试中...</p>';
            
            try {
                const response = await fetch('http://localhost:8181/api/captcha/get');
                const data = await response.json();
                
                if (data.success && data.data) {
                    stats.total++;
                    const { originalImageBase64, jigsawImageBase64 } = data.data;
                    
                    // 简单判断：中文背景图的base64数据通常较大
                    const isChineseBg = originalImageBase64.length > 100000;
                    
                    if (isChineseBg) {
                        stats.chinese++;
                        resultDiv.className = 'result chinese-bg';
                        resultDiv.innerHTML = `
                            <h3>🎉 发现中文背景图！</h3>
                            <div style="display: flex; gap: 20px;">
                                <div>
                                    <h4>背景图</h4>
                                    <img src="data:image/png;base64,${originalImageBase64}" style="max-width: 300px; border: 2px solid green;"/>
                                </div>
                                <div>
                                    <h4>拼图块</h4>
                                    <img src="data:image/png;base64,${jigsawImageBase64}" style="border: 2px solid green;"/>
                                    <p>✅ 成功！拼图块包含中文字符内容</p>
                                </div>
                            </div>
                        `;
                    } else {
                        stats.other++;
                        resultDiv.className = 'result other-bg';
                        resultDiv.innerHTML = `
                            <h3>📷 其他背景图</h3>
                            <div style="display: flex; gap: 20px;">
                                <div>
                                    <h4>背景图</h4>
                                    <img src="data:image/png;base64,${originalImageBase64}" style="max-width: 300px; border: 2px solid orange;"/>
                                </div>
                                <div>
                                    <h4>拼图块</h4>
                                    <img src="data:image/png;base64,${jigsawImageBase64}" style="border: 2px solid orange;"/>
                                    <p>这是其他AjCaptcha背景图</p>
                                </div>
                            </div>
                        `;
                    }
                    
                    updateStats();
                } else {
                    throw new Error('API返回错误');
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">❌ 错误: ${error.message}</p>`;
            }
        }
        
        async function testMultiple() {
            for (let i = 0; i < 10; i++) {
                await testCaptcha();
                await new Promise(r => setTimeout(r, 500));
            }
        }
        
        function updateStats() {
            document.getElementById('total').textContent = stats.total;
            document.getElementById('chinese').textContent = stats.chinese;
            document.getElementById('other').textContent = stats.other;
        }
        
        // 自动测试一次
        testCaptcha();
    </script>
</body>
</html>