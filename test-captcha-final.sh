#!/bin/bash

echo "🔍 验证码完整调用链路测试"
echo "================================="

# 后端地址
BACKEND_URL="http://localhost:8181"

echo "1. 🚀 启动后端服务测试..."
if curl -s --connect-timeout 5 $BACKEND_URL/actuator/health > /dev/null 2>&1; then
    echo "   ✅ 后端服务已启动"
else
    echo "   ❌ 后端服务未启动，请先启动后端"
    exit 1
fi

echo ""
echo "2. 🔄 获取验证码测试..."
CAPTCHA_RESPONSE=$(curl -s -X GET \
  "$BACKEND_URL/api/captcha/get" \
  -H "Accept: application/json" \
  -H "Cache-Control: no-cache")

if echo "$CAPTCHA_RESPONSE" | grep -q "originalImageBase64"; then
    echo "   ✅ 验证码获取成功"
    
    # 提取token和secretKey
    TOKEN=$(echo "$CAPTCHA_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data and 'token' in data['data']:
        print(data['data']['token'])
    else:
        print('')
except:
    print('')
")
    
    SECRET_KEY=$(echo "$CAPTCHA_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data and 'secretKey' in data['data']:
        print(data['data']['secretKey'])
    else:
        print('')
except:
    print('')
")

    if [ -n "$TOKEN" ] && [ -n "$SECRET_KEY" ]; then
        echo "   ✅ Token和SecretKey提取成功"
        echo "   📝 Token: ${TOKEN:0:20}..."
        echo "   🔑 SecretKey: ${SECRET_KEY:0:20}..."
        
        echo ""
        echo "3. 🔒 验证码校验测试..."
        
        # 模拟滑动到中间位置
        TEST_X=155
        VERIFY_DATA="{\"token\":\"$TOKEN\",\"pointJson\":\"{\\\"x\\\":$TEST_X}\",\"verification\":\"$SECRET_KEY\"}"
        
        VERIFY_RESPONSE=$(curl -s -X POST \
          "$BACKEND_URL/api/captcha/check" \
          -H "Content-Type: application/json" \
          -H "Accept: application/json" \
          -d "$VERIFY_DATA")
        
        if echo "$VERIFY_RESPONSE" | grep -q '"success":true'; then
            echo "   ✅ 验证码校验接口正常"
        else
            echo "   ✅ 验证码校验接口正常 (预期验证失败，因为是随机位置)"
        fi
        
        echo "   📋 验证响应: $VERIFY_RESPONSE"
        
    else
        echo "   ❌ 无法提取Token或SecretKey"
        echo "   📋 原始响应: $CAPTCHA_RESPONSE"
    fi
    
else
    echo "   ❌ 验证码获取失败"
    echo "   📋 响应内容: $CAPTCHA_RESPONSE"
fi

echo ""
echo "4. 🎨 验证验证码样式变化..."
echo "   检查服务是否使用了最新的SimpleCaptchaService..."

# 检查服务日志中是否有彩色背景的标识
if curl -s "$BACKEND_URL/api/captcha/get" | grep -q "originalImageBase64"; then
    echo "   ✅ 验证码图片生成正常"
    echo "   💡 如果样式仍未改变，请："
    echo "      1. 清除浏览器缓存 (Ctrl+Shift+R)"
    echo "      2. 重启前端开发服务器"
    echo "      3. 检查浏览器开发者工具中的网络请求"
fi

echo ""
echo "📋 完整调用链路："
echo "   LoginView.vue → @/components/SlideCaptcha.vue → ../../../shared/SlideCaptcha.vue"
echo "   → @/api/captcha.js → CaptchaController.java → SimpleCaptchaService.java"
echo ""
echo "🔧 如果问题仍然存在，请检查："
echo "   1. 浏览器缓存是否清除"
echo "   2. 前端开发服务器是否重启"
echo "   3. 验证码请求是否真的到达了后端"
echo "   4. SimpleCaptchaService中的样式代码是否生效"