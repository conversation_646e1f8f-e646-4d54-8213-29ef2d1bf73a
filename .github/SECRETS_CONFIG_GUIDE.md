# GitHub Secrets 配置指南 - 智能评估平台

## 🔐 快速配置清单

### 必须配置的 Secrets (CI/CD 正常运行所需)

| Secret 名称 | 描述 | 优先级 | 示例值 |
|-------------|------|--------|--------|
| `CODECOV_TOKEN` | Codecov 覆盖率上传令牌 | 🔴 必需 | `a1b2c3d4-e5f6-...` |
| `DINGTALK_WEBHOOK` | 钉钉机器人通知地址 | 🟡 推荐 | `https://oapi.dingtalk.com/robot/send?access_token=...` |
| `DOCKER_USERNAME` | Docker Hub 用户名 | 🟡 推荐 | `your-dockerhub-username` |
| `DOCKER_PASSWORD` | Docker Hub 密码/令牌 | 🟡 推荐 | `dckr_pat_abcdef123456...` |

### 可选配置的 Secrets (扩展功能)

| Secret 名称 | 描述 | 优先级 | 示例值 |
|-------------|------|--------|--------|
| `DEV_HOST` | 开发服务器地址 | 🔵 可选 | `dev.assessment.com` |
| `DEV_USERNAME` | 开发服务器用户名 | 🔵 可选 | `deploy` |
| `DEV_SSH_KEY` | 开发服务器SSH私钥 | 🔵 可选 | `-----BEGIN OPENSSH PRIVATE KEY-----...` |
| `PROD_HOST` | 生产服务器地址 | 🔵 可选 | `prod.assessment.com` |
| `PROD_USERNAME` | 生产服务器用户名 | 🔵 可选 | `deploy` |
| `PROD_SSH_KEY` | 生产服务器SSH私钥 | 🔵 可选 | `-----BEGIN OPENSSH PRIVATE KEY-----...` |
| `SONAR_TOKEN` | SonarCloud 代码质量令牌 | 🔵 可选 | `sqp_abc123def456...` |

## 🚀 逐步配置指南

### Step 1: 配置 Codecov (必需)

#### 1.1 注册 Codecov 账户
```bash
# 访问 Codecov 官网
open https://app.codecov.io/

# 使用 GitHub 账户登录
# 授权 Codecov 访问你的仓库
```

#### 1.2 获取项目 Token
```bash
# 在 Codecov 中添加 Assessment 项目
# 复制 Repository Upload Token
# 格式类似: a1b2c3d4-e5f6-7890-1234-567890abcdef
```

#### 1.3 添加到 GitHub Secrets
```bash
# 1. 访问 GitHub 仓库
open https://github.com/你的组织/Assessment/settings/secrets/actions

# 2. 点击 "New repository secret"
# 3. 添加:
Name: CODECOV_TOKEN
Value: [你的 Codecov Token]
```

### Step 2: 配置钉钉通知 (推荐)

#### 2.1 创建钉钉机器人
```bash
# 1. 打开钉钉群聊
# 2. 群设置 -> 智能群助手 -> 添加机器人
# 3. 选择 "自定义" -> 添加
# 4. 配置机器人名称: "Assessment CI/CD Bot"
# 5. 安全设置选择 "加签" (推荐) 或 "关键词"
# 6. 复制 Webhook 地址
```

#### 2.2 添加到 GitHub Secrets
```bash
Name: DINGTALK_WEBHOOK
Value: https://oapi.dingtalk.com/robot/send?access_token=你的token
```

### Step 3: 配置 Docker Hub (推荐)

#### 3.1 创建 Docker Hub 访问令牌
```bash
# 1. 登录 Docker Hub
open https://hub.docker.com/settings/security

# 2. 点击 "New Access Token"
# 3. 输入描述: "Assessment CI/CD"
# 4. 权限选择: "Read, Write, Delete"
# 5. 复制生成的令牌 (格式: dckr_pat_...)
```

#### 3.2 添加到 GitHub Secrets
```bash
Name: DOCKER_USERNAME
Value: 你的DockerHub用户名

Name: DOCKER_PASSWORD  
Value: dckr_pat_你的访问令牌
```

### Step 4: 配置部署服务器 (可选)

#### 4.1 生成专用 SSH 密钥
```bash
# 1. 在本地生成 SSH 密钥对
ssh-keygen -t ed25519 -C "github-actions@assessment" -f ~/.ssh/assessment_deploy

# 2. 查看私钥内容
cat ~/.ssh/assessment_deploy

# 3. 查看公钥内容  
cat ~/.ssh/assessment_deploy.pub
```

#### 4.2 配置服务器公钥
```bash
# 将公钥添加到目标服务器
ssh-copy-id -i ~/.ssh/assessment_deploy.pub deploy@your-dev-server
ssh-copy-id -i ~/.ssh/assessment_deploy.pub deploy@your-prod-server

# 或手动添加到服务器的 ~/.ssh/authorized_keys
```

#### 4.3 添加到 GitHub Secrets
```bash
# 开发环境
Name: DEV_HOST
Value: dev.assessment.com

Name: DEV_USERNAME
Value: deploy

Name: DEV_SSH_KEY
Value: [~/.ssh/assessment_deploy 的完整内容]

# 生产环境 (类似配置)
Name: PROD_HOST
Value: prod.assessment.com

Name: PROD_USERNAME  
Value: deploy

Name: PROD_SSH_KEY
Value: [生产环境的 SSH 私钥]
```

## 🧪 配置验证

### 创建测试工作流
```yaml
# .github/workflows/test-secrets.yml
name: Test Secrets Configuration

on:
  workflow_dispatch:

jobs:
  test-secrets:
    runs-on: ubuntu-latest
    steps:
    - name: Test Codecov Token
      run: |
        if [ -n "${{ secrets.CODECOV_TOKEN }}" ]; then
          echo "✅ Codecov Token 已配置"
        else
          echo "❌ Codecov Token 未配置"
          exit 1
        fi
    
    - name: Test DingTalk Webhook
      run: |
        if [ -n "${{ secrets.DINGTALK_WEBHOOK }}" ]; then
          echo "✅ DingTalk Webhook 已配置"
          # 测试发送通知
          curl -X POST "${{ secrets.DINGTALK_WEBHOOK }}" \
            -H 'Content-Type: application/json' \
            -d '{"msgtype": "text", "text": {"content": "GitHub Secrets 配置测试成功! ✅"}}' || echo "⚠️ 通知发送失败，请检查 Webhook 地址"
        else
          echo "⚠️ DingTalk Webhook 未配置 (可选)"
        fi
    
    - name: Test Docker Credentials
      run: |
        if [ -n "${{ secrets.DOCKER_USERNAME }}" ] && [ -n "${{ secrets.DOCKER_PASSWORD }}" ]; then
          echo "✅ Docker 认证信息已配置"
          # 测试登录 (不推送)
          echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_USERNAME }}" --password-stdin || echo "⚠️ Docker 登录失败"
        else
          echo "⚠️ Docker 认证信息未配置 (可选)"
        fi
    
    - name: Configuration Summary
      run: |
        echo "🔐 GitHub Secrets 配置总结:"
        echo "✅ 必需配置:"
        echo "  - Codecov Token: ${{ secrets.CODECOV_TOKEN != '' && '已配置' || '未配置' }}"
        echo ""
        echo "⚠️ 推荐配置:"
        echo "  - DingTalk Webhook: ${{ secrets.DINGTALK_WEBHOOK != '' && '已配置' || '未配置' }}"
        echo "  - Docker Username: ${{ secrets.DOCKER_USERNAME != '' && '已配置' || '未配置' }}"
        echo "  - Docker Password: ${{ secrets.DOCKER_PASSWORD != '' && '已配置' || '未配置' }}"
        echo ""
        echo "🔵 可选配置:"
        echo "  - Dev Server: ${{ secrets.DEV_HOST != '' && '已配置' || '未配置' }}"
        echo "  - Prod Server: ${{ secrets.PROD_HOST != '' && '已配置' || '未配置' }}"
```

### 运行验证测试
```bash
# 1. 提交测试工作流文件
git add .github/workflows/test-secrets.yml
git commit -m "feat: add secrets configuration test"
git push

# 2. 在 GitHub Actions 中手动运行测试
# 访问: https://github.com/你的组织/Assessment/actions/workflows/test-secrets.yml
# 点击 "Run workflow"

# 3. 查看测试结果，确认所有必需的 Secrets 已正确配置
```

## 🔒 安全最佳实践

### 1. 权限最小化
```bash
# Docker Hub 令牌权限
- 仅授予必要的读写权限
- 避免使用 Admin 权限

# SSH 密钥管理
- 为每个环境使用独立的密钥对
- 定期轮换密钥 (每季度)
- 使用强密钥类型 (ed25519 优于 rsa)
```

### 2. 监控和审计
```bash
# 定期检查 Secret 使用情况
- GitHub Settings -> Actions -> General -> Audit log
- 监控异常访问模式
- 设置访问告警
```

### 3. 轮换计划
```bash
# 建议的轮换频率
Codecov Token: 每年
Docker Hub Token: 每季度  
SSH 密钥: 每季度
DingTalk Webhook: 按需 (泄露时)
```

## 🚨 故障排除

### 问题 1: Codecov 上传失败
```bash
# 症状: 覆盖率数据未出现在 Codecov 仪表板
# 解决方案:
1. 检查 CODECOV_TOKEN 是否正确
2. 确认项目在 Codecov 中已添加
3. 检查覆盖率文件是否生成 (jacoco.xml)
4. 查看 GitHub Actions 日志中的错误信息
```

### 问题 2: 钉钉通知无法发送
```bash
# 症状: 工作流正常但收不到钉钉通知
# 解决方案:
1. 验证 DINGTALK_WEBHOOK URL 格式
2. 检查钉钉机器人安全设置
3. 确认群聊中机器人状态正常
4. 测试手动发送 curl 请求
```

### 问题 3: Docker 推送失败
```bash
# 症状: docker push 命令失败
# 解决方案:
1. 检查 DOCKER_USERNAME 和 DOCKER_PASSWORD
2. 确认 Docker Hub 仓库权限
3. 验证访问令牌未过期
4. 检查 Docker Hub 服务状态
```

## 📝 配置完成检查清单

- [ ] **Codecov Token** 已配置并测试
- [ ] **钉钉 Webhook** 已配置并测试通知
- [ ] **Docker 认证** 已配置并测试登录
- [ ] **SSH 密钥** 已生成并部署到服务器
- [ ] **测试工作流** 已运行并通过
- [ ] **安全审计** 已完成
- [ ] **轮换计划** 已制定
- [ ] **团队培训** 已完成

---

**🎉 配置完成后，智能评估平台的 CI/CD 流水线将具备完整的自动化能力！**

*最后更新: 2025-06-23*  
*维护团队: DevOps & 开发团队*