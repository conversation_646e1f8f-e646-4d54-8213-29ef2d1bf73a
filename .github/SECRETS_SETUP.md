# GitHub Secrets 配置指南

## 🔐 必需的 Secrets 配置清单

### 1. Docker Registry (必需)
```yaml
DOCKER_USERNAME: # Docker Hub 用户名
DOCKER_PASSWORD: # Docker Hub 密码或访问令牌
```

### 2. 部署服务器 (必需)
```yaml
# 开发环境
DEV_HOST: # 开发服务器IP或域名 (例: dev.assessment.com)
DEV_USERNAME: # SSH用户名 (例: deploy)
DEV_SSH_KEY: # SSH私钥 (使用 cat ~/.ssh/id_rsa 获取)
DEV_BASE_URL: # 开发环境URL (例: https://dev.assessment.com)

# 生产环境
PROD_HOST: # 生产服务器IP或域名
PROD_USERNAME: # SSH用户名
PROD_SSH_KEY: # SSH私钥
PROD_BASE_URL: # 生产环境URL
```

### 3. 通知服务 (可选但推荐)
```yaml
DINGTALK_WEBHOOK: # 钉钉机器人Webhook URL
# 获取方式: 钉钉群设置 -> 智能群助手 -> 添加机器人 -> 自定义 -> 复制Webhook地址
```

### 4. 代码质量服务 (推荐)
```yaml
CODECOV_TOKEN: # Codecov.io 项目令牌
# 获取方式: https://app.codecov.io/gh/你的组织/Assessment -> Settings -> General -> Repository Upload Token

SONAR_TOKEN: # SonarCloud 令牌 (可选)
SONAR_HOST_URL: # SonarCloud URL (可选)
```

### 5. 性能测试 (可选)
```yaml
K6_CLOUD_TOKEN: # K6 Cloud API 令牌
# 获取方式: https://app.k6.io/account/api-token
```

## 📝 配置步骤

### Step 1: 进入 GitHub 仓库设置
1. 访问: `https://github.com/你的组织/Assessment/settings/secrets/actions`
2. 点击 "New repository secret"

### Step 2: 添加必需的 Secrets

#### Docker Hub 配置
```bash
# 名称: DOCKER_USERNAME
# 值: 你的Docker Hub用户名

# 名称: DOCKER_PASSWORD
# 值: 你的Docker Hub密码
# 推荐使用访问令牌而不是密码
# 创建令牌: https://hub.docker.com/settings/security
```

#### SSH 密钥生成和配置
```bash
# 1. 在本地生成专用部署密钥对
ssh-keygen -t ed25519 -C "github-actions@assessment" -f ~/.ssh/assessment_deploy

# 2. 复制私钥内容
cat ~/.ssh/assessment_deploy

# 3. 将私钥内容添加为 GitHub Secret (DEV_SSH_KEY / PROD_SSH_KEY)

# 4. 将公钥添加到目标服务器
ssh-copy-id -i ~/.ssh/assessment_deploy.pub deploy@your-server
# 或手动添加到服务器的 ~/.ssh/authorized_keys
```

#### 钉钉机器人配置
```bash
# 1. 在钉钉群中添加自定义机器人
# 2. 安全设置选择"加签"
# 3. 复制 Webhook 地址
# 4. 添加为 DINGTALK_WEBHOOK secret
```

### Step 3: 验证配置

创建测试工作流验证 Secrets:
```yaml
# .github/workflows/test-secrets.yml
name: Test Secrets Configuration

on:
  workflow_dispatch:

jobs:
  test-secrets:
    runs-on: ubuntu-latest
    steps:
    - name: Check Docker credentials
      run: |
        echo "Docker username is set: ${{ secrets.DOCKER_USERNAME != '' }}"
        
    - name: Check deployment servers
      run: |
        echo "Dev host is set: ${{ secrets.DEV_HOST != '' }}"
        echo "Prod host is set: ${{ secrets.PROD_HOST != '' }}"
        
    - name: Check optional services
      run: |
        echo "DingTalk webhook is set: ${{ secrets.DINGTALK_WEBHOOK != '' }}"
        echo "Codecov token is set: ${{ secrets.CODECOV_TOKEN != '' }}"
```

## 🚨 安全注意事项

1. **永远不要**在代码中硬编码敏感信息
2. **定期轮换**密钥和令牌
3. **最小权限原则**: 为 CI/CD 创建专用账户
4. **审计日志**: 定期检查 Secret 访问日志
5. **加密传输**: 确保所有 Secrets 通过 HTTPS 传输

## 🔄 Secret 轮换计划

| Secret 类型 | 轮换频率 | 上次轮换 | 下次轮换 |
|------------|----------|----------|----------|
| SSH 密钥 | 每季度 | - | - |
| Docker 令牌 | 每月 | - | - |
| API 令牌 | 每半年 | - | - |
| Webhook URL | 按需 | - | - |

## 📊 配置状态检查清单

- [ ] Docker Hub 认证配置完成
- [ ] 开发环境部署配置完成
- [ ] 生产环境部署配置完成
- [ ] 钉钉通知配置完成
- [ ] Codecov 集成配置完成
- [ ] 所有 Secrets 已测试验证
- [ ] 安全审计完成
- [ ] 文档更新完成

## 🛠️ 故障排除

### 问题: SSH 连接失败
```bash
# 检查 SSH 密钥格式
echo "$SSH_KEY" | ssh-keygen -y -f /dev/stdin

# 测试连接
ssh -i ~/.ssh/assessment_deploy -o StrictHostKeyChecking=no deploy@server
```

### 问题: Docker 推送失败
```bash
# 验证 Docker 登录
echo $DOCKER_PASSWORD | docker login -u $DOCKER_USERNAME --password-stdin

# 检查仓库权限
docker pull $DOCKER_USERNAME/test-repo
```

### 问题: 钉钉通知失败
```bash
# 测试 Webhook
curl -X POST $DINGTALK_WEBHOOK \
  -H 'Content-Type: application/json' \
  -d '{"msgtype": "text", "text": {"content": "测试消息"}}'
```

---
*最后更新: 2025-06-23*
*安全责任人: DevOps Team*