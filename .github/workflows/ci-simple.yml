name: Simple CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '20'

jobs:
  backend-test:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: assessment123
          POSTGRES_USER: assessment_user
          POSTGRES_DB: assessment_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5433:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: Run backend tests
      working-directory: ./backend
      run: |
        chmod +x mvnw
        ./mvnw clean test
      env:
        SPRING_PROFILES_ACTIVE: test
        SPRING_DATASOURCE_URL: ************************************************
        SPRING_DATASOURCE_USERNAME: assessment_user
        SPRING_DATASOURCE_PASSWORD: assessment123
        SPRING_REDIS_HOST: localhost
        SPRING_REDIS_PORT: 6379

  frontend-test:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/uni-app/package-lock.json
          frontend/admin/package-lock.json
    
    - name: Test Admin Frontend
      working-directory: ./frontend/admin
      run: |
        npm ci
        npm run lint || true
        npm run type-check || true
        npm run test:unit || true
    
    - name: Test Uni-app Frontend
      working-directory: ./frontend/uni-app
      run: |
        npm ci
        npm run lint || true
        npm run build:h5 || true