name: Test Secrets Configuration

on:
  workflow_dispatch:
    inputs:
      test_notifications:
        description: '测试通知发送'
        required: false
        default: true
        type: boolean

permissions:
  contents: read
  issues: write
  pull-requests: read

jobs:
  test-secrets:
    name: 验证 Secrets 配置
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 测试 Codecov Token
      id: test-codecov
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      run: |
        if [ -n "$CODECOV_TOKEN" ]; then
          echo "✅ Codecov Token 已配置"
          echo "codecov_configured=true" >> $GITHUB_OUTPUT
          
          # 测试 Codecov API 连接
          response=$(curl -s -o /dev/null -w "%{http_code}" \
            -H "Authorization: token $CODECOV_TOKEN" \
            "https://codecov.io/api/v2/gh/${{ github.repository }}")
          
          if [ "$response" = "200" ] || [ "$response" = "404" ]; then
            echo "✅ Codecov API 连接正常"
          else
            echo "⚠️ Codecov API 连接异常 (HTTP $response)"
          fi
        else
          echo "❌ Codecov Token 未配置 (必需)"
          echo "codecov_configured=false" >> $GITHUB_OUTPUT
        fi
    
    - name: 设置 DingTalk 状态 (已禁用)
      id: test-dingtalk
      run: |
        echo "ℹ️ DingTalk 通知已禁用"
        echo "dingtalk_configured=false" >> $GITHUB_OUTPUT
    
    - name: 测试 Docker 认证
      id: test-docker
      env:
        DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
        DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
      run: |
        if [ -n "$DOCKER_USERNAME" ] && [ -n "$DOCKER_PASSWORD" ]; then
          echo "✅ Docker 认证信息已配置"
          echo "docker_configured=true" >> $GITHUB_OUTPUT
          
          # 测试 Docker 登录
          echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin 2>/dev/null
          
          if [ $? -eq 0 ]; then
            echo "✅ Docker Hub 登录成功"
            docker logout
          else
            echo "⚠️ Docker Hub 登录失败，请检查认证信息"
          fi
        else
          echo "⚠️ Docker 认证信息未配置 (推荐配置)"
          echo "docker_configured=false" >> $GITHUB_OUTPUT
        fi
    
    - name: 测试 SSH 连接配置
      id: test-ssh
      env:
        DEV_HOST: ${{ secrets.DEV_HOST }}
        DEV_USERNAME: ${{ secrets.DEV_USERNAME }}
        DEV_SSH_KEY: ${{ secrets.DEV_SSH_KEY }}
        PROD_HOST: ${{ secrets.PROD_HOST }}
        PROD_USERNAME: ${{ secrets.PROD_USERNAME }}
        PROD_SSH_KEY: ${{ secrets.PROD_SSH_KEY }}
      run: |
        ssh_configs=0
        
        if [ -n "$DEV_HOST" ] && [ -n "$DEV_USERNAME" ] && [ -n "$DEV_SSH_KEY" ]; then
          echo "✅ 开发环境 SSH 配置完整"
          ssh_configs=$((ssh_configs + 1))
          
          # 验证 SSH 密钥格式
          echo "$DEV_SSH_KEY" | ssh-keygen -y -f /dev/stdin >/dev/null 2>&1
          if [ $? -eq 0 ]; then
            echo "✅ 开发环境 SSH 密钥格式正确"
          else
            echo "⚠️ 开发环境 SSH 密钥格式错误"
          fi
        else
          echo "⚠️ 开发环境 SSH 配置不完整 (可选)"
        fi
        
        if [ -n "$PROD_HOST" ] && [ -n "$PROD_USERNAME" ] && [ -n "$PROD_SSH_KEY" ]; then
          echo "✅ 生产环境 SSH 配置完整"
          ssh_configs=$((ssh_configs + 1))
          
          # 验证 SSH 密钥格式
          echo "$PROD_SSH_KEY" | ssh-keygen -y -f /dev/stdin >/dev/null 2>&1
          if [ $? -eq 0 ]; then
            echo "✅ 生产环境 SSH 密钥格式正确"
          else
            echo "⚠️ 生产环境 SSH 密钥格式错误"
          fi
        else
          echo "⚠️ 生产环境 SSH 配置不完整 (可选)"
        fi
        
        echo "ssh_configs=$ssh_configs" >> $GITHUB_OUTPUT
    
    - name: 测试可选服务配置
      id: test-optional
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        K6_CLOUD_TOKEN: ${{ secrets.K6_CLOUD_TOKEN }}
      run: |
        optional_count=0
        
        if [ -n "$SONAR_TOKEN" ]; then
          echo "✅ SonarCloud Token 已配置"
          optional_count=$((optional_count + 1))
        else
          echo "⚪ SonarCloud Token 未配置 (可选)"
        fi
        
        if [ -n "$K6_CLOUD_TOKEN" ]; then
          echo "✅ K6 Cloud Token 已配置"
          optional_count=$((optional_count + 1))
        else
          echo "⚪ K6 Cloud Token 未配置 (可选)"
        fi
        
        echo "optional_count=$optional_count" >> $GITHUB_OUTPUT
    
    - name: 生成配置报告
      run: |
        echo "# 🔐 GitHub Secrets 配置报告" > secrets-report.md
        echo "" >> secrets-report.md
        echo "**测试时间**: $(date)" >> secrets-report.md
        echo "**工作流**: ${{ github.workflow }}" >> secrets-report.md
        echo "**运行ID**: ${{ github.run_id }}" >> secrets-report.md
        echo "" >> secrets-report.md
        
        echo "## ✅ 必需配置 (CI/CD 核心功能)" >> secrets-report.md
        echo "" >> secrets-report.md
        echo "| Secret | 状态 | 说明 |" >> secrets-report.md
        echo "|--------|------|------|" >> secrets-report.md
        
        if [ "${{ steps.test-codecov.outputs.codecov_configured }}" = "true" ]; then
          echo "| CODECOV_TOKEN | ✅ 已配置 | 覆盖率上传正常 |" >> secrets-report.md
        else
          echo "| CODECOV_TOKEN | ❌ 未配置 | **阻止覆盖率监控** |" >> secrets-report.md
        fi
        
        echo "" >> secrets-report.md
        echo "## ⚠️ 推荐配置 (增强功能)" >> secrets-report.md
        echo "" >> secrets-report.md
        echo "| Secret | 状态 | 说明 |" >> secrets-report.md
        echo "|--------|------|------|" >> secrets-report.md
        
        # DingTalk 通知已禁用
        echo "| DINGTALK_WEBHOOK | ⚪ 已禁用 | 不使用钉钉通知 |" >> secrets-report.md
        
        if [ "${{ steps.test-docker.outputs.docker_configured }}" = "true" ]; then
          echo "| DOCKER_USERNAME/PASSWORD | ✅ 已配置 | 镜像推送正常 |" >> secrets-report.md
        else
          echo "| DOCKER_USERNAME/PASSWORD | ⚠️ 未配置 | 无法推送镜像 |" >> secrets-report.md
        fi
        
        echo "" >> secrets-report.md
        echo "## 🔵 可选配置 (部署功能)" >> secrets-report.md
        echo "" >> secrets-report.md
        
        ssh_count=${{ steps.test-ssh.outputs.ssh_configs }}
        if [ "$ssh_count" -gt 0 ]; then
          echo "- SSH 部署配置: ✅ $ssh_count 个环境已配置" >> secrets-report.md
        else
          echo "- SSH 部署配置: ⚪ 未配置 (无自动部署)" >> secrets-report.md
        fi
        
        optional_count=${{ steps.test-optional.outputs.optional_count }}
        if [ "$optional_count" -gt 0 ]; then
          echo "- 扩展服务: ✅ $optional_count 个服务已配置" >> secrets-report.md
        else
          echo "- 扩展服务: ⚪ 未配置" >> secrets-report.md
        fi
        
        echo "" >> secrets-report.md
        echo "## 🎯 配置建议" >> secrets-report.md
        echo "" >> secrets-report.md
        
        if [ "${{ steps.test-codecov.outputs.codecov_configured }}" = "false" ]; then
          echo "### 🚨 紧急: 必需配置缺失" >> secrets-report.md
          echo "1. **立即配置 CODECOV_TOKEN**" >> secrets-report.md
          echo "   - 访问 [Codecov.io](https://app.codecov.io/)" >> secrets-report.md
          echo "   - 添加 Assessment 项目" >> secrets-report.md
          echo "   - 复制 Upload Token 到 GitHub Secrets" >> secrets-report.md
          echo "" >> secrets-report.md
        fi
        
        # DingTalk 通知已禁用，跳过配置建议
        
        if [ "${{ steps.test-docker.outputs.docker_configured }}" = "false" ]; then
          echo "### 📦 可选: 配置容器推送" >> secrets-report.md
          echo "3. **配置 Docker Hub 认证**" >> secrets-report.md
          echo "   - 创建 Docker Hub 访问令牌" >> secrets-report.md
          echo "   - 添加 DOCKER_USERNAME 和 DOCKER_PASSWORD" >> secrets-report.md
          echo "" >> secrets-report.md
        fi
        
        echo "## 📚 参考文档" >> secrets-report.md
        echo "- [详细配置指南](./.github/SECRETS_CONFIG_GUIDE.md)" >> secrets-report.md
        echo "- [CI/CD 工作流说明](./.github/workflows/)" >> secrets-report.md
        echo "- [监控仪表板](./.github/MONITORING_DASHBOARD.md)" >> secrets-report.md
        echo "" >> secrets-report.md
        echo "---" >> secrets-report.md
        echo "*此报告由 GitHub Actions 自动生成*" >> secrets-report.md
        
        # 显示报告内容
        cat secrets-report.md
    
    - name: 评估配置完整性
      id: assessment
      run: |
        score=0
        max_score=10
        
        # 必需配置 (6分)
        if [ "${{ steps.test-codecov.outputs.codecov_configured }}" = "true" ]; then
          score=$((score + 6))
          echo "✅ 必需配置: +6分"
        else
          echo "❌ 必需配置缺失: +0分"
        fi
        
        # 推荐配置 (3分)
        # DingTalk 通知已禁用，跳过评分
        
        if [ "${{ steps.test-docker.outputs.docker_configured }}" = "true" ]; then
          score=$((score + 1))
          echo "✅ Docker 配置: +1分"
        fi
        
        # 可选配置 (1分)
        ssh_count=${{ steps.test-ssh.outputs.ssh_configs }}
        if [ "$ssh_count" -gt 0 ]; then
          score=$((score + 1))
          echo "✅ 部署配置: +1分"
        fi
        
        percentage=$((score * 100 / max_score))
        
        echo ""
        echo "🎯 配置完整性评分: $score/$max_score ($percentage%)"
        
        if [ $percentage -ge 90 ]; then
          echo "🟢 配置优秀: CI/CD 功能完备"
          status="excellent"
        elif [ $percentage -ge 70 ]; then
          echo "🟡 配置良好: 核心功能可用"
          status="good"
        elif [ $percentage -ge 50 ]; then
          echo "🟠 配置基础: 需要补充"
          status="basic"
        else
          echo "🔴 配置不足: 影响功能"
          status="insufficient"
        fi
        
        echo "score=$score" >> $GITHUB_OUTPUT
        echo "percentage=$percentage" >> $GITHUB_OUTPUT
        echo "status=$status" >> $GITHUB_OUTPUT
    
    - name: 上传配置报告
      uses: actions/upload-artifact@v4
      with:
        name: secrets-configuration-report
        path: secrets-report.md
        retention-days: 30
    
    - name: 创建配置状态 Issue
      if: steps.assessment.outputs.status == 'insufficient' || steps.assessment.outputs.status == 'basic'
      continue-on-error: true
      uses: actions/github-script@v7
      with:
        script: |
          try {
            const fs = require('fs');
            const report = fs.readFileSync('secrets-report.md', 'utf8');
            
            const score = '${{ steps.assessment.outputs.score }}';
            const percentage = '${{ steps.assessment.outputs.percentage }}';
            const status = '${{ steps.assessment.outputs.status }}';
            
            const title = `🔐 GitHub Secrets 配置${status === 'insufficient' ? '不足' : '需要优化'} (${percentage}%)`;
            
            const issueBody = `${report}\n\n---\n\n**当前评分**: ${score}/10 (${percentage}%)\n**配置状态**: ${status}\n\n请按照上述建议完善 GitHub Secrets 配置，以启用完整的 CI/CD 功能。`;
            
            // 查找现有配置状态 Issue
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: 'configuration,secrets',
              state: 'open'
            });
            
            const existingIssue = issues.data.find(issue => 
              issue.title.includes('GitHub Secrets 配置')
            );
            
            if (existingIssue) {
              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: existingIssue.number,
                title: title,
                body: issueBody
              });
              console.log('✅ 更新了现有的配置状态 Issue');
            } else {
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: title,
                body: issueBody,
                labels: ['configuration', 'secrets', status === 'insufficient' ? 'urgent' : 'enhancement']
              });
              console.log('✅ 创建了新的配置状态 Issue');
            }
          } catch (error) {
            console.log('⚠️ 无法创建 Issue (权限不足):', error.message);
            console.log('📋 配置报告已上传为工件，请查看 Actions 页面');
          }
    
    - name: 配置状态总结
      run: |
        echo "🎉 GitHub Secrets 配置测试完成!"
        echo ""
        echo "📊 评分结果: ${{ steps.assessment.outputs.score }}/10 (${{ steps.assessment.outputs.percentage }}%)"
        echo "📈 配置状态: ${{ steps.assessment.outputs.status }}"
        echo ""
        echo "📋 下一步操作:"
        
        if [ "${{ steps.assessment.outputs.status }}" = "excellent" ]; then
          echo "✅ 配置完美! CI/CD 流水线已就绪"
        elif [ "${{ steps.assessment.outputs.status }}" = "good" ]; then
          echo "🎯 配置良好，建议补充可选功能"
        else
          echo "⚠️ 请按照报告建议完善配置"
          echo "📖 参考: .github/SECRETS_CONFIG_GUIDE.md"
        fi
