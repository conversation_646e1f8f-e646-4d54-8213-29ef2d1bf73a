name: CI - Test Coverage & Quality

on:
  push:
    branches: [ main, develop, 'cursor/*' ]
    paths:
      - 'backend/**'
      - 'frontend/**'
      - '.github/workflows/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - 'frontend/**'
      - '.github/workflows/**'

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '20'

jobs:
  backend-tests:
    name: Backend Tests & Coverage
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: assessment_test
          POSTGRES_USER: assessment_user
          POSTGRES_PASSWORD: assessment123
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5433:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better relevancy of SonarQube analysis

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Verify Maven wrapper
      working-directory: backend
      run: |
        chmod +x mvnw
        ./mvnw --version

    - name: Run backend tests with coverage
      working-directory: backend
      env:
        SPRING_PROFILES_ACTIVE: test
        SPRING_DATASOURCE_URL: ************************************************
        SPRING_DATASOURCE_USERNAME: assessment_user
        SPRING_DATASOURCE_PASSWORD: assessment123
        SPRING_REDIS_HOST: localhost
        SPRING_REDIS_PORT: 6379
      run: |
        ./mvnw clean verify -B
        echo "✅ Tests completed successfully"

    - name: Generate test report
      working-directory: backend
      if: always()
      run: |
        echo "## 📊 Test Results Summary" > test-summary.md
        if [ -f target/surefire-reports/TEST-*.xml ]; then
          TOTAL_TESTS=$(grep -h "tests=" target/surefire-reports/TEST-*.xml | sed 's/.*tests="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
          FAILED_TESTS=$(grep -h "failures=" target/surefire-reports/TEST-*.xml | sed 's/.*failures="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
          ERROR_TESTS=$(grep -h "errors=" target/surefire-reports/TEST-*.xml | sed 's/.*errors="\([0-9]*\)".*/\1/' | awk '{sum+=$1} END {print sum}')
          PASSED_TESTS=$((TOTAL_TESTS - FAILED_TESTS - ERROR_TESTS))
          
          echo "- **Total Tests**: $TOTAL_TESTS" >> test-summary.md
          echo "- **Passed**: $PASSED_TESTS ✅" >> test-summary.md
          echo "- **Failed**: $FAILED_TESTS ❌" >> test-summary.md
          echo "- **Errors**: $ERROR_TESTS ⚠️" >> test-summary.md
          
          if [ $FAILED_TESTS -eq 0 ] && [ $ERROR_TESTS -eq 0 ]; then
            echo "- **Status**: All tests passed! 🎉" >> test-summary.md
          else
            echo "- **Status**: Some tests failed 🚨" >> test-summary.md
          fi
        else
          echo "- **Status**: No test results found" >> test-summary.md
        fi

    - name: Check coverage thresholds
      working-directory: backend
      run: |
        if [ -f target/site/jacoco-merged/index.html ]; then
          echo "✅ Coverage report generated successfully"
          # Extract coverage percentage (this is a simplified approach)
          COVERAGE=$(grep -o 'Total[^%]*%' target/site/jacoco-merged/index.html | tail -1 | grep -o '[0-9]*%' || echo "N/A")
          echo "📈 Overall Coverage: $COVERAGE"
          echo "COVERAGE_PERCENTAGE=$COVERAGE" >> $GITHUB_ENV
        else
          echo "⚠️ Coverage report not found"
        fi

    - name: Upload coverage reports to Codecov
      if: always()
      uses: codecov/codecov-action@v4
      with:
        file: backend/target/site/jacoco-merged/jacoco.xml
        directory: backend/target/site/jacoco-merged/
        flags: backend
        name: backend-coverage
        verbose: true
        fail_ci_if_error: false  # Don't fail CI if Codecov upload fails
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

    - name: Archive test results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: backend-test-results
        path: |
          backend/target/surefire-reports/
          backend/target/failsafe-reports/
          backend/target/site/jacoco-merged/
          backend/test-summary.md
        retention-days: 30

    - name: Comment coverage on PR
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          let testSummary = '';
          try {
            testSummary = fs.readFileSync('backend/test-summary.md', 'utf8');
          } catch (error) {
            testSummary = '## 📊 Test Results Summary\n- **Status**: Test summary not available';
          }
          
          const coverage = process.env.COVERAGE_PERCENTAGE || 'N/A';
          
          const comment = `## 🔍 Backend Test & Coverage Report
          
          ${testSummary}
          
          ### 📈 Coverage Information
          - **Overall Coverage**: ${coverage}
          - **Target**: 85% instruction coverage
          - **Reports**: Available in CI artifacts
          
          ### 📋 Coverage Details
          - **Service Layer Target**: 90%
          - **Controller Layer Target**: 80%
          - **Integration Tests**: Included
          
          *Coverage reports are available in the CI artifacts for detailed analysis.*
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  frontend-tests:
    name: Frontend Web Tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/admin/package-lock.json

    - name: Install admin dependencies
      working-directory: frontend/admin
      run: npm ci

    - name: Run admin tests
      working-directory: frontend/admin
      run: |
        npm run test:unit || echo "⚠️ Admin tests failed"
        npm run test:coverage || echo "⚠️ Admin coverage failed"

    - name: Build admin application
      working-directory: frontend/admin
      run: npm run build

    - name: Archive frontend artifacts
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: frontend-web-results
        path: |
          frontend/admin/dist/
          frontend/admin/coverage/
        retention-days: 7

  mobile-tests:
    name: Mobile/Uni-app Tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/uni-app/package-lock.json

    - name: Install uni-app dependencies
      working-directory: frontend/uni-app
      run: npm ci

    - name: Run uni-app unit tests
      working-directory: frontend/uni-app
      run: |
        npm run test:unit || echo "⚠️ Uni-app tests failed"

    - name: Run uni-app linting
      working-directory: frontend/uni-app
      run: |
        npx eslint src/ --ext .js,.vue || echo "⚠️ Uni-app linting issues found"

    - name: Build uni-app for H5
      working-directory: frontend/uni-app
      run: npm run build:h5

    - name: Build uni-app for WeChat Mini Program
      working-directory: frontend/uni-app
      run: |
        npm run build:mp-weixin || echo "⚠️ WeChat mini-program build failed"

    - name: Build uni-app for Android (APK)
      working-directory: frontend/uni-app
      run: |
        npm run build:app-plus || echo "⚠️ Android build failed (expected in CI)"

    - name: Test uni-app component rendering
      working-directory: frontend/uni-app
      run: |
        # Test component imports and basic rendering
        node -e "
        const fs = require('fs');
        const path = require('path');
        
        // Check if key components exist
        const componentsDir = 'src/components';
        if (fs.existsSync(componentsDir)) {
          const components = fs.readdirSync(componentsDir, { recursive: true })
            .filter(file => file.endsWith('.vue'));
          console.log('✅ Found', components.length, 'Vue components');
          
          // Basic syntax check for components
          components.forEach(comp => {
            const compPath = path.join(componentsDir, comp);
            const content = fs.readFileSync(compPath, 'utf8');
            if (!content.includes('<template>')) {
              console.warn('⚠️ Component missing template:', comp);
            }
          });
        }
        
        // Check pages structure
        const pagesDir = 'src/pages';
        if (fs.existsSync(pagesDir)) {
          const pages = fs.readdirSync(pagesDir, { recursive: true })
            .filter(file => file.endsWith('.vue'));
          console.log('✅ Found', pages.length, 'page components');
        }
        "

    - name: Validate uni-app configuration
      working-directory: frontend/uni-app
      run: |
        # Validate pages.json
        node -e "
        const fs = require('fs');
        const pagesConfig = JSON.parse(fs.readFileSync('src/pages.json', 'utf8'));
        
        console.log('📱 Uni-app Configuration Validation:');
        console.log('- Pages configured:', pagesConfig.pages?.length || 0);
        console.log('- Tab pages:', pagesConfig.tabBar?.list?.length || 0);
        console.log('- Global style configured:', !!pagesConfig.globalStyle);
        
        // Validate all page paths exist
        if (pagesConfig.pages) {
          pagesConfig.pages.forEach(page => {
            const pagePath = 'src/' + page.path + '.vue';
            if (!fs.existsSync(pagePath)) {
              console.error('❌ Missing page file:', pagePath);
              process.exit(1);
            }
          });
          console.log('✅ All page files exist');
        }
        "

    - name: Generate mobile test report
      working-directory: frontend/uni-app
      run: |
        echo "## 📱 Mobile/Uni-app Test Report" > mobile-test-report.md
        echo "" >> mobile-test-report.md
        echo "### Build Results" >> mobile-test-report.md
        
        if [ -d "dist/build/h5" ]; then
          H5_SIZE=$(du -sh dist/build/h5 | cut -f1)
          echo "- **H5 Build**: ✅ Success (Size: $H5_SIZE)" >> mobile-test-report.md
        else
          echo "- **H5 Build**: ❌ Failed" >> mobile-test-report.md
        fi
        
        if [ -d "dist/build/mp-weixin" ]; then
          WEIXIN_SIZE=$(du -sh dist/build/mp-weixin | cut -f1)
          echo "- **WeChat Mini Program**: ✅ Success (Size: $WEIXIN_SIZE)" >> mobile-test-report.md
        else
          echo "- **WeChat Mini Program**: ⚠️ Build issues" >> mobile-test-report.md
        fi
        
        echo "" >> mobile-test-report.md
        echo "### Platform Support" >> mobile-test-report.md
        echo "- **H5**: Web browser compatibility" >> mobile-test-report.md
        echo "- **WeChat Mini Program**: 微信小程序支持" >> mobile-test-report.md
        echo "- **App Plus**: Android/iOS native app (build in CI environment may fail)" >> mobile-test-report.md

    - name: Archive mobile artifacts
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: mobile-build-results
        path: |
          frontend/uni-app/dist/
          frontend/uni-app/mobile-test-report.md
        retention-days: 7

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Run OWASP Dependency Check
      working-directory: backend
      run: |
        chmod +x mvnw
        ./mvnw org.owasp:dependency-check-maven:check -DfailBuildOnCVSS=7 || echo "⚠️ Security vulnerabilities found"

    - name: Upload security reports
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: security-reports
        path: backend/target/dependency-check-report.html
        retention-days: 30

  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, mobile-tests]
    if: always()

    steps:
    - name: Check backend test results
      run: |
        if [[ "${{ needs.backend-tests.result }}" == "success" ]]; then
          echo "✅ Backend tests passed"
        else
          echo "❌ Backend tests failed"
          exit 1
        fi

    - name: Check frontend test results
      run: |
        if [[ "${{ needs.frontend-tests.result }}" == "success" ]]; then
          echo "✅ Frontend web tests passed"
        else
          echo "⚠️ Frontend web tests had issues (non-blocking)"
        fi

    - name: Check mobile test results
      run: |
        if [[ "${{ needs.mobile-tests.result }}" == "success" ]]; then
          echo "✅ Mobile/uni-app tests passed"
        else
          echo "⚠️ Mobile/uni-app tests had issues (non-blocking)"
        fi

    - name: Final status
      run: |
        echo "🎯 Quality gate check completed"
        echo "📊 Test Results Summary:"
        echo "  - Backend: ${{ needs.backend-tests.result }}"
        echo "  - Frontend Web: ${{ needs.frontend-tests.result }}"
        echo "  - Mobile/Uni-app: ${{ needs.mobile-tests.result }}"
        echo "✅ All critical tests must pass for merge approval"


