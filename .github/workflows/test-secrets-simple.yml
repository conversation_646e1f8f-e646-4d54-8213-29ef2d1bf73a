name: Simple Secrets Test

on:
  workflow_dispatch:
    inputs:
      test_notifications:
        description: '测试通知发送'
        required: false
        default: true
        type: boolean

jobs:
  test-secrets:
    name: 快速验证 Secrets 配置
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 测试 Codecov Token
      id: test-codecov
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      run: |
        echo "🔍 检查 CODECOV_TOKEN..."
        if [ -n "$CODECOV_TOKEN" ]; then
          echo "✅ CODECOV_TOKEN 已配置"
          echo "codecov_configured=true" >> $GITHUB_OUTPUT
          
          # 简单验证token格式
          token_length=$(echo "$CODECOV_TOKEN" | wc -c)
          if [ $token_length -gt 10 ]; then
            echo "✅ Token 格式正确 (长度: $token_length 字符)"
          else
            echo "⚠️ Token 可能有问题 (长度: $token_length 字符)"
          fi
        else
          echo "❌ CODECOV_TOKEN 未配置"
          echo "codecov_configured=false" >> $GITHUB_OUTPUT
        fi
    
    - name: 设置 DingTalk 状态 (已禁用)
      id: test-dingtalk
      run: |
        echo "ℹ️ DingTalk 通知已禁用"
        echo "dingtalk_configured=false" >> $GITHUB_OUTPUT
    
    - name: 测试 Docker 认证
      id: test-docker
      env:
        DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
        DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
      run: |
        echo "🔍 检查 Docker 认证..."
        if [ -n "$DOCKER_USERNAME" ] && [ -n "$DOCKER_PASSWORD" ]; then
          echo "✅ Docker 认证信息已配置"
          echo "docker_configured=true" >> $GITHUB_OUTPUT
          echo "📦 用户名: $DOCKER_USERNAME"
        else
          echo "⚠️ Docker 认证信息未配置"
          echo "docker_configured=false" >> $GITHUB_OUTPUT
        fi
    
    - name: 生成简单评分
      id: assessment
      run: |
        score=0
        max_score=10
        
        # 必需配置 (6分)
        if [ "${{ steps.test-codecov.outputs.codecov_configured }}" = "true" ]; then
          score=$((score + 6))
          echo "✅ 必需配置: +6分 (CODECOV_TOKEN)"
        else
          echo "❌ 必需配置缺失: +0分"
        fi
        
        # 推荐配置 (3分)
        # DingTalk 通知已禁用，跳过评分
        
        if [ "${{ steps.test-docker.outputs.docker_configured }}" = "true" ]; then
          score=$((score + 2))
          echo "✅ Docker 配置: +2分"
        fi
        
        percentage=$((score * 100 / max_score))
        
        echo ""
        echo "🎯 配置完整性评分: $score/$max_score ($percentage%)"
        
        if [ $percentage -ge 90 ]; then
          echo "🟢 配置优秀: CI/CD 功能完备"
          status="excellent"
        elif [ $percentage -ge 60 ]; then
          echo "🟡 配置良好: 核心功能可用"
          status="good"
        elif [ $percentage -ge 30 ]; then
          echo "🟠 配置基础: 需要补充"
          status="basic"
        else
          echo "🔴 配置不足: 影响功能"
          status="insufficient"
        fi
        
        echo "score=$score" >> $GITHUB_OUTPUT
        echo "percentage=$percentage" >> $GITHUB_OUTPUT
        echo "status=$status" >> $GITHUB_OUTPUT
    
    - name: 配置建议
      run: |
        echo "🎉 GitHub Secrets 配置测试完成!"
        echo ""
        echo "📊 评分结果: ${{ steps.assessment.outputs.score }}/10 (${{ steps.assessment.outputs.percentage }}%)"
        echo "📈 配置状态: ${{ steps.assessment.outputs.status }}"
        echo ""
        
        if [ "${{ steps.assessment.outputs.status }}" = "excellent" ]; then
          echo "✅ 配置完美! CI/CD 流水线已就绪"
          echo "🚀 您可以运行完整的工作流了"
        elif [ "${{ steps.assessment.outputs.status }}" = "good" ]; then
          echo "🎯 配置良好，建议补充可选功能"
          echo "📋 考虑添加: DOCKER认证"
        else
          echo "⚠️ 请按照以下建议完善配置:"
          echo ""
          if [ "${{ steps.test-codecov.outputs.codecov_configured }}" = "false" ]; then
            echo "🚨 紧急: 配置 CODECOV_TOKEN"
            echo "   1. 访问 https://app.codecov.io/"
            echo "   2. 登录并添加您的仓库"
            echo "   3. 复制 Upload Token 到 GitHub Secrets"
          fi
          echo ""
          echo "📖 详细指南: CODECOV_SETUP_GUIDE.md"
        fi
