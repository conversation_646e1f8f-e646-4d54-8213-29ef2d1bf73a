name: Security Scan - 安全扫描

on:
  schedule:
    # 每周一凌晨2点运行安全扫描
    - cron: '0 2 * * 1'
  workflow_dispatch:
  push:
    branches: [ main ]
    paths:
      - 'backend/pom.xml'
      - 'frontend/*/package.json'
      - 'frontend/*/package-lock.json'

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '20'

jobs:
  dependency-scan:
    name: 依赖安全扫描
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 设置 Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: 运行 OWASP 依赖检查 - 后端
      working-directory: ./backend
      run: |
        ./mvnw clean dependency-check:check \
          -DfailBuildOnCVSS=7 \
          -DsuppressionsLocation=./.github/security/dependency-check-suppressions.xml
    
    - name: 安装前端依赖
      working-directory: ./frontend/uni-app
      run: npm ci
    
    - name: 运行 npm audit - uni-app
      working-directory: ./frontend/uni-app
      run: |
        npm audit --audit-level=moderate --json > npm-audit-uni-app.json || true
    
    - name: 安装前端依赖 - admin
      working-directory: ./frontend/admin
      run: npm ci
    
    - name: 运行 npm audit - admin
      working-directory: ./frontend/admin
      run: |
        npm audit --audit-level=moderate --json > npm-audit-admin.json || true
    
    - name: 生成安全报告
      run: |
        echo "# 智能评估平台 - 安全扫描报告" > security-report.md
        echo "" >> security-report.md
        echo "**扫描时间**: $(date)" >> security-report.md
        echo "**Git Commit**: ${{ github.sha }}" >> security-report.md
        echo "" >> security-report.md
        
        echo "## 🔒 后端依赖安全扫描 (OWASP)" >> security-report.md
        echo "" >> security-report.md
        
        if [ -f "./backend/target/dependency-check-report.xml" ]; then
          # 解析 OWASP 报告获取漏洞统计
          vulnerabilities=$(grep -o '<vulnerability ' ./backend/target/dependency-check-report.xml | wc -l)
          echo "- 发现漏洞数量: **$vulnerabilities** 个" >> security-report.md
          
          if [ $vulnerabilities -gt 0 ]; then
            echo "- ⚠️  请查看详细报告: [dependency-check-report.html]" >> security-report.md
          else
            echo "- ✅ 未发现高危漏洞" >> security-report.md
          fi
        fi
        
        echo "" >> security-report.md
        echo "## 📦 前端依赖安全扫描 (npm audit)" >> security-report.md
        echo "" >> security-report.md
        
        # 解析 npm audit 结果
        if [ -f "./frontend/uni-app/npm-audit-uni-app.json" ]; then
          echo "### uni-app 移动端" >> security-report.md
          vulnerabilities_count=$(jq '.metadata.vulnerabilities.total // 0' ./frontend/uni-app/npm-audit-uni-app.json)
          if [ "$vulnerabilities_count" -gt 0 ]; then
            echo "- ⚠️ 发现 $vulnerabilities_count 个漏洞" >> security-report.md
            echo "- 高危: $(jq '.metadata.vulnerabilities.high // 0' ./frontend/uni-app/npm-audit-uni-app.json)" >> security-report.md
            echo "- 中危: $(jq '.metadata.vulnerabilities.moderate // 0' ./frontend/uni-app/npm-audit-uni-app.json)" >> security-report.md
            echo "- 低危: $(jq '.metadata.vulnerabilities.low // 0' ./frontend/uni-app/npm-audit-uni-app.json)" >> security-report.md
          else
            echo "- ✅ 未发现安全漏洞" >> security-report.md
          fi
        fi
        
        if [ -f "./frontend/admin/npm-audit-admin.json" ]; then
          echo "" >> security-report.md
          echo "### admin 管理后台" >> security-report.md
          vulnerabilities_count=$(jq '.metadata.vulnerabilities.total // 0' ./frontend/admin/npm-audit-admin.json)
          if [ "$vulnerabilities_count" -gt 0 ]; then
            echo "- ⚠️ 发现 $vulnerabilities_count 个漏洞" >> security-report.md
            echo "- 高危: $(jq '.metadata.vulnerabilities.high // 0' ./frontend/admin/npm-audit-admin.json)" >> security-report.md
            echo "- 中危: $(jq '.metadata.vulnerabilities.moderate // 0' ./frontend/admin/npm-audit-admin.json)" >> security-report.md
            echo "- 低危: $(jq '.metadata.vulnerabilities.low // 0' ./frontend/admin/npm-audit-admin.json)" >> security-report.md
          else
            echo "- ✅ 未发现安全漏洞" >> security-report.md
          fi
        fi
        
        echo "" >> security-report.md
        echo "## 🛡️ 安全建议" >> security-report.md
        echo "" >> security-report.md
        echo "1. **定期更新依赖**: 保持依赖库为最新稳定版本" >> security-report.md
        echo "2. **及时修复漏洞**: 高危漏洞应在24小时内修复" >> security-report.md
        echo "3. **代码审查**: 重点审查安全相关代码变更" >> security-report.md
        echo "4. **监控告警**: 设置安全漏洞检测告警" >> security-report.md
        echo "" >> security-report.md
        echo "---" >> security-report.md
        echo "*本报告由 GitHub Actions 自动生成*" >> security-report.md
    
    - name: 上传安全扫描报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-scan-reports
        path: |
          security-report.md
          backend/target/dependency-check-report.*
          frontend/uni-app/npm-audit-uni-app.json
          frontend/admin/npm-audit-admin.json
        retention-days: 90
    
    - name: 创建安全问题 Issue
      if: failure()
      uses: actions/github-script@v7
      with:
        script: |
          const { readFileSync } = require('fs');
          
          let issueBody = '🔒 **安全扫描发现问题**\n\n';
          
          try {
            const securityReport = readFileSync('security-report.md', 'utf8');
            issueBody += securityReport;
          } catch (error) {
            issueBody += '请查看 GitHub Actions 运行日志获取详细信息。\n';
          }
          
          issueBody += '\n\n---\n';
          issueBody += `📍 **相关链接**:\n`;
          issueBody += `- [GitHub Actions 运行](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})\n`;
          issueBody += `- 提交: ${{ github.sha }}\n`;
          
          await github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: `🚨 安全扫描发现问题 - ${new Date().toISOString().split('T')[0]}`,
            body: issueBody,
            labels: ['security', 'vulnerability', 'urgent']
          });
    
  code-analysis:
    name: 代码静态分析
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 初始化 CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: java, javascript
        queries: security-and-quality
    
    - name: 编译代码
      working-directory: ./backend
      run: ./mvnw clean compile -DskipTests
    
    - name: 执行 CodeQL 分析
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:java,javascript"
    
    - name: 运行 SpotBugs 安全检查
      working-directory: ./backend
      run: |
        ./mvnw spotbugs:check \
          -Dspotbugs.effort=Max \
          -Dspotbugs.threshold=Low \
          -Dspotbugs.includeFilterFile=./.github/security/spotbugs-security-include.xml
    
    - name: 上传 SpotBugs 报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: spotbugs-reports
        path: backend/target/spotbugsXml.xml
        retention-days: 30

  container-scan:
    name: 容器镜像安全扫描
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 构建 Docker 镜像
      run: |
        docker build -t assessment-platform:security-scan -f docker/Dockerfile .
    
    - name: 运行 Trivy 漏洞扫描
      uses: aquasecurity/trivy-action@0.28.0
      with:
        image-ref: 'assessment-platform:security-scan'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: 上传 Trivy 扫描结果到 GitHub Security
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: 生成容器安全报告
      run: |
        docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
          -v $(pwd):/tmp aquasec/trivy:latest \
          image --format table --output /tmp/trivy-report.txt \
          assessment-platform:security-scan
    
    - name: 上传容器扫描报告
      uses: actions/upload-artifact@v4
      with:
        name: container-security-report
        path: trivy-report.txt
        retention-days: 30


