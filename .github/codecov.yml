# Codecov 配置文件
# 文档: https://docs.codecov.io/docs/codecov-yaml

coverage:
  precision: 2
  round: down
  range: "70...100"
  
  status:
    project:
      default:
        target: 82%           # 目标覆盖率
        threshold: 2%         # 允许的覆盖率下降幅度
        base: auto            # 与主分支比较
        branches:
          - main
          - develop
    
    patch:
      default:
        target: 80%           # 新代码目标覆盖率
        threshold: 5%         # 允许的覆盖率变化

  ignore:
    # 忽略的文件和目录
    - "backend/src/main/java/**/config/**"
    - "backend/src/main/java/**/dto/**"
    - "backend/src/main/java/**/entity/**"
    - "backend/src/main/java/**/Application.java"
    - "backend/src/main/java/**/ElderlyAssessmentApplication.java"
    - "backend/src/test/**"
    - "frontend/*/test/**"
    - "frontend/*/tests/**"
    - "frontend/*/coverage/**"
    - "frontend/*/dist/**"
    - "frontend/*/node_modules/**"
    - "**/*.md"
    - "**/LICENSE"
    - "docker/**"
    - "scripts/**"

# 标记 (flags) 配置，用于区分不同模块的覆盖率
flag_management:
  default_rules:
    carryforward: true        # 继承之前的覆盖率数据
    statuses:
      - type: project
        target: 82%
      - type: patch
        target: 80%

flags:
  backend:
    paths:
      - backend/src/main/java/
    carryforward: true
    
  frontend-uniapp:
    paths:
      - frontend/uni-app/src/
    carryforward: true
    
  frontend-admin:
    paths:
      - frontend/admin/src/
    carryforward: true

# 评论配置
comment:
  layout: "reach,diff,flags,tree,reach"
  behavior: default
  require_changes: false
  require_base: false
  require_head: true
  branches:
    - main
    - develop

# 组件覆盖率分析
component_management:
  default_rules:
    statuses:
      - type: project
        target: 82%
        
  individual_components:
    - component_id: backend-controllers
      name: "Backend Controllers"
      paths:
        - backend/src/main/java/**/controller/**
      statuses:
        - type: project
          target: 85%
          
    - component_id: backend-services
      name: "Backend Services"
      paths:
        - backend/src/main/java/**/service/**
      statuses:
        - type: project
          target: 90%
          
    - component_id: backend-security
      name: "Security Layer"
      paths:
        - backend/src/main/java/**/security/**
      statuses:
        - type: project
          target: 95%
          
    - component_id: frontend-components
      name: "Frontend Components"
      paths:
        - frontend/*/src/components/**
      statuses:
        - type: project
          target: 75%

# 通知配置
github_checks:
  annotations: true