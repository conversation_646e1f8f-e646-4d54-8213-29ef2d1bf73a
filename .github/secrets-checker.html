<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub Secrets 配置检查器 - 智能评估平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .section.required {
            border-left: 5px solid #e74c3c;
            background: #ffeaea;
        }
        
        .section.recommended {
            border-left: 5px solid #f39c12;
            background: #fff8e1;
        }
        
        .section.optional {
            border-left: 5px solid #3498db;
            background: #e3f2fd;
        }
        
        .section h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .section h3 .icon {
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .secret-item {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        
        .secret-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .secret-description {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        
        .secret-example {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.85em;
            color: #495057;
            border-left: 3px solid #28a745;
        }
        
        .checkbox-container {
            margin-top: 10px;
        }
        
        .checkbox-container input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }
        
        .checkbox-container label {
            cursor: pointer;
            user-select: none;
        }
        
        .progress-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c 0%, #f39c12 50%, #27ae60 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .progress-text {
            font-size: 1.1em;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .status-excellent {
            color: #27ae60;
        }
        
        .status-good {
            color: #f39c12;
        }
        
        .status-poor {
            color: #e74c3c;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            transition: background 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1em;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .actions .btn {
            margin: 0 10px;
        }
        
        .guide-links {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .guide-links h4 {
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .guide-links ul {
            list-style: none;
        }
        
        .guide-links li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .guide-links li::before {
            content: "📚";
            position: absolute;
            left: 0;
        }
        
        .guide-links a {
            color: #3498db;
            text-decoration: none;
        }
        
        .guide-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 GitHub Secrets 配置检查器</h1>
            <p>智能评估平台 CI/CD 配置助手</p>
        </div>
        
        <div class="content">
            <div class="progress-section">
                <h3>配置完整性</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">0% - 开始配置</div>
                <p id="statusMessage">请勾选已完成配置的项目</p>
            </div>
            
            <!-- 必需配置 -->
            <div class="section required">
                <h3>
                    <span class="icon">🚨</span>
                    必需配置 (CI/CD 核心功能)
                </h3>
                <p>这些配置是 CI/CD 流水线正常运行的必要条件</p>
                
                <div class="secret-item">
                    <div class="secret-name">CODECOV_TOKEN</div>
                    <div class="secret-description">Codecov 覆盖率上传令牌，用于自动生成和发布测试覆盖率报告</div>
                    <div class="secret-example">示例: a1b2c3d4-e5f6-7890-1234-567890abcdef</div>
                    <div class="checkbox-container">
                        <input type="checkbox" id="codecov" data-weight="60" data-category="required">
                        <label for="codecov">已配置 CODECOV_TOKEN</label>
                    </div>
                </div>
            </div>
            
            <!-- 推荐配置 -->
            <div class="section recommended">
                <h3>
                    <span class="icon">⚠️</span>
                    推荐配置 (增强功能)
                </h3>
                <p>这些配置可以显著提升开发体验和运维效率</p>
                
                <div class="secret-item">
                    <div class="secret-name">DINGTALK_WEBHOOK</div>
                    <div class="secret-description">钉钉机器人 Webhook 地址，用于接收构建状态和告警通知</div>
                    <div class="secret-example">示例: https://oapi.dingtalk.com/robot/send?access_token=...</div>
                    <div class="checkbox-container">
                        <input type="checkbox" id="dingtalk" data-weight="15" data-category="recommended">
                        <label for="dingtalk">已配置 DINGTALK_WEBHOOK</label>
                    </div>
                </div>
                
                <div class="secret-item">
                    <div class="secret-name">DOCKER_USERNAME & DOCKER_PASSWORD</div>
                    <div class="secret-description">Docker Hub 认证信息，用于自动构建和推送容器镜像</div>
                    <div class="secret-example">
                        用户名: your-dockerhub-username<br>
                        密码: dckr_pat_abcdef123456...
                    </div>
                    <div class="checkbox-container">
                        <input type="checkbox" id="docker" data-weight="15" data-category="recommended">
                        <label for="docker">已配置 Docker 认证信息</label>
                    </div>
                </div>
            </div>
            
            <!-- 可选配置 -->
            <div class="section optional">
                <h3>
                    <span class="icon">🔵</span>
                    可选配置 (部署功能)
                </h3>
                <p>这些配置用于启用自动部署和扩展功能</p>
                
                <div class="secret-item">
                    <div class="secret-name">服务器部署配置</div>
                    <div class="secret-description">SSH 密钥和服务器信息，用于自动部署到开发/生产环境</div>
                    <div class="secret-example">
                        DEV_HOST: dev.assessment.com<br>
                        DEV_USERNAME: deploy<br>
                        DEV_SSH_KEY: -----BEGIN OPENSSH PRIVATE KEY-----...
                    </div>
                    <div class="checkbox-container">
                        <input type="checkbox" id="deployment" data-weight="8" data-category="optional">
                        <label for="deployment">已配置服务器部署</label>
                    </div>
                </div>
                
                <div class="secret-item">
                    <div class="secret-name">扩展服务</div>
                    <div class="secret-description">SonarCloud、K6 Cloud 等第三方服务集成</div>
                    <div class="secret-example">
                        SONAR_TOKEN: sqp_abc123def456...<br>
                        K6_CLOUD_TOKEN: your-k6-token
                    </div>
                    <div class="checkbox-container">
                        <input type="checkbox" id="extended" data-weight="2" data-category="optional">
                        <label for="extended">已配置扩展服务</label>
                    </div>
                </div>
            </div>
            
            <div class="actions">
                <button class="btn btn-success" onclick="generateReport()">生成配置报告</button>
                <a href="https://github.com/your-org/Assessment/settings/secrets/actions" class="btn" target="_blank">前往配置 Secrets</a>
            </div>
            
            <div class="guide-links">
                <h4>📚 配置指南和文档</h4>
                <ul>
                    <li><a href="./.github/SECRETS_CONFIG_GUIDE.md">详细配置指南</a></li>
                    <li><a href="./.github/workflows/test-secrets.yml">配置验证工作流</a></li>
                    <li><a href="./.github/MONITORING_DASHBOARD.md">监控仪表板说明</a></li>
                    <li><a href="./scripts/setup-secrets.sh">快速配置脚本</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // 更新进度条
        function updateProgress() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            let totalWeight = 0;
            let completedWeight = 0;
            
            checkboxes.forEach(checkbox => {
                const weight = parseInt(checkbox.dataset.weight);
                totalWeight += weight;
                if (checkbox.checked) {
                    completedWeight += weight;
                }
            });
            
            const percentage = Math.round((completedWeight / totalWeight) * 100);
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const statusMessage = document.getElementById('statusMessage');
            
            progressFill.style.width = percentage + '%';
            progressText.textContent = percentage + '% - ';
            
            if (percentage >= 90) {
                progressText.textContent += '配置优秀';
                progressText.className = 'progress-text status-excellent';
                statusMessage.textContent = '🎉 配置完美！CI/CD 流水线已就绪';
            } else if (percentage >= 70) {
                progressText.textContent += '配置良好';
                progressText.className = 'progress-text status-good';
                statusMessage.textContent = '👍 配置良好，建议补充可选功能';
            } else if (percentage >= 50) {
                progressText.textContent += '基础配置';
                progressText.className = 'progress-text status-good';
                statusMessage.textContent = '⚠️ 基础配置完成，请补充推荐配置';
            } else {
                progressText.textContent += '配置不足';
                progressText.className = 'progress-text status-poor';
                statusMessage.textContent = '🚨 配置不足，影响 CI/CD 功能';
            }
        }
        
        // 生成配置报告
        function generateReport() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            let report = '# GitHub Secrets 配置报告\n\n';
            report += '**生成时间**: ' + new Date().toLocaleString('zh-CN') + '\n\n';
            
            const categories = {
                required: { name: '必需配置', items: [] },
                recommended: { name: '推荐配置', items: [] },
                optional: { name: '可选配置', items: [] }
            };
            
            checkboxes.forEach(checkbox => {
                const category = checkbox.dataset.category;
                const name = checkbox.previousElementSibling.textContent;
                const status = checkbox.checked ? '✅ 已配置' : '❌ 未配置';
                categories[category].items.push(`- ${name}: ${status}`);
            });
            
            Object.keys(categories).forEach(key => {
                const category = categories[key];
                report += `## ${category.name}\n\n`;
                report += category.items.join('\n') + '\n\n';
            });
            
            // 计算总体评分
            let totalWeight = 0;
            let completedWeight = 0;
            
            checkboxes.forEach(checkbox => {
                const weight = parseInt(checkbox.dataset.weight);
                totalWeight += weight;
                if (checkbox.checked) {
                    completedWeight += weight;
                }
            });
            
            const percentage = Math.round((completedWeight / totalWeight) * 100);
            
            report += `## 配置评分\n\n`;
            report += `**总体评分**: ${completedWeight}/${totalWeight} (${percentage}%)\n\n`;
            
            if (percentage >= 90) {
                report += `**配置状态**: 🟢 优秀 - CI/CD 功能完备\n\n`;
            } else if (percentage >= 70) {
                report += `**配置状态**: 🟡 良好 - 核心功能可用\n\n`;
            } else if (percentage >= 50) {
                report += `**配置状态**: 🟠 基础 - 需要补充\n\n`;
            } else {
                report += `**配置状态**: 🔴 不足 - 影响功能\n\n`;
            }
            
            report += `## 下一步行动\n\n`;
            if (percentage < 90) {
                report += `1. 补充缺失的配置项\n`;
                report += `2. 运行配置验证测试\n`;
                report += `3. 查看详细配置指南\n\n`;
            } else {
                report += `1. 运行配置验证测试确认\n`;
                report += `2. 启用完整 CI/CD 流水线\n`;
                report += `3. 监控运行状况\n\n`;
            }
            
            report += `---\n*由 GitHub Secrets 配置检查器生成*`;
            
            // 下载报告
            const blob = new Blob([report], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'github-secrets-report.md';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            alert('配置报告已生成并下载！');
        }
        
        // 监听复选框变化
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', updateProgress);
        });
        
        // 初始化进度
        updateProgress();
        
        // 设置仓库链接
        window.addEventListener('load', function() {
            // 可以根据实际仓库路径动态设置链接
            const repoLinks = document.querySelectorAll('a[href*="your-org/Assessment"]');
            repoLinks.forEach(link => {
                // 这里可以动态替换为实际的仓库路径
                // link.href = link.href.replace('your-org/Assessment', 'actual-org/Assessment');
            });
        });
    </script>
</body>
</html>