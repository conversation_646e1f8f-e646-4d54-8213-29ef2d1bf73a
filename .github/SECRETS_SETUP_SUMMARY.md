# GitHub Secrets 配置完整指南 - 智能评估平台

## 🎯 快速开始

### 立即配置 (5分钟)
1. **Codecov Token** (必需)
   ```bash
   # 访问 https://app.codecov.io/
   # 添加项目并复制 Token
   # 添加到 GitHub Secrets: CODECOV_TOKEN
   ```

2. **运行配置脚本**
   ```bash
   cd /path/to/Assessment
   ./scripts/setup-secrets.sh
   ```

3. **验证配置**
   ```bash
   # 访问 GitHub Actions
   # 运行 test-secrets.yml 工作流
   ```

## 📋 配置清单

### 🚨 必需配置 (60分)
| Secret | 描述 | 获取方式 | 优先级 |
|--------|------|----------|--------|
| `CODECOV_TOKEN` | 覆盖率报告上传 | [Codecov.io](https://app.codecov.io/) | 🔴 立即 |

### ⚠️ 推荐配置 (30分)
| Secret | 描述 | 获取方式 | 优先级 |
|--------|------|----------|--------|
| `DINGTALK_WEBHOOK` | 钉钉通知机器人 | 钉钉群 > 添加机器人 | 🟡 推荐 |
| `DOCKER_USERNAME` | Docker Hub 用户名 | [Docker Hub](https://hub.docker.com/) | 🟡 推荐 |
| `DOCKER_PASSWORD` | Docker Hub 访问令牌 | Docker Hub > Security | 🟡 推荐 |

### 🔵 可选配置 (10分)
| Secret | 描述 | 获取方式 | 优先级 |
|--------|------|----------|--------|
| `DEV_HOST` | 开发服务器地址 | 你的服务器 IP/域名 | 🔵 可选 |
| `DEV_USERNAME` | SSH 用户名 | 服务器用户 | 🔵 可选 |
| `DEV_SSH_KEY` | SSH 私钥 | `ssh-keygen` 生成 | 🔵 可选 |
| `PROD_HOST` | 生产服务器地址 | 生产服务器 IP/域名 | 🔵 可选 |
| `PROD_USERNAME` | 生产 SSH 用户名 | 生产服务器用户 | 🔵 可选 |
| `PROD_SSH_KEY` | 生产 SSH 私钥 | 专用密钥对 | 🔵 可选 |

## 🛠️ 配置工具

### 1. 自动化配置脚本
```bash
# 交互式配置助手
./scripts/setup-secrets.sh

# 功能：
# - 生成 SSH 密钥对
# - 显示配置指引
# - 生成配置模板
# - 测试网络连接
```

### 2. Web 配置检查器
```bash
# 在浏览器中打开
open .github/secrets-checker.html

# 功能：
# - 可视化配置进度
# - 实时评分系统
# - 生成配置报告
# - 配置指导链接
```

### 3. CI/CD 验证测试
```bash
# GitHub Actions 工作流
.github/workflows/test-secrets.yml

# 功能：
# - 自动检测 Secrets 配置
# - 测试服务连接
# - 生成配置报告
# - 创建改进 Issue
```

## 📊 配置评分系统

### 评分标准
- **90-100分**: 🟢 配置优秀 - CI/CD 功能完备
- **70-89分**: 🟡 配置良好 - 核心功能可用  
- **50-69分**: 🟠 配置基础 - 需要补充
- **0-49分**: 🔴 配置不足 - 影响功能

### 权重分配
- 必需配置: 60% (Codecov Token)
- 推荐配置: 30% (通知 + Docker)
- 可选配置: 10% (部署 + 扩展)

## 🔐 详细配置步骤

### Step 1: Codecov Token (必需)

#### 1.1 注册和设置
```bash
# 1. 访问 Codecov
open https://app.codecov.io/

# 2. GitHub 登录
# 3. 添加 Assessment 项目
# 4. 复制 Repository Upload Token
```

#### 1.2 添加到 GitHub
```bash
# 1. 访问仓库设置
open https://github.com/your-org/Assessment/settings/secrets/actions

# 2. 点击 "New repository secret"
# 3. 输入:
Name: CODECOV_TOKEN
Value: [你的 Token]
```

### Step 2: 钉钉通知 (推荐)

#### 2.1 创建机器人
```bash
# 1. 钉钉群 > 群设置 > 智能群助手
# 2. 添加机器人 > 自定义
# 3. 配置安全设置 (加签 / 关键词)
# 4. 复制 Webhook URL
```

#### 2.2 添加到 GitHub
```bash
Name: DINGTALK_WEBHOOK
Value: https://oapi.dingtalk.com/robot/send?access_token=your_token
```

### Step 3: Docker Hub (推荐)

#### 3.1 创建访问令牌
```bash
# 1. 登录 Docker Hub
open https://hub.docker.com/settings/security

# 2. "New Access Token"
# 3. 权限: Read, Write, Delete
# 4. 复制令牌 (dckr_pat_...)
```

#### 3.2 添加到 GitHub
```bash
Name: DOCKER_USERNAME
Value: your-dockerhub-username

Name: DOCKER_PASSWORD
Value: dckr_pat_your_access_token
```

### Step 4: 服务器部署 (可选)

#### 4.1 生成 SSH 密钥
```bash
# 使用配置脚本
./scripts/setup-secrets.sh
# 选择选项 1

# 或手动生成
ssh-keygen -t ed25519 -C "github-actions@assessment" -f ~/.ssh/assessment_deploy
```

#### 4.2 部署公钥到服务器
```bash
# 复制公钥到服务器
ssh-copy-id -i ~/.ssh/assessment_deploy.pub deploy@your-server

# 或手动添加到 ~/.ssh/authorized_keys
cat ~/.ssh/assessment_deploy.pub >> ~/.ssh/authorized_keys
```

#### 4.3 添加到 GitHub
```bash
# 开发环境
Name: DEV_HOST
Value: dev.assessment.com

Name: DEV_USERNAME  
Value: deploy

Name: DEV_SSH_KEY
Value: [~/.ssh/assessment_deploy 文件内容]

# 生产环境类似配置
```

## 🧪 配置验证

### 自动化测试
```bash
# 1. 推送代码触发 CI/CD
git add .
git commit -m "feat: configure GitHub Secrets"
git push

# 2. 手动运行验证测试
# 访问: GitHub Actions > test-secrets.yml > Run workflow

# 3. 查看测试结果
# 检查所有测试是否通过
```

### 手动验证
```bash
# 1. 检查 Codecov 集成
# 访问: https://app.codecov.io/gh/your-org/Assessment

# 2. 测试钉钉通知
curl -X POST "$DINGTALK_WEBHOOK" \
  -H 'Content-Type: application/json' \
  -d '{"msgtype": "text", "text": {"content": "测试通知"}}'

# 3. 验证 Docker 登录
echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
```

## 🚨 常见问题

### Q1: Codecov 上传失败
```bash
# 检查项目是否已添加到 Codecov
# 确认 Token 格式正确
# 查看 GitHub Actions 日志
```

### Q2: 钉钉通知无法发送
```bash
# 验证 Webhook URL 格式
# 检查机器人安全设置
# 确认群聊状态正常
```

### Q3: SSH 连接失败
```bash
# 检查密钥格式
ssh-keygen -y -f ~/.ssh/assessment_deploy

# 测试连接
ssh -i ~/.ssh/assessment_deploy deploy@server
```

### Q4: Docker 推送失败
```bash
# 验证访问令牌权限
# 检查仓库名称和权限
# 确认令牌未过期
```

## 🔄 维护和轮换

### 建议轮换频率
| Secret 类型 | 轮换频率 | 监控方式 |
|-------------|----------|----------|
| Codecov Token | 每年 | 功能监控 |
| Docker Token | 每季度 | 推送监控 |
| SSH 密钥 | 每季度 | 访问日志 |
| Webhook URL | 按需 | 通知测试 |

### 自动化监控
```yaml
# 每月检查 Secret 状态
schedule:
  - cron: '0 9 1 * *'

# 检查项目：
# - Secret 是否过期
# - 服务连接是否正常
# - 权限是否足够
```

## 📈 配置完成后的收益

### 即时收益
- ✅ 自动化测试覆盖率监控
- ✅ 实时构建状态通知
- ✅ 代码质量门禁检查
- ✅ 安全漏洞自动扫描

### 中期收益
- 🚀 全自动 CI/CD 流水线
- 📊 详细的质量趋势分析
- 🔔 智能化告警和通知
- 🛡️ 全面的安全保障

### 长期收益
- 📈 开发效率显著提升
- 🎯 代码质量持续改进
- 🔒 安全风险有效控制
- 🤖 运维成本大幅降低

## 📚 相关文档

### 配置文档
- [详细配置指南](./.github/SECRETS_CONFIG_GUIDE.md)
- [监控仪表板](./.github/MONITORING_DASHBOARD.md)
- [CI/CD 工作流说明](./.github/workflows/)

### 工具和脚本
- [配置助手脚本](./scripts/setup-secrets.sh)
- [Web 配置检查器](./.github/secrets-checker.html)
- [验证测试工作流](./.github/workflows/test-secrets.yml)

### 故障排除
- [常见问题解答](./.github/SECRETS_CONFIG_GUIDE.md#故障排除)
- [网络连接测试](./scripts/setup-secrets.sh)
- [日志分析指南](./.github/MONITORING_DASHBOARD.md#故障响应流程)

---

## 🎉 配置完成检查清单

- [ ] **Codecov Token** 已配置并验证
- [ ] **钉钉 Webhook** 已配置并测试通知
- [ ] **Docker 认证** 已配置并测试登录  
- [ ] **SSH 密钥** 已生成并部署到服务器
- [ ] **验证测试** 已运行并通过 (90%+ 评分)
- [ ] **CI/CD 流水线** 已验证正常运行
- [ ] **监控仪表板** 已启用并配置
- [ ] **团队培训** 已完成相关文档学习

**🚀 配置完成后，智能评估平台将拥有业界领先的 CI/CD 和质量保障体系！**

---
*最后更新: 2025-06-23*  
*维护团队: DevOps Team*