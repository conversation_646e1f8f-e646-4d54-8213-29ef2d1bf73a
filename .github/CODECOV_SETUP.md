# Codecov 集成设置指南

## 🎯 Codecov 集成目标
- 自动化测试覆盖率监控
- 覆盖率变化趋势分析  
- PR 覆盖率检查
- 多模块覆盖率报告

## 📋 设置步骤

### Step 1: 创建 Codecov 账户
1. 访问 [Codecov.io](https://app.codecov.io/)
2. 使用 GitHub 账户登录
3. 授权 Codecov 访问你的仓库

### Step 2: 添加项目到 Codecov
1. 在 Codecov 仪表板点击 "Add new repository"
2. 选择 Assessment 项目
3. 复制提供的 Upload Token

### Step 3: 配置 GitHub Secret
```bash
# 在 GitHub Settings > Secrets and variables > Actions 中添加:
CODECOV_TOKEN=你的_Upload_Token
```

### Step 4: 验证集成
```bash
# 推送代码触发 CI/CD 流水线
git push origin main

# 检查 Codecov 仪表板是否接收到覆盖率数据
# URL: https://app.codecov.io/gh/你的组织/Assessment
```

## 🔧 配置文件说明

### `.github/codecov.yml` 主要配置
```yaml
coverage:
  status:
    project:
      default:
        target: 82%          # 项目整体目标覆盖率
    patch:
      default:
        target: 80%          # 新代码目标覆盖率

flags:
  backend:                   # 后端覆盖率标记
    paths: [backend/src/main/java/]
  frontend-uniapp:           # uni-app 覆盖率标记
    paths: [frontend/uni-app/src/]
  frontend-admin:            # admin 覆盖率标记
    paths: [frontend/admin/src/]
```

### 多模块报告配置
```yaml
component_management:
  individual_components:
    - component_id: backend-controllers
      paths: [backend/src/main/java/**/controller/**]
      target: 85%
    - component_id: backend-services  
      paths: [backend/src/main/java/**/service/**]
      target: 90%
    - component_id: backend-security
      paths: [backend/src/main/java/**/security/**]
      target: 95%
```

## 📊 覆盖率监控仪表板

### 关键指标监控
| 指标 | 当前值 | 目标值 | 趋势 |
|------|--------|--------|------|
| 整体覆盖率 | 82% | 85% | ↗️ |
| 后端覆盖率 | 82% | 85% | ↗️ |
| 前端覆盖率 | - | 75% | - |
| 安全模块覆盖率 | 94% | 95% | ↗️ |

### Codecov Badge 集成
在 README.md 中添加覆盖率徽章:
```markdown
[![codecov](https://codecov.io/gh/你的组织/Assessment/branch/main/graph/badge.svg)](https://codecov.io/gh/你的组织/Assessment)
```

## 🚨 覆盖率质量门禁

### PR 检查规则
- ✅ 新代码覆盖率 ≥ 80%
- ✅ 整体覆盖率不下降超过 2%
- ✅ 安全模块覆盖率 ≥ 95%
- ⚠️ 控制器覆盖率 ≥ 85%

### 自动化操作
```yaml
# 当覆盖率下降时自动创建 Issue
- name: Create coverage issue
  if: ${{ env.coverage_dropped == 'true' }}
  uses: actions/github-script@v7
  with:
    script: |
      github.rest.issues.create({
        owner: context.repo.owner,
        repo: context.repo.repo,
        title: '🚨 覆盖率下降警告',
        body: '检测到测试覆盖率下降，请及时处理。',
        labels: ['coverage', 'quality']
      });
```

## 📈 覆盖率趋势分析

### 周报自动生成
```yaml
name: Weekly Coverage Report

on:
  schedule:
    - cron: '0 9 * * 1'  # 每周一上午9点

jobs:
  coverage-report:
    runs-on: ubuntu-latest
    steps:
    - name: Generate coverage trend report
      run: |
        # 调用 Codecov API 获取历史数据
        curl -H "Authorization: token ${{ secrets.CODECOV_TOKEN }}" \
          "https://codecov.io/api/gh/${{ github.repository }}/coverage/trend"
```

### 覆盖率趋势可视化
- **图表类型**: 线性趋势图
- **时间范围**: 最近30天
- **数据粒度**: 每日覆盖率数据
- **对比维度**: 分支、模块、文件类型

## 🔍 高级分析功能

### 1. 热力图分析
```bash
# 生成覆盖率热力图
curl -H "Authorization: Bearer $CODECOV_TOKEN" \
  "https://codecov.io/api/v2/gh/$OWNER/$REPO/coverage/tree"
```

### 2. 差异覆盖率分析
```bash
# 比较两个分支的覆盖率差异
curl -H "Authorization: Bearer $CODECOV_TOKEN" \
  "https://codecov.io/api/v2/gh/$OWNER/$REPO/compare/$BASE...$HEAD"
```

### 3. 文件级覆盖率详情
```bash
# 获取特定文件的覆盖率详情
curl -H "Authorization: Bearer $CODECOV_TOKEN" \
  "https://codecov.io/api/v2/gh/$OWNER/$REPO/file/$FILE_PATH"
```

## 🛠️ 故障排除

### 问题: 覆盖率数据未上传
```yaml
# 检查 CI/CD 日志中的 Codecov 步骤
- name: Debug Codecov upload
  run: |
    find . -name "*.xml" -o -name "*.info" -o -name "lcov.info"
    ls -la backend/target/site/jacoco/
```

### 问题: 覆盖率计算不准确
```yaml
# 验证覆盖率文件格式
- name: Validate coverage files
  run: |
    head -20 backend/target/site/jacoco/jacoco.xml
    xmllint --noout backend/target/site/jacoco/jacoco.xml
```

### 问题: 多模块覆盖率合并失败
```yaml
# 确保使用正确的 flags
- name: Upload backend coverage
  uses: codecov/codecov-action@v4
  with:
    file: ./backend/target/site/jacoco/jacoco.xml
    flags: backend
    name: backend-coverage
```

## 📚 相关服务集成

### SonarCloud 集成 (可选)
```yaml
# 添加 SonarCloud 质量门禁
- name: SonarCloud Scan
  uses: SonarSource/sonarcloud-github-action@master
  env:
    GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
```

### Code Climate 集成 (可选)
```yaml
# 添加 Code Climate 可维护性分析
- name: Code Climate coverage
  uses: paambaati/codeclimate-action@v5
  env:
    CC_TEST_REPORTER_ID: ${{ secrets.CC_TEST_REPORTER_ID }}
  with:
    coverageLocations: |
      backend/target/site/jacoco/jacoco.xml:jacoco
```

---
*最后更新: 2025-06-23*
*负责人: DevOps Team*