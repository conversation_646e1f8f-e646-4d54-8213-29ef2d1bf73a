version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: assessment_postgres_prod
    restart: always
    environment:
      POSTGRES_DB: assessment_multitenant
      POSTGRES_USER: ${DB_USER:-assessment_user}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password_here}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    ports:
      - "127.0.0.1:5432:5432"
    networks:
      - assessment_network_prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U assessment_user"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: assessment_redis_prod
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD:-secure_redis_password}
    volumes:
      - redis_data_prod:/data
    ports:
      - "127.0.0.1:6379:6379"
    networks:
      - assessment_network_prod
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端服务
  backend:
    image: ${DOCKER_USERNAME}/assessment-backend:${VERSION:-latest}
    container_name: assessment_backend_prod
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ******************************************************
      SPRING_DATASOURCE_USERNAME: ${DB_USER:-assessment_user}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD:-secure_password_here}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-secure_redis_password}
      JWT_SECRET: ${JWT_SECRET:-secure_jwt_secret_key_here}
      JWT_EXPIRATION: 3600000
      JWT_REFRESH_EXPIRATION: **********
      # LM Studio 配置
      LM_STUDIO_BASE_URL: ${LM_STUDIO_BASE_URL:-http://localhost:1234/v1}
      LM_STUDIO_API_KEY: ${LM_STUDIO_API_KEY:-lm-studio}
      # 文件上传配置
      FILE_UPLOAD_PATH: /app/uploads
      FILE_MAX_SIZE: 52428800
      # 日志配置
      LOGGING_LEVEL_ROOT: INFO
      LOGGING_LEVEL_COM_ASSESSMENT: INFO
      # JVM 优化
      JAVA_OPTS: >-
        -Xms512m
        -Xmx2g
        -XX:+UseG1GC
        -XX:MaxGCPauseMillis=200
        -XX:+UseStringDeduplication
    volumes:
      - backend_uploads_prod:/app/uploads
      - backend_logs_prod:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "127.0.0.1:8080:8080"
    networks:
      - assessment_network_prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 前端管理后台
  frontend-admin:
    image: ${DOCKER_USERNAME}/assessment-admin:${VERSION:-latest}
    container_name: assessment_admin_prod
    restart: always
    environment:
      VITE_API_BASE_URL: ${API_BASE_URL:-https://api.assessment.com}
      VITE_APP_TITLE: 智慧养老评估平台
    ports:
      - "127.0.0.1:3000:80"
    networks:
      - assessment_network_prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端移动端
  frontend-mobile:
    image: ${DOCKER_USERNAME}/assessment-mobile:${VERSION:-latest}
    container_name: assessment_mobile_prod
    restart: always
    environment:
      VITE_API_BASE_URL: ${API_BASE_URL:-https://api.assessment.com}
      VITE_APP_TITLE: 智慧养老评估
    ports:
      - "127.0.0.1:3001:80"
    networks:
      - assessment_network_prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: assessment_nginx_prod
    restart: always
    volumes:
      - ./nginx/prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs_prod:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend-admin
      - frontend-mobile
    networks:
      - assessment_network_prod
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 备份服务
  backup:
    image: prodrigestivill/postgres-backup-local:15
    container_name: assessment_backup_prod
    restart: always
    environment:
      POSTGRES_HOST: postgres
      POSTGRES_DB: assessment_multitenant
      POSTGRES_USER: ${DB_USER:-assessment_user}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password_here}
      SCHEDULE: "@daily"
      BACKUP_KEEP_DAYS: 7
      BACKUP_KEEP_WEEKS: 4
      BACKUP_KEEP_MONTHS: 6
    volumes:
      - backup_data_prod:/backups
    depends_on:
      - postgres
    networks:
      - assessment_network_prod

# 网络配置
networks:
  assessment_network_prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  backend_uploads_prod:
    driver: local
  backend_logs_prod:
    driver: local
  nginx_logs_prod:
    driver: local
  backup_data_prod:
    driver: local