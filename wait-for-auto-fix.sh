#!/bin/bash

# 等待 CI/CD 自动修复完成的脚本
echo "⏳ 等待 CI/CD 自动修复完成..."
echo "================================"

# 获取最新推送的提交
LAST_COMMIT=$(git rev-parse HEAD)
echo "📌 当前提交: ${LAST_COMMIT:0:8}"

# 获取当前工作流
echo "🔍 查找相关工作流..."
WORKFLOW_ID=$(gh run list --commit $LAST_COMMIT --limit 1 --json databaseId --jq '.[0].databaseId')

if [ -z "$WORKFLOW_ID" ] || [ "$WORKFLOW_ID" = "null" ]; then
    echo "❌ 未找到相关工作流"
    exit 1
fi

echo "🚀 工作流ID: $WORKFLOW_ID"
echo "🔗 查看地址: https://github.com/changxiaoyangbrain/assessment/actions/runs/$WORKFLOW_ID"
echo ""

# 监控函数
check_status() {
    STATUS_JSON=$(gh run view $WORKFLOW_ID --json status,conclusion 2>/dev/null)
    STATUS=$(echo "$STATUS_JSON" | jq -r '.status')
    CONCLUSION=$(echo "$STATUS_JSON" | jq -r '.conclusion')
    
    case $STATUS in
        "queued")
            echo "⏳ 排队中..."
            return 1
            ;;
        "in_progress")
            echo "🔄 运行中..."
            return 1
            ;;
        "completed")
            if [ "$CONCLUSION" = "success" ]; then
                echo "✅ 工作流成功完成！"
                return 0
            else
                echo "❌ 工作流失败 ($CONCLUSION)"
                return 2
            fi
            ;;
        *)
            echo "❓ 未知状态: $STATUS"
            return 1
            ;;
    esac
}

# 等待循环
echo "开始监控 (最多等待5分钟)..."
START_TIME=$(date +%s)
MAX_WAIT=300  # 5分钟

while true; do
    CURRENT_TIME=$(date +%s)
    ELAPSED=$((CURRENT_TIME - START_TIME))
    
    if [ $ELAPSED -gt $MAX_WAIT ]; then
        echo "⏰ 超时 (5分钟)，停止等待"
        echo "💡 请手动检查: gh run view $WORKFLOW_ID"
        exit 1
    fi
    
    # 显示进度
    printf "\r⏳ 已等待 %ds/%ds " $ELAPSED $MAX_WAIT
    
    # 检查状态
    check_status
    RESULT=$?
    
    if [ $RESULT -eq 0 ]; then
        # 成功完成
        echo ""
        echo "🎉 自动修复完成！"
        
        # 检查是否有新提交
        echo "🔍 检查是否有自动修复提交..."
        git fetch origin
        
        REMOTE_COMMIT=$(git rev-parse origin/main)
        if [ "$LAST_COMMIT" != "$REMOTE_COMMIT" ]; then
            echo "✨ 发现自动修复提交！"
            echo "📋 新提交:"
            git log --oneline $LAST_COMMIT..origin/main
            
            echo ""
            read -p "是否立即拉取自动修复的代码？(Y/n): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Nn]$ ]]; then
                echo "⬇️ 拉取自动修复..."
                git pull origin main
                echo "✅ 同步完成！"
            fi
        else
            echo "ℹ️ 没有新的自动修复提交"
        fi
        
        break
        
    elif [ $RESULT -eq 2 ]; then
        # 失败
        echo ""
        echo "❌ 工作流失败，可能需要手动修复"
        echo "📝 查看详细日志: gh run view $WORKFLOW_ID --log"
        exit 1
    fi
    
    # 等待10秒后再次检查
    sleep 10
done

echo ""
echo "🎯 自动修复流程完成！"