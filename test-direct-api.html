<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码直接API测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .captcha-container { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .timestamp { color: #666; font-size: 12px; }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 验证码直接API测试</h1>
        <p>此页面直接调用后端API，绕过前端缓存，用于测试验证码样式是否真的改变了。</p>
        
        <div class="captcha-container">
            <h3>当前验证码</h3>
            <div id="captcha-display">
                <p>点击"获取新验证码"按钮开始测试</p>
            </div>
            <button onclick="getCaptcha()">🔄 获取新验证码</button>
            <button onclick="getCaptcha(true)">🚫 强制刷新 (无缓存)</button>
        </div>
        
        <div id="status"></div>
        
        <div class="timestamp" id="timestamp"></div>
    </div>

    <script>
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${isError ? 'error' : 'success'}`;
            statusDiv.textContent = message;
        }

        function updateTimestamp() {
            document.getElementById('timestamp').textContent = 
                '最后更新: ' + new Date().toLocaleString('zh-CN');
        }

        async function getCaptcha(forceRefresh = false) {
            const captchaDisplay = document.getElementById('captcha-display');
            captchaDisplay.innerHTML = '<p>🔄 正在获取验证码...</p>';
            
            try {
                // 构建请求URL，强制刷新时添加时间戳
                let url = 'http://localhost:8181/api/captcha/get';
                if (forceRefresh) {
                    url += '?_t=' + Date.now();
                }
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        // 强制无缓存
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    },
                    // 强制不使用缓存
                    cache: 'no-store'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data) {
                    const { originalImageBase64, jigsawImageBase64, y, token } = data.data;
                    
                    captchaDisplay.innerHTML = `
                        <div style="margin: 10px 0;">
                            <h4>🖼️ 背景图片：</h4>
                            <img src="data:image/png;base64,${originalImageBase64}" 
                                 style="border: 1px solid #ccc; max-width: 100%;" 
                                 alt="验证码背景"/>
                        </div>
                        <div style="margin: 10px 0;">
                            <h4>🧩 拼图滑块：</h4>
                            <img src="data:image/png;base64,${jigsawImageBase64}" 
                                 style="border: 1px solid #ccc; background: #f0f0f0;" 
                                 alt="验证码滑块"/>
                        </div>
                        <div style="margin: 10px 0; font-size: 12px; color: #666;">
                            <p>📍 Y坐标: ${y}</p>
                            <p>🔑 Token: ${token.substring(0, 20)}...</p>
                        </div>
                    `;
                    
                    showStatus(`✅ 验证码获取成功！检查上方图片是否为彩色样式（天蓝色背景，金黄色滑块）`);
                    updateTimestamp();
                } else {
                    throw new Error('API返回数据格式错误');
                }
                
            } catch (error) {
                console.error('获取验证码失败:', error);
                captchaDisplay.innerHTML = `<p style="color: red;">❌ 获取失败: ${error.message}</p>`;
                showStatus(`❌ 获取验证码失败: ${error.message}`, true);
            }
        }

        // 页面加载时自动获取一次验证码
        document.addEventListener('DOMContentLoaded', function() {
            getCaptcha();
        });
    </script>
</body>
</html>