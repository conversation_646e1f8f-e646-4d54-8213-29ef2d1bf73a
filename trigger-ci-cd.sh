#!/bin/bash

# 触发 CI/CD 工作流的脚本
echo "============================================="
echo "       触发 GitHub Actions CI/CD"
echo "============================================="

# 检查是否在 git 仓库中
if [ ! -d ".git" ]; then
    echo "❌ 错误：当前目录不是 Git 仓库"
    exit 1
fi

# 检查是否有未跟踪的文件
echo "📋 检查 Git 状态..."
git status --porcelain

# 询问用户是否继续
echo ""
read -p "是否要提交当前更改并触发 CI/CD？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

# 添加所有更改
echo "📦 添加文件到暂存区..."
git add .

# 提交更改
COMMIT_MSG="ci: 测试 CI/CD 工作流 - $(date '+%Y-%m-%d %H:%M:%S')"
echo "💾 提交更改: $COMMIT_MSG"
git commit -m "$COMMIT_MSG"

# 推送到远程仓库
echo "🚀 推送到远程仓库..."
git push origin main

echo ""
echo "✅ 代码已推送！GitHub Actions 工作流现在应该自动开始。"
echo ""
echo "📊 查看工作流状态："
echo "   网页: https://github.com/$(git remote get-url origin | sed 's/.*github.com[:/]\([^.]*\).*/\1/')/actions"
echo ""
echo "💡 可以使用以下命令查看工作流状态："
echo "   gh run list  # 如果安装了 GitHub CLI"