<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文背景图验证码实验</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            padding: 20px;
            margin: 0;
            color: white;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 18px;
        }
        
        .experiment-section {
            margin: 30px 0;
            padding: 25px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }
        
        .experiment-title {
            color: #2d3748;
            font-size: 20px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .captcha-display {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            text-align: center;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .theory-section {
            background: #2d3748;
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 30px 0;
        }
        
        .theory-section h3 {
            color: #81c784;
            margin-bottom: 15px;
        }
        
        .prediction {
            background: rgba(129, 199, 132, 0.1);
            border-left: 4px solid #81c784;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .step {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .step h4 {
            color: #81c784;
            margin-bottom: 8px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .comparison-item.before {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid #f44336;
        }
        
        .comparison-item.after {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid #4caf50;
        }
        
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
            .steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🀄 中文字符背景图验证码实验</h1>
            <p>测试彩色中文字符背景图的拼图滑块自动生成效果</p>
        </div>
        
        <div class="theory-section">
            <h3>🔬 实验理论预测</h3>
            <div class="prediction">
                <h4>📊 预期效果：</h4>
                <p>当使用彩色中文字符作为背景图时，系统将：</p>
                <div class="steps">
                    <div class="step">
                        <h4>🎯 第1步</h4>
                        <p>加载中文字符背景图</p>
                    </div>
                    <div class="step">
                        <h4>📐 第2步</h4>
                        <p>随机选择位置提取47×47像素区域</p>
                    </div>
                    <div class="step">
                        <h4>🀄 第3步</h4>
                        <p>拼图块将包含该位置的中文字符</p>
                    </div>
                    <div class="step">
                        <h4>🧩 第4步</h4>
                        <p>形成带有中文字符的真实拼图</p>
                    </div>
                </div>
            </div>
            
            <div class="comparison">
                <div class="comparison-item before">
                    <h4>🔴 一般背景图</h4>
                    <p>拼图块包含风景、建筑等图像元素</p>
                </div>
                <div class="comparison-item after">
                    <h4>🟢 中文字符背景</h4>
                    <p>拼图块包含彩色中文字符，更具识别特色</p>
                </div>
            </div>
        </div>
        
        <div class="experiment-section">
            <h3 class="experiment-title">🧪 实验操作区</h3>
            <div style="text-align: center; margin-bottom: 20px;">
                <button class="test-button" onclick="testChineseCaptcha()">🔄 生成中文背景验证码</button>
                <button class="test-button" onclick="testMultipleTimes()">🎲 连续测试5次</button>
                <button class="test-button" onclick="showCurrentImages()">📸 查看当前所有背景图</button>
            </div>
            
            <div id="experiment-status" class="status info">
                点击"生成中文背景验证码"开始实验
            </div>
            
            <div id="captcha-results" class="captcha-display">
                <p style="color: #666;">准备显示验证码实验结果...</p>
            </div>
        </div>
        
        <div class="theory-section">
            <h3>📋 实验步骤说明</h3>
            <ol style="line-height: 1.8; padding-left: 20px;">
                <li><strong>保存图片</strong>：将你的中文字符背景图保存为 <code>bg_chinese.png</code></li>
                <li><strong>放置目录</strong>：复制到 <code>backend/src/main/resources/images/</code></li>
                <li><strong>重启服务</strong>：重新编译并启动后端服务</li>
                <li><strong>测试效果</strong>：观察拼图块是否包含中文字符</li>
                <li><strong>验证机制</strong>：确认拼图块与背景图完美匹配</li>
            </ol>
            
            <div style="margin-top: 20px; padding: 15px; background: rgba(255, 193, 7, 0.1); border-radius: 8px;">
                <h4 style="color: #ffc107;">⚠️ 注意事项：</h4>
                <ul style="line-height: 1.6; padding-left: 20px;">
                    <li>确保图片尺寸适合（建议310×155像素或等比例）</li>
                    <li>中文字符要有足够的对比度便于识别</li>
                    <li>避免字符过小影响拼图块的视觉效果</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let testCount = 0;
        
        async function testChineseCaptcha() {
            const statusDiv = document.getElementById('experiment-status');
            const resultsDiv = document.getElementById('captcha-results');
            
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 正在生成中文背景验证码实验...';
            resultsDiv.innerHTML = '<p style="color: #666;">加载中...</p>';
            
            try {
                const response = await fetch('http://localhost:8181/api/captcha/get', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data) {
                    testCount++;
                    const { originalImageBase64, jigsawImageBase64, y, token } = data.data;
                    
                    resultsDiv.innerHTML = `
                        <div style="border: 2px solid #667eea; border-radius: 12px; padding: 20px; background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);">
                            <h4 style="color: #2d3748; margin-bottom: 15px; text-align: center;">
                                🧪 实验结果 #${testCount} - 验证码生成成功
                            </h4>
                            
                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin: 20px 0;">
                                <div style="text-align: center;">
                                    <h5 style="color: #2d3748; margin-bottom: 10px;">🀄 中文字符背景图</h5>
                                    <img src="data:image/png;base64,${originalImageBase64}" 
                                         style="max-width: 100%; border: 2px solid #667eea; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);" 
                                         alt="中文背景验证码"/>
                                    <p style="font-size: 12px; color: #666; margin-top: 8px;">
                                        📐 尺寸: 310×155px | 观察缺口位置
                                    </p>
                                </div>
                                
                                <div style="text-align: center;">
                                    <h5 style="color: #2d3748; margin-bottom: 10px;">🧩 提取的拼图块</h5>
                                    <div style="display: flex; justify-content: center; align-items: center; height: 155px;">
                                        <img src="data:image/png;base64,${jigsawImageBase64}" 
                                             style="border: 2px solid #48bb78; border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);" 
                                             alt="中文拼图块"/>
                                    </div>
                                    <p style="font-size: 12px; color: #666; margin-top: 8px;">
                                        🎯 尺寸: 47×47px | Y坐标: ${y}px
                                    </p>
                                </div>
                            </div>
                            
                            <div style="background: #e6fffa; border: 1px solid #38b2ac; border-radius: 8px; padding: 15px; margin-top: 20px;">
                                <h5 style="color: #234e52; margin-bottom: 8px;">🔍 实验观察要点：</h5>
                                <ul style="color: #234e52; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                                    <li>拼图块是否包含中文字符的部分内容？</li>
                                    <li>拼图块的颜色是否与背景图该位置匹配？</li>
                                    <li>缺口位置是否与拼图块大小和位置对应？</li>
                                    <li>整体视觉效果是否比纯色背景更有特色？</li>
                                </ul>
                            </div>
                            
                            <div style="text-align: center; margin-top: 15px; padding: 10px; background: rgba(102, 126, 234, 0.1); border-radius: 6px;">
                                <small style="color: #667eea;">
                                    🆔 Token: ${token.substring(0, 20)}... | ⏰ 生成时间: ${new Date().toLocaleTimeString()}
                                </small>
                            </div>
                        </div>
                    `;
                    
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        ✅ 中文背景验证码实验成功！<br>
                        <small>观察拼图块是否包含了中文字符的内容</small>
                    `;
                } else {
                    throw new Error('API返回数据格式错误');
                }
                
            } catch (error) {
                console.error('实验失败:', error);
                resultsDiv.innerHTML = `
                    <div style="border: 2px solid #f56565; border-radius: 8px; padding: 15px; background: #fed7d7;">
                        <h4 style="color: #c53030; margin-top: 0;">❌ 实验失败</h4>
                        <p style="color: #c53030; margin: 0;">${error.message}</p>
                        <p style="color: #c53030; margin: 10px 0 0 0; font-size: 14px;">
                            请确保已将中文背景图保存为 bg_chinese.png 并重启了服务
                        </p>
                    </div>
                `;
                statusDiv.className = 'status warning';
                statusDiv.textContent = `⚠️ 实验失败: ${error.message}`;
            }
        }
        
        async function testMultipleTimes() {
            const statusDiv = document.getElementById('experiment-status');
            statusDiv.className = 'status info';
            statusDiv.textContent = '🎲 开始连续测试5次，观察随机性...';
            
            for (let i = 1; i <= 5; i++) {
                await testChineseCaptcha();
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            }
            
            statusDiv.className = 'status success';
            statusDiv.textContent = '🎉 连续测试完成！观察每次生成的拼图块位置和内容是否不同';
        }
        
        function showCurrentImages() {
            const resultsDiv = document.getElementById('captcha-results');
            resultsDiv.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="color: #2d3748; margin-bottom: 15px;">📁 当前验证码背景图列表</h4>
                    <div style="background: #f7fafc; border-radius: 8px; padding: 15px; margin: 15px 0;">
                        <p style="color: #2d3748; font-weight: 500; margin-bottom: 10px;">系统会从以下图片中随机选择：</p>
                        <div style="font-family: monospace; font-size: 14px; color: #4a5568; line-height: 1.8;">
                            bg1.png, bg2.png, bg3.png, bg4.png, bg5.png<br>
                            bg6.png, bg7.png, bg8.png, bg9.png, <span style="background: #ffd700; padding: 2px 4px; border-radius: 3px; color: #000;">bg_chinese.png</span>
                        </div>
                        <p style="color: #667eea; font-size: 14px; margin-top: 15px;">
                            🎯 你的中文字符背景图 <strong>bg_chinese.png</strong> 已加入随机选择列表
                        </p>
                    </div>
                </div>
            `;
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('experiment-status').innerHTML = `
                <strong>🚀 准备开始实验</strong><br>
                <small>请确保已将中文背景图保存为 bg_chinese.png 并放置在正确目录</small>
            `;
        });
    </script>
</body>
</html>