#!/bin/bash

# Test captcha API endpoint directly

echo "Testing captcha GET endpoint..."
echo "=============================="

# Test with curl
response=$(curl -s -X GET http://localhost:8181/api/captcha/get -H "Accept: application/json")
echo "Response: $response"

# Check if response contains error
if echo "$response" | grep -q '"success":false'; then
    echo "ERROR: Captcha request failed"
    echo "Checking backend logs..."
    
    # Try to get more info from Docker logs
    docker-compose logs backend | tail -20
else
    echo "SUCCESS: Captcha generated"
    # Extract token
    token=$(echo "$response" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
    echo "Token: $token"
fi