#!/bin/bash

# 滑动验证码功能测试脚本
# 版本: v1.0
# 日期: 2025-06-23

echo "🔐 滑动验证码功能测试脚本"
echo "=================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查服务状态
check_service() {
    echo -e "${BLUE}📡 检查后端服务状态...${NC}"
    
    # 检查8181端口是否开放
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8181/actuator/health | grep -q "200"; then
        echo -e "${GREEN}✅ 后端服务运行正常 (端口8181)${NC}"
        return 0
    else
        echo -e "${RED}❌ 后端服务未运行或不可达${NC}"
        echo -e "${YELLOW}💡 请先启动后端服务: cd backend && ./mvnw spring-boot:run${NC}"
        return 1
    fi
}

# 测试验证码生成接口
test_captcha_generation() {
    echo -e "${BLUE}🎨 测试验证码生成接口...${NC}"
    
    RESPONSE=$(curl -s -X GET "http://localhost:8181/api/captcha/get" \
        -H "Accept: application/json")
    
    if echo "$RESPONSE" | jq -e '.success == true' >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 验证码生成成功${NC}"
        
        # 提取关键信息
        TOKEN=$(echo "$RESPONSE" | jq -r '.data.token')
        SECRET_KEY=$(echo "$RESPONSE" | jq -r '.data.secretKey')
        
        echo -e "${BLUE}📝 Token: ${TOKEN:0:20}...${NC}"
        echo -e "${BLUE}🔑 SecretKey: ${SECRET_KEY:0:20}...${NC}"
        
        # 检查Base64图片数据
        BG_IMAGE_SIZE=$(echo "$RESPONSE" | jq -r '.data.originalImageBase64' | wc -c)
        PIECE_IMAGE_SIZE=$(echo "$RESPONSE" | jq -r '.data.jigsawImageBase64' | wc -c)
        
        echo -e "${BLUE}🖼️  背景图片大小: ${BG_IMAGE_SIZE} 字符${NC}"
        echo -e "${BLUE}🧩 拼图块大小: ${PIECE_IMAGE_SIZE} 字符${NC}"
        
        # 保存数据供后续测试使用
        export CAPTCHA_TOKEN="$TOKEN"
        export CAPTCHA_SECRET="$SECRET_KEY"
        
        return 0
    else
        echo -e "${RED}❌ 验证码生成失败${NC}"
        echo -e "${YELLOW}📋 响应内容: $RESPONSE${NC}"
        return 1
    fi
}

# 测试验证码校验接口
test_captcha_verification() {
    echo -e "${BLUE}🔍 测试验证码校验接口...${NC}"
    
    if [ -z "$CAPTCHA_TOKEN" ] || [ -z "$CAPTCHA_SECRET" ]; then
        echo -e "${RED}❌ 缺少验证码数据，请先运行生成测试${NC}"
        return 1
    fi
    
    # 模拟滑动到位置120px
    VERIFY_DATA='{
        "captchaType": "blockPuzzle",
        "token": "'$CAPTCHA_TOKEN'",
        "pointJson": "{\"x\":120,\"y\":5}",
        "verification": "'$CAPTCHA_SECRET'"
    }'
    
    RESPONSE=$(curl -s -X POST "http://localhost:8181/api/captcha/check" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$VERIFY_DATA")
    
    if echo "$RESPONSE" | jq -e '.success == true' >/dev/null 2>&1; then
        RESULT=$(echo "$RESPONSE" | jq -r '.data.result')
        MESSAGE=$(echo "$RESPONSE" | jq -r '.data.message')
        
        if [ "$RESULT" = "true" ]; then
            echo -e "${GREEN}✅ 验证码校验成功: $MESSAGE${NC}"
        else
            echo -e "${YELLOW}⚠️  验证码校验失败: $MESSAGE${NC}"
            echo -e "${BLUE}💡 这是正常的，因为滑动位置可能不匹配${NC}"
        fi
        return 0
    else
        echo -e "${RED}❌ 验证码校验接口异常${NC}"
        echo -e "${YELLOW}📋 响应内容: $RESPONSE${NC}"
        return 1
    fi
}

# 测试前端组件
test_frontend_components() {
    echo -e "${BLUE}🎯 检查前端组件...${NC}"
    
    # 检查uni-app组件
    if [ -f "/Volumes/acasis/Assessment/frontend/uni-app/src/components/AjCaptcha/index.vue" ]; then
        echo -e "${GREEN}✅ uni-app验证码组件存在${NC}"
    else
        echo -e "${RED}❌ uni-app验证码组件缺失${NC}"
    fi
    
    # 检查Vue3管理后台组件
    if [ -f "/Volumes/acasis/Assessment/frontend/admin/src/components/AjCaptcha.vue" ]; then
        echo -e "${GREEN}✅ Vue3管理后台验证码组件存在${NC}"
    else
        echo -e "${RED}❌ Vue3管理后台验证码组件缺失${NC}"
    fi
    
    # 检查API接口文件
    if [ -f "/Volumes/acasis/Assessment/frontend/uni-app/src/api/captcha.js" ]; then
        echo -e "${GREEN}✅ uni-app验证码API存在${NC}"
    else
        echo -e "${RED}❌ uni-app验证码API缺失${NC}"
    fi
    
    if [ -f "/Volumes/acasis/Assessment/frontend/admin/src/api/captcha.js" ]; then
        echo -e "${GREEN}✅ Vue3验证码API存在${NC}"
    else
        echo -e "${RED}❌ Vue3验证码API缺失${NC}"
    fi
}

# 检查Redis连接
test_redis_connection() {
    echo -e "${BLUE}📊 检查Redis连接...${NC}"
    
    # 检查Redis是否运行
    if redis-cli ping >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis连接正常${NC}"
        
        # 检查验证码缓存
        CAPTCHA_KEYS=$(redis-cli keys "simple_captcha:*" 2>/dev/null | wc -l)
        echo -e "${BLUE}🔑 当前验证码缓存数量: $CAPTCHA_KEYS${NC}"
        
        return 0
    else
        echo -e "${YELLOW}⚠️  Redis连接失败或未启动${NC}"
        echo -e "${BLUE}💡 验证码将使用内存缓存 (重启后丢失)${NC}"
        return 1
    fi
}

# 生成测试报告
generate_report() {
    echo ""
    echo "🧪 验证码功能测试报告"
    echo "=================================="
    
    # 统计测试结果
    TOTAL_TESTS=5
    PASSED_TESTS=0
    
    echo "📋 功能检查清单:"
    
    # 后端服务
    if check_service >/dev/null 2>&1; then
        echo -e "  ${GREEN}✅ 后端服务运行${NC}"
        ((PASSED_TESTS++))
    else
        echo -e "  ${RED}❌ 后端服务异常${NC}"
    fi
    
    # 验证码生成
    if test_captcha_generation >/dev/null 2>&1; then
        echo -e "  ${GREEN}✅ 验证码生成${NC}"
        ((PASSED_TESTS++))
    else
        echo -e "  ${RED}❌ 验证码生成失败${NC}"
    fi
    
    # 验证码校验
    if test_captcha_verification >/dev/null 2>&1; then
        echo -e "  ${GREEN}✅ 验证码校验${NC}"
        ((PASSED_TESTS++))
    else
        echo -e "  ${RED}❌ 验证码校验失败${NC}"
    fi
    
    # 前端组件
    if test_frontend_components >/dev/null 2>&1; then
        echo -e "  ${GREEN}✅ 前端组件完整${NC}"
        ((PASSED_TESTS++))
    else
        echo -e "  ${RED}❌ 前端组件缺失${NC}"
    fi
    
    # Redis连接
    if test_redis_connection >/dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Redis缓存${NC}"
        ((PASSED_TESTS++))
    else
        echo -e "  ${YELLOW}⚠️  Redis缓存 (可选)${NC}"
        ((PASSED_TESTS++))  # Redis是可选的，不影响总分
    fi
    
    echo ""
    echo -e "${BLUE}📊 测试评分: ${PASSED_TESTS}/${TOTAL_TESTS}${NC}"
    
    if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
        echo -e "${GREEN}🎉 验证码功能完全正常！${NC}"
    elif [ $PASSED_TESTS -ge 3 ]; then
        echo -e "${YELLOW}⚠️  验证码功能基本正常，有小问题需要修复${NC}"
    else
        echo -e "${RED}❌ 验证码功能存在严重问题，需要排查${NC}"
    fi
    
    echo ""
    echo "🔗 相关链接:"
    echo "  📖 功能说明文档: docs/滑动验证码功能说明.md"
    echo "  🧪 测试页面: test-captcha.html"
    echo "  📋 API文档: http://localhost:8181/swagger-ui.html"
}

# 主函数
main() {
    echo "开始验证码功能全面测试..."
    echo ""
    
    # 检查依赖工具
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}❌ 缺少curl工具，请先安装${NC}"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}❌ 缺少jq工具，请先安装: brew install jq${NC}"
        exit 1
    fi
    
    # 执行测试
    check_service
    echo ""
    
    test_captcha_generation
    echo ""
    
    test_captcha_verification
    echo ""
    
    test_frontend_components
    echo ""
    
    test_redis_connection
    echo ""
    
    # 生成报告
    generate_report
}

# 执行主函数
main "$@"