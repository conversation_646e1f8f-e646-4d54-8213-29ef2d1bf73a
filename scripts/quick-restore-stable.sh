#!/bin/bash
# ========================================
# 智能评估平台 - 快速恢复到稳定状态脚本
# 基于2025-06-25的100%登录成功状态
# ========================================

# 配置变量
DB_HOST="localhost"
DB_PORT="5433"
DB_USER="assessment_user"
DB_NAME="assessment_multitenant"
DB_PASSWORD="assessment123"
BACKUP_DIR="database-backups/stable-state-2025-06-25"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}智能评估平台 - 快速恢复到稳定状态${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 函数：检查数据库连接
check_database_connection() {
    echo -e "${YELLOW}检查数据库连接...${NC}"
    
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "SELECT 1;" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 数据库连接正常${NC}"
        return 0
    else
        echo -e "${RED}✗ 数据库连接失败${NC}"
        echo "请检查："
        echo "1. PostgreSQL是否运行: docker ps | grep postgres"
        echo "2. 连接参数是否正确: $DB_HOST:$DB_PORT"
        echo "3. 用户密码是否正确"
        return 1
    fi
}

# 函数：检查备份文件
check_backup_files() {
    echo -e "${YELLOW}检查备份文件...${NC}"
    
    local missing_files=0
    
    if [ ! -f "$BACKUP_DIR/assessment_multitenant_stable_full.backup" ]; then
        echo -e "${RED}✗ 缺失完整备份文件${NC}"
        missing_files=$((missing_files + 1))
    else
        echo -e "${GREEN}✓ 完整备份文件存在${NC}"
    fi
    
    if [ ! -f "$BACKUP_DIR/assessment_multitenant_stable_complete.sql" ]; then
        echo -e "${RED}✗ 缺失SQL备份文件${NC}"
        missing_files=$((missing_files + 1))
    else
        echo -e "${GREEN}✓ SQL备份文件存在${NC}"
    fi
    
    if [ $missing_files -gt 0 ]; then
        echo -e "${RED}错误: 缺失备份文件，请先运行备份脚本${NC}"
        return 1
    fi
    
    return 0
}

# 函数：选择恢复方式
select_restore_method() {
    echo ""
    echo -e "${YELLOW}请选择恢复方式:${NC}"
    echo "1) 快速恢复 (使用二进制备份，推荐)"
    echo "2) SQL恢复 (使用SQL文件)"
    echo "3) 仅执行迁移脚本 (适用于空数据库)"
    echo "4) 退出"
    echo ""
    
    read -p "请输入选择 (1-4): " choice
    echo ""
    
    case $choice in
        1)
            restore_from_binary
            ;;
        2)
            restore_from_sql
            ;;
        3)
            run_migrations_only
            ;;
        4)
            echo "退出恢复操作"
            exit 0
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            select_restore_method
            ;;
    esac
}

# 函数：从二进制备份恢复
restore_from_binary() {
    echo -e "${YELLOW}开始从二进制备份恢复...${NC}"
    
    # 1. 删除现有数据库（如果存在）
    echo "步骤1: 清理现有数据库..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null
    
    # 2. 创建新数据库
    echo "步骤2: 创建数据库..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME;" 2>/dev/null
    
    # 3. 恢复数据
    echo "步骤3: 恢复数据..."
    if PGPASSWORD=$DB_PASSWORD pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -v "$BACKUP_DIR/assessment_multitenant_stable_full.backup" 2>/dev/null; then
        echo -e "${GREEN}✓ 二进制备份恢复成功${NC}"
        verify_restoration
    else
        echo -e "${RED}✗ 二进制备份恢复失败${NC}"
        return 1
    fi
}

# 函数：从SQL备份恢复
restore_from_sql() {
    echo -e "${YELLOW}开始从SQL备份恢复...${NC}"
    
    # 1. 删除现有数据库
    echo "步骤1: 清理现有数据库..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null
    
    # 2. 创建新数据库
    echo "步骤2: 创建数据库..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME;" 2>/dev/null
    
    # 3. 恢复数据
    echo "步骤3: 恢复数据..."
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f "$BACKUP_DIR/assessment_multitenant_stable_complete.sql" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ SQL备份恢复成功${NC}"
        verify_restoration
    else
        echo -e "${RED}✗ SQL备份恢复失败${NC}"
        return 1
    fi
}

# 函数：仅运行迁移脚本
run_migrations_only() {
    echo -e "${YELLOW}开始运行迁移脚本...${NC}"
    
    # 检查数据库是否存在
    if ! PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" >/dev/null 2>&1; then
        echo -e "${RED}数据库不存在，请先创建数据库或选择完整恢复${NC}"
        return 1
    fi
    
    # 运行最新迁移
    echo "运行稳定状态迁移..."
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f "backend/src/main/resources/db/migration/V5__Stable_multitenant_login_system.sql" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 迁移脚本执行成功${NC}"
        verify_restoration
    else
        echo -e "${RED}✗ 迁移脚本执行失败${NC}"
        return 1
    fi
}

# 函数：验证恢复结果
verify_restoration() {
    echo -e "${YELLOW}验证恢复结果...${NC}"
    
    # 检查关键表
    local tables=(
        "tenants"
        "platform_users" 
        "tenant_user_memberships"
    )
    
    for table in "${tables[@]}"; do
        local count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' \n')
        if [ "$count" -gt 0 ]; then
            echo -e "${GREEN}✓ $table: $count 条记录${NC}"
        else
            echo -e "${RED}✗ $table: 无数据${NC}"
        fi
    done
    
    # 检查系统健康状况
    echo ""
    echo -e "${YELLOW}系统健康检查:${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT * FROM system_health_check ORDER BY metric;" 2>/dev/null
    
    echo ""
    echo -e "${GREEN}🎉 数据库恢复完成！${NC}"
    echo ""
    echo -e "${YELLOW}下一步操作:${NC}"
    echo "1. 重启后端应用: cd backend && ./mvnw spring-boot:run"
    echo "2. 运行登录测试: ./scripts/test-current-users.sh"
    echo "3. 访问应用: http://localhost:8181"
    echo ""
    echo -e "${YELLOW}测试账号 (密码: 123456):${NC}"
    echo "• 超级管理员: superadmin@PLATFORM"
    echo "• 平台管理员: admin@PLATFORM"
    echo "• 上海管理员: sh_admin@SH_HQ"
    echo "• 浦东评估师: pd_assessor@SH_PD"
}

# 主函数
main() {
    # 检查必要条件
    if ! check_database_connection; then
        exit 1
    fi
    
    if ! check_backup_files; then
        exit 1
    fi
    
    # 用户确认
    echo -e "${YELLOW}警告: 此操作将替换当前数据库中的所有数据！${NC}"
    read -p "确认继续吗? (y/N): " confirm
    
    if [[ $confirm != [yY] ]]; then
        echo "操作已取消"
        exit 0
    fi
    
    # 选择恢复方式
    select_restore_method
}

# 脚本入口
main "$@"