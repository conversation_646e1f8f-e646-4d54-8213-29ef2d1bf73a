#!/bin/bash

# 删除所有工作流中的钉钉通知步骤

echo "正在删除钉钉通知步骤..."

# 使用sed删除包含DINGTALK_WEBHOOK的通知步骤
for workflow in .github/workflows/*.yml; do
    if [ -f "$workflow" ]; then
        echo "处理: $(basename $workflow)"
        
        # 创建临时文件
        tmp_file="${workflow}.tmp"
        
        # 删除钉钉通知相关的步骤
        awk '
        /- name:.*钉钉通知|发送.*通知.*钉钉/ {
            in_dingtalk = 1
            next
        }
        /- name:/ && in_dingtalk {
            in_dingtalk = 0
        }
        !in_dingtalk {
            print
        }
        ' "$workflow" > "$tmp_file"
        
        # 替换原文件
        mv "$tmp_file" "$workflow"
    fi
done

echo "完成！"