#!/bin/bash

# GitHub Actions Secrets 验证测试脚本
# 用于手动触发和监控测试状态

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 图标定义
CHECKMARK="✅"
WARNING="⚠️"
ERROR="❌"
INFO="ℹ️"
ROCKET="🚀"

echo -e "${BLUE}${ROCKET} GitHub Actions Secrets 验证测试${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 获取仓库信息
get_repo_info() {
    if git remote -v | grep -q "github.com"; then
        REPO_URL=$(git remote get-url origin)
        REPO_PATH=$(echo "$REPO_URL" | sed -E 's|.*github\.com[:/]([^/]+/[^/]+)\.git.*|\1|')
        echo -e "${GREEN}${CHECKMARK} 检测到 GitHub 仓库: ${REPO_PATH}${NC}"
    else
        echo -e "${RED}${ERROR} 未检测到 GitHub 仓库${NC}"
        exit 1
    fi
    echo ""
}

# 检查 Git 状态
check_git_status() {
    echo -e "${BLUE}${INFO} 检查 Git 状态...${NC}"
    
    if git diff --quiet && git diff --staged --quiet; then
        echo -e "${GREEN}${CHECKMARK} 工作目录干净，没有未提交的更改${NC}"
    else
        echo -e "${YELLOW}${WARNING} 检测到未提交的更改${NC}"
        echo "建议先提交更改后再运行测试"
    fi
    
    # 检查是否有推送到远程
    LOCAL_COMMIT=$(git rev-parse HEAD)
    REMOTE_COMMIT=$(git rev-parse origin/$(git branch --show-current) 2>/dev/null || echo "none")
    
    if [ "$LOCAL_COMMIT" = "$REMOTE_COMMIT" ]; then
        echo -e "${GREEN}${CHECKMARK} 本地代码已同步到远程仓库${NC}"
    else
        echo -e "${YELLOW}${WARNING} 本地代码未推送到远程${NC}"
        echo "运行: git push 来同步代码"
    fi
    echo ""
}

# 显示 GitHub Actions 链接
show_github_actions_links() {
    echo -e "${BLUE}${INFO} GitHub Actions 工作流链接:${NC}"
    echo ""
    
    local base_url="https://github.com/${REPO_PATH}/actions"
    
    echo -e "${GREEN}📋 可用的工作流:${NC}"
    echo "1. Secrets 配置验证: ${base_url}/workflows/test-secrets.yml"
    echo "2. CI/CD 主流水线: ${base_url}/workflows/ci-cd.yml"
    echo "3. 覆盖率监控: ${base_url}/workflows/coverage-monitoring.yml"
    echo "4. 安全扫描: ${base_url}/workflows/security-scan.yml"
    echo "5. 依赖审查: ${base_url}/workflows/dependency-review.yml"
    echo ""
    
    echo -e "${YELLOW}🎯 重点测试工作流:${NC}"
    echo "➤ Secrets 验证测试: ${base_url}/workflows/test-secrets.yml"
    echo ""
}

# 生成手动触发指令
generate_trigger_instructions() {
    echo -e "${BLUE}${INFO} 手动触发测试步骤:${NC}"
    echo ""
    
    echo -e "${GREEN}方法 1: 通过 GitHub Web 界面${NC}"
    echo "1. 访问: https://github.com/${REPO_PATH}/actions/workflows/test-secrets.yml"
    echo "2. 点击 'Run workflow' 按钮"
    echo "3. 选择分支: $(git branch --show-current)"
    echo "4. 勾选 '测试通知发送' (可选)"
    echo "5. 点击 'Run workflow' 确认"
    echo ""
    
    if command -v gh &> /dev/null; then
        echo -e "${GREEN}方法 2: 使用 GitHub CLI${NC}"
        echo "gh workflow run test-secrets.yml --repo ${REPO_PATH}"
        echo ""
    else
        echo -e "${YELLOW}${WARNING} GitHub CLI 未安装，仅可使用 Web 界面${NC}"
        echo "安装 GitHub CLI: brew install gh (macOS) 或访问 https://cli.github.com/"
        echo ""
    fi
    
    echo -e "${GREEN}方法 3: 推送代码自动触发${NC}"
    echo "某些工作流会在推送代码时自动触发"
    echo ""
}

# 检查常见 Secrets 配置
check_secrets_locally() {
    echo -e "${BLUE}${INFO} 检查 Secrets 配置指南...${NC}"
    
    local secrets_guide=".github/SECRETS_CONFIG_GUIDE.md"
    local secrets_summary=".github/SECRETS_SETUP_SUMMARY.md"
    
    if [ -f "$secrets_guide" ]; then
        echo -e "${GREEN}${CHECKMARK} Secrets 配置指南已就绪${NC}"
    else
        echo -e "${RED}${ERROR} Secrets 配置指南缺失${NC}"
    fi
    
    if [ -f "$secrets_summary" ]; then
        echo -e "${GREEN}${CHECKMARK} Secrets 配置总结已就绪${NC}"
    else
        echo -e "${RED}${ERROR} Secrets 配置总结缺失${NC}"
    fi
    
    echo ""
    echo -e "${YELLOW}💡 配置提示:${NC}"
    echo "1. 必需配置: CODECOV_TOKEN (优先级最高)"
    echo "2. 推荐配置: DINGTALK_WEBHOOK, DOCKER_USERNAME, DOCKER_PASSWORD"
    echo "3. 可选配置: 服务器部署相关 SSH 密钥"
    echo ""
}

# 监控测试结果
monitor_test_results() {
    echo -e "${BLUE}${INFO} 测试结果监控指南:${NC}"
    echo ""
    
    echo -e "${GREEN}📊 关注的指标:${NC}"
    echo "• 配置完整性评分 (目标: 90%+)"
    echo "• Codecov Token 验证状态"
    echo "• 通知功能测试结果"
    echo "• Docker 认证验证状态"
    echo ""
    
    echo -e "${GREEN}📋 预期测试结果:${NC}"
    echo "• ✅ Codecov Token 已配置: 必需通过"
    echo "• ✅ DingTalk Webhook 已配置: 推荐通过"
    echo "• ✅ Docker 认证已配置: 推荐通过"
    echo "• 🔵 SSH 部署配置: 可选通过"
    echo ""
    
    echo -e "${YELLOW}⚠️ 如果测试失败:${NC}"
    echo "1. 查看 GitHub Actions 日志详细错误信息"
    echo "2. 参考 .github/SECRETS_CONFIG_GUIDE.md 重新配置"
    echo "3. 运行本地配置脚本: ./scripts/setup-secrets.sh"
    echo "4. 重新触发测试验证"
    echo ""
}

# 显示后续步骤
show_next_steps() {
    echo -e "${BLUE}${ROCKET} 后续步骤建议:${NC}"
    echo ""
    
    echo -e "${GREEN}1. 运行 Secrets 验证测试${NC}"
    echo "   手动触发测试并查看评分结果"
    echo ""
    
    echo -e "${GREEN}2. 根据测试结果优化配置${NC}"
    echo "   补充缺失的 Secrets 配置项"
    echo ""
    
    echo -e "${GREEN}3. 验证完整 CI/CD 流水线${NC}"
    echo "   推送代码到 main 分支触发完整流水线"
    echo ""
    
    echo -e "${GREEN}4. 监控覆盖率和质量指标${NC}"
    echo "   确保覆盖率监控和告警正常工作"
    echo ""
}

# 主函数
main() {
    get_repo_info
    check_git_status
    show_github_actions_links
    generate_trigger_instructions
    check_secrets_locally
    monitor_test_results
    show_next_steps
    
    echo -e "${GREEN}🎉 准备就绪！现在可以手动触发 GitHub Actions 验证测试${NC}"
    echo -e "${BLUE}访问: https://github.com/${REPO_PATH}/actions/workflows/test-secrets.yml${NC}"
}

# 运行主函数
main "$@"