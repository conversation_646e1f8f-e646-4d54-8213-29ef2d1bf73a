-- ========================================
-- 修复订阅计划枚举值匹配问题
-- 将PROFESSIONAL改为STANDARD（标准版）
-- 执行时间: 2025-06-25
-- ========================================

-- 1. 查看当前的订阅计划分布
SELECT '修改前订阅计划分布' as operation, subscription_plan, COUNT(*) as count
FROM tenants
GROUP BY subscription_plan
ORDER BY subscription_plan;

-- 2. 将PROFESSIONAL改为STANDARD
-- PROFESSIONAL在业务含义上最接近STANDARD（标准版）
UPDATE tenants 
SET subscription_plan = 'STANDARD' 
WHERE subscription_plan = 'PROFESSIONAL';

-- 3. 查看修改后的订阅计划分布
SELECT '修改后订阅计划分布' as operation, subscription_plan, COUNT(*) as count
FROM tenants
GROUP BY subscription_plan
ORDER BY subscription_plan;

-- 4. 验证所有租户的订阅计划都是有效值
SELECT 
    code,
    name,
    subscription_plan,
    subscription_status,
    status
FROM tenants
WHERE subscription_plan NOT IN ('BASIC', 'STANDARD', 'PREMIUM', 'ENTERPRISE')
ORDER BY code;

-- 5. 显示所有准备测试的用户（应该没有错误了）
SELECT 
    pu.username,
    t.code as tenant_code,
    t.name as tenant_name,
    t.subscription_plan,
    tum.tenant_role
FROM platform_users pu
JOIN tenant_user_memberships tum ON pu.id = tum.user_id
JOIN tenants t ON tum.tenant_id = t.id
WHERE tum.status = 'ACTIVE' 
    AND pu.is_active = true
    AND t.status = 'ACTIVE'
ORDER BY t.subscription_plan, pu.username;

-- 6. 统计修复后的数据
SELECT 
    '总租户数' as metric,
    COUNT(*) as count
FROM tenants
WHERE status = 'ACTIVE'
UNION ALL
SELECT 
    'BASIC级别租户',
    COUNT(*)
FROM tenants
WHERE subscription_plan = 'BASIC' AND status = 'ACTIVE'
UNION ALL
SELECT 
    'STANDARD级别租户',
    COUNT(*)
FROM tenants
WHERE subscription_plan = 'STANDARD' AND status = 'ACTIVE'
UNION ALL
SELECT 
    'ENTERPRISE级别租户',
    COUNT(*)
FROM tenants
WHERE subscription_plan = 'ENTERPRISE' AND status = 'ACTIVE';