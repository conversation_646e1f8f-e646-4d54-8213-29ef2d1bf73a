-- ========================================
-- 智能评估平台 - 完整演示数据初始化脚本
-- 基于测试账号和机构数据指南.md
-- 创建日期: 2025-06-25
-- ========================================

-- 清理现有数据
TRUNCATE TABLE tenant_user_memberships CASCADE;
TRUNCATE TABLE tenants CASCADE;
TRUNCATE TABLE platform_users CASCADE;

-- ========================================
-- 1. 创建租户（机构）- 按照测试指南
-- ========================================

-- 1. 省级总部机构（一级）
INSERT INTO tenants (id, code, name, industry, subscription_plan, status, created_at) VALUES
('10000001-0000-0000-0000-000000000001', 'SH_HQ', '上海长护评估管理中心', '政府机构', 'ENTERPRISE', 'active', CURRENT_TIMESTAMP),
('10000002-0000-0000-0000-000000000001', 'HN_HQ', '海南健康评估总部', '政府机构', 'ENTERPRISE', 'active', CURRENT_TIMESTAMP),
('10000003-0000-0000-0000-000000000001', 'HB_HQ', '湖北省护理评估中心', '政府机构', 'ENTERPRISE', 'active', CURRENT_TIMESTAMP);

-- 2. 市级分支机构（二级）
INSERT INTO tenants (id, code, name, industry, subscription_plan, status, created_at) VALUES
-- 上海市下属机构
('20000001-0000-0000-0000-000000000001', 'SH_PD', '浦东新区评估中心', '政府机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('20000002-0000-0000-0000-000000000001', 'SH_XH', '徐汇区康复评估中心', '政府机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('20000003-0000-0000-0000-000000000001', 'SH_JA', '静安区养老评估站', '政府机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
-- 海南省下属机构
('20000004-0000-0000-0000-000000000001', 'HN_HK', '海口市评估分中心', '政府机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('20000005-0000-0000-0000-000000000001', 'HN_SY', '三亚市健康评估站', '政府机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
-- 湖北省下属机构
('20000006-0000-0000-0000-000000000001', 'HB_WH', '武汉市评估总站', '政府机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('20000007-0000-0000-0000-000000000001', 'HB_YC', '宜昌市评估分中心', '政府机构', 'BASIC', 'active', CURRENT_TIMESTAMP);

-- 3. 区级/街道机构（三级）
INSERT INTO tenants (id, code, name, industry, subscription_plan, status, created_at) VALUES
('30000001-0000-0000-0000-000000000001', 'SH_PD_LJZ', '陆家嘴街道评估点', '政府机构', 'BASIC', 'active', CURRENT_TIMESTAMP),
('30000002-0000-0000-0000-000000000001', 'SH_PD_ZJ', '张江镇评估服务站', '政府机构', 'BASIC', 'active', CURRENT_TIMESTAMP),
('30000003-0000-0000-0000-000000000001', 'SH_XH_XJH', '徐家汇评估服务点', '政府机构', 'BASIC', 'active', CURRENT_TIMESTAMP);

-- 4. 医疗机构
INSERT INTO tenants (id, code, name, industry, subscription_plan, status, created_at) VALUES
('40000001-0000-0000-0000-000000000001', 'HOSP_RJ', '上海瑞金医院', '医疗机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('40000002-0000-0000-0000-000000000001', 'HOSP_HS', '华山医院康复科', '医疗机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('40000003-0000-0000-0000-000000000001', 'HOSP_HNRM', '海南省人民医院', '医疗机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('40000004-0000-0000-0000-000000000001', 'HOSP_TJ', '武汉同济医院', '医疗机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('40000005-0000-0000-0000-000000000001', 'HOSP_SQWS', '社区卫生服务中心', '医疗机构', 'BASIC', 'active', CURRENT_TIMESTAMP);

-- 5. 养老机构
INSERT INTO tenants (id, code, name, industry, subscription_plan, status, created_at) VALUES
('50000001-0000-0000-0000-000000000001', 'CARE_FSK', '上海福寿康养老院', '养老机构', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('50000002-0000-0000-0000-000000000001', 'CARE_CXM', '椿萱茂养老社区', '养老机构', 'ENTERPRISE', 'active', CURRENT_TIMESTAMP),
('50000003-0000-0000-0000-000000000001', 'CARE_XYH', '海南夕阳红养护院', '养老机构', 'BASIC', 'active', CURRENT_TIMESTAMP),
('50000004-0000-0000-0000-000000000001', 'CARE_XFZJ', '武汉幸福之家', '养老机构', 'BASIC', 'active', CURRENT_TIMESTAMP),
('50000005-0000-0000-0000-000000000001', 'CARE_YG', '阳光护理院', '养老机构', 'BASIC', 'active', CURRENT_TIMESTAMP);

-- 6. 保险公司
INSERT INTO tenants (id, code, name, industry, subscription_plan, status, created_at) VALUES
('60000001-0000-0000-0000-000000000001', 'INS_ZGRS', '中国人寿保险', '保险公司', 'ENTERPRISE', 'active', CURRENT_TIMESTAMP),
('*************-0000-0000-000000000001', 'INS_TPYB', '太平洋保险', '保险公司', 'ENTERPRISE', 'active', CURRENT_TIMESTAMP),
('60000003-0000-0000-0000-000000000001', 'INS_TKRS', '泰康人寿', '保险公司', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP),
('60000004-0000-0000-0000-000000000001', 'INS_PAJK', '平安健康险', '保险公司', 'PROFESSIONAL', 'active', CURRENT_TIMESTAMP);

-- ========================================
-- 2. 创建平台用户
-- ========================================

-- 超级管理员（平台级）
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('00000001-0000-0000-0000-000000000001', 'superadmin', '<EMAIL>', '$2a$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36Zf4yXrwfJpRTOgY7Xov1i', 'Super', 'Admin', 'super_admin', true, CURRENT_TIMESTAMP),
('00000002-0000-0000-0000-000000000001', 'admin', '<EMAIL>', '$2a$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36Zf4yXrwfJpRTOgY7Xov1i', 'Platform', 'Admin', 'admin', true, CURRENT_TIMESTAMP);

-- 省级机构管理员
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('10001001-0000-0000-0000-000000000001', 'sh_admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '张', '明华', 'user', true, CURRENT_TIMESTAMP),
('10002001-0000-0000-0000-000000000001', 'hn_admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '李', '海南', 'user', true, CURRENT_TIMESTAMP),
('10003001-0000-0000-0000-000000000001', 'hb_admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '王', '武汉', 'user', true, CURRENT_TIMESTAMP);

-- 市级机构用户
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('20001001-0000-0000-0000-000000000001', 'pd_admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '陈', '浦东', 'user', true, CURRENT_TIMESTAMP),
('20001002-0000-0000-0000-000000000001', 'pd_assessor', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '赵', '评估', 'user', true, CURRENT_TIMESTAMP),
('20004001-0000-0000-0000-000000000001', 'hk_manager', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '刘', '海口', 'user', true, CURRENT_TIMESTAMP),
('20006001-0000-0000-0000-000000000001', 'wh_reviewer', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '周', '审核', 'user', true, CURRENT_TIMESTAMP);

-- 医疗机构用户
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('40001001-0000-0000-0000-000000000001', 'rj_doctor', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '王', '医生', 'user', true, CURRENT_TIMESTAMP),
('40001002-0000-0000-0000-000000000001', 'rj_nurse', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '李', '护士', 'user', true, CURRENT_TIMESTAMP),
('40002001-0000-0000-0000-000000000001', 'hs_admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '张', '院长', 'user', true, CURRENT_TIMESTAMP),
('40004001-0000-0000-0000-000000000001', 'tj_assessor', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '刘', '评估师', 'user', true, CURRENT_TIMESTAMP);

-- 养老机构用户
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('50001001-0000-0000-0000-000000000001', 'fsk_admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '孙', '院长', 'user', true, CURRENT_TIMESTAMP),
('50001002-0000-0000-0000-000000000001', 'fsk_nurse', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '钱', '护理', 'user', true, CURRENT_TIMESTAMP),
('50002001-0000-0000-0000-000000000001', 'cxm_manager', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '吴', '经理', 'user', true, CURRENT_TIMESTAMP),
('50003001-0000-0000-0000-000000000001', 'xyh_assessor', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '郑', '评估', 'user', true, CURRENT_TIMESTAMP);

-- 保险公司用户
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('60001001-0000-0000-0000-000000000001', 'zgrs_manager', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '冯', '经理', 'user', true, CURRENT_TIMESTAMP),
('60001002-0000-0000-0000-000000000001', 'zgrs_auditor', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '陈', '审核', 'user', true, CURRENT_TIMESTAMP),
('60002001-0000-0000-0000-000000000001', 'tpy_viewer', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '杨', '查看', 'user', true, CURRENT_TIMESTAMP);

-- ========================================
-- 3. 创建租户用户关系
-- ========================================

-- 省级机构管理员
INSERT INTO tenant_user_memberships (user_id, tenant_id, tenant_role, display_name, department, status) VALUES
('10001001-0000-0000-0000-000000000001', '10000001-0000-0000-0000-000000000001', 'ADMIN', '张明华', '管理中心', 'active'),
('10002001-0000-0000-0000-000000000001', '10000002-0000-0000-0000-000000000001', 'ADMIN', '李海南', '管理中心', 'active'),
('10003001-0000-0000-0000-000000000001', '10000003-0000-0000-0000-000000000001', 'ADMIN', '王武汉', '管理中心', 'active');

-- 市级机构用户
INSERT INTO tenant_user_memberships (user_id, tenant_id, tenant_role, display_name, department, status) VALUES
('20001001-0000-0000-0000-000000000001', '20000001-0000-0000-0000-000000000001', 'ADMIN', '陈浦东', '评估中心', 'active'),
('20001002-0000-0000-0000-000000000001', '20000001-0000-0000-0000-000000000001', 'ASSESSOR', '赵评估', '评估科', 'active'),
('20004001-0000-0000-0000-000000000001', '20000004-0000-0000-0000-000000000001', 'ADMIN', '刘海口', '评估中心', 'active'),
('20006001-0000-0000-0000-000000000001', '20000006-0000-0000-0000-000000000001', 'REVIEWER', '周审核', '审核科', 'active');

-- 医疗机构用户
INSERT INTO tenant_user_memberships (user_id, tenant_id, tenant_role, display_name, department, status) VALUES
('40001001-0000-0000-0000-000000000001', '40000001-0000-0000-0000-000000000001', 'ASSESSOR', '王医生', '康复科', 'active'),
('40001002-0000-0000-0000-000000000001', '40000001-0000-0000-0000-000000000001', 'ASSESSOR', '李护士', '护理部', 'active'),
('40002001-0000-0000-0000-000000000001', '40000002-0000-0000-0000-000000000001', 'ADMIN', '张院长', '院办', 'active'),
('40004001-0000-0000-0000-000000000001', '40000004-0000-0000-0000-000000000001', 'ASSESSOR', '刘评估师', '评估科', 'active');

-- 养老机构用户
INSERT INTO tenant_user_memberships (user_id, tenant_id, tenant_role, display_name, department, status) VALUES
('50001001-0000-0000-0000-000000000001', '50000001-0000-0000-0000-000000000001', 'ADMIN', '孙院长', '院办', 'active'),
('50001002-0000-0000-0000-000000000001', '50000001-0000-0000-0000-000000000001', 'ASSESSOR', '钱护理', '护理部', 'active'),
('50002001-0000-0000-0000-000000000001', '50000002-0000-0000-0000-000000000001', 'ADMIN', '吴经理', '运营部', 'active'),
('50003001-0000-0000-0000-000000000001', '50000003-0000-0000-0000-000000000001', 'ASSESSOR', '郑评估', '评估科', 'active');

-- 保险公司用户
INSERT INTO tenant_user_memberships (user_id, tenant_id, tenant_role, display_name, department, status) VALUES
('60001001-0000-0000-0000-000000000001', '60000001-0000-0000-0000-000000000001', 'ADMIN', '冯经理', '长护险部', 'active'),
('60001002-0000-0000-0000-000000000001', '60000001-0000-0000-0000-000000000001', 'REVIEWER', '陈审核', '理赔部', 'active'),
('60002001-0000-0000-0000-000000000001', '*************-0000-0000-000000000001', 'VIEWER', '杨查看', '客服部', 'active');

-- ========================================
-- 4. 备注：租户层级关系功能暂未实现
-- 当前版本暂不创建 tenant_hierarchies 表
-- ========================================

-- 记录初始化完成（暂不使用migrations log）
-- INSERT INTO schema_migrations_log (version, description, executed_at) 
-- VALUES ('DEMO_V1', 'Complete demo data initialization based on test guide', NOW())
-- ON CONFLICT DO NOTHING;

-- 显示创建的租户数量
SELECT 'Tenants created: ' || COUNT(*) as summary FROM tenants;
SELECT 'Platform users created: ' || COUNT(*) as summary FROM platform_users;
SELECT 'User memberships created: ' || COUNT(*) as summary FROM tenant_user_memberships;