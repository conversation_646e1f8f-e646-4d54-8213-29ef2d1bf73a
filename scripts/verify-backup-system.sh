#!/bin/bash
# ========================================
# 备份和恢复系统验证脚本
# 验证备份文件完整性和恢复流程
# ========================================

# 配置变量
DB_HOST="localhost"
DB_PORT="5433"
DB_USER="assessment_user"
DB_NAME="assessment_multitenant"
DB_PASSWORD="assessment123"
BACKUP_DIR="database-backups/stable-state-2025-06-25"
TEST_DB_NAME="assessment_test_restore"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}备份和恢复系统验证${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 验证备份文件完整性
verify_backup_files() {
    echo -e "${YELLOW}1. 验证备份文件完整性...${NC}"
    
    local files=(
        "assessment_multitenant_stable_full.backup"
        "assessment_multitenant_stable_complete.sql"
        "assessment_multitenant_stable_schema.sql"
        "assessment_multitenant_stable_data.sql"
    )
    
    for file in "${files[@]}"; do
        local filepath="$BACKUP_DIR/$file"
        if [ -f "$filepath" ]; then
            local size=$(ls -lh "$filepath" | awk '{print $5}')
            echo -e "${GREEN}✓ $file ($size)${NC}"
        else
            echo -e "${RED}✗ $file (缺失)${NC}"
            return 1
        fi
    done
    
    echo ""
}

# 测试二进制备份恢复
test_binary_restore() {
    echo -e "${YELLOW}2. 测试二进制备份恢复...${NC}"
    
    # 创建测试数据库
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE IF EXISTS $TEST_DB_NAME;" >/dev/null 2>&1
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $TEST_DB_NAME;" >/dev/null 2>&1
    
    # 恢复备份
    if PGPASSWORD=$DB_PASSWORD pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $TEST_DB_NAME "$BACKUP_DIR/assessment_multitenant_stable_full.backup" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 二进制备份恢复成功${NC}"
        
        # 验证数据
        local user_count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $TEST_DB_NAME -t -c "SELECT COUNT(*) FROM platform_users;" 2>/dev/null | tr -d ' \n')
        local tenant_count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $TEST_DB_NAME -t -c "SELECT COUNT(*) FROM tenants;" 2>/dev/null | tr -d ' \n')
        
        echo "  - 用户数量: $user_count"
        echo "  - 租户数量: $tenant_count"
        
        if [ "$user_count" -eq 20 ] && [ "$tenant_count" -eq 28 ]; then
            echo -e "${GREEN}✓ 数据验证通过${NC}"
        else
            echo -e "${RED}✗ 数据验证失败${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ 二进制备份恢复失败${NC}"
        return 1
    fi
    
    # 清理测试数据库
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE $TEST_DB_NAME;" >/dev/null 2>&1
    echo ""
}

# 测试SQL备份恢复
test_sql_restore() {
    echo -e "${YELLOW}3. 测试SQL备份恢复...${NC}"
    
    # 创建测试数据库
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE IF EXISTS $TEST_DB_NAME;" >/dev/null 2>&1
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $TEST_DB_NAME;" >/dev/null 2>&1
    
    # 恢复备份
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $TEST_DB_NAME -f "$BACKUP_DIR/assessment_multitenant_stable_complete.sql" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ SQL备份恢复成功${NC}"
        
        # 验证数据
        local user_count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $TEST_DB_NAME -t -c "SELECT COUNT(*) FROM platform_users;" 2>/dev/null | tr -d ' \n')
        local tenant_count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $TEST_DB_NAME -t -c "SELECT COUNT(*) FROM tenants;" 2>/dev/null | tr -d ' \n')
        
        echo "  - 用户数量: $user_count"
        echo "  - 租户数量: $tenant_count"
        
        if [ "$user_count" -eq 20 ] && [ "$tenant_count" -eq 28 ]; then
            echo -e "${GREEN}✓ 数据验证通过${NC}"
        else
            echo -e "${RED}✗ 数据验证失败${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ SQL备份恢复失败${NC}"
        return 1
    fi
    
    # 清理测试数据库
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE $TEST_DB_NAME;" >/dev/null 2>&1
    echo ""
}

# 验证快速恢复脚本
verify_restore_script() {
    echo -e "${YELLOW}4. 验证快速恢复脚本...${NC}"
    
    if [ -f "scripts/quick-restore-stable.sh" ]; then
        if [ -x "scripts/quick-restore-stable.sh" ]; then
            echo -e "${GREEN}✓ 快速恢复脚本存在且可执行${NC}"
        else
            echo -e "${YELLOW}⚠ 快速恢复脚本存在但不可执行${NC}"
            chmod +x scripts/quick-restore-stable.sh
            echo -e "${GREEN}✓ 已设置执行权限${NC}"
        fi
    else
        echo -e "${RED}✗ 快速恢复脚本不存在${NC}"
        return 1
    fi
    echo ""
}

# 验证迁移文件
verify_migration_files() {
    echo -e "${YELLOW}5. 验证迁移文件...${NC}"
    
    local migrations=(
        "V4__Fix_schema_for_multitenant_login.sql"
        "V5__Stable_multitenant_login_system.sql"
    )
    
    for migration in "${migrations[@]}"; do
        local filepath="backend/src/main/resources/db/migration/$migration"
        if [ -f "$filepath" ]; then
            echo -e "${GREEN}✓ $migration${NC}"
        else
            echo -e "${RED}✗ $migration (缺失)${NC}"
            return 1
        fi
    done
    echo ""
}

# 验证测试脚本
verify_test_scripts() {
    echo -e "${YELLOW}6. 验证测试脚本...${NC}"
    
    if [ -f "scripts/test-current-users.sh" ]; then
        if [ -x "scripts/test-current-users.sh" ]; then
            echo -e "${GREEN}✓ 用户登录测试脚本可用${NC}"
        else
            chmod +x scripts/test-current-users.sh
            echo -e "${GREEN}✓ 已修复用户登录测试脚本权限${NC}"
        fi
    else
        echo -e "${RED}✗ 用户登录测试脚本不存在${NC}"
        return 1
    fi
    echo ""
}

# 生成验证报告
generate_report() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}验证结果报告${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    echo -e "${GREEN}✅ 备份系统状态: 完全就绪${NC}"
    echo ""
    echo "📊 备份文件统计:"
    echo "  - 完整二进制备份: $(ls -lh $BACKUP_DIR/assessment_multitenant_stable_full.backup | awk '{print $5}')"
    echo "  - 完整SQL备份: $(ls -lh $BACKUP_DIR/assessment_multitenant_stable_complete.sql | awk '{print $5}')"
    echo "  - 结构备份: $(ls -lh $BACKUP_DIR/assessment_multitenant_stable_schema.sql | awk '{print $5}')"
    echo "  - 数据备份: $(ls -lh $BACKUP_DIR/assessment_multitenant_stable_data.sql | awk '{print $5}')"
    echo ""
    
    echo "🔧 可用工具:"
    echo "  - 快速恢复脚本: ./scripts/quick-restore-stable.sh"
    echo "  - 登录测试脚本: ./scripts/test-current-users.sh"
    echo "  - 迁移文件: V4, V5 (完整)"
    echo ""
    
    echo "🎯 验证的功能:"
    echo "  ✓ 备份文件完整性"
    echo "  ✓ 二进制备份恢复"
    echo "  ✓ SQL备份恢复"
    echo "  ✓ 数据一致性验证"
    echo "  ✓ 脚本可执行性"
    echo ""
    
    echo -e "${YELLOW}💡 使用建议:${NC}"
    echo "1. 定期运行此验证脚本确保备份系统正常"
    echo "2. 在重要变更前创建新的备份点"
    echo "3. 测试环境定期验证恢复流程"
    echo ""
}

# 主执行流程
main() {
    local failed=0
    
    verify_backup_files || failed=1
    test_binary_restore || failed=1
    test_sql_restore || failed=1
    verify_restore_script || failed=1
    verify_migration_files || failed=1
    verify_test_scripts || failed=1
    
    if [ $failed -eq 0 ]; then
        generate_report
        echo -e "${GREEN}🎉 所有验证项目通过！${NC}"
        exit 0
    else
        echo -e "${RED}❌ 验证过程中发现问题${NC}"
        exit 1
    fi
}

# 运行验证
main "$@"