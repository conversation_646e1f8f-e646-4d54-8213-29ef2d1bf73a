#!/bin/bash

# GitHub Actions 本地测试脚本
# 使用 act 工具在本地测试工作流

set -e

echo "========================================"
echo "GitHub Actions 本地测试"
echo "========================================"

# 检查是否安装了 act
if ! command -v act &> /dev/null; then
    echo "❌ 未安装 act 工具"
    echo ""
    echo "请安装 act:"
    echo "  macOS: brew install act"
    echo "  Linux: curl https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash"
    echo ""
    exit 1
fi

# 检查 Docker 是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker 未运行"
    echo "请先启动 Docker Desktop"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 创建本地secrets文件（如果不存在）
if [ ! -f .secrets ]; then
    cat > .secrets << 'EOF'
# GitHub Actions 本地测试用 Secrets
# 这些是测试值，不要用于生产环境
GITHUB_TOKEN=fake-github-token
DOCKER_USERNAME=test-user
DOCKER_PASSWORD=test-password
DEV_HOST=localhost
DEV_USERNAME=test
DEV_SSH_KEY=test-key
DEV_BASE_URL=http://localhost:8080
CODECOV_TOKEN=test-codecov-token
EOF
    echo "📝 已创建 .secrets 文件（测试用）"
fi

# 选择要测试的工作流
echo "请选择要测试的工作流:"
echo "1) 代码质量检查 (code-quality)"
echo "2) 安全扫描 (security)"
echo "3) CI/CD 完整流程 (ci-cd)"
echo "4) 测试覆盖率 (ci-test-coverage)"
echo "5) 所有工作流"
read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "🔍 测试代码质量检查工作流..."
        act -W .github/workflows/code-quality.yml \
            --secret-file .secrets \
            --platform ubuntu-latest=catthehacker/ubuntu:act-latest \
            -P ubuntu-latest=catthehacker/ubuntu:act-latest
        ;;
    2)
        echo "🔐 测试安全扫描工作流..."
        act -W .github/workflows/security.yml \
            --secret-file .secrets \
            --platform ubuntu-latest=catthehacker/ubuntu:act-latest \
            -P ubuntu-latest=catthehacker/ubuntu:act-latest
        ;;
    3)
        echo "🚀 测试CI/CD完整流程..."
        act -W .github/workflows/ci-cd.yml \
            --secret-file .secrets \
            --platform ubuntu-latest=catthehacker/ubuntu:act-latest \
            -P ubuntu-latest=catthehacker/ubuntu:act-latest \
            -j code-quality
        ;;
    4)
        echo "📊 测试覆盖率工作流..."
        act -W .github/workflows/ci-test-coverage.yml \
            --secret-file .secrets \
            --platform ubuntu-latest=catthehacker/ubuntu:act-latest \
            -P ubuntu-latest=catthehacker/ubuntu:act-latest
        ;;
    5)
        echo "🔄 测试所有工作流..."
        for workflow in .github/workflows/*.yml; do
            if [ -f "$workflow" ]; then
                echo ""
                echo "测试: $(basename $workflow)"
                echo "----------------------------------------"
                act -W "$workflow" \
                    --secret-file .secrets \
                    --platform ubuntu-latest=catthehacker/ubuntu:act-latest \
                    -P ubuntu-latest=catthehacker/ubuntu:act-latest \
                    --list || true
            fi
        done
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "========================================"
echo "💡 提示:"
echo "========================================"
echo "1. act 工具模拟 GitHub Actions 环境"
echo "2. 某些功能可能在本地无法完全模拟"
echo "3. 使用 --verbose 参数查看详细日志"
echo "4. 使用 --dry-run 参数查看将执行的步骤"
echo ""
echo "更多信息: https://github.com/nektos/act"