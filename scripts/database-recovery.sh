#!/bin/bash

# ========================================
# 智能评估平台 - 数据库恢复脚本
# 创建日期: 2025-06-25
# 用途: 快速恢复数据库到已知正常状态
# ========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库连接配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5433}"
DB_USER="${DB_USER:-assessment_user}"
DB_NAME="${DB_NAME:-assessment_multitenant}"
DB_PASSWORD="${DB_PASSWORD:-assessment123}"
BACKUP_DIR="/Volumes/acasis/Assessment/database-backups"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}智能评估平台 - 数据库恢复工具${NC}"
echo -e "${BLUE}========================================${NC}"

# 函数：检查数据库连接
check_database_connection() {
    echo -e "${YELLOW}🔍 检查数据库连接...${NC}"
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "SELECT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 数据库连接正常${NC}"
        return 0
    else
        echo -e "${RED}❌ 数据库连接失败！请检查PostgreSQL服务是否运行${NC}"
        return 1
    fi
}

# 函数：列出可用备份
list_available_backups() {
    echo -e "${YELLOW}📋 可用的数据库备份：${NC}"
    echo ""
    if [ -d "$BACKUP_DIR" ] && [ "$(ls -A $BACKUP_DIR 2>/dev/null)" ]; then
        ls -lt $BACKUP_DIR/*.backup 2>/dev/null | head -10 | while read line; do
            echo "  $line"
        done
        echo ""
        ls -lt $BACKUP_DIR/*.sql 2>/dev/null | head -5 | while read line; do
            echo "  $line"
        done
    else
        echo -e "${RED}❌ 未找到备份文件！${NC}"
        return 1
    fi
}

# 函数：恢复完整备份
restore_full_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo -e "${RED}❌ 备份文件不存在: $backup_file${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}🔄 开始恢复完整备份...${NC}"
    echo -e "${YELLOW}📁 备份文件: $(basename $backup_file)${NC}"
    
    # 删除现有数据库（如果存在）
    echo -e "${YELLOW}🗑️  删除现有数据库...${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" || true
    
    # 创建新数据库
    echo -e "${YELLOW}🆕 创建新数据库...${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;"
    
    # 恢复备份
    echo -e "${YELLOW}📥 恢复数据...${NC}"
    PGPASSWORD=$DB_PASSWORD pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --verbose --clean --if-exists "$backup_file"
    
    echo -e "${GREEN}✅ 数据库恢复完成！${NC}"
}

# 函数：恢复SQL备份
restore_sql_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo -e "${RED}❌ 备份文件不存在: $backup_file${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}🔄 开始恢复SQL备份...${NC}"
    echo -e "${YELLOW}📁 备份文件: $(basename $backup_file)${NC}"
    
    # 删除现有数据库（如果存在）
    echo -e "${YELLOW}🗑️  删除现有数据库...${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" || true
    
    # 创建新数据库
    echo -e "${YELLOW}🆕 创建新数据库...${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;"
    
    # 恢复备份
    echo -e "${YELLOW}📥 恢复数据...${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f "$backup_file"
    
    echo -e "${GREEN}✅ 数据库恢复完成！${NC}"
}

# 函数：验证恢复结果
verify_restoration() {
    echo -e "${YELLOW}🔍 验证恢复结果...${NC}"
    
    # 检查租户数量
    local tenant_count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM tenants;" 2>/dev/null | xargs)
    echo -e "${BLUE}📊 租户数量: $tenant_count${NC}"
    
    # 检查用户数量
    local user_count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM platform_users;" 2>/dev/null | xargs)
    echo -e "${BLUE}👥 平台用户数量: $user_count${NC}"
    
    # 检查用户关系数量
    local membership_count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM tenant_user_memberships;" 2>/dev/null | xargs)
    echo -e "${BLUE}🔗 用户关系数量: $membership_count${NC}"
    
    # 测试登录接口（如果后端运行中）
    if curl -s http://localhost:8181/actuator/health > /dev/null 2>&1; then
        echo -e "${YELLOW}🧪 测试登录接口...${NC}"
        local login_test=$(curl -s -X POST http://localhost:8181/api/auth/login \
            -H "Content-Type: application/json" \
            -d '{"tenantCode":"SH_HQ","username":"sh_admin","password":"123456"}' | jq -r .accessToken 2>/dev/null)
        
        if [ "$login_test" != "null" ] && [ -n "$login_test" ]; then
            echo -e "${GREEN}✅ 登录接口测试通过${NC}"
        else
            echo -e "${YELLOW}⚠️  登录接口测试失败，请检查后端服务${NC}"
        fi
    else
        echo -e "${YELLOW}ℹ️  后端服务未运行，跳过登录测试${NC}"
    fi
}

# 函数：创建快速恢复
quick_restore() {
    echo -e "${YELLOW}🚀 执行快速恢复（使用最新备份）...${NC}"
    
    # 查找最新的完整备份
    local latest_backup=$(find $BACKUP_DIR -name "*.backup" -type f -printf '%T@ %p\n' 2>/dev/null | sort -nr | head -1 | cut -d' ' -f2-)
    
    if [ -n "$latest_backup" ]; then
        echo -e "${BLUE}📁 使用最新备份: $(basename $latest_backup)${NC}"
        restore_full_backup "$latest_backup"
        verify_restoration
    else
        echo -e "${RED}❌ 未找到完整备份文件${NC}"
        return 1
    fi
}

# 主菜单
show_menu() {
    echo ""
    echo -e "${BLUE}请选择操作：${NC}"
    echo "1. 🔍 检查数据库连接"
    echo "2. 📋 列出可用备份"
    echo "3. 🚀 快速恢复（使用最新备份）"
    echo "4. 📥 恢复指定备份文件"
    echo "5. 🧪 验证当前数据库状态"
    echo "6. 📊 显示数据库统计信息"
    echo "7. 🚪 退出"
    echo ""
    read -p "请输入选项 (1-7): " choice
}

# 主程序
main() {
    while true; do
        show_menu
        
        case $choice in
            1)
                check_database_connection
                ;;
            2)
                list_available_backups
                ;;
            3)
                check_database_connection && quick_restore
                ;;
            4)
                list_available_backups
                echo ""
                read -p "请输入备份文件的完整路径: " backup_path
                if [[ "$backup_path" == *.backup ]]; then
                    check_database_connection && restore_full_backup "$backup_path"
                elif [[ "$backup_path" == *.sql ]]; then
                    check_database_connection && restore_sql_backup "$backup_path"
                else
                    echo -e "${RED}❌ 不支持的文件格式${NC}"
                fi
                ;;
            5)
                check_database_connection && verify_restoration
                ;;
            6)
                if check_database_connection; then
                    echo -e "${YELLOW}📊 数据库统计信息：${NC}"
                    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
                    SELECT 
                        'Tables' as type, COUNT(*) as count 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                    UNION ALL
                    SELECT 
                        'Indexes' as type, COUNT(*) as count 
                    FROM pg_indexes 
                    WHERE schemaname = 'public'
                    UNION ALL
                    SELECT 
                        'Tenants' as type, COUNT(*) as count 
                    FROM tenants
                    UNION ALL
                    SELECT 
                        'Platform Users' as type, COUNT(*) as count 
                    FROM platform_users
                    UNION ALL
                    SELECT 
                        'User Memberships' as type, COUNT(*) as count 
                    FROM tenant_user_memberships;
                    "
                fi
                ;;
            7)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选项，请重新选择${NC}"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 检查依赖
if ! command -v psql &> /dev/null; then
    echo -e "${RED}❌ 未找到 psql 命令，请安装 PostgreSQL 客户端${NC}"
    exit 1
fi

if ! command -v pg_restore &> /dev/null; then
    echo -e "${RED}❌ 未找到 pg_restore 命令，请安装 PostgreSQL 客户端${NC}"
    exit 1
fi

# 运行主程序
if [ $# -eq 0 ]; then
    # 交互模式
    main
else
    # 命令行模式
    case "$1" in
        "quick")
            check_database_connection && quick_restore
            ;;
        "check")
            check_database_connection && verify_restoration
            ;;
        "list")
            list_available_backups
            ;;
        *)
            echo "用法: $0 [quick|check|list]"
            echo "  quick - 快速恢复最新备份"
            echo "  check - 检查数据库状态"
            echo "  list  - 列出可用备份"
            echo "  无参数 - 交互模式"
            ;;
    esac
fi