-- ========================================
-- 完整修复所有枚举值问题
-- 执行时间: 2025-06-25
-- ========================================

-- 1. 修复租户表的subscription_status字段（小写 -> 大写）
UPDATE tenants SET subscription_status = 'ACTIVE' WHERE subscription_status = 'active';
UPDATE tenants SET subscription_status = 'INACTIVE' WHERE subscription_status = 'inactive';
UPDATE tenants SET subscription_status = 'SUSPENDED' WHERE subscription_status = 'suspended';
UPDATE tenants SET subscription_status = 'CANCELLED' WHERE subscription_status = 'cancelled';

-- 如果subscription_status为空，设置默认值
UPDATE tenants SET subscription_status = 'ACTIVE' WHERE subscription_status IS NULL;

-- 2. 显示修复后的数据统计
SELECT 'subscription_status修复' as operation, COUNT(*) as count, subscription_status as value
FROM tenants 
GROUP BY subscription_status
UNION ALL
SELECT 'subscription_plan分布', COUNT(*), subscription_plan
FROM tenants
GROUP BY subscription_plan
UNION ALL
SELECT 'status分布', COUNT(*), status
FROM tenants
GROUP BY status
ORDER BY operation, value;

-- 3. 显示所有租户的关键信息
SELECT 
    code,
    name,
    status,
    subscription_status,
    subscription_plan
FROM tenants
ORDER BY code
LIMIT 10;

-- 4. 再次验证用户登录数据完整性
SELECT 
    'Total Users' as metric,
    COUNT(*) as count
FROM platform_users
WHERE is_active = true
UNION ALL
SELECT 
    'Total Active Memberships',
    COUNT(*)
FROM tenant_user_memberships
WHERE status = 'ACTIVE'
UNION ALL
SELECT 
    'Users with Correct Password',
    COUNT(*)
FROM platform_users
WHERE password_hash = '$2a$10$1qE3wIiB2BNm5V74yRel1OCfq.AAeL6tbmdi3hJnDEY/DkejD4PT2';

-- 5. 显示前10个准备测试的用户
SELECT 
    pu.username,
    t.code as tenant_code,
    t.status as tenant_status,
    t.subscription_status,
    t.subscription_plan,
    tum.tenant_role
FROM platform_users pu
JOIN tenant_user_memberships tum ON pu.id = tum.user_id
JOIN tenants t ON tum.tenant_id = t.id
WHERE tum.status = 'ACTIVE' 
    AND pu.is_active = true
    AND t.status = 'ACTIVE'
ORDER BY pu.username
LIMIT 10;