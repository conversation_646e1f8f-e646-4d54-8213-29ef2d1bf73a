#!/bin/bash

# GitHub Secrets 配置脚本
# 用于设置智能评估平台CI/CD所需的所有Secrets

set -e

echo "========================================"
echo "GitHub Secrets 配置向导"
echo "========================================"

# 检查是否安装了 GitHub CLI
if ! command -v gh &> /dev/null; then
    echo "❌ 错误: 未安装 GitHub CLI (gh)"
    echo "请先安装: https://cli.github.com/"
    exit 1
fi

# 检查是否已登录
if ! gh auth status &> /dev/null; then
    echo "❌ 错误: 未登录 GitHub"
    echo "请运行: gh auth login"
    exit 1
fi

# 获取仓库信息
REPO=$(gh repo view --json nameWithOwner -q .nameWithOwner 2>/dev/null || echo "")
if [ -z "$REPO" ]; then
    echo "❌ 错误: 当前目录不是Git仓库或未关联到GitHub"
    exit 1
fi

echo "✅ 当前仓库: $REPO"
echo ""

# 创建 .env.secrets 模板文件
cat > .env.secrets.template << 'EOF'
# Docker Hub 配置
DOCKER_USERNAME=your-docker-username
DOCKER_PASSWORD=your-docker-password

# 开发环境部署配置
DEV_HOST=dev.example.com
DEV_USERNAME=deploy
DEV_SSH_KEY="-----BEGIN OPENSSH PRIVATE KEY-----
your-dev-ssh-private-key
-----END OPENSSH PRIVATE KEY-----"
DEV_BASE_URL=https://dev.example.com

# 生产环境部署配置
PROD_HOST=prod.example.com
PROD_USERNAME=deploy
PROD_SSH_KEY="-----BEGIN OPENSSH PRIVATE KEY-----
your-prod-ssh-private-key
-----END OPENSSH PRIVATE KEY-----"

# 监控和测试配置
CODECOV_TOKEN=your-codecov-token
SONAR_TOKEN=your-sonarqube-token
K6_CLOUD_TOKEN=your-k6-cloud-token

# 通知配置
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=your-token
EOF

echo "📝 已创建 .env.secrets.template 模板文件"
echo ""

# 交互式配置
echo "请选择配置方式:"
echo "1) 交互式配置（推荐）"
echo "2) 从 .env.secrets 文件读取"
echo "3) 跳过配置"
read -p "请选择 (1-3): " choice

case $choice in
    1)
        # 交互式配置
        echo ""
        echo "=== Docker Hub 配置 ==="
        read -p "Docker Hub 用户名 (可选，回车跳过): " DOCKER_USERNAME
        if [ -n "$DOCKER_USERNAME" ]; then
            read -s -p "Docker Hub 密码: " DOCKER_PASSWORD
            echo ""
            gh secret set DOCKER_USERNAME -b "$DOCKER_USERNAME" -R "$REPO"
            gh secret set DOCKER_PASSWORD -b "$DOCKER_PASSWORD" -R "$REPO"
            echo "✅ Docker Hub 配置完成"
        fi

        echo ""
        echo "=== 开发环境配置 ==="
        read -p "开发服务器地址 (可选，回车跳过): " DEV_HOST
        if [ -n "$DEV_HOST" ]; then
            read -p "开发服务器用户名 [deploy]: " DEV_USERNAME
            DEV_USERNAME=${DEV_USERNAME:-deploy}
            echo "请输入开发服务器SSH私钥（粘贴后按Ctrl+D结束）:"
            DEV_SSH_KEY=$(cat)
            read -p "开发环境基础URL: " DEV_BASE_URL
            
            gh secret set DEV_HOST -b "$DEV_HOST" -R "$REPO"
            gh secret set DEV_USERNAME -b "$DEV_USERNAME" -R "$REPO"
            gh secret set DEV_SSH_KEY -b "$DEV_SSH_KEY" -R "$REPO"
            gh secret set DEV_BASE_URL -b "$DEV_BASE_URL" -R "$REPO"
            echo "✅ 开发环境配置完成"
        fi

        echo ""
        echo "=== 生产环境配置 ==="
        read -p "生产服务器地址 (可选，回车跳过): " PROD_HOST
        if [ -n "$PROD_HOST" ]; then
            read -p "生产服务器用户名 [deploy]: " PROD_USERNAME
            PROD_USERNAME=${PROD_USERNAME:-deploy}
            echo "请输入生产服务器SSH私钥（粘贴后按Ctrl+D结束）:"
            PROD_SSH_KEY=$(cat)
            
            gh secret set PROD_HOST -b "$PROD_HOST" -R "$REPO"
            gh secret set PROD_USERNAME -b "$PROD_USERNAME" -R "$REPO"
            gh secret set PROD_SSH_KEY -b "$PROD_SSH_KEY" -R "$REPO"
            echo "✅ 生产环境配置完成"
        fi

        echo ""
        echo "=== 监控工具配置 ==="
        read -p "Codecov Token (可选，回车跳过): " CODECOV_TOKEN
        if [ -n "$CODECOV_TOKEN" ]; then
            gh secret set CODECOV_TOKEN -b "$CODECOV_TOKEN" -R "$REPO"
            echo "✅ Codecov 配置完成"
        fi

        read -p "SonarQube Token (可选，回车跳过): " SONAR_TOKEN
        if [ -n "$SONAR_TOKEN" ]; then
            gh secret set SONAR_TOKEN -b "$SONAR_TOKEN" -R "$REPO"
            echo "✅ SonarQube 配置完成"
        fi

        read -p "K6 Cloud Token (可选，回车跳过): " K6_CLOUD_TOKEN
        if [ -n "$K6_CLOUD_TOKEN" ]; then
            gh secret set K6_CLOUD_TOKEN -b "$K6_CLOUD_TOKEN" -R "$REPO"
            echo "✅ K6 配置完成"
        fi

        echo ""
        echo "=== 通知配置 ==="
        read -p "钉钉机器人Webhook (可选，回车跳过): " DINGTALK_WEBHOOK
        if [ -n "$DINGTALK_WEBHOOK" ]; then
            gh secret set DINGTALK_WEBHOOK -b "$DINGTALK_WEBHOOK" -R "$REPO"
            echo "✅ 钉钉通知配置完成"
        fi
        ;;
        
    2)
        # 从文件读取
        if [ ! -f .env.secrets ]; then
            echo "❌ 错误: .env.secrets 文件不存在"
            echo "请先复制 .env.secrets.template 并填写配置"
            exit 1
        fi
        
        echo "📖 从 .env.secrets 文件读取配置..."
        source .env.secrets
        
        # 设置所有secrets
        [ -n "$DOCKER_USERNAME" ] && gh secret set DOCKER_USERNAME -b "$DOCKER_USERNAME" -R "$REPO"
        [ -n "$DOCKER_PASSWORD" ] && gh secret set DOCKER_PASSWORD -b "$DOCKER_PASSWORD" -R "$REPO"
        [ -n "$DEV_HOST" ] && gh secret set DEV_HOST -b "$DEV_HOST" -R "$REPO"
        [ -n "$DEV_USERNAME" ] && gh secret set DEV_USERNAME -b "$DEV_USERNAME" -R "$REPO"
        [ -n "$DEV_SSH_KEY" ] && gh secret set DEV_SSH_KEY -b "$DEV_SSH_KEY" -R "$REPO"
        [ -n "$DEV_BASE_URL" ] && gh secret set DEV_BASE_URL -b "$DEV_BASE_URL" -R "$REPO"
        [ -n "$PROD_HOST" ] && gh secret set PROD_HOST -b "$PROD_HOST" -R "$REPO"
        [ -n "$PROD_USERNAME" ] && gh secret set PROD_USERNAME -b "$PROD_USERNAME" -R "$REPO"
        [ -n "$PROD_SSH_KEY" ] && gh secret set PROD_SSH_KEY -b "$PROD_SSH_KEY" -R "$REPO"
        [ -n "$CODECOV_TOKEN" ] && gh secret set CODECOV_TOKEN -b "$CODECOV_TOKEN" -R "$REPO"
        [ -n "$SONAR_TOKEN" ] && gh secret set SONAR_TOKEN -b "$SONAR_TOKEN" -R "$REPO"
        [ -n "$K6_CLOUD_TOKEN" ] && gh secret set K6_CLOUD_TOKEN -b "$K6_CLOUD_TOKEN" -R "$REPO"
        [ -n "$DINGTALK_WEBHOOK" ] && gh secret set DINGTALK_WEBHOOK -b "$DINGTALK_WEBHOOK" -R "$REPO"
        
        echo "✅ 所有配置已完成"
        ;;
        
    3)
        echo "⏭️  跳过配置"
        ;;
        
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "========================================"
echo "📋 当前已配置的 Secrets:"
echo "========================================"
gh secret list -R "$REPO"

echo ""
echo "========================================"
echo "📚 使用说明:"
echo "========================================"
echo "1. 基础CI/CD功能无需所有Secrets即可运行"
echo "2. 部署功能需要配置相应环境的Secrets"
echo "3. 监控工具为可选配置，用于增强功能"
echo "4. 可随时重新运行此脚本更新配置"
echo ""
echo "🔧 测试CI/CD:"
echo "   git push origin develop"
echo ""
echo "🚀 部署到生产:"
echo "   git push origin main"
echo ""
echo "✅ 配置完成！"