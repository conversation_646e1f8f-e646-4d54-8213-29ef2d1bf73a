#!/bin/bash

# GitHub 环境设置脚本
# 用于创建CI/CD所需的部署环境

set -e

echo "========================================"
echo "GitHub 环境配置向导"
echo "========================================"

# 检查是否安装了 GitHub CLI
if ! command -v gh &> /dev/null; then
    echo "❌ 错误: 未安装 GitHub CLI (gh)"
    echo "请先安装: https://cli.github.com/"
    exit 1
fi

# 检查是否已登录
if ! gh auth status &> /dev/null; then
    echo "❌ 错误: 未登录 GitHub"
    echo "请运行: gh auth login"
    exit 1
fi

# 获取仓库信息
REPO=$(gh repo view --json nameWithOwner -q .nameWithOwner 2>/dev/null || echo "")
if [ -z "$REPO" ]; then
    echo "❌ 错误: 当前目录不是Git仓库或未关联到GitHub"
    exit 1
fi

echo "✅ 当前仓库: $REPO"
echo ""

# 创建环境的函数
create_environment() {
    local env_name=$1
    local protection=$2
    
    echo "创建环境: $env_name"
    
    # 使用 GitHub API 创建环境
    gh api \
        --method PUT \
        -H "Accept: application/vnd.github+json" \
        -H "X-GitHub-Api-Version: 2022-11-28" \
        "/repos/$REPO/environments/$env_name" \
        -f name="$env_name" || true
    
    if [ "$protection" = "true" ]; then
        echo "配置环境保护规则..."
        # 配置环境保护规则
        gh api \
            --method PUT \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/$REPO/environments/$env_name/deployment-protection-rules" \
            -F "required_reviewers[]=@${GITHUB_USER:-owner}" \
            -F "deployment_branch_policy.protected_branches=true" \
            -F "deployment_branch_policy.custom_branch_policies=false" \
            2>/dev/null || echo "  (保护规则需要仓库管理员权限)"
    fi
    
    echo "✅ 环境 $env_name 创建成功"
    echo ""
}

# 创建开发环境
echo "1️⃣ 创建开发环境 (development)"
create_environment "development" "false"

# 创建生产环境
echo "2️⃣ 创建生产环境 (production)"
create_environment "production" "true"

# 列出所有环境
echo "========================================"
echo "📋 当前配置的环境:"
echo "========================================"
gh api \
    -H "Accept: application/vnd.github+json" \
    -H "X-GitHub-Api-Version: 2022-11-28" \
    "/repos/$REPO/environments" \
    --jq '.environments[] | "• \(.name)"' || echo "无法获取环境列表"

echo ""
echo "========================================"
echo "✅ 环境配置完成！"
echo "========================================"
echo ""
echo "提示："
echo "1. development 环境用于开发分支的自动部署"
echo "2. production 环境用于主分支的生产部署（需要审批）"
echo "3. 可以在 GitHub 仓库设置中进一步配置环境保护规则"
echo ""
echo "访问以下链接查看和管理环境："
echo "https://github.com/$REPO/settings/environments"