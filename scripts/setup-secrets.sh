#!/bin/bash

# GitHub Secrets 快速配置脚本
# 智能评估平台 CI/CD 配置助手

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 图标定义
CHECKMARK="✅"
WARNING="⚠️"
ERROR="❌"
INFO="ℹ️"
ROCKET="🚀"
LOCK="🔐"

echo -e "${PURPLE}${LOCK} GitHub Secrets 配置助手${NC}"
echo -e "${PURPLE}======================================${NC}"
echo ""

# 检查必要工具
check_dependencies() {
    echo -e "${BLUE}${INFO} 检查依赖工具...${NC}"
    
    local missing_tools=()
    
    if ! command -v curl &> /dev/null; then
        missing_tools+=("curl")
    fi
    
    if ! command -v git &> /dev/null; then
        missing_tools+=("git")
    fi
    
    if ! command -v ssh-keygen &> /dev/null; then
        missing_tools+=("ssh-keygen")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo -e "${RED}${ERROR} 缺少必要工具: ${missing_tools[*]}${NC}"
        echo "请先安装这些工具后重新运行脚本"
        exit 1
    fi
    
    echo -e "${GREEN}${CHECKMARK} 依赖工具检查完成${NC}"
    echo ""
}

# 获取仓库信息
get_repo_info() {
    echo -e "${BLUE}${INFO} 获取仓库信息...${NC}"
    
    if git remote -v | grep -q "github.com"; then
        REPO_URL=$(git remote get-url origin)
        REPO_PATH=$(echo "$REPO_URL" | sed -E 's|.*github\.com[:/]([^/]+/[^/]+)\.git.*|\1|')
        echo -e "${GREEN}${CHECKMARK} 检测到 GitHub 仓库: ${REPO_PATH}${NC}"
    else
        echo -e "${RED}${ERROR} 未检测到 GitHub 仓库${NC}"
        echo "请在 GitHub 仓库目录中运行此脚本"
        exit 1
    fi
    
    echo ""
}

# 生成 SSH 密钥
generate_ssh_keys() {
    echo -e "${BLUE}${INFO} 生成部署 SSH 密钥...${NC}"
    
    SSH_DIR="$HOME/.ssh"
    KEY_NAME="assessment_deploy"
    KEY_PATH="$SSH_DIR/$KEY_NAME"
    
    if [ -f "$KEY_PATH" ]; then
        echo -e "${YELLOW}${WARNING} SSH 密钥已存在: $KEY_PATH${NC}"
        read -p "是否重新生成? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${BLUE}${INFO} 使用现有 SSH 密钥${NC}"
            return
        fi
    fi
    
    mkdir -p "$SSH_DIR"
    chmod 700 "$SSH_DIR"
    
    ssh-keygen -t ed25519 -C "github-actions@assessment" -f "$KEY_PATH" -N ""
    
    echo -e "${GREEN}${CHECKMARK} SSH 密钥生成完成${NC}"
    echo -e "${CYAN}公钥路径: ${KEY_PATH}.pub${NC}"
    echo -e "${CYAN}私钥路径: ${KEY_PATH}${NC}"
    echo ""
}

# 显示配置指引
show_configuration_guide() {
    echo -e "${PURPLE}${ROCKET} 配置指引${NC}"
    echo -e "${PURPLE}============${NC}"
    echo ""
    
    echo -e "${YELLOW}📋 必需配置 (立即设置)${NC}"
    echo ""
    
    echo -e "${CYAN}1. Codecov Token${NC}"
    echo "   访问: https://app.codecov.io/"
    echo "   登录并添加项目: $REPO_PATH"
    echo "   复制 Repository Upload Token"
    echo "   GitHub Secret 名称: CODECOV_TOKEN"
    echo ""
    
    echo -e "${YELLOW}📋 推荐配置 (增强功能)${NC}"
    echo ""
    
    echo -e "${CYAN}2. 钉钉通知机器人${NC}"
    echo "   在钉钉群中添加自定义机器人"
    echo "   复制 Webhook URL"
    echo "   GitHub Secret 名称: DINGTALK_WEBHOOK"
    echo ""
    
    echo -e "${CYAN}3. Docker Hub 认证${NC}"
    echo "   访问: https://hub.docker.com/settings/security"
    echo "   创建访问令牌 (Read, Write, Delete 权限)"
    echo "   GitHub Secret 名称: DOCKER_USERNAME, DOCKER_PASSWORD"
    echo ""
    
    echo -e "${YELLOW}📋 可选配置 (部署功能)${NC}"
    echo ""
    
    echo -e "${CYAN}4. 服务器部署${NC}"
    if [ -f "$HOME/.ssh/assessment_deploy.pub" ]; then
        echo -e "${GREEN}   SSH 公钥 (复制到服务器):${NC}"
        echo "   $(cat $HOME/.ssh/assessment_deploy.pub)"
        echo ""
        echo -e "${GREEN}   SSH 私钥 (添加到 GitHub Secrets):${NC}"
        echo "   Secret 名称: DEV_SSH_KEY 或 PROD_SSH_KEY"
        echo "   Secret 值: (下方私钥内容)"
        echo "   ----------------------------------------"
        cat "$HOME/.ssh/assessment_deploy"
        echo "   ----------------------------------------"
        echo ""
        echo "   其他 Secrets:"
        echo "   DEV_HOST: 开发服务器地址"
        echo "   DEV_USERNAME: SSH 用户名"
        echo "   PROD_HOST: 生产服务器地址"
        echo "   PROD_USERNAME: SSH 用户名"
    else
        echo "   请先运行密钥生成功能"
    fi
    echo ""
}

# 生成配置模板
generate_config_template() {
    echo -e "${BLUE}${INFO} 生成配置模板文件...${NC}"
    
    cat > "github-secrets-template.txt" << EOF
# GitHub Secrets 配置模板
# 智能评估平台 - $REPO_PATH

## 必需配置 (CI/CD 核心功能)
CODECOV_TOKEN=你的_Codecov_Upload_Token

## 推荐配置 (增强功能)  
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=你的token
DOCKER_USERNAME=你的DockerHub用户名
DOCKER_PASSWORD=dckr_pat_你的访问令牌

## 可选配置 (部署功能)
DEV_HOST=dev.assessment.com
DEV_USERNAME=deploy
DEV_SSH_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
...
-----END OPENSSH PRIVATE KEY-----

PROD_HOST=prod.assessment.com  
PROD_USERNAME=deploy
PROD_SSH_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
...
-----END OPENSSH PRIVATE KEY-----

## 扩展服务 (可选)
SONAR_TOKEN=sqp_你的SonarCloud令牌
K6_CLOUD_TOKEN=你的K6Cloud令牌

# 配置步骤:
# 1. 访问 GitHub 仓库 Settings > Secrets and variables > Actions
# 2. 点击 "New repository secret"
# 3. 按照上述格式添加每个 Secret
# 4. 运行测试工作流验证配置: .github/workflows/test-secrets.yml
EOF

    echo -e "${GREEN}${CHECKMARK} 配置模板已生成: github-secrets-template.txt${NC}"
    echo ""
}

# 测试网络连接
test_network() {
    echo -e "${BLUE}${INFO} 测试网络连接...${NC}"
    
    local test_urls=(
        "https://api.github.com"
        "https://codecov.io"
        "https://hub.docker.com"
    )
    
    for url in "${test_urls[@]}"; do
        if curl -s --max-time 5 "$url" > /dev/null; then
            echo -e "${GREEN}${CHECKMARK} $url 连接正常${NC}"
        else
            echo -e "${YELLOW}${WARNING} $url 连接失败${NC}"
        fi
    done
    echo ""
}

# 显示下一步操作
show_next_steps() {
    echo -e "${PURPLE}${ROCKET} 下一步操作${NC}"
    echo -e "${PURPLE}============${NC}"
    echo ""
    
    echo -e "${GREEN}1. 配置 GitHub Secrets${NC}"
    echo "   📖 参考: github-secrets-template.txt"
    echo "   🔗 访问: https://github.com/$REPO_PATH/settings/secrets/actions"
    echo ""
    
    echo -e "${GREEN}2. 运行配置测试${NC}"
    echo "   🔗 访问: https://github.com/$REPO_PATH/actions/workflows/test-secrets.yml"
    echo "   点击 'Run workflow' 按钮"
    echo ""
    
    echo -e "${GREEN}3. 查看详细文档${NC}"
    echo "   📚 配置指南: .github/SECRETS_CONFIG_GUIDE.md"
    echo "   📊 监控仪表板: .github/MONITORING_DASHBOARD.md"
    echo ""
    
    echo -e "${GREEN}4. 启用 CI/CD 流水线${NC}"
    echo "   🚀 推送代码到 main 分支触发完整流水线"
    echo "   📈 查看覆盖率报告和质量监控"
    echo ""
}

# 主菜单
show_menu() {
    echo -e "${CYAN}请选择操作:${NC}"
    echo "1) 生成 SSH 部署密钥"
    echo "2) 显示配置指引"
    echo "3) 生成配置模板文件"
    echo "4) 测试网络连接"
    echo "5) 显示下一步操作"
    echo "6) 全部执行"
    echo "0) 退出"
    echo ""
}

# 主函数
main() {
    check_dependencies
    get_repo_info
    
    while true; do
        show_menu
        read -p "请输入选项 (0-6): " choice
        echo ""
        
        case $choice in
            1)
                generate_ssh_keys
                ;;
            2)
                show_configuration_guide
                ;;
            3)
                generate_config_template
                ;;
            4)
                test_network
                ;;
            5)
                show_next_steps
                ;;
            6)
                echo -e "${BLUE}${INFO} 执行完整配置流程...${NC}"
                echo ""
                generate_ssh_keys
                generate_config_template
                test_network
                show_configuration_guide
                show_next_steps
                break
                ;;
            0)
                echo -e "${GREEN}${CHECKMARK} 配置助手退出${NC}"
                break
                ;;
            *)
                echo -e "${RED}${ERROR} 无效选项，请重新选择${NC}"
                echo ""
                ;;
        esac
        
        read -p "按 Enter 继续..." -r
        echo ""
    done
    
    echo -e "${PURPLE}感谢使用 GitHub Secrets 配置助手！${NC}"
    echo -e "${CYAN}如有问题，请参考 .github/SECRETS_CONFIG_GUIDE.md${NC}"
}

# 运行主函数
main "$@"