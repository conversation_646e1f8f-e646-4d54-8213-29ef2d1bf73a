#!/bin/bash

# CI/CD 监控脚本

echo "========================================"
echo "🔍 CI/CD 运行状态监控"
echo "========================================"
echo ""

# 获取最新的运行状态
echo "最近的工作流运行："
echo ""
gh run list --limit 10

echo ""
echo "========================================"
echo "📊 运行统计："
echo "========================================"

# 统计各状态的数量
SUCCESS_COUNT=$(gh run list --limit 50 --json status | jq '[.[] | select(.status == "completed" and .conclusion == "success")] | length')
FAILURE_COUNT=$(gh run list --limit 50 --json status | jq '[.[] | select(.status == "completed" and .conclusion == "failure")] | length')
IN_PROGRESS=$(gh run list --limit 50 --json status | jq '[.[] | select(.status == "in_progress")] | length')

echo "✅ 成功: $SUCCESS_COUNT"
echo "❌ 失败: $FAILURE_COUNT"
echo "🔄 进行中: $IN_PROGRESS"

echo ""
echo "========================================"
echo "🔧 常见问题快速修复："
echo "========================================"
echo ""
echo "1. Maven Wrapper错误："
echo "   ./scripts/fix-ci-issues.sh"
echo ""
echo "2. 查看失败的日志："
echo "   gh run view [RUN_ID] --log-failed"
echo ""
echo "3. 重新运行失败的工作流："
echo "   gh run rerun [RUN_ID]"
echo ""
echo "4. 查看特定工作流的日志："
echo "   gh run view [RUN_ID] --job [JOB_ID] --log"
echo ""
echo "💡 提示: 使用 'gh run watch' 实时监控正在运行的工作流"