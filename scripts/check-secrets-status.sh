#!/bin/bash

# GitHub Secrets 配置状态检查脚本
# 用于本地预估配置完整性

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🔐 GitHub Secrets 配置状态检查${NC}"
echo -e "${PURPLE}====================================${NC}"
echo ""

# 获取仓库信息
REPO_URL=$(git remote get-url origin 2>/dev/null || echo "unknown")
REPO_PATH=$(echo "$REPO_URL" | sed -E 's|.*github\.com[:/]([^/]+/[^/]+)\.git.*|\1|')

echo -e "${BLUE}📋 仓库信息: ${REPO_PATH}${NC}"
echo -e "${BLUE}📅 检查时间: $(date)${NC}"
echo ""

# 检查必需配置
echo -e "${RED}🚨 必需配置 (60分) - CI/CD 核心功能${NC}"
echo -e "${RED}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# 检查 Codecov Token
echo -n "CODECOV_TOKEN: "
echo -e "${YELLOW}⏳ 需要在 GitHub 中验证${NC}"
echo "   获取方式: https://app.codecov.io/ → 添加项目 → 复制 Token"
echo "   配置位置: GitHub Settings → Secrets → CODECOV_TOKEN"
echo ""

echo -e "${YELLOW}⚠️ 推荐配置 (30分) - 增强功能${NC}"
echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# 检查钉钉 Webhook
echo -n "DINGTALK_WEBHOOK: "
echo -e "${YELLOW}⏳ 需要在 GitHub 中验证${NC}"
echo "   获取方式: 钉钉群 → 群设置 → 智能群助手 → 添加机器人"
echo "   配置位置: GitHub Settings → Secrets → DINGTALK_WEBHOOK"
echo ""

# 检查 Docker 认证
echo -n "DOCKER_USERNAME/PASSWORD: "
echo -e "${YELLOW}⏳ 需要在 GitHub 中验证${NC}"
echo "   获取方式: https://hub.docker.com/settings/security → 创建访问令牌"
echo "   配置位置: GitHub Settings → Secrets → DOCKER_USERNAME, DOCKER_PASSWORD"
echo ""

echo -e "${BLUE}🔵 可选配置 (10分) - 部署功能${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# 检查 SSH 密钥
SSH_KEY_PATH="$HOME/.ssh/assessment_deploy"
if [ -f "$SSH_KEY_PATH" ]; then
    echo -e "SSH 部署密钥: ${GREEN}✅ 已生成${NC}"
    echo "   私钥路径: $SSH_KEY_PATH"
    echo "   公钥路径: ${SSH_KEY_PATH}.pub"
    echo ""
    echo -e "   ${YELLOW}📋 部署配置清单:${NC}"
    echo "   □ DEV_HOST: 开发服务器地址"
    echo "   □ DEV_USERNAME: SSH 用户名"  
    echo "   □ DEV_SSH_KEY: 下方私钥内容"
    echo "   □ PROD_HOST: 生产服务器地址"
    echo "   □ PROD_USERNAME: SSH 用户名"
    echo "   □ PROD_SSH_KEY: 生产环境私钥"
    echo ""
    echo -e "   ${GREEN}🔑 SSH 私钥内容 (复制到 GitHub Secrets):${NC}"
    echo "   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    cat "$SSH_KEY_PATH" | sed 's/^/   /'
    echo "   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo -e "   ${BLUE}📤 SSH 公钥内容 (部署到服务器):${NC}"
    echo "   $(cat ${SSH_KEY_PATH}.pub)"
else
    echo -e "SSH 部署密钥: ${RED}❌ 未生成${NC}"
    echo "   生成方式: ./scripts/setup-secrets.sh → 选择选项 1"
fi
echo ""

# 扩展服务配置
echo -n "扩展服务 (SonarCloud, K6): "
echo -e "${BLUE}🔵 可选配置${NC}"
echo "   SONAR_TOKEN: SonarCloud 代码质量分析"
echo "   K6_CLOUD_TOKEN: K6 性能测试云服务"
echo ""

# 生成配置检查清单
echo -e "${PURPLE}📝 配置检查清单${NC}"
echo -e "${PURPLE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo ""
echo "□ 1. 访问 GitHub 仓库设置"
echo "     https://github.com/${REPO_PATH}/settings/secrets/actions"
echo ""
echo "□ 2. 配置必需 Secrets (必须完成)"
echo "     □ CODECOV_TOKEN"
echo ""
echo "□ 3. 配置推荐 Secrets (强烈建议)"
echo "     □ DINGTALK_WEBHOOK"
echo "     □ DOCKER_USERNAME"
echo "     □ DOCKER_PASSWORD"
echo ""
echo "□ 4. 配置可选 Secrets (按需选择)"
echo "     □ DEV_HOST, DEV_USERNAME, DEV_SSH_KEY"
echo "     □ PROD_HOST, PROD_USERNAME, PROD_SSH_KEY"
echo "     □ SONAR_TOKEN, K6_CLOUD_TOKEN"
echo ""
echo "□ 5. 运行验证测试"
echo "     https://github.com/${REPO_PATH}/actions/workflows/test-secrets.yml"
echo ""

# 显示快速链接
echo -e "${GREEN}🔗 快速配置链接${NC}"
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo ""
echo "📚 详细配置指南:"
echo "   file://${PWD}/.github/SECRETS_CONFIG_GUIDE.md"
echo ""
echo "🌐 Web 配置检查器:"
echo "   file://${PWD}/.github/secrets-checker.html"
echo ""
echo "⚙️ 交互式配置脚本:"
echo "   ${PWD}/scripts/setup-secrets.sh"
echo ""
echo "🧪 GitHub Actions 验证测试:"
echo "   https://github.com/${REPO_PATH}/actions/workflows/test-secrets.yml"
echo ""
echo "⚡ GitHub Secrets 设置页面:"
echo "   https://github.com/${REPO_PATH}/settings/secrets/actions"
echo ""

echo -e "${BLUE}💡 配置提示${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo ""
echo "🎯 优先级顺序:"
echo "   1. CODECOV_TOKEN (必需) - 立即配置"
echo "   2. DINGTALK_WEBHOOK (推荐) - 接收通知"
echo "   3. Docker 认证 (推荐) - 镜像推送"
echo "   4. SSH 部署 (可选) - 自动部署"
echo ""
echo "📊 评分目标:"
echo "   • 90-100分: 🟢 配置优秀"
echo "   • 70-89分: 🟡 配置良好"
echo "   • 50-69分: 🟠 配置基础"
echo "   • 0-49分: 🔴 配置不足"
echo ""

echo -e "${GREEN}🚀 下一步操作${NC}"
echo "1. 完成上述配置清单"
echo "2. 运行 GitHub Actions 验证测试"
echo "3. 查看测试结果和评分"
echo "4. 根据报告补充缺失配置"