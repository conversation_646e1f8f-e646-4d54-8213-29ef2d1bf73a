#!/bin/bash
# 测试登录示例脚本
# 用于快速验证不同类型用户的登录功能

API_BASE="http://localhost:8181/api/unified-auth"

echo "=== 智能评估平台 - 登录测试工具 ==="
echo "测试服务器: $API_BASE"
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_login() {
    local user_type="$1"
    local identifier="$2"  
    local password="$3"
    local login_type="$4"
    local description="$5"
    
    echo -e "${BLUE}测试: $description${NC}"
    echo "标识符: $identifier"
    
    if [ "$login_type" = "INDIVIDUAL" ]; then
        # 个人用户登录
        result=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "{\"loginType\":\"INDIVIDUAL\",\"identifier\":\"$identifier\",\"password\":\"$password\"}" \
            "$API_BASE/login")
    else
        # 机构用户登录 - 需要分离用户名和机构代码
        username=$(echo "$identifier" | cut -d'@' -f1)
        tenant_code=$(echo "$identifier" | cut -d'@' -f2)
        result=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "{\"loginType\":\"INSTITUTIONAL\",\"username\":\"$username\",\"tenantCode\":\"$tenant_code\",\"password\":\"$password\"}" \
            "$API_BASE/login")
    fi
    
    if echo "$result" | grep -q '"accessToken"'; then
        echo -e "${GREEN}✓ 登录成功${NC}"
        # 提取用户信息
        if echo "$result" | grep -q '"displayName"'; then
            display_name=$(echo "$result" | grep -o '"displayName":"[^"]*"' | cut -d'"' -f4)
            tenant_name=$(echo "$result" | grep -o '"tenantName":"[^"]*"' | cut -d'"' -f4)
            tenant_role=$(echo "$result" | grep -o '"tenantRole":"[^"]*"' | cut -d'"' -f4)
            echo "  用户: $display_name"
            echo "  机构: $tenant_name"
            echo "  角色: $tenant_role"
        fi
    else
        echo -e "${RED}✗ 登录失败${NC}"
        if echo "$result" | grep -q '"message"'; then
            error_msg=$(echo "$result" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
            echo "  错误: $error_msg"
        fi
    fi
    echo ""
}

# 测试验证码发送
test_verification_code() {
    echo -e "${BLUE}测试: 邮箱验证码发送${NC}"
    result=$(curl -s -X POST "$API_BASE/send-email-verification?email=<EMAIL>")
    
    if echo "$result" | grep -q '"success":true'; then
        echo -e "${GREEN}✓ 验证码发送成功${NC}"
        if echo "$result" | grep -q '"code"'; then
            code=$(echo "$result" | grep -o '"code":"[^"]*"' | cut -d'"' -f4)
            echo "  验证码: $code"
        fi
    else
        echo -e "${RED}✗ 验证码发送失败${NC}"
    fi
    echo ""
}

# 检查服务状态
check_service() {
    echo -e "${YELLOW}检查服务状态...${NC}"
    health=$(curl -s "http://localhost:8181/actuator/health" 2>/dev/null)
    if echo "$health" | grep -q '"status":"UP"'; then
        echo -e "${GREEN}✓ 服务运行正常${NC}"
    else
        echo -e "${RED}✗ 服务未启动或异常${NC}"
        echo "请确保后端服务已启动: ./mvnw spring-boot:run"
        exit 1
    fi
    echo ""
}

# 主测试流程
main() {
    check_service
    
    echo -e "${YELLOW}=== 机构用户登录测试 ===${NC}"
    
    # 省级管理员
    test_login "机构管理员" "sh_admin@SH_HQ" "Test@123" "INSTITUTIONAL" "上海省级管理员"
    test_login "机构管理员" "hn_admin@HN_HQ" "Test@123" "INSTITUTIONAL" "海南省级管理员"
    
    # 市级用户
    test_login "机构管理员" "pd_admin@SH_PD" "Test@123" "INSTITUTIONAL" "浦东区管理员"
    test_login "评估员" "pd_assessor@SH_PD" "Test@123" "INSTITUTIONAL" "浦东区评估员"
    
    # 医疗机构
    test_login "医生" "rj_doctor@HOSP_RJ" "Test@123" "INSTITUTIONAL" "瑞金医院医生"
    test_login "护士" "rj_nurse@HOSP_RJ" "Test@123" "INSTITUTIONAL" "瑞金医院护士"
    
    # 养老机构
    test_login "院长" "fsk_admin@CARE_FSK" "Test@123" "INSTITUTIONAL" "福寿康院长"
    test_login "护理" "fsk_nurse@CARE_FSK" "Test@123" "INSTITUTIONAL" "福寿康护理主管"
    
    # 保险公司
    test_login "经理" "zgrs_manager@INS_ZGRS" "Test@123" "INSTITUTIONAL" "人寿保险经理"
    test_login "审核员" "zgrs_auditor@INS_ZGRS" "Test@123" "INSTITUTIONAL" "人寿保险审核员"
    
    echo -e "${YELLOW}=== 个人用户登录测试 ===${NC}"
    
    # 个人用户（需要先有数据）
    test_login "个人用户" "<EMAIL>" "User@123" "INDIVIDUAL" "免费版个人用户"
    test_login "个人用户" "<EMAIL>" "123456" "INDIVIDUAL" "已注册个人用户"
    
    echo -e "${YELLOW}=== 验证码功能测试 ===${NC}"
    test_verification_code
    
    echo -e "${YELLOW}=== 错误情况测试 ===${NC}"
    test_login "错误用户" "nonexist@FAKE" "wrong" "INSTITUTIONAL" "不存在的机构用户"
    test_login "错误密码" "sh_admin@SH_HQ" "wrong" "INSTITUTIONAL" "错误密码"
    
    echo -e "${GREEN}=== 测试完成 ===${NC}"
    echo ""
    echo "📋 常用测试账号："
    echo "  机构管理员: sh_admin@SH_HQ / Test@123"
    echo "  机构评估员: pd_assessor@SH_PD / Test@123"
    echo "  医院医生:   rj_doctor@HOSP_RJ / Test@123"
    echo "  养老院长:   fsk_admin@CARE_FSK / Test@123"
    echo "  个人用户:   <EMAIL> / 123456"
    echo ""
    echo "📖 详细文档: docs/测试账号和机构数据指南.md"
}

# 如果直接运行脚本
if [ "$1" = "quick" ]; then
    # 快速测试模式
    check_service
    echo -e "${YELLOW}=== 快速登录测试 ===${NC}"
    test_login "机构管理员" "sh_admin@SH_HQ" "Test@123" "INSTITUTIONAL" "上海管理员"
    test_login "个人用户" "<EMAIL>" "123456" "INDIVIDUAL" "个人用户"
elif [ "$1" = "help" ] || [ "$1" = "-h" ]; then
    echo "用法: $0 [quick|help]"
    echo "  quick  - 快速测试模式（只测试2个关键账号）"
    echo "  help   - 显示帮助信息" 
    echo "  无参数 - 完整测试模式"
else
    main
fi