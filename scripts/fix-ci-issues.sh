#!/bin/bash

echo "========================================"
echo "🔧 修复CI/CD常见问题"
echo "========================================"
echo ""

# 1. 确保Maven Wrapper可执行
echo "1. 设置Maven Wrapper权限..."
chmod +x backend/mvnw
echo "✅ 完成"

# 2. 确保Maven Wrapper JAR存在
echo ""
echo "2. 检查Maven Wrapper JAR..."
if [ ! -f "backend/.mvn/wrapper/maven-wrapper.jar" ]; then
    echo "❌ Maven Wrapper JAR缺失，正在下载..."
    mkdir -p backend/.mvn/wrapper
    curl -o backend/.mvn/wrapper/maven-wrapper.jar \
        https://repo1.maven.org/maven2/org/apache/maven/wrapper/maven-wrapper/3.2.0/maven-wrapper-3.2.0.jar
    echo "✅ 下载完成"
else
    echo "✅ Maven Wrapper JAR已存在"
fi

# 3. 添加到Git
echo ""
echo "3. 添加必要文件到Git..."
git add -f backend/.mvn/wrapper/maven-wrapper.jar
git add backend/.mvn/wrapper/maven-wrapper.properties
echo "✅ 完成"

# 4. 创建简化的CI配置（可选）
echo ""
echo "4. 是否创建简化的CI配置？（只保留基础功能）[y/N]"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    cp .github/workflows/ci-cd.yml .github/workflows/ci-cd.yml.backup
    cat > .github/workflows/ci-basic.yml << 'EOF'
name: Basic CI
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        cache: maven
    
    - name: Run tests
      working-directory: ./backend
      run: |
        chmod +x mvnw
        ./mvnw clean test
      env:
        SPRING_PROFILES_ACTIVE: test
        SPRING_DATASOURCE_URL: ***************************************
        SPRING_DATASOURCE_USERNAME: postgres
        SPRING_DATASOURCE_PASSWORD: testpass
EOF
    echo "✅ 简化配置已创建: .github/workflows/ci-basic.yml"
fi

echo ""
echo "========================================"
echo "📋 下一步操作："
echo "========================================"
echo "1. 提交修复："
echo "   git add ."
echo "   git commit -m 'fix: 添加Maven Wrapper JAR和修复CI配置'"
echo "   git push origin main"
echo ""
echo "2. 查看运行结果："
echo "   gh run list --limit 5"
echo ""
echo "✅ 修复完成！"