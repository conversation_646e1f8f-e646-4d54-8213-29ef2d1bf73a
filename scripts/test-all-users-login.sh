#!/bin/bash
# 全面用户登录测试脚本
# 测试数据库中所有用户的登录功能

API_BASE="http://localhost:8181/api/unified-auth"
AUTH_API="http://localhost:8181/api/auth"

echo "=== 智能评估平台 - 全面用户登录测试 ==="
echo "统一认证API: $API_BASE"
echo "机构认证API: $AUTH_API"
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试计数器
total_tests=0
passed_tests=0
failed_tests=0

# 测试统一登录接口函数
test_unified_login() {
    local user_type="$1"
    local identifier="$2"  
    local password="$3"
    local login_type="$4"
    local description="$5"
    
    total_tests=$((total_tests + 1))
    echo -e "${BLUE}[$total_tests] 测试: $description${NC}"
    echo "标识符: $identifier"
    
    if [ "$login_type" = "INDIVIDUAL" ]; then
        # 个人用户登录
        result=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "{\"loginType\":\"INDIVIDUAL\",\"identifier\":\"$identifier\",\"password\":\"$password\"}" \
            "$API_BASE/login")
    else
        # 机构用户登录 - 需要分离用户名和机构代码
        username=$(echo "$identifier" | cut -d'@' -f1)
        tenant_code=$(echo "$identifier" | cut -d'@' -f2)
        result=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "{\"loginType\":\"INSTITUTIONAL\",\"username\":\"$username\",\"tenantCode\":\"$tenant_code\",\"password\":\"$password\"}" \
            "$API_BASE/login")
    fi
    
    if echo "$result" | grep -q '"accessToken"'; then
        echo -e "${GREEN}✓ 登录成功${NC}"
        passed_tests=$((passed_tests + 1))
        # 提取关键信息
        if echo "$result" | grep -q '"displayName"'; then
            display_name=$(echo "$result" | grep -o '"displayName":"[^"]*"' | cut -d'"' -f4)
            if echo "$result" | grep -q '"tenantName"'; then
                tenant_name=$(echo "$result" | grep -o '"tenantName":"[^"]*"' | cut -d'"' -f4)
                tenant_role=$(echo "$result" | grep -o '"tenantRole":"[^"]*"' | cut -d'"' -f4)
                echo "  用户: $display_name | 机构: $tenant_name | 角色: $tenant_role"
            else
                echo "  用户: $display_name | 个人用户"
            fi
        fi
    else
        echo -e "${RED}✗ 登录失败${NC}"
        failed_tests=$((failed_tests + 1))
        if echo "$result" | grep -q '"message"'; then
            error_msg=$(echo "$result" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
            echo "  错误: $error_msg"
        fi
    fi
    echo ""
}

# 测试传统机构登录接口
test_auth_login() {
    local username="$1"
    local tenant_code="$2"
    local password="$3"
    local description="$4"
    
    total_tests=$((total_tests + 1))
    echo -e "${BLUE}[$total_tests] 测试: $description (传统接口)${NC}"
    echo "用户名: $username | 租户: $tenant_code"
    
    result=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$username\",\"tenantCode\":\"$tenant_code\",\"password\":\"$password\"}" \
        "$AUTH_API/login")
    
    if echo "$result" | grep -q '"accessToken"'; then
        echo -e "${GREEN}✓ 登录成功${NC}"
        passed_tests=$((passed_tests + 1))
    else
        echo -e "${RED}✗ 登录失败${NC}"
        failed_tests=$((failed_tests + 1))
        if echo "$result" | grep -q '"message"'; then
            error_msg=$(echo "$result" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
            echo "  错误: $error_msg"
        fi
    fi
    echo ""
}

# 检查服务状态
check_service() {
    echo -e "${YELLOW}检查服务状态...${NC}"
    health=$(curl -s "http://localhost:8181/actuator/health" 2>/dev/null)
    if echo "$health" | grep -q '"status":"UP"'; then
        echo -e "${GREEN}✓ 服务运行正常${NC}"
    else
        echo -e "${RED}✗ 服务未启动或异常${NC}"
        echo "请确保后端服务已启动: cd backend && ./mvnw spring-boot:run"
        exit 1
    fi
    echo ""
}

# 主测试流程
main() {
    check_service
    
    echo -e "${YELLOW}=== 个人用户登录测试 ===${NC}"
    
    # 测试所有个人用户
    test_unified_login "个人用户" "<EMAIL>" "123456" "INDIVIDUAL" "测试用户(FREE版)"
    test_unified_login "个人用户" "<EMAIL>" "Test@123" "INDIVIDUAL" "验证码测试用户(FREE版)"
    test_unified_login "个人用户" "<EMAIL>" "Test@123" "INDIVIDUAL" "密码测试用户(FREE版)"
    
    echo -e "${YELLOW}=== 机构用户登录测试(统一接口) ===${NC}"
    
    # 上海地区机构用户
    test_unified_login "机构管理员" "zhangminghua.001@SH01MZ" "Test@123" "INSTITUTIONAL" "上海长护评估管理中心-管理员"
    test_unified_login "机构评估员" "zhaopinggu.001@SH02PD" "Test@123" "INSTITUTIONAL" "浦东新区评估中心-评估员"
    test_unified_login "医院医生" "wangyisheng.001@SH02RJ" "Test@123" "INSTITUTIONAL" "上海瑞金医院-医生"
    test_unified_login "养老院长" "sunyuanzhang.001@SH02FS" "Test@123" "INSTITUTIONAL" "上海福寿康养老院-院长"
    test_unified_login "保险经理" "fengjingli.001@SH02ZG" "Test@123" "INSTITUTIONAL" "中国人寿保险-经理"
    
    # 政府机构用户
    test_unified_login "省级管理员" "gov_province_admin.001@gov_province_civil" "Test@123" "INSTITUTIONAL" "省民政厅-管理员"
    
    # 演示机构用户
    test_unified_login "社区管理员" "demo_community_admin.001@demo_community" "Test@123" "INSTITUTIONAL" "演示社区中心-管理员"
    test_unified_login "社区评估员" "demo_community_assessor.001@demo_community" "Test@123" "INSTITUTIONAL" "演示社区中心-评估员"
    test_unified_login "社区审核员" "demo_community_reviewer.001@demo_community" "Test@123" "INSTITUTIONAL" "演示社区中心-审核员"
    
    test_unified_login "医院管理员" "demo_hospital_admin.001@demo_hospital" "Test@123" "INSTITUTIONAL" "演示医院-管理员"
    test_unified_login "医院评估员" "demo_hospital_assessor.001@demo_hospital" "Test@123" "INSTITUTIONAL" "演示医院-评估员"
    test_unified_login "医院审核员" "demo_hospital_reviewer.001@demo_hospital" "Test@123" "INSTITUTIONAL" "演示医院-审核员"
    
    test_unified_login "养老院管理员" "demo_nursing_admin.001@demo_nursing" "Test@123" "INSTITUTIONAL" "演示养老院-管理员"
    test_unified_login "养老院评估员" "demo_nursing_assessor.001@demo_nursing" "Test@123" "INSTITUTIONAL" "演示养老院-评估员"  
    test_unified_login "养老院审核员" "demo_nursing_reviewer.001@demo_nursing" "Test@123" "INSTITUTIONAL" "演示养老院-审核员"
    
    echo -e "${YELLOW}=== 机构用户登录测试(传统接口) ===${NC}"
    
    # 测试几个关键用户的传统登录接口
    test_auth_login "zhangminghua.001" "SH01MZ" "Test@123" "上海长护评估管理中心-管理员"
    test_auth_login "zhaopinggu.001" "SH02PD" "Test@123" "浦东新区评估中心-评估员"
    test_auth_login "wangyisheng.001" "SH02RJ" "Test@123" "上海瑞金医院-医生"
    
    echo -e "${YELLOW}=== 超级管理员登录测试 ===${NC}"
    
    # 超级管理员测试
    test_auth_login "superadmin" "platform" "admin123" "超级管理员(platform)"
    test_auth_login "admin" "SYSTEM" "admin123" "系统管理员(SYSTEM)"
    
    echo -e "${YELLOW}=== 测试结果汇总 ===${NC}"
    echo ""
    echo "总测试数: $total_tests"
    echo -e "成功: ${GREEN}$passed_tests${NC}"
    echo -e "失败: ${RED}$failed_tests${NC}"
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}🎉 所有用户登录测试通过！${NC}"
        exit 0
    else
        echo -e "${RED}⚠️  有 $failed_tests 个登录测试失败${NC}"
        exit 1
    fi
}

# 快速测试模式
if [ "$1" = "quick" ]; then
    check_service
    echo -e "${YELLOW}=== 快速登录测试 ===${NC}"
    test_unified_login "个人用户" "<EMAIL>" "123456" "INDIVIDUAL" "个人用户"
    test_unified_login "机构管理员" "zhangminghua.001@SH01MZ" "Test@123" "INSTITUTIONAL" "上海管理员"
    test_auth_login "zhangminghua.001" "SH01MZ" "Test@123" "上海管理员(传统接口)"
    echo "快速测试完成: $passed_tests/$total_tests 通过"
elif [ "$1" = "help" ] || [ "$1" = "-h" ]; then
    echo "用法: $0 [quick|help]"
    echo "  quick  - 快速测试模式（只测试3个关键账号）"
    echo "  help   - 显示帮助信息" 
    echo "  无参数 - 完整测试模式（测试所有用户）"
else
    main
fi