#!/bin/bash

# GitHub Actions 工作流验证脚本
# 验证所有工作流文件的语法

echo "========================================"
echo "🔍 GitHub Actions 工作流验证"
echo "========================================"
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 错误计数
ERROR_COUNT=0
WARNING_COUNT=0

# 验证工作流文件
for workflow in .github/workflows/*.yml; do
    if [ -f "$workflow" ]; then
        filename=$(basename "$workflow")
        echo -n "检查 $filename ... "
        
        # 使用GitHub CLI验证工作流语法
        if gh workflow view "$filename" &>/dev/null; then
            echo -e "${GREEN}✅ 有效${NC}"
        else
            # 尝试使用yamllint进行基础验证
            if command -v yamllint &> /dev/null; then
                if yamllint -d relaxed "$workflow" &>/dev/null; then
                    echo -e "${YELLOW}⚠️  YAML语法正确，但可能存在GitHub Actions特定问题${NC}"
                    ((WARNING_COUNT++))
                else
                    echo -e "${RED}❌ 无效${NC}"
                    ((ERROR_COUNT++))
                fi
            else
                echo -e "${YELLOW}⚠️  无法验证（需要登录GitHub）${NC}"
                ((WARNING_COUNT++))
            fi
        fi
    fi
done

echo ""
echo "========================================"
echo "📊 验证结果:"
echo "========================================"
echo -e "错误: ${ERROR_COUNT}"
echo -e "警告: ${WARNING_COUNT}"

if [ $ERROR_COUNT -gt 0 ]; then
    echo ""
    echo -e "${RED}❌ 发现错误，请修复后再提交${NC}"
    exit 1
elif [ $WARNING_COUNT -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}⚠️  存在警告，建议检查${NC}"
    echo "提示: 登录GitHub CLI后可以进行完整验证"
    echo "运行: gh auth login"
else
    echo ""
    echo -e "${GREEN}✅ 所有工作流验证通过${NC}"
fi

echo ""
echo "💡 提示:"
echo "1. 完整验证需要登录GitHub CLI"
echo "2. 推送到GitHub后会自动进行完整验证"
echo "3. 查看Actions标签页了解详细错误信息"