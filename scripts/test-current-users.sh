#!/bin/bash
# 测试当前数据库中所有用户的登录功能
# 基于 complete-demo-data.sql 中的最新数据
# 所有用户密码统一为: 123456

API_BASE="http://localhost:8181/api"

echo "=== 智能评估平台 - 当前用户登录测试 ==="
echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 测试计数器
total_tests=0
passed_tests=0
failed_tests=0

# 测试机构用户登录
test_institutional_login() {
    local username="$1"
    local tenant_code="$2"
    local password="$3"
    local description="$4"
    
    total_tests=$((total_tests + 1))
    echo -e "${BLUE}[$total_tests] 测试: $description${NC}"
    echo "  用户名: $username | 机构代码: $tenant_code"
    
    # 使用多租户登录接口
    result=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$username\",\"tenantCode\":\"$tenant_code\",\"password\":\"$password\"}" \
        "$API_BASE/auth/login" 2>/dev/null)
    
    if echo "$result" | grep -q '"accessToken"'; then
        echo -e "  ${GREEN}✓ 登录成功${NC}"
        passed_tests=$((passed_tests + 1))
        
        # 提取返回信息
        display_name=$(echo "$result" | grep -o '"displayName":"[^"]*"' | cut -d'"' -f4)
        tenant_name=$(echo "$result" | grep -o '"tenantName":"[^"]*"' | cut -d'"' -f4)
        tenant_role=$(echo "$result" | grep -o '"tenantRole":"[^"]*"' | cut -d'"' -f4)
        
        echo "  返回信息: 姓名=$display_name, 机构=$tenant_name, 角色=$tenant_role"
    else
        echo -e "  ${RED}✗ 登录失败${NC}"
        failed_tests=$((failed_tests + 1))
        
        # 显示错误信息
        if echo "$result" | grep -q '"message"'; then
            error_msg=$(echo "$result" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
            echo "  错误: $error_msg"
        fi
    fi
    echo ""
}

# 检查服务状态
echo -e "${YELLOW}1. 检查服务状态...${NC}"
health=$(curl -s "http://localhost:8181/actuator/health" 2>/dev/null)
if echo "$health" | grep -q '"status":"UP"'; then
    echo -e "${GREEN}✓ 后端服务运行正常${NC}"
else
    echo -e "${RED}✗ 后端服务未启动${NC}"
    exit 1
fi

# 检查数据库连接
db_health=$(curl -s "http://localhost:8181/actuator/health/db" 2>/dev/null)
if echo "$db_health" | grep -q '"status":"UP"'; then
    echo -e "${GREEN}✓ 数据库连接正常${NC}"
else
    echo -e "${RED}✗ 数据库连接失败${NC}"
fi
echo ""

# 测试超级管理员（特殊处理）
echo -e "${YELLOW}2. 测试系统管理员（2个用户）${NC}"
test_institutional_login "superadmin" "PLATFORM" "123456" "超级管理员"
test_institutional_login "admin" "PLATFORM" "123456" "平台管理员"

# 测试省级机构管理员
echo -e "${YELLOW}3. 测试省级机构管理员（3个用户）${NC}"
test_institutional_login "sh_admin" "SH_HQ" "123456" "上海长护评估管理中心 - 管理员"
test_institutional_login "hn_admin" "HN_HQ" "123456" "海南健康评估总部 - 管理员"
test_institutional_login "hb_admin" "HB_HQ" "123456" "湖北省护理评估中心 - 管理员"

# 测试市级机构用户
echo -e "${YELLOW}4. 测试市级机构用户（4个用户）${NC}"
test_institutional_login "pd_admin" "SH_PD" "123456" "浦东新区评估中心 - 管理员"
test_institutional_login "pd_assessor" "SH_PD" "123456" "浦东新区评估中心 - 评估师"
test_institutional_login "hk_manager" "HN_HK" "123456" "海口市评估分中心 - 经理"
test_institutional_login "wh_reviewer" "HB_WH" "123456" "武汉市评估总站 - 审核员"

# 测试医疗机构用户
echo -e "${YELLOW}5. 测试医疗机构用户（4个用户）${NC}"
test_institutional_login "rj_doctor" "HOSP_RJ" "123456" "上海瑞金医院 - 医生"
test_institutional_login "rj_nurse" "HOSP_RJ" "123456" "上海瑞金医院 - 护士"
test_institutional_login "hs_admin" "HOSP_HS" "123456" "华山医院康复科 - 管理员"
test_institutional_login "tj_assessor" "HOSP_TJ" "123456" "武汉同济医院 - 评估师"

# 测试养老机构用户
echo -e "${YELLOW}6. 测试养老机构用户（4个用户）${NC}"
test_institutional_login "fsk_admin" "CARE_FSK" "123456" "上海福寿康养老院 - 管理员"
test_institutional_login "fsk_nurse" "CARE_FSK" "123456" "上海福寿康养老院 - 护理员"
test_institutional_login "cxm_manager" "CARE_CXM" "123456" "椿萱茂养老社区 - 经理"
test_institutional_login "xyh_assessor" "CARE_XYH" "123456" "海南夕阳红养护院 - 评估师"

# 测试保险公司用户
echo -e "${YELLOW}7. 测试保险公司用户（3个用户）${NC}"
test_institutional_login "zgrs_manager" "INS_ZGRS" "123456" "中国人寿保险 - 经理"
test_institutional_login "zgrs_auditor" "INS_ZGRS" "123456" "中国人寿保险 - 审核员"
test_institutional_login "tpy_viewer" "INS_TPYB" "123456" "太平洋保险 - 查看员"

# 显示测试结果汇总
echo -e "${YELLOW}=== 测试结果汇总 ===${NC}"
echo ""
echo "总测试数: $total_tests"
echo -e "成功: ${GREEN}$passed_tests${NC}"
echo -e "失败: ${RED}$failed_tests${NC}"
echo "成功率: $(( passed_tests * 100 / total_tests ))%"
echo ""

# 详细统计
echo -e "${YELLOW}测试覆盖统计:${NC}"
echo "• 系统管理员: 2个用户"
echo "• 省级机构: 3个管理员" 
echo "• 市级机构: 4个用户（管理员、评估师、审核员）"
echo "• 医疗机构: 4个用户（管理员、医生、护士、评估师）"
echo "• 养老机构: 4个用户（管理员、护理员、经理、评估师）"
echo "• 保险公司: 3个用户（经理、审核员、查看员）"
echo ""

# 角色统计
echo -e "${YELLOW}角色类型统计:${NC}"
echo "• ADMIN角色: 9个用户"
echo "• ASSESSOR角色: 6个用户"
echo "• REVIEWER角色: 2个用户"
echo "• VIEWER角色: 1个用户"
echo "• 特殊角色: 2个（超级管理员）"
echo ""

if [ $failed_tests -eq 0 ]; then
    echo -e "${GREEN}🎉 所有用户登录测试通过！${NC}"
    exit 0
else
    echo -e "${RED}⚠️  有 $failed_tests 个登录测试失败${NC}"
    echo ""
    echo "可能的原因:"
    echo "1. 密码不正确（应该都是 123456）"
    echo "2. 用户名或机构代码不匹配"
    echo "3. 用户关系数据未正确加载"
    echo "4. 后端认证服务有问题"
    echo ""
    echo "调试建议:"
    echo "• 检查数据库: psql -h localhost -p 5433 -U assessment_user -d assessment_multitenant"
    echo "• 查看后端日志: tail -f backend/logs/app.log"
    echo "• 重新加载数据: psql ... -f scripts/complete-demo-data.sql"
    exit 1
fi