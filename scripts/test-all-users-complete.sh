#!/bin/bash
# 完整的所有用户登录测试脚本
# 包含个人用户、真实机构用户、demo机构用户和超级管理员

API_BASE="http://localhost:8181/api/unified-auth"
AUTH_API="http://localhost:8181/api/auth"

echo "=== 智能评估平台 - 完整用户登录测试 ==="
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 测试计数器
total_tests=0
passed_tests=0
failed_tests=0

# 测试函数
test_login() {
    local username="$1"
    local tenant_code="$2"
    local password="$3"
    local description="$4"
    
    total_tests=$((total_tests + 1))
    echo -e "${BLUE}[$total_tests] 测试: $description${NC}"
    
    result=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$username\",\"tenantCode\":\"$tenant_code\",\"password\":\"$password\"}" \
        "$AUTH_API/login")
    
    if echo "$result" | grep -q '"accessToken"'; then
        echo -e "${GREEN}✓ 登录成功${NC}"
        passed_tests=$((passed_tests + 1))
        display_name=$(echo "$result" | grep -o '"displayName":"[^"]*"' | cut -d'"' -f4)
        tenant_name=$(echo "$result" | grep -o '"tenantName":"[^"]*"' | cut -d'"' -f4)
        tenant_role=$(echo "$result" | grep -o '"tenantRole":"[^"]*"' | cut -d'"' -f4)
        echo "  用户: $display_name | 机构: $tenant_name | 角色: $tenant_role"
    else
        echo -e "${RED}✗ 登录失败${NC}"
        failed_tests=$((failed_tests + 1))
        if echo "$result" | grep -q '"message"'; then
            error_msg=$(echo "$result" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
            echo "  错误: $error_msg"
        fi
    fi
    echo ""
}

# 测试个人用户
test_individual() {
    local email="$1"
    local password="$2"
    local description="$3"
    
    total_tests=$((total_tests + 1))
    echo -e "${BLUE}[$total_tests] 测试: $description${NC}"
    
    result=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"loginType\":\"INDIVIDUAL\",\"identifier\":\"$email\",\"password\":\"$password\"}" \
        "$API_BASE/login")
    
    if echo "$result" | grep -q '"accessToken"'; then
        echo -e "${GREEN}✓ 登录成功${NC}"
        passed_tests=$((passed_tests + 1))
        display_name=$(echo "$result" | grep -o '"displayName":"[^"]*"' | cut -d'"' -f4)
        service_tier=$(echo "$result" | grep -o '"tenantRole":"[^"]*"' | cut -d'"' -f4)
        echo "  用户: $display_name | 服务: $service_tier"
    else
        echo -e "${RED}✗ 登录失败${NC}"
        failed_tests=$((failed_tests + 1))
        if echo "$result" | grep -q '"message"'; then
            error_msg=$(echo "$result" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
            echo "  错误: $error_msg"
        fi
    fi
    echo ""
}

# 检查服务状态
echo -e "${YELLOW}检查服务状态...${NC}"
health=$(curl -s "http://localhost:8181/actuator/health" 2>/dev/null)
if echo "$health" | grep -q '"status":"UP"'; then
    echo -e "${GREEN}✓ 服务运行正常${NC}"
else
    echo -e "${RED}✗ 服务未启动${NC}"
    exit 1
fi
echo ""

echo -e "${YELLOW}=== 个人用户测试 (3个用户) ===${NC}"
test_individual "<EMAIL>" "123456" "测试用户(FREE版)"
test_individual "<EMAIL>" "Test@123" "验证码测试用户(FREE版)"
test_individual "<EMAIL>" "Test@123" "密码测试用户(FREE版)"

echo -e "${YELLOW}=== 上海地区机构用户测试 (5个用户) ===${NC}"
test_login "zhangminghua.001" "SH01MZ" "Test@123" "上海长护评估管理中心-管理员"
test_login "zhaopinggu.001" "SH02PD" "Test@123" "浦东新区评估中心-评估员"
test_login "wangyisheng.001" "SH02RJ" "Test@123" "上海瑞金医院-医生"
test_login "sunyuanzhang.001" "SH02FS" "Test@123" "上海福寿康养老院-院长"
test_login "fengjingli.001" "SH02ZG" "Test@123" "中国人寿保险-经理"

echo -e "${YELLOW}=== 政府机构用户测试 (1个用户) ===${NC}"
test_login "admin.001" "GV01MZ" "Test@123" "省民政厅-管理员"

echo -e "${YELLOW}=== 演示机构用户测试 (9个用户) ===${NC}"
echo -e "${BLUE}演示医院用户:${NC}"
test_login "admin.001" "DM01YY" "Test@123" "演示医院-管理员"
test_login "assessor.001" "DM01YY" "Test@123" "演示医院-评估员"
test_login "reviewer.001" "DM01YY" "Test@123" "演示医院-审核员"

echo -e "${BLUE}演示养老院用户:${NC}"
test_login "admin.001" "DM02YL" "Test@123" "演示养老院-管理员"
test_login "assessor.001" "DM02YL" "Test@123" "演示养老院-评估员"
test_login "reviewer.001" "DM02YL" "Test@123" "演示养老院-审核员"

echo -e "${BLUE}演示社区中心用户:${NC}"
test_login "admin.001" "DM03SQ" "Test@123" "演示社区中心-管理员"
test_login "assessor.001" "DM03SQ" "Test@123" "演示社区中心-评估员"
test_login "reviewer.001" "DM03SQ" "Test@123" "演示社区中心-审核员"

echo -e "${YELLOW}=== 超级管理员测试 (2个用户) ===${NC}"
test_login "superadmin" "SYSTEM" "admin123" "超级管理员"
test_login "admin" "SYSTEM" "admin123" "系统管理员"

echo -e "${YELLOW}=== 测试结果汇总 ===${NC}"
echo ""
echo "总测试数: $total_tests"
echo -e "成功: ${GREEN}$passed_tests${NC}"
echo -e "失败: ${RED}$failed_tests${NC}"

if [ $failed_tests -eq 0 ]; then
    echo -e "${GREEN}🎉 所有用户登录测试通过！${NC}"
    echo ""
    echo "📊 测试覆盖范围："
    echo "• 个人用户: 3个 ✅"
    echo "• 真实机构用户: 6个 ✅"
    echo "• 演示机构用户: 9个 ✅"
    echo "• 超级管理员: 2个 ✅"
    echo "• 总计: $total_tests 个用户 ✅"
    exit 0
else
    echo -e "${RED}⚠️  有 $failed_tests 个登录测试失败${NC}"
    exit 1
fi