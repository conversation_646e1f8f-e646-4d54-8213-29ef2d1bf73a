-- ========================================
-- 修复枚举值大小写问题
-- 执行时间: 2025-06-25
-- ========================================

-- 1. 修复租户状态枚举值（小写 -> 大写）
UPDATE tenants SET status = 'ACTIVE' WHERE status = 'active';
UPDATE tenants SET status = 'INACTIVE' WHERE status = 'inactive';
UPDATE tenants SET status = 'SUSPENDED' WHERE status = 'suspended';

-- 2. 修复平台用户角色枚举值（下划线问题）
UPDATE platform_users SET platform_role = 'ADMIN' WHERE platform_role = 'admin';
UPDATE platform_users SET platform_role = 'USER' WHERE platform_role = 'user';
-- 特殊处理超级管理员角色
UPDATE platform_users SET platform_role = 'ADMIN' WHERE platform_role = 'super_admin';
UPDATE platform_users SET platform_role = 'ADMIN' WHERE platform_role = 'SUPER_ADMIN';

-- 3. 修复租户用户关系状态枚举值
UPDATE tenant_user_memberships SET status = 'ACTIVE' WHERE status = 'active';
UPDATE tenant_user_memberships SET status = 'INACTIVE' WHERE status = 'inactive';

-- 4. 修复密码（确保所有测试用户使用统一密码）
-- 密码: 123456 的 BCrypt 哈希值
UPDATE platform_users 
SET password_hash = '$2a$10$1qE3wIiB2BNm5V74yRel1OCfq.AAeL6tbmdi3hJnDEY/DkejD4PT2' 
WHERE username IN (
    'superadmin', 'admin', 'sh_admin', 'hn_admin', 'hb_admin',
    'pd_admin', 'pd_assessor', 'hk_manager', 'wh_reviewer',
    'rj_doctor', 'rj_nurse', 'hs_admin', 'tj_assessor',
    'fsk_admin', 'fsk_nurse', 'cxm_manager', 'xyh_assessor',
    'zgrs_manager', 'zgrs_auditor', 'tpy_viewer'
);

-- 5. 创建PLATFORM租户（如果不存在）
INSERT INTO tenants (id, code, name, industry, subscription_plan, status, created_at)
VALUES ('00000000-0000-0000-0000-000000000001', 'PLATFORM', '平台管理', '系统', 'ENTERPRISE', 'ACTIVE', NOW())
ON CONFLICT (code) DO UPDATE 
SET status = 'ACTIVE', 
    name = '平台管理',
    subscription_plan = 'ENTERPRISE';

-- 6. 创建超级管理员的租户关系（如果不存在）
-- superadmin
INSERT INTO tenant_user_memberships (user_id, tenant_id, tenant_role, display_name, status)
VALUES (
    '00000001-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001',
    'ADMIN',
    '超级管理员',
    'ACTIVE'
) ON CONFLICT (user_id, tenant_id) DO UPDATE 
SET tenant_role = 'ADMIN', status = 'ACTIVE';

-- admin
INSERT INTO tenant_user_memberships (user_id, tenant_id, tenant_role, display_name, status)
VALUES (
    '00000002-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001',
    'ADMIN',
    '平台管理员',
    'ACTIVE'
) ON CONFLICT (user_id, tenant_id) DO UPDATE 
SET tenant_role = 'ADMIN', status = 'ACTIVE';

-- 7. 显示修复结果
SELECT '租户状态修复' as operation, COUNT(*) as count FROM tenants WHERE status = 'ACTIVE'
UNION ALL
SELECT '平台用户角色修复', COUNT(*) FROM platform_users WHERE platform_role IN ('ADMIN', 'USER')
UNION ALL
SELECT '用户关系状态修复', COUNT(*) FROM tenant_user_memberships WHERE status = 'ACTIVE'
UNION ALL
SELECT 'PLATFORM租户', COUNT(*) FROM tenants WHERE code = 'PLATFORM'
UNION ALL
SELECT '密码统一更新', COUNT(*) FROM platform_users WHERE password_hash = '$2a$10$1qE3wIiB2BNm5V74yRel1OCfq.AAeL6tbmdi3hJnDEY/DkejD4PT2';

-- 显示准备测试的用户
SELECT 
    pu.username,
    t.code as tenant_code,
    t.name as tenant_name,
    tum.tenant_role,
    t.status as tenant_status,
    pu.platform_role
FROM platform_users pu
JOIN tenant_user_memberships tum ON pu.id = tum.user_id
JOIN tenants t ON tum.tenant_id = t.id
WHERE tum.status = 'ACTIVE' AND pu.is_active = true
ORDER BY pu.username;