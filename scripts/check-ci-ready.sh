#!/bin/bash

# CI/CD 就绪检查脚本
# 检查项目是否准备好使用GitHub Actions

echo "========================================"
echo "🔍 CI/CD 就绪状态检查"
echo "========================================"
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_item() {
    if [ "$1" -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        echo "   $3"
    fi
}

warn_item() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    echo "   $2"
}

# 1. 检查Git配置
echo "1️⃣ Git配置检查"
git status &>/dev/null
check_item $? "Git仓库已初始化" "请运行: git init"

REMOTE=$(git remote get-url origin 2>/dev/null)
if [ -n "$REMOTE" ]; then
    check_item 0 "已配置远程仓库: $REMOTE"
else
    check_item 1 "未配置远程仓库" "请运行: git remote add origin <your-repo-url>"
fi
echo ""

# 2. 检查GitHub Actions工作流
echo "2️⃣ GitHub Actions工作流检查"
if [ -d ".github/workflows" ]; then
    WORKFLOW_COUNT=$(ls -1 .github/workflows/*.yml 2>/dev/null | wc -l)
    check_item 0 "工作流目录存在 (${WORKFLOW_COUNT}个工作流)"
    
    # 检查关键工作流
    [ -f ".github/workflows/ci-cd.yml" ] && echo "   ✓ 主CI/CD流程"
    [ -f ".github/workflows/code-quality.yml" ] && echo "   ✓ 代码质量检查"
    [ -f ".github/workflows/security.yml" ] && echo "   ✓ 安全扫描"
else
    check_item 1 "工作流目录不存在" "请确保.github/workflows目录存在"
fi
echo ""

# 3. 检查项目文件
echo "3️⃣ 项目文件检查"
[ -f "backend/pom.xml" ] && check_item 0 "后端项目文件存在" || check_item 1 "后端项目文件缺失" "backend/pom.xml"
[ -f "frontend/admin/package.json" ] && check_item 0 "管理后台项目文件存在" || check_item 1 "管理后台项目文件缺失" "frontend/admin/package.json"
[ -f "frontend/uni-app/package.json" ] && check_item 0 "移动端项目文件存在" || check_item 1 "移动端项目文件缺失" "frontend/uni-app/package.json"
echo ""

# 4. 检查GitHub CLI
echo "4️⃣ GitHub CLI检查"
if command -v gh &> /dev/null; then
    check_item 0 "GitHub CLI已安装"
    
    if gh auth status &> /dev/null; then
        check_item 0 "已登录GitHub"
        
        # 尝试列出Secrets
        echo ""
        echo "📋 已配置的Secrets:"
        SECRETS=$(gh secret list 2>/dev/null || echo "")
        if [ -n "$SECRETS" ]; then
            echo "$SECRETS" | sed 's/^/   /'
        else
            echo "   （无法获取或未配置任何Secrets）"
        fi
    else
        check_item 1 "未登录GitHub" "请运行: gh auth login"
    fi
else
    check_item 1 "GitHub CLI未安装" "请运行: brew install gh"
fi
echo ""

# 5. 基础CI功能状态
echo "5️⃣ 基础CI功能状态"
echo -e "${GREEN}✅ 基础CI功能无需配置Secrets即可使用:${NC}"
echo "   • 代码质量检查（Checkstyle, SpotBugs, ESLint）"
echo "   • 单元测试和集成测试"
echo "   • 安全漏洞扫描"
echo "   • PR检查和覆盖率报告"
echo ""

# 6. 可选功能提示
echo "6️⃣ 可选功能配置建议"
warn_item "Docker镜像构建" "需要配置 DOCKER_USERNAME 和 DOCKER_PASSWORD"
warn_item "自动部署功能" "需要配置相应环境的 HOST, USERNAME, SSH_KEY"
warn_item "监控集成" "可选配置 CODECOV_TOKEN, SONAR_TOKEN 等"
echo ""

# 7. 下一步操作
echo "========================================"
echo "📚 下一步操作建议:"
echo "========================================"
echo ""

if ! gh auth status &> /dev/null 2>&1; then
    echo "1. 登录GitHub CLI:"
    echo "   gh auth login"
    echo ""
fi

echo "2. 配置Secrets（可选，用于增强功能）:"
echo "   cp .env.secrets.example .env.secrets"
echo "   # 编辑.env.secrets填写需要的配置"
echo "   ./scripts/setup-github-secrets.sh"
echo ""

echo "3. 测试CI/CD:"
echo "   git add ."
echo "   git commit -m 'test: CI/CD configuration'"
echo "   git push origin $(git branch --show-current)"
echo ""

echo "4. 查看运行结果:"
echo "   • 访问 GitHub 仓库 → Actions 标签页"
echo "   • 或运行: gh run list"
echo ""

echo "========================================"
echo "✨ 提示: 即使不配置任何Secrets，"
echo "   基础CI功能也可以正常工作！"
echo "========================================"