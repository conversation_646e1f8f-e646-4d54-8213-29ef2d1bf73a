#!/bin/bash

# 生产环境部署脚本
# 版本: v1.0
# 创建日期: 2025-06-26
# 说明: 用于生产环境的部署和优化

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
APP_NAME="assessment-platform"
APP_VERSION=$(grep version pom.xml | head -1 | sed 's/.*<version>\(.*\)<\/version>.*/\1/')
DEPLOY_DIR="/opt/assessment"
LOG_DIR="/var/log/assessment"
BACKUP_DIR="/var/backup/assessment"

# 打印带颜色的日志
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查环境
check_environment() {
    log_step "检查生产环境配置..."
    
    # 检查必要的环境变量
    required_vars=(
        "DB_HOST"
        "DB_PASSWORD"
        "REDIS_HOST" 
        "REDIS_PASSWORD"
        "JWT_SECRET"
        "MINIO_ACCESS_KEY"
        "MINIO_SECRET_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "环境变量 $var 未设置"
            exit 1
        fi
    done
    
    log_info "环境变量检查通过"
    
    # 检查Java版本
    java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | cut -d'.' -f1)
    if [ "$java_version" -lt 21 ]; then
        log_error "需要Java 21或更高版本，当前版本: $java_version"
        exit 1
    fi
    
    log_info "Java版本检查通过: Java $java_version"
}

# 构建应用
build_application() {
    log_step "构建生产环境应用..."
    
    # 清理和构建
    ./mvnw clean package -DskipTests -Pprod
    
    if [ ! -f "target/${APP_NAME}-${APP_VERSION}.jar" ]; then
        log_error "构建失败，找不到JAR文件"
        exit 1
    fi
    
    log_info "构建成功: ${APP_NAME}-${APP_VERSION}.jar"
}

# 备份当前版本
backup_current() {
    log_step "备份当前运行版本..."
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    # 备份当前JAR和配置
    if [ -f "$DEPLOY_DIR/${APP_NAME}.jar" ]; then
        backup_name="${APP_NAME}_$(date +%Y%m%d_%H%M%S).jar"
        cp "$DEPLOY_DIR/${APP_NAME}.jar" "$BACKUP_DIR/$backup_name"
        log_info "备份完成: $backup_name"
    fi
    
    # 保留最近10个备份
    cd "$BACKUP_DIR"
    ls -t ${APP_NAME}_*.jar | tail -n +11 | xargs -r rm
}

# 部署新版本
deploy_application() {
    log_step "部署新版本..."
    
    # 创建必要目录
    mkdir -p "$DEPLOY_DIR" "$LOG_DIR"
    
    # 复制JAR文件
    cp "target/${APP_NAME}-${APP_VERSION}.jar" "$DEPLOY_DIR/${APP_NAME}.jar"
    
    # 复制生产配置
    cp "src/main/resources/application-prod.yml" "$DEPLOY_DIR/"
    
    # 创建启动脚本
    cat > "$DEPLOY_DIR/start.sh" << 'EOF'
#!/bin/bash

# 生产环境JVM参数
JVM_OPTS="-server \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UseStringDeduplication \
  -XX:+OptimizeStringConcat \
  -Xms2g \
  -Xmx4g \
  -XX:MetaspaceSize=256m \
  -XX:MaxMetaspaceSize=512m \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/var/log/assessment/heap.hprof \
  -Dfile.encoding=UTF-8 \
  -Djava.security.egd=file:/dev/./urandom"

# 启动应用
exec java $JVM_OPTS \
  -Dspring.profiles.active=prod \
  -Dserver.port=${SERVER_PORT:-8080} \
  -jar /opt/assessment/assessment-platform.jar \
  >> /var/log/assessment/app.log 2>&1
EOF
    
    chmod +x "$DEPLOY_DIR/start.sh"
    
    log_info "部署完成"
}

# 创建systemd服务
create_service() {
    log_step "创建systemd服务..."
    
    cat > /etc/systemd/system/assessment.service << EOF
[Unit]
Description=Assessment Platform
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=assessment
Group=assessment
WorkingDirectory=/opt/assessment
ExecStart=/opt/assessment/start.sh
ExecStop=/bin/kill -TERM \$MAINPID
Restart=on-failure
RestartSec=30
StandardOutput=journal
StandardError=journal
SyslogIdentifier=assessment

# 资源限制
LimitNOFILE=65535
LimitNPROC=32768

# 环境变量
Environment="LANG=zh_CN.UTF-8"
EnvironmentFile=-/etc/assessment/assessment.env

[Install]
WantedBy=multi-user.target
EOF
    
    # 创建应用用户
    if ! id -u assessment >/dev/null 2>&1; then
        useradd -r -s /bin/false assessment
        log_info "创建用户: assessment"
    fi
    
    # 设置权限
    chown -R assessment:assessment "$DEPLOY_DIR" "$LOG_DIR"
    
    # 重载systemd
    systemctl daemon-reload
    
    log_info "systemd服务创建完成"
}

# 优化系统参数
optimize_system() {
    log_step "优化系统参数..."
    
    # 优化内核参数
    cat > /etc/sysctl.d/99-assessment.conf << EOF
# 网络优化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30
net.ipv4.ip_local_port_range = 1024 65535

# 文件句柄
fs.file-max = 655350

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 10
vm.dirty_background_ratio = 5
EOF
    
    sysctl -p /etc/sysctl.d/99-assessment.conf
    
    # 优化limits
    cat > /etc/security/limits.d/assessment.conf << EOF
assessment soft nofile 65535
assessment hard nofile 65535
assessment soft nproc 32768
assessment hard nproc 32768
EOF
    
    log_info "系统优化完成"
}

# 配置日志轮转
setup_logrotate() {
    log_step "配置日志轮转..."
    
    cat > /etc/logrotate.d/assessment << EOF
/var/log/assessment/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 0644 assessment assessment
    sharedscripts
    postrotate
        systemctl reload assessment >/dev/null 2>&1 || true
    endscript
}
EOF
    
    log_info "日志轮转配置完成"
}

# 健康检查
health_check() {
    log_step "执行健康检查..."
    
    # 等待应用启动
    sleep 10
    
    # 检查健康端点
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/management/health)
    
    if [ "$response" == "200" ]; then
        log_info "健康检查通过"
        
        # 显示详细健康信息
        curl -s http://localhost:8080/management/health | jq '.'
    else
        log_error "健康检查失败，HTTP状态码: $response"
        
        # 显示最近的日志
        tail -50 /var/log/assessment/app.log
        
        return 1
    fi
}

# 主函数
main() {
    log_info "开始部署 $APP_NAME v$APP_VERSION 到生产环境"
    
    # 检查是否以root运行
    if [ "$EUID" -ne 0 ]; then 
        log_error "请使用root权限运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_environment
    build_application
    backup_current
    
    # 停止旧版本
    if systemctl is-active --quiet assessment; then
        log_step "停止当前运行的服务..."
        systemctl stop assessment
        sleep 5
    fi
    
    deploy_application
    create_service
    optimize_system
    setup_logrotate
    
    # 启动新版本
    log_step "启动服务..."
    systemctl start assessment
    systemctl enable assessment
    
    # 健康检查
    if health_check; then
        log_info "部署成功! 🎉"
        log_info "访问地址: http://$(hostname -I | awk '{print $1}'):8080"
        log_info "监控地址: http://$(hostname -I | awk '{print $1}'):8080/management"
    else
        log_error "部署失败，回滚到上一版本"
        # 这里可以添加回滚逻辑
        exit 1
    fi
}

# 执行主函数
main "$@"