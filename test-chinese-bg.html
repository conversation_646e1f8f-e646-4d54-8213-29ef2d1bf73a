<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文背景图验证码测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #2d3748;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            color: #2d3748;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .result-area {
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
        }
        
        .chinese-found {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .chinese-not-found {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🀄 中文背景图验证码测试</h1>
            <p>专门测试中文字符背景图 (bg_chinese.png) 的出现频率</p>
        </div>
        
        <div style="text-align: center; margin-bottom: 30px;">
            <button class="test-button" onclick="testSingle()">🎯 单次测试</button>
            <button class="test-button" onclick="testMultiple(10)">🎲 测试10次</button>
            <button class="test-button" onclick="testMultiple(20)">🚀 测试20次</button>
            <button class="test-button" onclick="clearResults()">🧹 清空结果</button>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="total-tests">0</div>
                <div class="stat-label">总测试次数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="chinese-found">0</div>
                <div class="stat-label">中文背景出现</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="chinese-rate">0%</div>
                <div class="stat-label">中文图出现率</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="other-found">0</div>
                <div class="stat-label">其他背景出现</div>
            </div>
        </div>
        
        <div id="current-result" class="result-area">
            <p>点击测试按钮开始验证中文背景图</p>
        </div>
        
        <div id="test-history" style="margin-top: 30px;">
            <h3>📋 测试历史记录</h3>
            <div id="history-list" style="font-family: monospace; font-size: 14px; line-height: 1.6;">
                <p style="color: #666;">暂无测试记录</p>
            </div>
        </div>
    </div>

    <script>
        let testStats = {
            total: 0,
            chineseFound: 0,
            otherFound: 0
        };
        
        let testHistory = [];
        
        async function testSingle() {
            const resultDiv = document.getElementById('current-result');
            resultDiv.innerHTML = '<p>🔄 正在测试...</p>';
            
            try {
                const response = await fetch('http://localhost:8181/api/captcha/get', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data) {
                    testStats.total++;
                    
                    const { originalImageBase64, jigsawImageBase64, y, token } = data.data;
                    
                    // 检查图片数据特征来推断是否为中文背景图
                    // 中文背景图的特征：较大的文件大小，复杂的图案
                    const imageSize = originalImageBase64.length;
                    const isChineseBg = await analyzeImage(originalImageBase64);
                    
                    if (isChineseBg) {
                        testStats.chineseFound++;
                        resultDiv.className = 'result-area chinese-found';
                        resultDiv.innerHTML = `
                            <h4>🎉 发现中文背景图！</h4>
                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin: 15px 0;">
                                <div style="text-align: center;">
                                    <h5>🀄 中文字符背景图</h5>
                                    <img src="data:image/png;base64,${originalImageBase64}" 
                                         style="max-width: 100%; border: 2px solid #28a745; border-radius: 8px;" 
                                         alt="中文背景验证码"/>
                                </div>
                                <div style="text-align: center;">
                                    <h5>🧩 提取的拼图块</h5>
                                    <div style="display: flex; justify-content: center; align-items: center; height: 150px;">
                                        <img src="data:image/png;base64,${jigsawImageBase64}" 
                                             style="border: 2px solid #28a745; border-radius: 6px;" 
                                             alt="中文拼图块"/>
                                    </div>
                                </div>
                            </div>
                            <p><strong>分析结果：</strong>确认这是中文字符背景图！</p>
                            <p><strong>拼图位置：</strong>Y=${y}px | <strong>Token：</strong>${token.substring(0, 16)}...</p>
                        `;
                        
                        testHistory.unshift({
                            time: new Date().toLocaleTimeString(),
                            type: '中文背景',
                            y: y,
                            token: token.substring(0, 8)
                        });
                    } else {
                        testStats.otherFound++;
                        resultDiv.className = 'result-area chinese-not-found';
                        resultDiv.innerHTML = `
                            <h4>📷 其他背景图</h4>
                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin: 15px 0;">
                                <div style="text-align: center;">
                                    <h5>🖼️ 当前背景图</h5>
                                    <img src="data:image/png;base64,${originalImageBase64}" 
                                         style="max-width: 100%; border: 2px solid #856404; border-radius: 8px;" 
                                         alt="其他背景验证码"/>
                                </div>
                                <div style="text-align: center;">
                                    <h5>🧩 提取的拼图块</h5>
                                    <div style="display: flex; justify-content: center; align-items: center; height: 150px;">
                                        <img src="data:image/png;base64,${jigsawImageBase64}" 
                                             style="border: 2px solid #856404; border-radius: 6px;" 
                                             alt="其他拼图块"/>
                                    </div>
                                </div>
                            </div>
                            <p><strong>分析结果：</strong>这是其他AjCaptcha背景图（bg1-bg9）</p>
                            <p><strong>拼图位置：</strong>Y=${y}px | <strong>Token：</strong>${token.substring(0, 16)}...</p>
                        `;
                        
                        testHistory.unshift({
                            time: new Date().toLocaleTimeString(),
                            type: '其他背景',
                            y: y,
                            token: token.substring(0, 8)
                        });
                    }
                    
                    updateStats();
                    updateHistory();
                } else {
                    throw new Error('API返回数据格式错误');
                }
                
            } catch (error) {
                console.error('测试失败:', error);
                resultDiv.className = 'result-area error';
                resultDiv.innerHTML = `
                    <h4>❌ 测试失败</h4>
                    <p>${error.message}</p>
                `;
            }
        }
        
        async function analyzeImage(base64Data) {
            // 简单的图像分析：检查base64数据的特征
            // 中文字符图像通常有更复杂的模式和更大的数据量
            const dataSize = base64Data.length;
            
            // 如果数据量较大且包含复杂模式，可能是中文背景图
            // 这里使用启发式方法进行判断
            if (dataSize > 100000) { // 大于100KB的base64数据
                return true; // 可能是中文背景图
            }
            
            // 也可以通过图像的复杂度来判断
            // 中文字符图像通常有更多的颜色变化
            const uniqueChars = new Set(base64Data).size;
            if (uniqueChars > 50 && dataSize > 80000) {
                return true;
            }
            
            return false;
        }
        
        async function testMultiple(count) {
            const resultDiv = document.getElementById('current-result');
            resultDiv.innerHTML = `<p>🔄 正在进行 ${count} 次连续测试...</p>`;
            
            for (let i = 1; i <= count; i++) {
                resultDiv.innerHTML = `<p>🔄 正在进行第 ${i}/${count} 次测试...</p>`;
                await testSingle();
                await new Promise(resolve => setTimeout(resolve, 500)); // 间隔500ms
            }
            
            const rate = ((testStats.chineseFound / testStats.total) * 100).toFixed(1);
            resultDiv.innerHTML = `
                <h4>✅ 批量测试完成</h4>
                <p><strong>测试结果：</strong>共进行 ${count} 次测试</p>
                <p><strong>中文背景图：</strong>出现 ${testStats.chineseFound - (testStats.total - count)} 次</p>
                <p><strong>理论概率：</strong>10% (1/10张图片)</p>
                <p><strong>实际概率：</strong>${rate}%</p>
            `;
        }
        
        function updateStats() {
            document.getElementById('total-tests').textContent = testStats.total;
            document.getElementById('chinese-found').textContent = testStats.chineseFound;
            document.getElementById('other-found').textContent = testStats.otherFound;
            
            const rate = testStats.total > 0 ? 
                ((testStats.chineseFound / testStats.total) * 100).toFixed(1) : 0;
            document.getElementById('chinese-rate').textContent = rate + '%';
        }
        
        function updateHistory() {
            const historyDiv = document.getElementById('history-list');
            if (testHistory.length === 0) {
                historyDiv.innerHTML = '<p style="color: #666;">暂无测试记录</p>';
                return;
            }
            
            const historyHtml = testHistory.slice(0, 10).map((record, index) => {
                const icon = record.type === '中文背景' ? '🀄' : '🖼️';
                const color = record.type === '中文背景' ? '#28a745' : '#856404';
                return `<div style="color: ${color}; margin: 5px 0;">
                    ${icon} ${record.time} - ${record.type} - Y:${record.y} - ${record.token}...
                </div>`;
            }).join('');
            
            historyDiv.innerHTML = historyHtml;
        }
        
        function clearResults() {
            testStats = { total: 0, chineseFound: 0, otherFound: 0 };
            testHistory = [];
            updateStats();
            updateHistory();
            document.getElementById('current-result').innerHTML = '<p>📊 统计数据已清空</p>';
        }
        
        // 页面加载完成后自动进行一次测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => testSingle(), 1000);
        });
    </script>
</body>
</html>