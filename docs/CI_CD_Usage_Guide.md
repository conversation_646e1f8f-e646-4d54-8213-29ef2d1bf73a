# CI/CD 使用指南

## 📋 概述

本文档介绍如何配置和使用智能评估平台的CI/CD系统。系统基于GitHub Actions构建，支持自动化测试、代码质量检查、安全扫描和自动部署。

## 🚀 快速开始

### 1. 配置GitHub Secrets

运行配置脚本：
```bash
./scripts/setup-github-secrets.sh
```

或手动在GitHub仓库设置中添加以下Secrets：

#### 必需的Secrets（基础CI功能）
- 无需配置，使用GitHub默认提供的GITHUB_TOKEN

#### 可选的Secrets（增强功能）

**Docker Hub（用于镜像构建）**
- `DOCKER_USERNAME`: Docker Hub用户名
- `DOCKER_PASSWORD`: Docker Hub密码

**部署相关**
- `DEV_HOST`: 开发服务器地址
- `DEV_USERNAME`: 开发服务器用户名
- `DEV_SSH_KEY`: 开发服务器SSH私钥
- `PROD_HOST`: 生产服务器地址
- `PROD_USERNAME`: 生产服务器用户名
- `PROD_SSH_KEY`: 生产服务器SSH私钥

**监控工具**
- `CODECOV_TOKEN`: Codecov覆盖率服务token
- `SONAR_TOKEN`: SonarQube代码质量token
- `K6_CLOUD_TOKEN`: K6性能测试云服务token

**通知**
- `DINGTALK_WEBHOOK`: 钉钉机器人webhook地址

### 2. 工作流触发

#### 自动触发
- **推送到main分支**: 触发完整CI/CD流程，包括生产部署
- **推送到develop分支**: 触发CI流程和开发环境部署
- **创建PR**: 触发代码质量检查和测试

#### 手动触发
在GitHub Actions页面可以手动运行任意工作流。

### 3. 本地测试

使用act工具在本地测试工作流：
```bash
# 安装act
brew install act  # macOS
# 或
curl https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash  # Linux

# 运行测试脚本
./scripts/test-github-actions.sh
```

## 📊 工作流说明

### ci-cd.yml - 主CI/CD流程
完整的持续集成和部署流程：
- ✅ 代码质量检查（Checkstyle, SpotBugs, PMD, ESLint）
- ✅ 单元测试和集成测试
- ✅ 测试覆盖率报告
- ✅ 安全漏洞扫描
- ✅ Docker镜像构建
- ✅ 自动部署到开发/生产环境
- ✅ 性能基准测试
- ✅ 构建通知

### code-quality.yml - 代码质量
专注于代码质量和标准：
- ✅ 后端代码质量检查
- ✅ 前端ESLint检查
- ✅ TypeScript类型检查
- ✅ uni-app多平台构建验证
- ✅ PR覆盖率强制执行

### security.yml - 安全扫描
全面的安全检查：
- ✅ CodeQL代码安全分析
- ✅ Trivy容器安全扫描
- ✅ OWASP依赖漏洞检查
- ✅ npm audit安全审计
- ✅ 每日定时扫描

### ci-test-coverage.yml - 测试覆盖率
测试和覆盖率跟踪：
- ✅ JaCoCo覆盖率报告
- ✅ 前端测试覆盖率
- ✅ 覆盖率趋势分析
- ✅ PR覆盖率评论

## 🛡️ 覆盖率要求

项目强制执行以下覆盖率标准：
- **整体项目**: 85%指令覆盖率
- **服务层**: 90%指令覆盖率
- **控制器层**: 80%指令覆盖率
- **最大未覆盖类数**: 5个

不满足覆盖率要求的PR将无法合并。

## 🚢 部署流程

### 开发环境部署
1. 推送代码到`develop`分支
2. CI/CD自动构建和测试
3. 构建Docker镜像
4. 自动部署到开发服务器

### 生产环境部署
1. 创建PR从`develop`到`main`
2. 代码审查和批准
3. 合并PR到`main`分支
4. CI/CD自动构建和测试
5. 构建生产镜像
6. 自动部署到生产服务器

### 部署配置
生产环境使用`docker-compose.prod.yml`：
```bash
# 在生产服务器上
cd /opt/assessment-platform
cp .env.prod.template .env.prod
# 编辑.env.prod填写实际配置
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 故障排除

### 常见问题

1. **工作流失败：缺少Secrets**
   - 检查是否配置了必要的Secrets
   - 运行`./scripts/setup-github-secrets.sh`

2. **测试失败：数据库连接错误**
   - 确保测试使用了正确的测试配置
   - 检查PostgreSQL服务是否正常启动

3. **部署失败：SSH连接错误**
   - 验证SSH密钥格式正确
   - 确保服务器允许SSH连接

4. **覆盖率不足**
   - 运行`./mvnw test jacoco:report`查看详细报告
   - 为未覆盖的代码添加测试

### 日志查看

1. **GitHub Actions日志**
   - 访问仓库的Actions标签页
   - 点击具体的工作流运行查看详细日志

2. **服务器日志**
   ```bash
   # 查看Docker容器日志
   docker-compose -f docker-compose.prod.yml logs -f backend
   ```

## 📈 监控和报告

### 覆盖率报告
- 自动上传到Codecov（如配置）
- PR中自动添加覆盖率评论
- 查看趋势：Actions → 选择工作流 → 查看历史

### 安全报告
- OWASP报告：下载工作流artifacts
- CodeQL警告：Security标签页
- Trivy扫描结果：工作流日志

### 性能报告
- K6测试结果（如配置K6 Cloud）
- 基准测试比较

## 🔄 持续改进

### 添加新的检查
1. 编辑相应的工作流文件
2. 添加新的job或step
3. 提交并推送更改

### 更新依赖
- 定期运行安全扫描
- 使用Dependabot自动更新
- 审查并测试更新

### 优化构建时间
- 使用缓存策略
- 并行运行独立任务
- 优化Docker镜像大小

## 📚 相关资源

- [GitHub Actions文档](https://docs.github.com/actions)
- [act本地测试工具](https://github.com/nektos/act)
- [Docker Compose文档](https://docs.docker.com/compose/)
- [JaCoCo覆盖率工具](https://www.jacoco.org/jacoco/)

## 🤝 贡献指南

1. Fork仓库
2. 创建功能分支
3. 提交更改（确保通过所有检查）
4. 创建Pull Request
5. 等待代码审查和自动检查
6. 合并到主分支

---

如有问题，请提交Issue或联系项目维护者。