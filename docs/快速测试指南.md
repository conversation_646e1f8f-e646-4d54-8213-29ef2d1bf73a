# 快速测试指南

**目的**: 快速验证多租户登录系统的各种功能

---

## 🚀 快速开始

### 1. 启动服务
```bash
# 在backend目录下启动后端服务
cd backend
./mvnw spring-boot:run

# 等待服务启动（约15-20秒）
# 看到 "Started AssessmentApplication" 表示启动成功
```

### 2. 运行测试脚本
```bash
# 完整测试（推荐）
./scripts/test-login-examples.sh

# 快速测试
./scripts/test-login-examples.sh quick

# 查看帮助
./scripts/test-login-examples.sh help
```

---

## 📱 手动测试命令

### 机构用户登录
```bash
# 上海管理员
curl -X POST -H "Content-Type: application/json" \
  -d '{"loginType":"INSTITUTIONAL","username":"sh_admin","tenantCode":"SH_HQ","password":"Test@123"}' \
  'http://localhost:8181/api/unified-auth/login'

# 浦东评估员  
curl -X POST -H "Content-Type: application/json" \
  -d '{"loginType":"INSTITUTIONAL","username":"pd_assessor","tenantCode":"SH_PD","password":"Test@123"}' \
  'http://localhost:8181/api/unified-auth/login'

# 瑞金医院医生
curl -X POST -H "Content-Type: application/json" \
  -d '{"loginType":"INSTITUTIONAL","username":"rj_doctor","tenantCode":"HOSP_RJ","password":"Test@123"}' \
  'http://localhost:8181/api/unified-auth/login'
```

### 个人用户登录
```bash
# 已注册个人用户
curl -X POST -H "Content-Type: application/json" \
  -d '{"loginType":"INDIVIDUAL","identifier":"<EMAIL>","password":"123456"}' \
  'http://localhost:8181/api/unified-auth/login'
```

### 个人用户注册
```bash
# 发送验证码
curl -X POST 'http://localhost:8181/api/unified-auth/send-email-verification?email=<EMAIL>'

# 使用验证码注册（将CODE替换为返回的验证码）
curl -X POST -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","name":"新用户","password":"User@123","verificationCode":"CODE"}' \
  'http://localhost:8181/api/unified-auth/register'
```

---

## 🎯 测试重点

### 1. 权限隔离测试
- ✅ 上级能看下级数据
- ✅ 同级数据互相隔离  
- ✅ 不同机构完全隔离

### 2. 角色功能测试
- ✅ ADMIN：管理权限
- ✅ ASSESSOR：评估权限
- ✅ REVIEWER：审核权限
- ✅ VIEWER：只读权限

### 3. 多机构类型测试
- ✅ 政府机构（分级管理）
- ✅ 医疗机构（专业评估）
- ✅ 养老机构（护理评估）
- ✅ 保险公司（理赔审核）

---

## 📊 预期测试结果

### 成功登录示例
```json
{
  "accessToken": "eyJ...",
  "refreshToken": "eyJ...",
  "userId": "uuid",
  "displayName": "张明华",
  "tenantName": "上海长护评估管理中心",
  "tenantRole": "ADMIN",
  "permissions": ["..."],
  "active": true
}
```

### 失败登录示例
```json
{
  "success": false,
  "message": "用户不存在，请检查邮箱/手机号或先注册账号",
  "errorCode": "AUTHENTICATION_ERROR"
}
```

---

## 🏆 常用测试账号

| 用户类型 | 登录标识 | 密码 | 描述 |
|---------|---------|------|------|
| 省级管理员 | sh_admin@SH_HQ | Test@123 | 上海总部 |
| 区级评估员 | pd_assessor@SH_PD | Test@123 | 浦东评估员 |
| 医院医生 | rj_doctor@HOSP_RJ | Test@123 | 瑞金医院 |
| 养老院长 | fsk_admin@CARE_FSK | Test@123 | 福寿康 |
| 保险审核 | zgrs_auditor@INS_ZGRS | Test@123 | 中国人寿 |
| 个人用户 | <EMAIL> | 123456 | 已注册 |

---

## 🔧 故障排除

### 1. 服务未启动
```bash
# 检查服务状态
curl http://localhost:8181/actuator/health

# 重启服务
cd backend && ./mvnw spring-boot:run
```

### 2. 数据库连接问题
```bash
# 检查PostgreSQL
brew services list | grep postgresql

# 检查Redis  
brew services list | grep redis
```

### 3. 端口冲突
```bash
# 检查端口占用
lsof -i :8181

# 杀死进程
kill -9 PID
```

---

## 📋 测试清单

### 基础功能测试
- [ ] 服务健康检查
- [ ] 邮箱验证码发送
- [ ] 个人用户注册
- [ ] 个人用户登录
- [ ] 机构用户登录

### 权限测试
- [ ] 省级查看市级数据
- [ ] 市级数据隔离
- [ ] 跨机构数据隔离
- [ ] 角色权限正确性

### 错误处理测试
- [ ] 错误密码
- [ ] 不存在用户
- [ ] 验证码过期
- [ ] 格式错误

---

## 📖 相关文档

- [测试账号和机构数据指南](测试账号和机构数据指南.md) - 完整测试数据
- [用户权限和平台功能规划](用户权限和平台功能规划.md) - 权限体系设计
- [CLAUDE.md](../CLAUDE.md) - 项目总体介绍

---

更新时间：2025-06-24