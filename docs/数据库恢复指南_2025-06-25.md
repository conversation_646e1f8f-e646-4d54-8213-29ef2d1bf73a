# 数据库恢复指南

**版本**: v1.0  
**创建日期**: 2025-06-25  
**基于状态**: 100%登录成功的稳定状态  
**适用环境**: 开发、测试、生产  

---

## 概述

本指南提供了智能评估平台数据库的完整恢复方案，确保在任何问题发生时都能快速恢复到稳定的工作状态。

### 稳定状态特征

- ✅ 27个租户机构，覆盖政府、医疗、养老、保险行业
- ✅ 20个测试用户，100%登录成功
- ✅ 完整的多租户架构
- ✅ 所有枚举值与Java代码完全匹配
- ✅ 性能优化索引已建立

---

## 备份文件说明

### 备份位置
```
database-backups/stable-state-2025-06-25/
├── assessment_multitenant_stable_full.backup      # 完整二进制备份（推荐）
├── assessment_multitenant_stable_complete.sql     # 完整SQL备份
├── assessment_multitenant_stable_schema.sql       # 仅结构备份
└── assessment_multitenant_stable_data.sql         # 仅数据备份
```

### 备份文件特点

| 文件类型 | 大小 | 用途 | 恢复速度 |
|---------|------|------|----------|
| **二进制备份** | 压缩后最小 | 生产环境推荐 | 最快 |
| **完整SQL** | 可读文本 | 调试和检查 | 中等 |
| **仅结构** | 最小 | 开发环境初始化 | 快 |
| **仅数据** | 中等 | 数据恢复 | 中等 |

---

## 快速恢复方法

### 方法1: 使用自动化脚本（推荐）

```bash
# 确保在项目根目录
cd /Volumes/acasis/Assessment

# 设置执行权限
chmod +x scripts/quick-restore-stable.sh

# 运行恢复脚本
./scripts/quick-restore-stable.sh
```

**脚本功能**:
- 🔍 自动检查数据库连接
- 📋 验证备份文件完整性
- 🎯 提供多种恢复选项
- ✅ 自动验证恢复结果
- 📊 显示系统健康状况

### 方法2: 手动恢复

#### 2.1 使用二进制备份（最快）

```bash
# 1. 停止应用
# 2. 删除现有数据库
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user -d postgres -c "DROP DATABASE IF EXISTS assessment_multitenant;"

# 3. 创建新数据库
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user -d postgres -c "CREATE DATABASE assessment_multitenant;"

# 4. 恢复数据
PGPASSWORD=assessment123 pg_restore -h localhost -p 5433 -U assessment_user -d assessment_multitenant -v database-backups/stable-state-2025-06-25/assessment_multitenant_stable_full.backup
```

#### 2.2 使用SQL备份

```bash
# 1. 删除现有数据库
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user -d postgres -c "DROP DATABASE IF EXISTS assessment_multitenant;"

# 2. 创建新数据库
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user -d postgres -c "CREATE DATABASE assessment_multitenant;"

# 3. 恢复数据
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user -d assessment_multitenant -f database-backups/stable-state-2025-06-25/assessment_multitenant_stable_complete.sql
```

---

## 恢复验证步骤

### 1. 数据完整性检查

```sql
-- 连接数据库
psql -h localhost -p 5433 -U assessment_user -d assessment_multitenant

-- 检查系统健康状况
SELECT * FROM system_health_check ORDER BY metric;

-- 应该看到类似结果：
-- BASIC Subscriptions     | 8  | tenants
-- ENTERPRISE Subscriptions | 7  | tenants  
-- STANDARD Subscriptions  | 13 | tenants
-- Total Active Memberships | 20 | tenant_user_memberships
-- Total Active Tenants    | 28 | tenants
-- Total Active Users      | 20 | platform_users
```

### 2. 登录功能测试

```bash
# 运行完整登录测试
./scripts/test-current-users.sh

# 期望结果：
# 总测试数: 20
# 成功: 20
# 失败: 0  
# 成功率: 100%
```

### 3. 应用服务验证

```bash
# 启动后端应用
cd backend && ./mvnw spring-boot:run

# 检查应用健康状况
curl http://localhost:8181/actuator/health

# 访问API文档
open http://localhost:8181/swagger-ui/index.html
```

---

## 测试账号信息

### 系统管理员
| 用户名 | 机构代码 | 密码 | 角色 |
|--------|----------|------|------|
| superadmin | PLATFORM | 123456 | 超级管理员 |
| admin | PLATFORM | 123456 | 平台管理员 |

### 省级机构管理员
| 用户名 | 机构代码 | 机构名称 | 角色 |
|--------|----------|----------|------|
| sh_admin | SH_HQ | 上海长护评估管理中心 | ADMIN |
| hn_admin | HN_HQ | 海南健康评估总部 | ADMIN |
| hb_admin | HB_HQ | 湖北省护理评估中心 | ADMIN |

### 市级机构用户
| 用户名 | 机构代码 | 机构名称 | 角色 |
|--------|----------|----------|------|
| pd_admin | SH_PD | 浦东新区评估中心 | ADMIN |
| pd_assessor | SH_PD | 浦东新区评估中心 | ASSESSOR |
| hk_manager | HN_HK | 海口市评估分中心 | ADMIN |
| wh_reviewer | HB_WH | 武汉市评估总站 | REVIEWER |

### 医疗机构用户
| 用户名 | 机构代码 | 机构名称 | 角色 |
|--------|----------|----------|------|
| rj_doctor | HOSP_RJ | 上海瑞金医院 | ASSESSOR |
| rj_nurse | HOSP_RJ | 上海瑞金医院 | ASSESSOR |
| hs_admin | HOSP_HS | 华山医院康复科 | ADMIN |
| tj_assessor | HOSP_TJ | 武汉同济医院 | ASSESSOR |

---

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查PostgreSQL容器状态
docker ps | grep postgres

# 应该看到类似输出：
# assessment-postgres   Up X hours   0.0.0.0:5433->5432/tcp

# 如果没有运行，启动容器：
docker-compose up -d postgres
```

#### 2. 权限问题
```bash
# 确保用户有正确权限
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user -d postgres -c "
ALTER USER assessment_user CREATEDB;
GRANT ALL PRIVILEGES ON DATABASE assessment_multitenant TO assessment_user;
"
```

#### 3. 备份文件损坏
```bash
# 检查备份文件完整性
ls -la database-backups/stable-state-2025-06-25/

# 重新创建备份
PGPASSWORD=assessment123 pg_dump -h localhost -p 5433 -U assessment_user -d assessment_multitenant --format=custom --compress=9 --file=new_backup.backup
```

#### 4. 迁移冲突
```sql
-- 检查迁移状态
SELECT * FROM flyway_schema_history ORDER BY installed_on DESC LIMIT 5;

-- 如果需要，清理迁移历史：
DELETE FROM flyway_schema_history WHERE version >= '5';
```

### 紧急恢复流程

如果出现严重问题，按以下顺序执行：

1. **停止所有服务**
   ```bash
   # 停止后端应用
   pkill -f spring-boot
   
   # 停止前端服务
   pkill -f npm
   ```

2. **完全重置数据库**
   ```bash
   ./scripts/quick-restore-stable.sh
   # 选择 "1) 快速恢复"
   ```

3. **验证恢复结果**
   ```bash
   ./scripts/test-current-users.sh
   ```

4. **重启应用服务**
   ```bash
   cd backend && ./mvnw spring-boot:run
   ```

---

## 定期维护建议

### 每日备份
```bash
# 添加到crontab
0 2 * * * cd /Volumes/acasis/Assessment && ./scripts/create-daily-backup.sh
```

### 每周验证
```bash
# 每周日验证登录功能
0 0 * * 0 cd /Volumes/acasis/Assessment && ./scripts/test-current-users.sh > weekly-test-report.log
```

### 每月归档
```bash
# 归档旧备份
find database-backups/ -name "*.backup" -mtime +30 -exec mv {} archived-backups/ \;
```

---

## 联系支持

如果遇到本指南未涵盖的问题：

1. 📋 检查应用日志：`backend/logs/`
2. 📊 查看系统监控：`http://localhost:8181/actuator/health`
3. 🔍 搜索错误信息：项目文档和GitHub Issues

---

**最后更新**: 2025-06-25  
**验证状态**: ✅ 100%测试通过  
**维护人员**: 开发团队