# 智慧养老评估平台开发用户指南

**版本**: v1.0  
**适用项目**: 智慧养老评估平台 (Smart Elderly Assessment Platform)  
**更新日期**: 2025-06-28  
**架构类型**: 多租户SaaS平台  

---

## 🎯 核心开发原则

### 1. 多租户数据隔离原则

- **严格数据隔离**: 所有数据操作必须包含租户ID(tenant_id)过滤
- **跨租户访问禁止**: 绝不允许一个租户访问另一个租户的数据
- **全局用户支持**: 支持用户跨租户访问，但数据仍需隔离
- **租户级配置**: 每个租户可独立配置业务规则和界面样式

### 2. 医疗数据安全合规原则

- **数据加密**: 敏感数据传输和存储必须加密
- **访问审计**: 所有数据访问操作必须记录审计日志
- **权限最小化**: 用户只能访问其职责范围内的数据
- **数据脱敏**: 在非生产环境中使用脱敏数据

### 3. 前后端分离协作原则

- **API优先设计**: 后端优先设计RESTful API，前端基于API开发
- **统一数据格式**: 使用标准的响应格式和错误码
- **版本兼容性**: API变更必须保持向后兼容
- **文档同步**: API文档与代码实现保持同步

### 4. 移动端优先设计原则

- **响应式设计**: 优先考虑移动端体验，再适配桌面端
- **离线功能**: 核心评估功能支持离线操作
- **性能优化**: 针对移动网络环境优化加载速度
- **适老化设计**: 大字体、高对比度、简化操作流程

---

## 🏗️ 技术栈开发规范

### 后端开发规范 (Spring Boot 3.x + Java 21)

#### 项目结构要求

```text
backend/src/main/java/com/assessment/
├── config/          # 配置类
├── controller/      # REST控制器
├── service/         # 业务逻辑层
├── repository/      # 数据访问层
├── entity/          # 数据实体
├── dto/            # 数据传输对象
├── security/       # 安全相关
├── tenant/         # 多租户支持
└── util/           # 工具类
```

#### 多租户开发要求

- **实体设计**: 所有业务实体必须包含`tenantId`字段
- **Repository层**: 使用`@Query`注解添加租户过滤条件
- **Service层**: 在业务逻辑中验证租户权限
- **Controller层**: 从JWT token中提取租户信息

#### 代码示例

```java
// 实体类示例
@Entity
@Table(name = "assessments")
public class Assessment {
    @Id
    private Long id;

    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    // 其他字段...
}

// Repository示例
@Repository
public interface AssessmentRepository extends JpaRepository<Assessment, Long> {
    @Query("SELECT a FROM Assessment a WHERE a.tenantId = :tenantId")
    List<Assessment> findByTenantId(@Param("tenantId") Long tenantId);
}
```

### 前端开发规范

#### Vue 3 管理后台开发

- **组件命名**: 使用PascalCase命名组件
- **状态管理**: 使用Pinia进行状态管理
- **类型安全**: 严格使用TypeScript类型定义
- **UI一致性**: 统一使用Element Plus组件库

#### uni-app 移动端开发

- **页面结构**: 遵循uni-app页面生命周期
- **跨平台兼容**: 确保H5、小程序、App三端兼容
- **性能优化**: 使用虚拟列表处理大数据量
- **离线存储**: 使用uni.setStorage进行本地数据缓存

---

## 📊 业务逻辑开发要求

### 评估量表处理

- **量表解析**: 支持PDF量表的AI智能解析
- **数据验证**: 严格验证评估数据的完整性和准确性
- **版本管理**: 支持量表版本控制和历史记录
- **标准化**: 遵循国家标准和行业规范

### 用户权限体系

- **角色定义**: ADMIN/ASSESSOR/REVIEWER/VIEWER四级权限
- **功能权限**: 基于角色的功能访问控制
- **数据权限**: 基于租户和部门的数据访问控制
- **动态权限**: 支持运行时权限变更

### 报告生成系统

- **模板引擎**: 使用可配置的报告模板
- **数据导出**: 支持PDF、Excel、Word多种格式
- **批量处理**: 支持批量报告生成
- **水印保护**: 自动添加机构水印和防伪标识

---

## 🔧 开发环境配置

### Apple M4优化配置

```bash
# 使用ARM64原生镜像
docker pull --platform=linux/arm64 postgres:15
docker pull --platform=linux/arm64 redis:7

# Maven配置优化
export MAVEN_OPTS="-Xmx4g -XX:+UseG1GC"

# Node.js配置
export NODE_OPTIONS="--max-old-space-size=8192"
```

### 开发工具要求

- **IDE**: IntelliJ IDEA Ultimate / VS Code
- **Java**: OpenJDK 21 LTS
- **Node.js**: v18+ (推荐v20)
- **Docker**: 支持ARM64架构
- **数据库工具**: DBeaver / pgAdmin 4

---

## ✅ 代码质量要求

### 测试覆盖率标准

- **单元测试**: 覆盖率 ≥ 80%
- **集成测试**: 核心业务流程 100% 覆盖
- **端到端测试**: 主要用户场景覆盖
- **安全测试**: 权限控制和数据安全测试

### 代码审查标准

- **业务逻辑**: 确保多租户数据隔离
- **安全性**: 检查权限控制和数据验证
- **性能**: 关注数据库查询和缓存使用
- **可维护性**: 代码结构清晰，注释完整

### 质量检查工具

```bash
# 运行完整质量检查
./scripts/code-quality-check.sh --report

# 后端质量检查
mvn clean test jacoco:report spotbugs:check checkstyle:check

# 前端质量检查
npm run lint && npm run type-check
```

---

## 🚀 部署和运维规范

### 容器化部署

- **多环境支持**: local/dev/staging/prod环境配置
- **健康检查**: 配置应用健康检查端点
- **日志管理**: 统一日志格式和收集策略
- **监控告警**: 配置关键指标监控

### 数据库管理

- **迁移脚本**: 使用Flyway管理数据库版本
- **备份策略**: 定期自动备份和恢复测试
- **性能优化**: 定期分析和优化查询性能
- **索引管理**: 合理创建和维护数据库索引

---

## 📋 开发流程规范

### Git工作流

1. **分支策略**: 使用GitFlow分支模型
2. **提交规范**: 遵循Conventional Commits规范
3. **代码审查**: 所有代码必须经过审查
4. **自动化测试**: CI/CD流程自动运行测试

### 问题处理流程

1. **错误分析**: 使用思考链推理方式分析问题根因
2. **影响评估**: 评估问题对多租户系统的影响范围
3. **修复验证**: 在多个租户环境中验证修复效果
4. **文档更新**: 及时更新相关技术文档

---

## 🔒 安全开发要求

### 数据保护

- **传输加密**: 使用HTTPS和TLS 1.3
- **存储加密**: 敏感字段使用AES-256加密
- **访问控制**: 实现细粒度的权限控制
- **审计日志**: 记录所有敏感操作

### 合规要求

- **数据脱敏**: 开发和测试环境使用脱敏数据
- **隐私保护**: 遵循个人信息保护法规
- **数据留存**: 按照法规要求管理数据生命周期
- **安全评估**: 定期进行安全漏洞扫描

---

**指南总结**: 本指南专门针对智慧养老评估平台的技术特点和业务需求制定，强调多租户架构、医疗数据安全、前后端分离和移动端优化等关键要素，为开发团队提供明确的技术规范和最佳实践指导。

## 📝 具体实施细节

### 开发前准备检查清单

```bash
# 1. 环境检查
./scripts/check-m4-compatibility.sh
./scripts/check-ports.sh

# 2. 依赖安装
cd backend && mvn clean install
cd frontend/admin && npm install
cd frontend/uni-app && npm install

# 3. 数据库初始化
./scripts/init-db.sql
./scripts/migrate-to-multi-tenant.sh

# 4. 启动开发环境
./scripts/dev-start-m4.sh
```

### 新功能开发流程

1. **需求分析**: 明确功能的租户隔离要求
2. **API设计**: 优先设计RESTful API接口
3. **数据模型**: 确保包含租户ID和审计字段
4. **权限设计**: 定义功能的权限控制策略
5. **测试用例**: 编写多租户场景的测试用例
6. **文档更新**: 同步更新API文档和用户手册

### 常见问题处理模式

#### 多租户数据泄露问题

```java
// ❌ 错误示例 - 缺少租户过滤
@GetMapping("/assessments")
public List<Assessment> getAssessments() {
    return assessmentRepository.findAll(); // 危险！会返回所有租户数据
}

// ✅ 正确示例 - 包含租户过滤
@GetMapping("/assessments")
public List<Assessment> getAssessments(Authentication auth) {
    Long tenantId = getCurrentTenantId(auth);
    return assessmentRepository.findByTenantId(tenantId);
}
```

#### 前后端数据同步问题

```typescript
// 前端状态管理示例
interface AssessmentState {
  currentTenant: Tenant | null;
  assessments: Assessment[];
  loading: boolean;
  error: string | null;
}

// 确保前端状态与后端数据一致
const useAssessmentStore = defineStore('assessment', {
  state: (): AssessmentState => ({
    currentTenant: null,
    assessments: [],
    loading: false,
    error: null
  }),

  actions: {
    async fetchAssessments() {
      if (!this.currentTenant) {
        throw new Error('当前租户信息缺失');
      }
      // 实现数据获取逻辑
    }
  }
});
```

### 性能优化指导

#### 数据库查询优化

```sql
-- 为多租户查询创建复合索引
CREATE INDEX idx_assessments_tenant_created
ON assessments(tenant_id, created_at DESC);

-- 使用分区表优化大数据量查询
CREATE TABLE assessments_2025 PARTITION OF assessments
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

#### 缓存策略

```java
// Redis缓存键命名规范：{service}:{tenant}:{entity}:{id}
@Cacheable(value = "assessments", key = "#tenantId + ':' + #assessmentId")
public Assessment getAssessment(Long tenantId, Long assessmentId) {
    return assessmentRepository.findByTenantIdAndId(tenantId, assessmentId);
}
```

### 安全开发检查点

#### API安全检查

- [ ] 所有API端点都有权限验证
- [ ] 输入参数进行了严格验证
- [ ] 敏感数据已脱敏处理
- [ ] 错误信息不泄露系统内部信息
- [ ] 实现了请求频率限制

#### 数据安全检查

- [ ] 数据库连接使用SSL
- [ ] 敏感字段已加密存储
- [ ] 实现了数据访问审计
- [ ] 配置了数据备份和恢复
- [ ] 遵循数据保留政策

---

## 🎓 团队协作规范

### 代码审查要点

1. **多租户安全**: 检查是否正确实现租户数据隔离
2. **API设计**: 验证接口设计的一致性和完整性
3. **错误处理**: 确保异常处理不泄露敏感信息
4. **性能考虑**: 评估代码对系统性能的影响
5. **测试覆盖**: 验证测试用例的完整性

### 沟通协作原则

- **技术决策**: 重大技术变更需要团队讨论
- **问题反馈**: 及时反馈遇到的技术问题
- **知识分享**: 定期分享技术心得和最佳实践
- **文档维护**: 及时更新技术文档和API文档

### 持续改进机制

- **定期回顾**: 每月进行技术债务回顾
- **性能监控**: 持续监控系统性能指标
- **安全审计**: 定期进行安全漏洞扫描
- **用户反馈**: 收集并分析用户使用反馈

---

**使用建议**:

1. 新团队成员必须熟读本指南并通过技术考核
2. 定期根据项目发展和技术演进更新指南内容
3. 在代码审查中严格执行指南要求，建立质量门禁
4. 建立指南执行情况的定期检查和改进机制
5. 将指南作为技术培训和知识传承的重要材料
