# 智能评估平台 - 用户权限和平台功能规划

**文档版本**: v2.0  
**创建日期**: 2025-06-23  
**最后更新**: 2025-06-23  
**维护负责人**: 开发团队  

## 版本更新记录
| 版本 | 日期 | 变更内容 |
|------|------|----------|
| v1.0 | 2025-06-23 | 初始版本，机构用户权限规划 |
| v2.0 | 2025-06-23 | 新增个人用户体系，双轨登录系统设计 |

## 文档概述

本文档详细规划智能评估平台的用户权限体系和跨平台功能分配，**支持个人用户和机构用户双轨并行**，明确不同用户角色在Web端（电脑端）和移动端的功能边界，确保系统安全性和用户体验的最优化。

## 1. 平台架构概览

### 1.1 技术架构
```
智能评估平台
├── Web端 (管理后台)
│   ├── 技术栈: Vue 3 + Element Plus + TypeScript
│   ├── 访问地址: http://localhost:5274
│   └── 定位: 复杂管理功能、数据分析、系统配置
├── 移动端 (uni-app)
│   ├── 技术栈: Vue 3 + uni-app + TypeScript
│   ├── 支持平台: H5/微信小程序/App
│   └── 定位: 评估操作、数据查看、简单管理
└── 后端服务
    ├── Spring Boot 3.5.2 + Java 21
    └── 多租户架构 + 层级权限体系
```

### 1.2 双轨用户体系分析

#### 1.2.1 个人用户体系（B2C模式）
```yaml
目标群体:
  - 自我评估的老年人及家属
  - 独立执业的评估师和医护人员
  - 研究人员和学习者
  - 家庭护理人员和志愿者

注册方式:
  - 手机号 + 短信验证码（主要方式）
  - 邮箱 + 邮件验证（备用方式）
  - 社交登录（微信、支付宝等，未来扩展）

付费模式:
  - 免费版：基础评估功能，有使用限制
  - 付费版：全功能使用，无限制使用
```

| 个人用户类型 | 角色代码 | 主要职责 | 登录方式 | 套餐类型 |
|-------------|---------|---------|---------|---------|
| 个人免费用户 | INDIVIDUAL_FREE | 基础自评、历史查看 | 13800138000 | 免费版 |
| 个人付费用户 | INDIVIDUAL_PREMIUM | 全功能评估、报告导出 | <EMAIL> | 付费版 |
| 专业个人用户 | INDIVIDUAL_PRO | 专业评估、数据分析 | <EMAIL> | 专业版 |

#### 1.2.2 机构用户体系（B2B模式）
```yaml
目标群体:
  - 养老机构、医疗机构
  - 政府部门（民政、卫健委等）
  - 第三方评估机构
  - 企业和事业单位

管理方式:
  - 机构统一采购和管理
  - 管理员分配用户账号
  - 基于角色的权限控制
  - 数据归机构所有
```

| 机构用户类型 | 角色代码 | 主要职责 | 登录方式 | 数据范围 |
|-------------|---------|---------|---------|---------|
| 系统超级管理员 | PLATFORM_ADMIN | 平台管理、租户管理 | admin@SYSTEM | 全平台 |
| 机构管理员 | TENANT_ADMIN | 机构管理、用户管理 | zhangsan.admin@SH02YL01 | 本机构+下级 |
| 督导员 | SUPERVISOR | 质量控制、审核监督 | lisi.spv@SH02YL01 | 直接下级 |
| 评估师 | ASSESSOR | 执行评估、数据录入 | wangwu.asm@SH02YL01 | 自己负责 |
| 普通员工 | VIEWER | 查看评估、基础操作 | zhaoliu.001@SH02YL01 | 授权范围 |

## 2. 双轨登录系统设计

### 2.1 统一登录界面

**技术实现**: `/frontend/admin/src/views/UnifiedLoginView.vue`

```yaml
界面特点:
  - 智能切换：个人用户 ↔ 机构用户
  - 自动识别：根据输入格式判断用户类型
  - 统一验证：前后端一致的安全验证机制
  - 个人注册：集成注册流程和验证码发送

登录识别规则:
  个人用户格式:
    - 手机号: 13800138000 (11位数字)
    - 邮箱: <EMAIL> (标准邮箱格式)
  
  机构用户格式:
    - 复合标识: zhangsan.001@SH02YL01 (用户名@机构代码)
    - 系统管理: admin@SYSTEM (特殊格式)
```

### 2.2 个人用户注册流程

```yaml
注册步骤:
  1. 手机号验证: 输入手机号，发送6位验证码
  2. 验证码确认: 验证短信验证码有效性
  3. 基本信息: 填写姓名、密码等基础信息
  4. 服务选择: 选择免费版/付费版服务套餐
  5. 协议确认: 用户协议和隐私政策确认
  6. 完成注册: 自动登录并引导使用

技术支持:
  - 短信服务: 阿里云SMS/腾讯云SMS
  - 验证码防刷: 60秒倒计时，IP频率限制
  - 数据验证: 前后端双重格式验证
  - 安全加密: 密码bcrypt加密存储
```

### 2.3 数据隔离策略

```yaml
个人用户数据:
  - 物理隔离: user_type = 'INDIVIDUAL'
  - 逻辑隔离: 所有查询必须带user_id过滤
  - 数据归属: 个人拥有，支持删除和导出
  - 隐私保护: GDPR/CCPA合规，数据脱敏

机构用户数据:
  - 租户隔离: tenant_id严格隔离
  - 层级权限: 基于组织架构的数据访问
  - 数据归属: 机构拥有，统一管理
  - 审计日志: 完整的操作审计记录
```

## 3. 平台功能权限矩阵

### 3.1 个人用户功能权限

#### 👤 个人免费用户 (INDIVIDUAL_FREE)
**Web端功能**:
```yaml
基础评估:
  ✅ 简单评估: 基础量表评估（每月5次限制）
  ✅ 结果查看: 评估结果查看和解读
  ✅ 历史记录: 近3个月评估历史
  ✅ 个人档案: 基本信息管理
  
限制功能:
  ❌ 高级评估: 复杂量表需要付费版
  ❌ 报告导出: 无PDF/Excel导出功能
  ❌ 数据分析: 无趋势分析和对比功能
  ❌ 批量操作: 无批量导入导出功能
```

**移动端功能**:
```yaml
便民功能:
  ✅ 快速评估: 移动端优化的评估界面
  ✅ 结果查看: 评估结果移动端查看
  ✅ 消息通知: 评估提醒和结果通知
  ✅ 帮助指导: 评估指导和健康建议
```

#### 💰 个人付费用户 (INDIVIDUAL_PREMIUM)
**Web端功能 (完整功能)**:
```yaml
全功能评估:
  ✅ 无限评估: 所有量表无限制使用
  ✅ 高级分析: 趋势分析、对比分析
  ✅ 报告导出: PDF/Excel/Word多格式导出
  ✅ 数据管理: 完整的数据管理功能
  
增值服务:
  ✅ 专业报告: 详细的专业评估报告
  ✅ 健康建议: AI驱动的个性化建议
  ✅ 数据备份: 云端数据备份和同步
  ✅ 优先支持: 优先客服支持
```

**移动端功能 (增强体验)**:
```yaml
高级功能:
  ✅ 离线评估: 网络不稳定时的离线功能
  ✅ 语音输入: 语音转文字评估录入
  ✅ 智能提醒: 个性化评估提醒
  ✅ 家庭共享: 家庭成员评估数据共享
```

#### 🏥 专业个人用户 (INDIVIDUAL_PRO)
**Web端功能 (专业增强)**:
```yaml
专业工具:
  ✅ 自定义量表: 创建和编辑个人量表
  ✅ 批量管理: 批量评估和数据处理
  ✅ API接口: 数据API接口访问权限
  ✅ 数据分析: 高级统计分析工具
  
合规支持:
  ✅ 电子签名: 评估报告电子签名
  ✅ 审计日志: 完整的操作审计记录
  ✅ 数据认证: 评估数据可信度认证
  ✅ 法律模板: 符合法规的报告模板
```

### 3.2 机构用户功能权限

#### 🔧 系统超级管理员 (Web端专享)

#### 🔧 系统超级管理员 (Web端专享)
```yaml
核心功能:
  ✅ 租户管理: 创建、编辑、删除、配置租户
  ✅ 用户管理: 跨租户用户管理和权限分配
  ✅ 系统配置: 平台参数设置和系统维护
  ✅ 数据统计: 全平台数据分析和报表生成
  ✅ 安全监控: 系统日志审计和安全分析

高级功能:
  ✅ PDF量表数字化: AI解析PDF生成数字量表
  ✅ 量表模板管理: 系统级量表模板配置
  ✅ 多租户架构管理: 层级关系维护
  ✅ 系统备份恢复: 数据备份和灾难恢复
  ✅ API接口管理: 第三方系统集成配置
```

#### 🏥 机构管理员
**Web端功能 (完整管理权限)**:
```yaml
用户管理:
  ✅ 创建评估师账号: zhangsan.asm@机构代码
  ✅ 分配用户权限: 角色权限管理
  ✅ 用户状态管理: 启用/禁用账号
  ✅ 密码重置: 重置用户密码

量表管理:
  ✅ PDF量表导入: 上传PDF生成数字量表  
  ✅ 量表编辑器: 复杂量表结构编辑
  ✅ 量表版本控制: 版本管理和发布
  ✅ 量表权限设置: 使用权限分配

数据分析:
  ✅ 评估报告生成: PDF/Excel报告导出
  ✅ 统计分析: 多维度数据分析
  ✅ 趋势分析: 评估数据趋势图表
  ✅ 质量控制: 评估质量监控

系统配置:
  ✅ 机构信息维护: 机构基本信息管理
  ✅ 评估流程配置: 自定义评估工作流
  ✅ 通知设置: 系统通知和提醒配置
```

**移动端功能 (便民管理)**:
```yaml
快速管理:
  ✅ 评估进度查看: 实时评估状态监控
  ✅ 用户快速创建: 简化的用户创建流程
  ✅ 评估任务分配: 快速分配评估任务
  ✅ 通知推送: 重要消息推送和查看
  
数据查看:
  ✅ 关键指标: 核心数据指标展示
  ✅ 评估统计: 基础统计数据查看
  ✅ 用户状态: 用户在线状态和活跃度
  
❌ 限制功能:
  ❌ PDF量表导入: 需要Web端操作
  ❌ 复杂报告生成: 需要Web端处理
  ❌ 系统配置修改: 安全考虑仅Web端
```

### 2.2 普通用户功能权限

#### 📝 评估师 (ASSESSOR)
**Web端功能**:
```yaml
评估管理:
  ✅ 评估执行: 完整的评估操作界面
  ✅ 数据录入: 详细的数据录入表单
  ✅ 评估报告: 生成和查看评估报告
  ✅ 历史记录: 完整的评估历史查询
  
工具功能:
  ✅ 量表预览: 详细的量表结构查看
  ✅ 数据导出: PDF/Excel格式导出
  ✅ 批量操作: 批量评估和数据处理
  ✅ 备注编辑: 富文本评估备注编辑
```

**移动端功能 (主要使用场景)**:
```yaml
现场评估:
  ✅ 评估执行: 优化的移动端评估界面
  ✅ 语音输入: 语音转文字功能
  ✅ 拍照记录: 现场照片上传
  ✅ 离线评估: 网络不稳定时的离线模式
  
数据管理:
  ✅ 评估历史: 个人评估记录查看
  ✅ 简单报告: 基础评估报告查看
  ✅ 数据同步: 云端数据同步
  ✅ 快速查询: 基于关键字的快速搜索
```

#### 👀 普通用户 (VIEWER)
**Web端功能**:
```yaml
基础查看:
  ✅ 评估记录查看: 个人相关的评估记录
  ✅ 简单报告: 基础评估报告查看
  ✅ 数据导出: PDF格式报告导出
  ✅ 个人信息: 个人信息查看和更新
  
❌ 限制功能:
  ❌ 评估执行: 无评估操作权限
  ❌ 数据编辑: 无数据修改权限
  ❌ 用户管理: 无用户管理权限
```

**移动端功能**:
```yaml
便民查看:
  ✅ 评估结果: 个人评估结果查看
  ✅ 历史记录: 个人评估历史浏览
  ✅ 简单报告: 移动端优化的报告查看
  ✅ 消息通知: 评估相关通知接收
```

## 3. 登录界面规划

### 3.1 Web端登录界面 (管理后台)

**当前实现**: `/frontend/admin/src/views/LoginView.vue`

**特点**:
- 简洁安全的三字段登录：用户名、机构代码、密码
- 智能机构代码验证和提示
- 隐私保护设计，无用户信息泄漏
- 帮助文档集成

**目标用户**: 管理员、需要复杂功能的用户

### 3.2 移动端登录界面 (普通用户)

**规划位置**: `/frontend/uni-app/src/pages/login/index.vue`

**设计特点**:
```yaml
界面优化:
  - 移动端优化的触摸友好界面
  - 简化的登录流程
  - 指纹/面部识别支持 (App端)
  - 记住登录状态

功能特点:
  - 同样的安全验证机制
  - 机构代码智能提示
  - 离线登录缓存 (安全范围内)
  - 快速切换账号
```

## 4. 功能实现优先级

### 4.1 高优先级 (立即实现)
1. **移动端登录界面**: 基于Web端登录逻辑，适配移动端UI
2. **普通用户Web登录**: 限制功能权限的Web端登录
3. **移动端评估界面**: 优化评估师的移动端评估体验
4. **权限中间件**: 统一的权限验证中间件

### 4.2 中优先级 (近期实现)
1. **移动端管理功能**: 管理员的移动端简化管理界面
2. **离线评估支持**: 移动端离线评估和数据同步
3. **语音输入**: 移动端语音录入功能
4. **推送通知**: 跨平台消息推送系统

### 4.3 低优先级 (后期优化)
1. **生物识别**: 指纹/面部识别登录
2. **多设备同步**: 跨设备数据同步
3. **智能提醒**: AI驱动的评估提醒
4. **离线分析**: 移动端本地数据分析

## 5. 安全考虑

### 5.1 权限隔离原则
```yaml
数据安全:
  - 租户数据严格隔离
  - 层级权限继承控制
  - 敏感操作Web端限制
  - 审计日志完整记录

功能安全:
  - 复杂配置仅Web端开放
  - 批量操作权限控制
  - 关键功能二次验证
  - 会话超时自动登出
```

### 5.2 平台差异化安全策略
```yaml
Web端安全:
  - 较长的会话保持时间
  - 复杂功能的多重验证
  - IP白名单支持
  - 操作日志详细记录

移动端安全:
  - 较短的会话超时
  - 敏感操作实时验证
  - 设备绑定机制
  - 生物识别辅助验证
```

## 6. 商业模式设计

### 6.1 个人用户付费模式

```yaml
免费版 (INDIVIDUAL_FREE):
  月费: ¥0
  评估次数: 5次/月
  历史记录: 3个月
  报告导出: 不支持
  客服支持: 社区支持

付费版 (INDIVIDUAL_PREMIUM):
  月费: ¥29/月 或 ¥299/年
  评估次数: 无限制
  历史记录: 永久保存
  报告导出: 支持所有格式
  客服支持: 优先支持
  
专业版 (INDIVIDUAL_PRO):
  月费: ¥99/月 或 ¥999/年
  包含付费版所有功能
  自定义量表: 支持
  API接口: 1000次/月
  数据分析: 高级统计
  合规认证: 法律认可
```

### 6.2 机构用户计费模式

```yaml
基础版:
  年费: ¥5,000/年/机构
  用户数: 20个账号
  存储空间: 10GB
  技术支持: 邮件支持

标准版:
  年费: ¥15,000/年/机构
  用户数: 100个账号
  存储空间: 100GB
  技术支持: 电话+邮件

企业版:
  年费: ¥50,000/年/机构
  用户数: 无限制
  存储空间: 1TB
  技术支持: 专属客服
  定制开发: 支持
```

## 7. 开发规划

### 7.1 双轨系统实现路径
```yaml
阶段1 - 个人用户基础 (2周):
  ✅ 统一登录界面实现
  🔄 个人用户注册流程
  🔄 短信验证码服务集成
  🔄 个人用户数据模型设计

阶段2 - 权限体系完善 (2周):
  ✅ 双轨权限中间件
  🔄 个人用户权限控制
  🔄 付费功能限制机制
  🔄 使用量配额管理

阶段3 - 移动端适配 (2周):
  🔄 移动端统一登录界面
  🔄 个人用户移动端功能
  🔄 离线评估支持
  🔄 跨平台数据同步

阶段4 - 商业功能 (2周):
  🔄 付费订阅系统
  🔄 在线支付集成
  🔄 发票管理系统
  🔄 客服支持体系
```

### 7.2 双轨系统测试计划
```yaml
个人用户测试:
  - 注册流程完整性测试
  - 付费功能权限边界测试
  - 个人数据安全隔离测试
  - 跨设备数据同步测试

机构用户测试:
  - 层级权限访问控制测试
  - 多租户数据隔离测试
  - 组织架构权限继承测试
  - 管理功能安全性测试

跨平台测试:
  - Web端和移动端功能一致性测试
  - 双轨登录切换测试
  - 数据同步稳定性测试
  - 性能负载测试

安全测试:
  - 个人用户隐私保护测试
  - 机构用户权限绕过测试
  - 付费功能绕过测试
  - 数据泄漏风险评估
```

## 8. 结论与展望

### 8.1 双轨系统核心价值

通过**个人用户（B2C）+ 机构用户（B2B）**的双轨并行设计，我们实现了：

1. **市场覆盖最大化**: 
   - 个人用户：自我评估、家庭护理、专业个人用户
   - 机构用户：养老机构、医疗机构、政府部门

2. **商业模式多元化**:
   - B2C订阅制：月付/年付，免费+付费分层
   - B2B企业级：年度授权，按用户数和功能计费

3. **技术架构统一化**:
   - 同一套后端服务支持双轨用户
   - 统一的前端登录界面和权限控制
   - 一致的安全验证和数据保护机制

4. **用户体验差异化**:
   - 个人用户：简单易用，快速上手，移动优先
   - 机构用户：功能强大，管理完善，安全可控

### 8.2 技术实现亮点

```yaml
智能登录系统:
  ✅ 单一界面支持双轨切换
  ✅ 自动识别用户类型（格式匹配）
  ✅ 统一的安全验证机制
  ✅ 集成注册和找回密码流程

权限体系设计:
  ✅ 基于用户类型的权限隔离
  ✅ 付费功能的动态控制
  ✅ 跨平台权限一致性
  ✅ 数据安全和隐私保护

商业模式支持:
  ✅ 个人用户付费订阅系统
  ✅ 机构用户企业级授权
  ✅ 使用量配额和限制机制
  ✅ 多层级服务差异化
```

### 8.3 未来扩展方向

```yaml
技术扩展:
  - 微信/支付宝快捷登录
  - 人脸识别和指纹认证
  - AI智能评估建议
  - 区块链数据可信认证

商业扩展:
  - 企业定制化服务
  - API开放平台
  - 第三方生态集成
  - 国际化多语言支持

功能扩展:
  - 家庭健康档案管理
  - 社区养老服务对接
  - 远程医疗咨询集成
  - 大数据分析洞察
```

### 8.4 核心设计原则

1. **安全第一**: 个人隐私保护和机构数据安全并重
2. **体验优化**: 不同用户群体的差异化体验设计
3. **商业可行**: 可持续的商业模式和盈利路径
4. **技术前瞻**: 支持未来功能扩展的灵活架构

---

**文档总结**: 本规划完整定义了智能评估平台的双轨用户体系，为个人用户和机构用户提供了差异化的功能权限和商业模式，确保了系统的安全性、可用性和商业可行性。

**实施建议**: 
1. 优先实现个人用户注册登录功能
2. 完善双轨权限验证中间件
3. 开发付费功能限制机制
4. 逐步完善移动端用户体验

**成功指标**: 
- 个人用户注册转化率 >15%
- 付费用户转化率 >5%
- 机构用户满意度 >4.5/5
- 系统稳定性 >99.9%