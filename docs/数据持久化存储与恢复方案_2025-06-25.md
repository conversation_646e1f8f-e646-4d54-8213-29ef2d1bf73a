# 数据持久化存储与恢复方案

**文档版本**: v1.0  
**创建日期**: 2025-06-25  
**更新日期**: 2025-06-25  
**负责人**: 开发团队  

## 📋 概述

智能评估平台采用多层次的数据持久化策略，确保数据安全、可靠性和快速恢复能力。本文档详细说明了数据存储架构、备份策略和恢复方案。

## 🏗️ 数据持久化架构

### 1. 存储组件

| 组件 | 类型 | 持久化路径 | 容器内路径 | 备份策略 |
|------|------|------------|------------|----------|
| PostgreSQL | 关系数据库 | `./data/postgres` | `/var/lib/postgresql/data` | 自动+手动 |
| Redis | 内存缓存 | `./data/redis` | `/data` | 可选备份 |
| MinIO | 对象存储 | `./data/minio` | `/data` | 文件同步 |
| 应用日志 | 日志文件 | `./logs` | N/A | 日志轮转 |

### 2. Docker持久化配置

#### 2.1 PostgreSQL持久化
```yaml
# docker-compose.yml
postgres:
  image: postgres:15-alpine
  volumes:
    - ./data/postgres:/var/lib/postgresql/data  # 数据持久化
    - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql  # 初始化脚本
  environment:
    POSTGRES_DB: assessment_multitenant
    POSTGRES_USER: assessment_user
    POSTGRES_PASSWORD: ${DB_PASSWORD:-assessment123}
```

**持久化特点**：
- ✅ 完整的数据库数据文件持久化
- ✅ WAL日志文件持久化（支持时间点恢复）
- ✅ 配置文件持久化
- ✅ 重启容器数据不丢失

#### 2.2 Redis持久化
```yaml
redis:
  image: redis:7-alpine
  volumes:
    - ./data/redis:/data  # RDB和AOF文件持久化
  command: redis-server --requirepass ${REDIS_PASSWORD:-redis123}
```

**持久化特点**：
- ✅ RDB快照文件持久化
- ✅ AOF日志文件持久化（如果启用）
- ✅ 缓存数据重启后恢复

#### 2.3 MinIO对象存储持久化
```yaml
minio:
  image: minio/minio:latest
  volumes:
    - ./data/minio:/data  # 对象存储数据持久化
  environment:
    MINIO_ROOT_USER: ${MINIO_USER:-minioadmin}
    MINIO_ROOT_PASSWORD: ${MINIO_PASSWORD:-minioadmin}
```

**持久化特点**：
- ✅ 文件和对象完整持久化
- ✅ 元数据持久化
- ✅ 版本信息持久化

### 3. 数据目录结构

```
/Volumes/acasis/Assessment/
├── data/                           # 持久化数据根目录
│   ├── postgres/                   # PostgreSQL数据目录
│   │   ├── base/                   # 数据库文件
│   │   ├── global/                 # 全局数据
│   │   ├── pg_wal/                 # WAL日志文件
│   │   ├── postgresql.conf         # 配置文件
│   │   └── pg_hba.conf             # 访问控制文件
│   ├── redis/                      # Redis数据目录
│   │   ├── dump.rdb                # RDB快照文件
│   │   └── appendonly.aof          # AOF日志文件（如果启用）
│   └── minio/                      # MinIO对象存储目录
│       ├── assessment-files/       # 应用文件存储
│       └── .minio.sys/             # MinIO系统数据
├── database-backups/               # 数据库备份目录
│   ├── *.backup                    # PostgreSQL二进制备份
│   ├── *.sql                       # SQL格式备份
│   └── README.md                   # 备份说明
└── logs/                           # 应用日志目录
    ├── nginx/                      # Nginx日志
    ├── backend/                    # 后端应用日志
    └── system/                     # 系统日志
```

## 🔄 备份策略

### 1. 自动备份（计划实施）

#### 1.1 每日备份脚本
```bash
#!/bin/bash
# scripts/daily-backup.sh

BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/Volumes/acasis/Assessment/database-backups"
RETENTION_DAYS=30

# 创建完整备份
PGPASSWORD=assessment123 pg_dump -h localhost -p 5433 -U assessment_user \
  -d assessment_multitenant --format=custom --compress=9 \
  --file="${BACKUP_DIR}/daily_full_${BACKUP_DATE}.backup"

# 删除过期备份
find $BACKUP_DIR -name "daily_*.backup" -mtime +$RETENTION_DAYS -delete

echo "每日备份完成: daily_full_${BACKUP_DATE}.backup"
```

#### 1.2 增量备份（WAL归档）
```bash
# postgresql.conf 配置
archive_mode = on
archive_command = 'cp %p /Volumes/acasis/Assessment/data/postgres-archive/%f'
wal_level = replica
```

### 2. 手动备份

#### 2.1 完整备份命令
```bash
# 使用 pg_dump 创建完整备份
PGPASSWORD=assessment123 pg_dump -h localhost -p 5433 -U assessment_user \
  -d assessment_multitenant --format=custom --compress=9 \
  --file="assessment_full_$(date +%Y%m%d_%H%M%S).backup"
```

#### 2.2 仅结构备份
```bash
# 仅备份数据库结构
PGPASSWORD=assessment123 pg_dump -h localhost -p 5433 -U assessment_user \
  -d assessment_multitenant --schema-only \
  --file="assessment_schema_$(date +%Y%m%d_%H%M%S).sql"
```

#### 2.3 仅数据备份
```bash
# 仅备份数据
PGPASSWORD=assessment123 pg_dump -h localhost -p 5433 -U assessment_user \
  -d assessment_multitenant --data-only \
  --file="assessment_data_$(date +%Y%m%d_%H%M%S).sql"
```

### 3. 当前备份状态

#### 3.1 已创建的备份文件
```
database-backups/
├── assessment_multitenant_full_20250625_190608.backup     # 完整备份（175KB）
├── assessment_multitenant_schema_20250625_190608.sql      # 结构备份（127KB）
└── assessment_multitenant_data_20250625_190608.sql        # 数据备份（34KB）
```

#### 3.2 备份内容统计
- **租户数量**: 27个
- **平台用户**: 20个
- **用户关系**: 18个
- **数据表**: 42个（含分区表）
- **索引**: 154个
- **触发器**: 12个
- **视图**: 2个

## 🚀 快速恢复方案

### 1. 使用恢复脚本（推荐）

我们提供了交互式恢复脚本：
```bash
# 交互模式
./scripts/database-recovery.sh

# 命令行模式
./scripts/database-recovery.sh quick    # 快速恢复最新备份
./scripts/database-recovery.sh check    # 检查数据库状态
./scripts/database-recovery.sh list     # 列出可用备份
```

#### 1.1 脚本功能特点
- ✅ 交互式菜单操作
- ✅ 自动检查数据库连接
- ✅ 智能选择最新备份
- ✅ 完整性验证
- ✅ 登录接口测试
- ✅ 详细的进度显示

### 2. 手动恢复步骤

#### 2.1 从完整备份恢复
```bash
# 1. 停止应用服务
docker-compose down

# 2. 删除现有数据库
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user -d postgres \
  -c "DROP DATABASE IF EXISTS assessment_multitenant;"

# 3. 创建新数据库
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user -d postgres \
  -c "CREATE DATABASE assessment_multitenant OWNER assessment_user;"

# 4. 恢复备份
PGPASSWORD=assessment123 pg_restore -h localhost -p 5433 -U assessment_user \
  -d assessment_multitenant --verbose --clean --if-exists \
  /Volumes/acasis/Assessment/database-backups/assessment_multitenant_full_20250625_190608.backup

# 5. 启动服务
docker-compose up -d
```

#### 2.2 从SQL备份恢复
```bash
# 1-3步骤同上

# 4. 恢复SQL备份
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user \
  -d assessment_multitenant \
  -f /Volumes/acasis/Assessment/database-backups/assessment_multitenant_schema_20250625_190608.sql

PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user \
  -d assessment_multitenant \
  -f /Volumes/acasis/Assessment/database-backups/assessment_multitenant_data_20250625_190608.sql

# 5. 启动服务
docker-compose up -d
```

## 🔍 数据完整性验证

### 1. 数据库连接测试
```bash
# 测试数据库连接
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user \
  -d assessment_multitenant -c "SELECT version();"
```

### 2. 数据完整性检查
```bash
# 检查核心表数据
PGPASSWORD=assessment123 psql -h localhost -p 5433 -U assessment_user \
  -d assessment_multitenant -c "
SELECT 
    'Tenants' as table_name, COUNT(*) as record_count FROM tenants
UNION ALL
SELECT 
    'Platform Users' as table_name, COUNT(*) as record_count FROM platform_users
UNION ALL
SELECT 
    'User Memberships' as table_name, COUNT(*) as record_count FROM tenant_user_memberships;
"
```

### 3. 登录功能测试
```bash
# 测试多租户登录API
curl -X POST http://localhost:8181/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"tenantCode":"SH_HQ","username":"sh_admin","password":"123456"}' | jq .
```

**预期结果**: 返回包含 `accessToken` 的JSON响应

### 4. 系统健康检查
```bash
# 检查系统各组件健康状态
curl -s http://localhost:8181/actuator/health | jq .

# 检查数据库统计信息
curl -s "http://localhost:8181/api/system/stats" | jq .  # 如果接口可用
```

## ⚠️ 紧急恢复方案

### 1. 数据库完全损坏
```bash
# 场景：PostgreSQL数据目录损坏
# 解决：使用最新完整备份重建

# 1. 停止所有服务
docker-compose down

# 2. 备份损坏的数据目录（用于分析）
mv ./data/postgres ./data/postgres_corrupted_$(date +%Y%m%d)

# 3. 重新创建数据目录
mkdir -p ./data/postgres

# 4. 启动PostgreSQL服务
docker-compose up -d postgres

# 5. 等待数据库启动
sleep 10

# 6. 使用恢复脚本
./scripts/database-recovery.sh quick

# 7. 启动所有服务
docker-compose up -d
```

### 2. 容器数据丢失
```bash
# 场景：Docker容器数据丢失但持久化目录完好
# 解决：重新启动容器，数据自动恢复

# 1. 重新创建容器
docker-compose down
docker-compose up -d

# 2. 验证数据完整性
./scripts/database-recovery.sh check
```

### 3. 整个系统重装
```bash
# 场景：需要在新环境重新部署
# 解决：迁移持久化目录 + 恢复备份

# 1. 复制整个项目目录到新环境
# 2. 确保Docker和Docker Compose已安装
# 3. 启动服务
cd /Volumes/acasis/Assessment
docker-compose up -d

# 4. 验证恢复
./scripts/database-recovery.sh check
```

## 📊 监控和告警

### 1. 磁盘空间监控
```bash
# 检查数据目录磁盘使用情况
du -sh ./data/*
df -h /Volumes/acasis/Assessment/
```

### 2. 备份状态监控
```bash
# 检查最新备份时间
ls -lt database-backups/*.backup | head -1

# 检查备份文件大小
du -sh database-backups/*
```

### 3. 数据库性能监控
```sql
-- 检查数据库大小
SELECT pg_size_pretty(pg_database_size('assessment_multitenant'));

-- 检查连接数
SELECT count(*) FROM pg_stat_activity;

-- 检查慢查询
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;
```

## 📋 最佳实践

### 1. 日常维护
- ✅ 每日执行自动备份
- ✅ 每周验证备份完整性
- ✅ 每月清理过期备份
- ✅ 监控磁盘空间使用情况

### 2. 安全措施
- ✅ 备份文件加密存储（生产环境）
- ✅ 访问权限控制
- ✅ 网络安全配置
- ✅ 定期安全审计

### 3. 性能优化
- ✅ 定期执行 VACUUM ANALYZE
- ✅ 监控索引使用情况
- ✅ 优化查询性能
- ✅ 调整内存配置

### 4. 文档维护
- ✅ 记录每次重要操作
- ✅ 更新恢复程序
- ✅ 维护联系人信息
- ✅ 定期演练恢复流程

## 🔗 相关文档

- [多租户登录系统修复完成报告](./多租户登录系统修复完成报告_2025-06-25.md)
- [数据库迁移文件](../backend/src/main/resources/db/migration/)
- [Docker配置文件](../docker-compose.yml)
- [备份恢复脚本](../scripts/database-recovery.sh)

## 📞 联系信息

**技术负责人**: 开发团队  
**紧急联系**: Claude Code Assistant  
**文档维护**: 开发团队  

---

**最后更新**: 2025年6月25日 19:00  
**下次审查**: 2025年7月25日  

> 🛡️ **重要提醒**: 数据是系统的生命线，请严格按照本文档执行备份和恢复操作！