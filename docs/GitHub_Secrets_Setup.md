# GitHub Secrets 配置指南

## 📋 概述

本指南帮助您配置GitHub Actions所需的Secrets，以启用CI/CD的各项功能。

## 🚀 快速配置步骤

### 步骤1: 登录GitHub CLI

```bash
# 安装GitHub CLI（如果尚未安装）
brew install gh  # macOS
# 或访问 https://cli.github.com/ 查看其他平台安装方法

# 登录GitHub
gh auth login
# 选择 GitHub.com
# 选择 HTTPS
# 选择使用浏览器认证或粘贴token
```

### 步骤2: 准备配置文件

```bash
# 复制配置模板
cp .env.secrets.example .env.secrets

# 编辑配置文件，填写实际值
# 注意：只需配置您实际需要的功能
nano .env.secrets  # 或使用您喜欢的编辑器
```

### 步骤3: 运行配置脚本

```bash
# 运行配置向导
./scripts/setup-github-secrets.sh

# 选择选项2：从文件读取
# 脚本会自动读取.env.secrets并配置到GitHub
```

## 📝 手动配置方法

如果您更喜欢手动配置，可以通过GitHub网页界面：

1. 访问您的仓库页面
2. 点击 **Settings** → **Secrets and variables** → **Actions**
3. 点击 **New repository secret**
4. 逐个添加以下Secrets

## 🔑 Secrets说明

### 最小配置（仅CI功能）

无需配置任何Secrets即可使用：
- ✅ 代码质量检查
- ✅ 单元测试
- ✅ 安全扫描
- ✅ PR检查

### 基础配置（含Docker构建）

| Secret名称 | 说明 | 获取方式 |
|-----------|------|---------|
| DOCKER_USERNAME | Docker Hub用户名 | [注册Docker Hub](https://hub.docker.com/) |
| DOCKER_PASSWORD | Docker Hub密码 | 使用Docker Hub密码或访问令牌 |

### 部署配置

| Secret名称 | 说明 | 示例值 |
|-----------|------|--------|
| DEV_HOST | 开发服务器地址 | `dev.example.com` |
| DEV_USERNAME | SSH用户名 | `deploy` |
| DEV_SSH_KEY | SSH私钥 | 完整的私钥内容 |
| DEV_BASE_URL | 开发环境URL | `https://dev.example.com` |
| PROD_HOST | 生产服务器地址 | `prod.example.com` |
| PROD_USERNAME | SSH用户名 | `deploy` |
| PROD_SSH_KEY | SSH私钥 | 完整的私钥内容 |

### 监控工具（可选）

| Secret名称 | 说明 | 注册地址 |
|-----------|------|---------|
| CODECOV_TOKEN | 覆盖率报告 | [Codecov](https://about.codecov.io/) |
| SONAR_TOKEN | 代码质量分析 | [SonarCloud](https://sonarcloud.io/) |
| K6_CLOUD_TOKEN | 性能测试 | [K6 Cloud](https://k6.io/cloud/) |

### 通知配置（可选）

| Secret名称 | 说明 | 配置方法 |
|-----------|------|---------|
| DINGTALK_WEBHOOK | 钉钉通知 | [创建钉钉机器人](https://open.dingtalk.com/document/robots/custom-robot-access) |

## 🔧 SSH密钥生成

如果需要配置部署功能，需要生成SSH密钥：

```bash
# 生成新的SSH密钥对
ssh-keygen -t ed25519 -C "github-actions@your-project" -f ~/.ssh/github_actions_key

# 查看私钥（用于GitHub Secrets）
cat ~/.ssh/github_actions_key

# 查看公钥（添加到服务器）
cat ~/.ssh/github_actions_key.pub
```

将公钥添加到目标服务器：
```bash
# 在目标服务器上
echo "your-public-key-content" >> ~/.ssh/authorized_keys
```

## 🧪 测试配置

配置完成后，测试各项功能：

### 测试基础CI
```bash
# 创建测试分支并推送
git checkout -b test/ci-config
git push origin test/ci-config
# 查看GitHub Actions运行结果
```

### 测试Docker构建
确保配置了`DOCKER_USERNAME`和`DOCKER_PASSWORD`，然后：
```bash
git push origin develop
```

### 测试部署
确保配置了相应环境的Secrets，然后：
```bash
# 测试开发环境部署
git push origin develop

# 测试生产环境部署
git push origin main
```

## ❓ 常见问题

### 1. 如何查看已配置的Secrets？
```bash
gh secret list
```

### 2. 如何更新现有的Secret？
```bash
gh secret set SECRET_NAME -b "new-value"
```

### 3. 如何删除Secret？
```bash
gh secret remove SECRET_NAME
```

### 4. SSH连接失败？
- 确认SSH密钥格式正确（包含完整的BEGIN和END行）
- 确认服务器的`~/.ssh/authorized_keys`包含对应公钥
- 检查服务器SSH配置允许密钥认证

### 5. Docker推送失败？
- 确认Docker Hub用户名和密码正确
- 考虑使用Docker Hub的访问令牌而非密码
- 检查Docker Hub的仓库是否存在

## 📚 相关资源

- [GitHub Secrets文档](https://docs.github.com/en/actions/security-guides/encrypted-secrets)
- [GitHub CLI文档](https://cli.github.com/manual/)
- [Docker Hub访问令牌](https://docs.docker.com/docker-hub/access-tokens/)
- [SSH密钥认证](https://www.ssh.com/academy/ssh/public-key-authentication)

---

配置完成后，您的CI/CD系统就可以正常运行了！如有问题，请查看工作流日志或提交Issue。