# 测试账号和机构数据指南

**文档版本**: v1.0  
**创建日期**: 2025-06-24  
**用途**: 用于测试多租户系统的各种登录场景和权限体系

---

## 一、机构层级结构

### 1. 省级总部机构（一级）

| 机构名称 | 机构代码 | 行业类型 | 备注 |
|---------|---------|----------|------|
| 上海长护评估管理中心 | SH_HQ | 政府机构 | 上海市总部 |
| 海南健康评估总部 | HN_HQ | 政府机构 | 海南省总部 |
| 湖北省护理评估中心 | HB_HQ | 政府机构 | 湖北省总部 |

### 2. 市级分支机构（二级）

#### 上海市下属机构
| 机构名称 | 机构代码 | 上级机构 | 行业类型 |
|---------|---------|----------|----------|
| 浦东新区评估中心 | SH_PD | SH_HQ | 政府机构 |
| 徐汇区康复评估中心 | SH_XH | SH_HQ | 政府机构 |
| 静安区养老评估站 | SH_JA | SH_HQ | 政府机构 |

#### 海南省下属机构
| 机构名称 | 机构代码 | 上级机构 | 行业类型 |
|---------|---------|----------|----------|
| 海口市评估分中心 | HN_HK | HN_HQ | 政府机构 |
| 三亚市健康评估站 | HN_SY | HN_HQ | 政府机构 |

#### 湖北省下属机构
| 机构名称 | 机构代码 | 上级机构 | 行业类型 |
|---------|---------|----------|----------|
| 武汉市评估总站 | HB_WH | HB_HQ | 政府机构 |
| 宜昌市评估分中心 | HB_YC | HB_HQ | 政府机构 |

### 3. 区级/街道机构（三级）

| 机构名称 | 机构代码 | 上级机构 | 行业类型 |
|---------|---------|----------|----------|
| 陆家嘴街道评估点 | SH_PD_LJZ | SH_PD | 政府机构 |
| 张江镇评估服务站 | SH_PD_ZJ | SH_PD | 政府机构 |
| 徐家汇评估服务点 | SH_XH_XJH | SH_XH | 政府机构 |

### 4. 医疗机构

| 机构名称 | 机构代码 | 行业类型 | 级别 | 地区 |
|---------|---------|----------|------|------|
| 上海瑞金医院 | HOSP_RJ | 医疗机构 | 三甲医院 | 上海 |
| 华山医院康复科 | HOSP_HS | 医疗机构 | 三甲医院 | 上海 |
| 海南省人民医院 | HOSP_HNRM | 医疗机构 | 三甲医院 | 海南 |
| 武汉同济医院 | HOSP_TJ | 医疗机构 | 三甲医院 | 湖北 |
| 社区卫生服务中心 | HOSP_SQWS | 医疗机构 | 社区医院 | 通用 |

### 5. 养老机构

| 机构名称 | 机构代码 | 行业类型 | 规模 | 地区 |
|---------|---------|----------|------|------|
| 上海福寿康养老院 | CARE_FSK | 养老机构 | 大型连锁 | 上海 |
| 椿萱茂养老社区 | CARE_CXM | 养老机构 | 高端养老 | 全国 |
| 海南夕阳红养护院 | CARE_XYH | 养老机构 | 中型 | 海南 |
| 武汉幸福之家 | CARE_XFZJ | 养老机构 | 中型 | 湖北 |
| 阳光护理院 | CARE_YG | 养老机构 | 小型 | 通用 |

### 6. 保险公司

| 机构名称 | 机构代码 | 行业类型 | 业务范围 |
|---------|---------|----------|----------|
| 中国人寿保险 | INS_ZGRS | 保险公司 | 长护险 |
| 太平洋保险 | INS_TPYB | 保险公司 | 长护险 |
| 泰康人寿 | INS_TKRS | 保险公司 | 养老保险 |
| 平安健康险 | INS_PAJK | 保险公司 | 健康险 |

---

## 二、机构用户账号

### 1. 超级管理员（平台级）

| 用户名 | 密码 | 角色 | 权限说明 |
|--------|------|------|----------|
| superadmin | Admin@123 | 超级管理员 | 所有权限 |
| admin | Admin@123 | 平台管理员 | 平台管理权限 |

### 2. 省级机构管理员

| 机构代码 | 用户名 | 姓名 | 密码 | 角色 | 权限范围 |
|---------|--------|------|------|------|----------|
| SH_HQ | sh_admin | 张明华 | Test@123 | ADMIN | 上海全市 |
| HN_HQ | hn_admin | 李海南 | Test@123 | ADMIN | 海南全省 |
| HB_HQ | hb_admin | 王武汉 | Test@123 | ADMIN | 湖北全省 |

### 3. 市级机构用户

| 机构代码 | 用户名 | 姓名 | 密码 | 角色 | 权限范围 |
|---------|--------|------|------|------|----------|
| SH_PD | pd_admin | 陈浦东 | Test@123 | ADMIN | 浦东新区 |
| SH_PD | pd_assessor | 赵评估 | Test@123 | ASSESSOR | 浦东新区评估 |
| HN_HK | hk_manager | 刘海口 | Test@123 | ADMIN | 海口市 |
| HB_WH | wh_reviewer | 周审核 | Test@123 | REVIEWER | 武汉市审核 |

### 4. 医疗机构用户

| 机构代码 | 用户名 | 姓名 | 密码 | 角色 | 部门 |
|---------|--------|------|------|------|------|
| HOSP_RJ | rj_doctor | 王医生 | Test@123 | ASSESSOR | 康复科 |
| HOSP_RJ | rj_nurse | 李护士 | Test@123 | ASSESSOR | 护理部 |
| HOSP_HS | hs_admin | 张院长 | Test@123 | ADMIN | 院办 |
| HOSP_TJ | tj_assessor | 刘评估师 | Test@123 | ASSESSOR | 评估科 |

### 5. 养老机构用户

| 机构代码 | 用户名 | 姓名 | 密码 | 角色 | 职位 |
|---------|--------|------|------|------|------|
| CARE_FSK | fsk_admin | 孙院长 | Test@123 | ADMIN | 院长 |
| CARE_FSK | fsk_nurse | 钱护理 | Test@123 | ASSESSOR | 护理主管 |
| CARE_CXM | cxm_manager | 吴经理 | Test@123 | ADMIN | 运营经理 |
| CARE_XYH | xyh_assessor | 郑评估 | Test@123 | ASSESSOR | 评估员 |

### 6. 保险公司用户

| 机构代码 | 用户名 | 姓名 | 密码 | 角色 | 部门 |
|---------|--------|------|------|------|------|
| INS_ZGRS | zgrs_manager | 冯经理 | Test@123 | ADMIN | 长护险部 |
| INS_ZGRS | zgrs_auditor | 陈审核 | Test@123 | REVIEWER | 理赔部 |
| INS_TPYB | tpy_viewer | 杨查看 | Test@123 | VIEWER | 客服部 |

---

## 三、个人用户账号（B2C）

### 1. 免费版用户（FREE）

| 邮箱 | 姓名 | 密码 | 服务类型 | 月评估限制 |
|------|------|------|----------|-----------|
| <EMAIL> | 张三 | User@123 | FREE | 3次/月 |
| <EMAIL> | 李四 | User@123 | FREE | 3次/月 |
| <EMAIL> | 王五 | User@123 | FREE | 3次/月 |

### 2. 付费版用户（PREMIUM）

| 邮箱 | 姓名 | 密码 | 服务类型 | 月评估限制 |
|------|------|------|----------|-----------|
| <EMAIL> | 赵六 | User@123 | PREMIUM | 20次/月 |
| <EMAIL> | 孙七 | User@123 | PREMIUM | 20次/月 |
| <EMAIL> | 周八 | User@123 | PREMIUM | 20次/月 |

### 3. 专业版用户（PRO）

| 邮箱 | 姓名 | 密码 | 服务类型 | 月评估限制 |
|------|------|------|----------|-----------|
| <EMAIL> | 吴医生 | User@123 | PRO | 无限制 |
| <EMAIL> | 郑护士 | User@123 | PRO | 无限制 |
| <EMAIL> | 冯评估师 | User@123 | PRO | 无限制 |

---

## 四、测试场景指南

### 1. 多租户层级权限测试

#### 场景A：省级管理员查看下级数据
- 登录：`sh_admin@SH_HQ` / `Test@123`
- 预期：能看到上海所有区县的数据
- 测试：查看浦东、徐汇、静安的评估记录

#### 场景B：市级用户权限隔离
- 登录：`pd_admin@SH_PD` / `Test@123`
- 预期：只能看到浦东新区的数据
- 测试：尝试访问徐汇区数据（应被拒绝）

### 2. 角色权限测试

#### 场景A：评估员权限
- 登录：`pd_assessor@SH_PD` / `Test@123`
- 预期：可以创建和编辑评估，不能审核
- 测试：创建新评估，尝试审核（应无权限）

#### 场景B：审核员权限
- 登录：`wh_reviewer@HB_WH` / `Test@123`
- 预期：可以审核评估，不能创建新评估
- 测试：审核已有评估，尝试创建（应无权限）

#### 场景C：查看员权限
- 登录：`tpy_viewer@INS_TPYB` / `Test@123`
- 预期：只能查看，不能修改
- 测试：查看报告，尝试编辑（应无权限）

### 3. 跨机构类型测试

#### 场景A：医院用户
- 登录：`rj_doctor@HOSP_RJ` / `Test@123`
- 预期：医疗专业评估界面
- 测试：使用医疗评估量表

#### 场景B：养老机构用户
- 登录：`fsk_nurse@CARE_FSK` / `Test@123`
- 预期：日常护理评估界面
- 测试：使用护理评估量表

#### 场景C：保险公司用户
- 登录：`zgrs_auditor@INS_ZGRS` / `Test@123`
- 预期：理赔审核界面
- 测试：审核理赔相关评估

### 4. 个人用户测试

#### 场景A：免费用户限制
- 登录：`<EMAIL>` / `User@123`
- 预期：每月3次评估限制
- 测试：第4次评估应提示升级

#### 场景B：付费用户功能
- 登录：`<EMAIL>` / `User@123`
- 预期：20次/月，高级功能可用
- 测试：导出PDF报告功能

#### 场景C：专业用户完整功能
- 登录：`<EMAIL>` / `User@123`
- 预期：无限制，所有功能可用
- 测试：批量评估、高级分析

---

## 五、统一登录测试

### 1. 机构用户登录格式
```
用户名@机构代码
示例：sh_admin@SH_HQ
```

### 2. 个人用户登录格式
```
邮箱地址
示例：<EMAIL>
```

### 3. 自动识别测试
- 输入邮箱 → 自动识别为个人用户
- 输入用户名@机构代码 → 自动识别为机构用户

---

## 六、常见测试用例

### 1. 登录测试清单
- [ ] 超级管理员登录
- [ ] 省级管理员登录
- [ ] 市级管理员登录
- [ ] 医院用户登录
- [ ] 养老机构用户登录
- [ ] 保险公司用户登录
- [ ] 个人免费用户登录
- [ ] 个人付费用户登录
- [ ] 错误密码测试
- [ ] 不存在用户测试

### 2. 权限测试清单
- [ ] 上级查看下级数据
- [ ] 同级数据隔离
- [ ] 跨机构数据隔离
- [ ] 角色权限正确性
- [ ] 个人用户限制

### 3. 功能测试清单
- [ ] 创建评估
- [ ] 编辑评估
- [ ] 审核评估
- [ ] 导出报告
- [ ] 批量操作
- [ ] 数据统计

---

## 七、快速测试组合

### 最常用测试账号（推荐记住）

1. **机构管理员**：`sh_admin@SH_HQ` / `Test@123`（上海总部）
2. **机构评估员**：`pd_assessor@SH_PD` / `Test@123`（浦东评估员）
3. **医院医生**：`rj_doctor@HOSP_RJ` / `Test@123`（瑞金医院）
4. **养老院护士**：`fsk_nurse@CARE_FSK` / `Test@123`（福寿康）
5. **个人用户**：`<EMAIL>` / `User@123`（免费版）

---

## 八、注意事项

1. **密码规则**
   - 所有测试密码都符合：至少6位，包含大小写字母
   - 生产环境需要更强密码策略

2. **数据隔离**
   - 不同机构的数据完全隔离
   - 上级可查看下级，下级不可查看上级

3. **登录提示**
   - 机构用户必须输入完整格式：用户名@机构代码
   - 个人用户直接输入邮箱即可

4. **测试环境**
   - 所有测试账号仅用于开发测试
   - 生产环境需要重新创建真实账号

---

更新日期：2025-06-24
维护人员：开发团队