# GitHub Secrets 配置示例
# 复制此文件为 .env.secrets 并填写实际值
# 然后运行 ./scripts/setup-github-secrets.sh 选择"从文件读取"选项

# ===== 基础CI功能（可选配置） =====
# 如果不配置，基础CI功能仍可正常运行

# Docker Hub 配置（用于构建和推送镜像）
DOCKER_USERNAME=your-docker-username
DOCKER_PASSWORD=your-docker-password

# ===== 部署功能（如需部署则必须配置） =====

# 开发环境部署配置
DEV_HOST=dev.your-domain.com
DEV_USERNAME=deploy
DEV_SSH_KEY="-----BEGIN OPENSSH PRIVATE KEY-----
your-dev-ssh-private-key-content-here
-----END OPENSSH PRIVATE KEY-----"
DEV_BASE_URL=https://dev.your-domain.com

# 生产环境部署配置
PROD_HOST=prod.your-domain.com
PROD_USERNAME=deploy
PROD_SSH_KEY="-----BEGIN OPENSSH PRIVATE KEY-----
your-prod-ssh-private-key-content-here
-----END OPENSSH PRIVATE KEY-----"

# ===== 监控和测试（可选增强功能） =====

# Codecov 测试覆盖率服务
# 注册地址: https://about.codecov.io/
CODECOV_TOKEN=your-codecov-token

# SonarQube 代码质量分析
# 如果有私有SonarQube服务器
SONAR_TOKEN=your-sonarqube-token

# K6 Cloud 性能测试
# 注册地址: https://k6.io/cloud/
K6_CLOUD_TOKEN=your-k6-cloud-token

# ===== 通知配置（可选） =====

# 钉钉机器人通知
# 创建方法: https://open.dingtalk.com/document/robots/custom-robot-access
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=your-dingtalk-token