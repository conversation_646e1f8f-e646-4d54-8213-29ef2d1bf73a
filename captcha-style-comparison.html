<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码样式对比</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2px;
            background: #e9ecef;
        }
        
        .app-section {
            background: white;
            padding: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .app-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 2px solid #e9ecef;
            width: 100%;
            text-align: center;
        }
        
        .uni-app .app-title {
            color: #28a745;
            border-color: #28a745;
        }
        
        .admin-app .app-title {
            color: #007bff;
            border-color: #007bff;
        }
        
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .status-info {
            margin-top: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            width: 100%;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .status-info h4 {
            margin-bottom: 8px;
            color: #495057;
        }
        
        .status-list {
            list-style: none;
        }
        
        .status-list li {
            margin-bottom: 4px;
            padding-left: 20px;
            position: relative;
        }
        
        .status-list li::before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        
        .improvements {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px;
            text-align: center;
        }
        
        .improvements h3 {
            color: #856404;
            margin-bottom: 12px;
        }
        
        .improvements ul {
            list-style: none;
            margin: 12px 0;
        }
        
        .improvements li {
            margin: 8px 0;
            color: #856404;
        }
        
        .improvements li::before {
            content: "🎨 ";
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .app-section {
                padding: 20px;
            }
            
            iframe {
                height: 500px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 验证码样式统一优化对比</h1>
            <p>统一uni-app和管理后台的验证码样式设计</p>
        </div>
        
        <div class="improvements">
            <h3>🚀 样式优化要点</h3>
            <ul>
                <li>统一图片容器尺寸和圆角设计</li>
                <li>优化滑动条高度和渐变效果</li>
                <li>统一滑块按钮大小和悬停效果</li>
                <li>添加刷新按钮的旋转动画</li>
                <li>改进响应式设计和移动端体验</li>
            </ul>
        </div>
        
        <div class="comparison-grid">
            <div class="app-section uni-app">
                <div class="app-title">📱 uni-app 版本 (5273端口)</div>
                <iframe src="http://localhost:5273/#/pages/login/index" title="uni-app登录页面"></iframe>
                <div class="status-info">
                    <h4>特性列表：</h4>
                    <ul class="status-list">
                        <li>原生uni-app验证码组件</li>
                        <li>紧凑的布局设计</li>
                        <li>简洁的滑动条样式</li>
                        <li>适配移动端操作</li>
                    </ul>
                </div>
            </div>
            
            <div class="app-section admin-app">
                <div class="app-title">💼 管理后台版本 (5274端口)</div>
                <iframe src="http://localhost:5274/login" title="管理后台登录页面"></iframe>
                <div class="status-info">
                    <h4>优化特性：</h4>
                    <ul class="status-list">
                        <li>内联验证码组件实现</li>
                        <li>统一的视觉风格</li>
                        <li>渐变色滑动条效果</li>
                        <li>悬停动画和交互反馈</li>
                        <li>右上角旋转刷新按钮</li>
                        <li>响应式设计支持</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检查iframe加载状态
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach((iframe, index) => {
            iframe.onload = function() {
                console.log(`✅ ${index === 0 ? 'uni-app' : '管理后台'} iframe加载成功`);
            };
            iframe.onerror = function() {
                console.error(`❌ ${index === 0 ? 'uni-app' : '管理后台'} iframe加载失败`);
            };
        });
        
        // 添加实时时间显示
        const updateTime = () => {
            const now = new Date().toLocaleString('zh-CN');
            document.title = `验证码样式对比 - ${now}`;
        };
        
        updateTime();
        setInterval(updateTime, 60000); // 每分钟更新一次
    </script>
</body>
</html>