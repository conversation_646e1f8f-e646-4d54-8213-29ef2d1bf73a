# 智慧养老评估平台 (Smart Elderly Assessment Platform) - 开发指南

## 项目概述

这是一个面向养老机构、社区服务中心、医疗机构的专业老年人综合能力评估数字化平台。通过移动端应用和Web管理后台，实现老年人能力的标准化评估和智能化分析。

### 核心特性
- 多租户SaaS架构，支持机构级数据隔离
- PDF量表智能解析与数据库自动生成
- AI驱动的评估建议与分析
- 滑动验证码安全认证
- 实时数据可视化看板
- 移动端与Web端双平台支持

## 技术栈

### 后端技术栈
- **框架**: Spring Boot 3.5.3 + Java 21 LTS
- **数据库**: PostgreSQL 15 + Redis 7 + MinIO
- **安全**: Spring Security + JWT + 自研滑动验证码
- **构建**: Maven 3.9+ 
- **文档**: OpenAPI 3 + Swagger UI
- **AI集成**: LM Studio + Docling PDF解析
- **部署**: Docker + ARM64优化

### 前端技术栈
- **管理后台**: Vue 3 + TypeScript + Element Plus + Vite
- **移动端**: uni-app + Vue 3 + WeUI
- **状态管理**: Pinia + Vuex
- **样式**: TailwindCSS + SCSS
- **构建**: Vite 5.4+ + TypeScript 5.3+

## 项目结构

### 根目录结构
```
Assessment/
├── backend/                 # Spring Boot后端服务
├── frontend/               # 前端项目集合
│   ├── admin/             # Vue3管理后台
│   ├── uni-app/           # uni-app移动端
│   └── shared/            # 共享组件
├── docker/                # Docker配置与脚本
├── docs/                  # 项目文档
├── scripts/               # 自动化脚本
├── .github/               # CI/CD配置
├── nginx/                 # Nginx配置
└── test_files/            # 测试文件
```

### 后端包结构
```
com.assessment/
├── config/                # 配置类
├── controller/            # REST控制器
├── dto/                   # 数据传输对象
├── entity/                # JPA实体类
├── repository/            # 数据访问层
├── service/               # 业务逻辑层
├── security/              # 安全配置
├── pdf/                   # PDF解析服务
└── util/                  # 工具类
```

## 开发环境设置

### 必需软件
- **Java**: OpenJDK 21 LTS
- **Node.js**: 18.0+ (推荐使用 nvm)
- **Docker**: 20.10+ (支持ARM64)
- **PostgreSQL**: 15+ 
- **Redis**: 7+
- **Git**: 2.0+

### 快速启动
```bash
# 1. 环境检查（M4 Mac优化）
./scripts/check-m4-compatibility.sh

# 2. 启动基础服务
docker-compose up -d postgres redis minio

# 3. 启动后端
cd backend && ./mvnw spring-boot:run

# 4. 启动管理后台
cd frontend/admin && npm run dev

# 5. 启动移动端
cd frontend/uni-app && npm run dev:h5
```

### 环境配置
- 复制 `.env.example` 到 `.env` 并配置必要参数
- 数据库默认端口: 5433 (避免与系统PostgreSQL冲突)
- 后端服务端口: 8181
- 管理后台端口: 5274
- 移动端H5端口: 5273

## 代码规范与质量

### 语言要求
- **交流语言**: 所有代码注释、文档说明、用户交流必须使用中文
- **Git提交**: 可使用中英文混合
- **变量命名**: 使用英文，遵循驼峰命名法

### Java后端规范
- **代码风格**: 严格遵循Checkstyle规范
- **行长度**: 最大120字符
- **方法长度**: 最大50行
- **参数数量**: 最大7个参数
- **构造函数**: 工具类必须添加私有构造函数
- **参数声明**: 所有方法参数必须声明为final
- **操作符换行**: 操作符必须在新行开头，不能在行末
- **大括号空格**: 使用 `{ }` 而不是 `{}`
- **日志**: 使用@Slf4j，禁用System.out.println
- **注释**: 所有代码注释使用中文

#### Checkstyle具体规范
```java
// ✅ 正确：工具类私有构造函数
public class UtilityClass {
    private UtilityClass() {
        // 工具类，隐藏构造函数
    }
}

// ✅ 正确：方法参数声明为final
public void method(final String param1, final int param2) {
    // 方法实现
}

// ✅ 正确：操作符换行
String result = "长字符串"
              + "另一部分"
              + "第三部分";

// ✅ 正确：大括号空格
new Exception("message") { }

// ❌ 错误示例
new Exception("message") {}  // 缺少空格
String result = "长字符串" +  // 操作符在行末
              "另一部分";
```

#### Spring Boot开发模式
```java
// ✅ Controller模式
@RestController
@RequestMapping("/api/resource")
@Slf4j
public class ResourceController {
    
    @PostMapping
    public ResponseEntity<ApiResponse<ResourceDTO>> create(
            @Valid @RequestBody final CreateResourceRequest request) {
        // 实现逻辑
    }
}

// ✅ Service模式
@Service
@Transactional
@RequiredArgsConstructor
public class ResourceService {
    
    private final ResourceRepository repository;
    
    public ResourceDTO create(final CreateResourceRequest request) {
        // 业务逻辑
    }
}

// ✅ Repository模式
@Repository
public interface ResourceRepository extends JpaRepository<Resource, Long> {
    
    @Query("SELECT r FROM Resource r WHERE r.tenantId = :tenantId")
    List<Resource> findByTenantId(@Param("tenantId") Long tenantId);
}
```

### 前端规范
- **代码风格**: ESLint + Prettier
- **TypeScript**: 严格模式，完整类型定义
- **组件命名**: PascalCase
- **文件命名**: kebab-case
- **样式**: 优先使用TailwindCSS，SCSS作为补充
- **状态管理**: Pinia (新代码) + Vuex (兼容)

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构
test: 测试相关
chore: 构建/工具链更新
```

## 核心功能模块

### 1. 多租户架构
- **数据隔离**: 基于organization_id的逻辑隔离
- **权限控制**: 基于角色的访问控制(RBAC)
- **租户管理**: 支持租户层级结构
- **缓存策略**: Redis分布式缓存，租户级别隔离

### 2. PDF智能解析
- **解析引擎**: Docling + AI大模型
- **支持格式**: PDF评估量表自动识别
- **数据提取**: 表格结构智能识别
- **数据库生成**: 自动生成对应数据库表结构

### 3. AI评估分析
- **模型集成**: LM Studio本地部署
- **支持模型**: DeepSeek-R1, Qwen, Gemma等
- **分析功能**: 评估结果智能分析与建议生成
- **流式输出**: 支持实时流式响应

### 4. 安全认证
- **JWT认证**: 访问令牌 + 刷新令牌机制
- **滑动验证码**: 自研验证码系统，支持图片拼图
- **密码策略**: 强密码要求，登录失败锁定
- **CORS配置**: 跨域请求安全控制

#### 安全实现模式
```java
// ✅ JWT Token Provider
@Component
@RequiredArgsConstructor
public class JwtTokenProvider {
    
    @Value("${jwt.secret}")
    private String jwtSecret;
    
    public String generateToken(final UserDetails userDetails) {
        // Token生成逻辑
    }
}

// ✅ Security Configuration
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(final HttpSecurity http) throws Exception {
        return http
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(STATELESS))
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/auth/**").permitAll()
                .anyRequest().authenticated()
            )
            .build();
    }
}

// ✅ 多租户数据隔离
@Entity
@Table(name = "tenant_data")
public class TenantData extends BaseEntity {
    
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;
    
    // 其他字段
}
```

## 数据库设计

### 核心表结构
- **tenant**: 租户信息表
- **platform_user**: 平台用户表
- **tenant_user_membership**: 租户用户关系表
- **assessment_subject**: 评估对象表
- **tenant_assessment_record**: 评估记录表
- **global_scale_registry**: 全局量表注册表

### 数据迁移
- 使用Flyway进行版本化数据库迁移
- 迁移文件位置: `backend/src/main/resources/db/migration/`
- 命名规范: `V{版本号}__{描述}.sql`

## 测试策略

### 后端测试
- **单元测试**: JUnit 5 + Mockito
- **集成测试**: @SpringBootTest + TestContainers
- **覆盖率**: JaCoCo，目标覆盖率80%+
- **测试配置**: application-test.yml

### 前端测试
- **单元测试**: Vitest + Vue Test Utils
- **E2E测试**: 计划使用Playwright
- **组件测试**: 重点测试业务组件

### 质量检查工具
- **Java**: Checkstyle + SpotBugs + PMD
- **JavaScript/TypeScript**: ESLint + Prettier
- **安全扫描**: OWASP Dependency Check

## CI/CD流程

### GitHub Actions工作流
- **代码质量检查**: 每次PR触发
- **自动化测试**: 单元测试 + 集成测试
- **安全扫描**: 依赖漏洞检查
- **构建部署**: 自动构建Docker镜像
- **覆盖率报告**: Codecov集成

### 部署环境
- **开发环境**: 本地Docker Compose
- **测试环境**: GitHub Actions自动部署
- **生产环境**: Docker Swarm/Kubernetes

## 性能优化

### 后端优化
- **数据库连接池**: HikariCP优化配置
- **Redis缓存**: 多级缓存策略
- **异步处理**: @Async注解，线程池配置
- **分页查询**: 避免大数据量查询

### 前端优化
- **代码分割**: Vite动态导入
- **资源压缩**: Gzip压缩
- **缓存策略**: 浏览器缓存 + CDN
- **懒加载**: 图片和组件懒加载

## 安全最佳实践

### 数据安全
- **敏感数据加密**: 密码BCrypt加密
- **SQL注入防护**: JPA参数化查询
- **XSS防护**: 输入验证和输出编码
- **CSRF防护**: Spring Security CSRF保护

### 接口安全
- **认证授权**: JWT + 角色权限控制
- **请求限流**: Redis + 注解实现
- **参数校验**: Bean Validation
- **错误处理**: 统一异常处理，避免信息泄露

## 监控与日志

### 应用监控
- **健康检查**: Spring Boot Actuator
- **指标收集**: Micrometer + Prometheus
- **性能监控**: JVM指标监控
- **业务指标**: 自定义业务监控指标

### 日志管理
- **日志框架**: Logback + SLF4J
- **日志级别**: 开发DEBUG，生产INFO
- **日志格式**: 结构化JSON格式
- **日志轮转**: 按大小和时间轮转

## 常见问题解决

### 开发环境问题
- **端口冲突**: 使用非标准端口(5433, 8181等)
- **M4 Mac兼容**: 使用ARM64镜像
- **内存不足**: 调整JVM参数和Docker内存限制
- **权限问题**: 检查文件权限和Docker权限

### 构建问题
- **Maven依赖**: 使用阿里云镜像加速
- **Node.js依赖**: 使用npm/yarn缓存
- **Docker构建**: 使用多阶段构建优化
- **TypeScript编译**: 检查tsconfig.json配置

### 运行时问题
- **数据库连接**: 检查连接池配置
- **Redis连接**: 检查密码和网络配置
- **文件上传**: 检查MinIO配置和权限
- **AI服务**: 检查LM Studio服务状态

### 调试技巧
- **后端调试**: 使用IDE断点调试，查看application.yml日志级别
- **前端调试**: 使用浏览器开发者工具，检查网络请求
- **数据库调试**: 使用pgAdmin或命令行工具检查数据
- **缓存调试**: 使用Redis CLI检查缓存状态
- **容器调试**: 使用docker logs查看容器日志

### 性能分析
- **后端性能**: 使用Spring Boot Actuator监控端点
- **数据库性能**: 使用EXPLAIN分析SQL查询
- **前端性能**: 使用Chrome DevTools Performance面板
- **网络性能**: 检查API响应时间和数据传输大小

## 开发工作流

### 任务管理
- **任务跟踪**: 使用GitHub Issues和Project Board
- **优先级**: P0(关键) > P1(重要) > P2(一般) > P3(次要)
- **状态管理**: TODO → IN_PROGRESS → REVIEW → DONE
- **时间估算**: 使用Story Points进行工作量估算

### 分支策略
- **main**: 生产分支，受保护
- **develop**: 开发主分支
- **feature/***: 功能分支
- **hotfix/***: 紧急修复分支

### 代码审查
- **PR要求**: 所有代码必须通过PR合并
- **审查清单**: 代码质量、测试覆盖、文档更新
- **自动检查**: CI/CD自动运行质量检查
- **手动审查**: 至少一人审查通过

### 发布流程
1. 功能开发完成，创建PR到develop
2. 代码审查通过，合并到develop
3. 集成测试通过，创建release分支
4. 部署到测试环境验证
5. 合并到main分支，打tag发布

## 文档资源

### 核心文档
- **快速开始**: `QUICK_START.md`
- **开发计划**: `docs/plan/Development_Plan.md`
- **API文档**: http://localhost:8181/swagger-ui.html
- **数据库设计**: `docs/PDF_to_Database_Guide.md`

### 技术文档
- **LM Studio配置**: `docs/lm-studio-configuration-guide.md`
- **CI/CD指南**: `docs/CI_CD_Usage_Guide.md`
- **代码质量**: `docs/Code_Quality_Guide.md`
- **安全配置**: `.github/SECURITY.md`

## 联系与支持

### 开发团队
- **项目负责人**: 查看CODEOWNERS文件
- **技术支持**: 创建GitHub Issue
- **文档贡献**: 提交PR到docs目录

### 外部资源
- **Spring Boot文档**: https://spring.io/projects/spring-boot
- **Vue.js文档**: https://vuejs.org/
- **uni-app文档**: https://uniapp.dcloud.net.cn/
- **Element Plus文档**: https://element-plus.org/

---

**注意**: 本项目是生产级医疗健康平台，请在所有代码决策中优先考虑安全性、可靠性和可维护性。