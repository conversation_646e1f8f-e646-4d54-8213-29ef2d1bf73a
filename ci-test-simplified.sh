#!/bin/bash

# 简化的 CI/CD 测试脚本
set -e

echo "========================================="
echo "     CI/CD 核心功能测试"
echo "========================================="

# 测试计数器
PASS=0
FAIL=0

test_step() {
    local name="$1"
    local cmd="$2"
    
    echo -e "\n🔧 测试: $name"
    if eval "$cmd" > /dev/null 2>&1; then
        echo "✅ $name - 通过"
        ((PASS++))
    else
        echo "❌ $name - 失败"
        ((FAIL++))
    fi
}

# 核心测试步骤
echo -e "\n📋 开始核心测试..."

# 1. 后端构建测试
cd backend
test_step "后端编译" "./mvnw clean compile -q"
test_step "后端测试" "./mvnw test -q -Dspring.profiles.active=test"
test_step "后端打包" "./mvnw package -q -DskipTests"
cd ..

# 2. 前端构建测试
cd frontend/admin
test_step "前端依赖安装" "npm ci --silent"
test_step "前端类型检查" "npx vue-tsc --noEmit"
test_step "前端构建" "npm run build --silent"
cd ../..

# 3. Docker 构建测试（如果可用）
if command -v docker &> /dev/null; then
    # 创建简单的 Dockerfile（如果不存在）
    if [ ! -f "docker/Dockerfile.backend" ]; then
        mkdir -p docker
        cat > docker/Dockerfile.backend << 'EOF'
FROM eclipse-temurin:21-jre-alpine
WORKDIR /app
COPY backend/target/*.jar app.jar
EXPOSE 8081
ENTRYPOINT ["java", "-jar", "app.jar"]
EOF
    fi
    test_step "Docker 镜像构建" "docker build -f docker/Dockerfile.backend -t assessment-test:latest . --quiet"
else
    echo "⚠️  Docker 未安装，跳过容器测试"
fi

# 结果汇总
echo -e "\n========================================="
echo "            测试结果"
echo "========================================="
echo "✅ 通过: $PASS"
echo "❌ 失败: $FAIL"

if [ $FAIL -eq 0 ]; then
    echo -e "\n🎉 所有核心测试通过！CI/CD 工作流准备就绪。"
    echo -e "\n建议的下一步:"
    echo "1. 推送代码到 GitHub 触发 CI/CD"
    echo "2. 查看 GitHub Actions 的执行结果"
    echo "3. 根据需要调整工作流配置"
    exit 0
else
    echo -e "\n⚠️  有 $FAIL 个测试失败。请检查并修复问题。"
    exit 1
fi