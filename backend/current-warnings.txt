[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/DoclingProperties.java:19:26: 参数： service 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/DoclingProperties.java:34:22: '{' 后应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/DoclingProperties.java:34:23: '}' 前应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/DoclingProperties.java:36:20: 参数： other 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/GlobalScaleRegistry.java:143:19: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/GlobalScaleRegistry.java:160:16: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/GlobalScaleRegistry.java:178:17: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/GlobalScaleRegistry.java:196:28: 参数： newRating 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/AssessmentSubject.java:118:12: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/PlatformUser.java:92:23: 参数： other 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/PlatformUser.java:119:18: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:130:33: 参数： other 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:167:15: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:185:20: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:205:18: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:226:23: 参数： reviewNotes 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:226:30: 'reviewNotes' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:226:43: 参数： reviewerId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:226:50: 'reviewerId' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:238:22: 参数： reviewNotes 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:238:29: 'reviewNotes' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:238:42: 参数： reviewerId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:238:49: 'reviewerId' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:292:34: 参数： qualityScore 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:292:45: 'qualityScore' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:292:59: 参数： completenessScore 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:292:70: 'completenessScore' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantAssessmentRecord.java:299:32: 参数： stage 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantUserMembership.java:95:31: 参数： other 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantUserMembership.java:134:16: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantUserMembership.java:134:36: 参数： defaultPermissions 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantUserMembership.java:157:22: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/multitenant/TenantUserMembership.java:169:32: 参数： permission 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:41:28: 参数： createdAt 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:49:28: 参数： updatedAt 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:57:28: 参数： createdBy 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:65:28: 参数： updatedBy 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/IndividualUser.java:243:41: '5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/IndividualUser.java:256:33: 参数： loginIp 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/IndividualUser.java:308:34: 参数： expiresAt 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/IndividualUser.java:316:30: 参数： expiresAt 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentSection.java:23:28: 参数： questions 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentSection.java:27:27: 参数： question 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/QuestionType.java:13:16: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:31:26: 参数： headers 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:39:23: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:56:15: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:22:46: 参数： document 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:52:55: 参数： pageText 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:52:72: 参数： pageNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:106:47: 参数： rows 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:106:72: 参数： pageNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:130:30: 参数： line 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:136:38: 参数： line 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:159:55: 参数： rows 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:207:36: 参数： rows 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:219:39: 参数： table 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFContent.java:31:25: 参数： tables 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFContent.java:39:25: 参数： images 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:35:27: 参数： metadata 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:40:27: 参数： sections 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:45:31: 参数： scoringRules 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:31:55: 参数： pdfFile 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:74:31: 参数： pdfFile 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:104:7: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:104:38: 参数： schema 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:104:55: 参数： scoringRules 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:104:78: 参数： sourcePdfPath 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:124:36: 参数： title 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:132:46: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:141:37: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:146:37: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:151:42: 参数： typeString 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:18:25: 参数： option 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:55:21: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:59:24: 参数： title 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:63:23: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:67:23: 参数： type 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:71:27: 参数： required 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:75:24: 参数： order 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:79:26: 参数： options 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemUserController.java:256:3: 方法 manageTenantMembership 56 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:78:61: '7' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:84:62: '30' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:115:56: '60' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:116:55: '25' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:117:56: '15' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:125:68: '30' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:153:64: '30' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:172:56: '70' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:174:58: '5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:175:61: '5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:179:68: '30' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:190:3: 方法 getScaleStats 53 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:283:45: '120' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:284:41: '300' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:338:47: '0.3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:338:54: '0.4' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:338:80: '0.6' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:349:24: '0.8' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:351:31: '0.6' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:353:31: '0.4' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:398:63: '5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:446:45: '4.2' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:447:47: '78.5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:448:45: '92.3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:451:35: '45' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:452:35: '32' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:453:35: '15' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemDashboardController.java:454:35: '6' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/CaptchaController.java:69: 本行字符数 122个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/CaptchaController.java:90: 本行字符数 122个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemScaleController.java:114:3: 方法 createScale 68 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemScaleController.java:144: 本行字符数 130个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemScaleController.java:187:3: 可执行语句 33 条 （最多： 30 条）。 [ExecutableStatementCount]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemScaleController.java:187:3: 方法 updateScale 68 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemScaleController.java:206: 本行字符数 131个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemTenantController.java:44:3: 方法 getTenants 54 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/UnifiedAuthController.java:239:39: '300' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemAssessmentController.java:54:3: 方法 getAssessments 54 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemAssessmentController.java:73: 本行字符数 123个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemAssessmentController.java:76: 本行字符数 131个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemAssessmentController.java:79: 本行字符数 125个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/SystemAssessmentController.java:251:45: '1000' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:35:26: '{' 后应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:35:27: '}' 前应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:37:24: 参数： success 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:37:41: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:37:49: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:43:24: 参数： success 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:43:41: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:43:49: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:43:65: 参数： errorCode 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:53:46: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:60:46: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:60:54: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:67:44: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:74:44: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/common/response/ApiResponse.java:74:60: 参数： errorCode 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:145:36: '5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:206:36: '600000' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:208:47: '60000' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:306: 本行字符数 123个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:347:29: '240' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:347:34: '248' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:348:54: '230' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:348:59: '230' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:348:64: '250' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:372:76: '0.95f' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:375:45: '60' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:376:49: '8' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:377:72: '10000' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:378:57: '60' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:378:78: '8' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:387:45: '30' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:388:37: '0.5f' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:430: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:444:38: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:444:41: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:445:36: '9' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:474:82: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:503:34: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:504:38: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:554:30: '8' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:554:37: '8' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:554:44: '8' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:573:32: '256' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:574:32: '256' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:575:32: '256' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:578:49: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:578:72: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:582:29: '5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/SimpleCaptchaService.java:583:49: '30' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/MultiTenantAuthService.java:103:37: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/MultiTenantAuthService.java:145:20: '3600' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/MultiTenantAuthService.java:243:20: '3600' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/DatabaseService.java:128: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/DoclingService.java:42:3: 既然类 'DoclingService' 设计为可继承的，应为可重写的非空方法 'initHttpClient' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:71:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'getTenantId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:71:37: 第 37 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:72:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'setTenantId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:72:50: 第 50 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:74:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'getTenantCode' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:74:39: 第 39 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:75:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'setTenantCode' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:75:54: 第 54 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:77:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'getTenantName' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:77:39: 第 39 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:78:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'setTenantName' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:78:54: 第 54 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:80:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'getOrganizationType' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:80:55: 第 55 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:81:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'setOrganizationType' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:81:76: 第 76 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:83:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'getLevel' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:83:35: 第 35 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:84:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'setLevel' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:84:45: 第 45 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:86:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'getParentTenantId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:86:43: 第 43 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:87:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'setParentTenantId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:87:62: 第 62 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:89:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'getChildren' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:89:56: 第 56 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:90:9: 既然类 'TenantHierarchyNode' 设计为可继承的，应为可重写的非空方法 'setChildren' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:90:69: 第 69 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantHierarchyService.java:212: 本行字符数 135个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/JwtTokenService.java:127:65: '1000L' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/JwtTokenService.java:248:27: '32' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/JwtTokenService.java:250:37: '32' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/JwtTokenService.java:251:27: '32' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/IndividualUserService.java:104: 本行字符数 125个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/IndividualUserService.java:285:40: '5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/IndividualUserService.java:381:28: '3600' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantCacheService.java:80:105: '{' 后应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantCacheService.java:80:106: '}' 前应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantCacheService.java:116:105: '{' 后应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantCacheService.java:116:106: '}' 前应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantCacheService.java:233:61: '7' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantCacheService.java:258:65: '9' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/TenantCacheService.java:344:61: '30' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/scoring/DefaultScoringStrategy.java:81:62: ';' 后不应有空格。 [EmptyForIteratorPad]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/scoring/DefaultScoringStrategy.java:122:64: ';' 后不应有空格。 [EmptyForIteratorPad]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/ScaleAnalysisService.java:276:44: '4' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/ScaleAnalysisService.java:350:67: '4' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/ScaleAnalysisService.java:357:38: '4' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/ScaleAnalysisService.java:383:25: '4' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/ScaleAnalysisService.java:384:25: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/LMStudioService.java:59:3: 既然类 'LMStudioService' 设计为可继承的，应为可重写的非空方法 'initialize' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:36: 本行字符数 141个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:52:9: 既然类 'UserNameInfo' 设计为可继承的，应为可重写的非空方法 'getNamePrefix' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:52:39: 第 39 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:53:9: 既然类 'UserNameInfo' 设计为可继承的，应为可重写的非空方法 'getIdentifier' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:53:39: 第 39 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:54:9: 既然类 'UserNameInfo' 设计为可继承的，应为可重写的非空方法 'getFullUsername' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:54:41: 第 41 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:60:57: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:68:55: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:76:62: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:103:9: 既然类 'TenantCodeInfo' 设计为可继承的，应为可重写的非空方法 'getCode' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:103:33: 第 33 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:104:9: 既然类 'TenantCodeInfo' 设计为可继承的，应为可重写的非空方法 'getType' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:104:37: 第 37 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:105:9: 既然类 'TenantCodeInfo' 设计为可继承的，应为可重写的非空方法 'getLevel' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:105:31: 第 31 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:106:9: 既然类 'TenantCodeInfo' 设计为可继承的，应为可重写的非空方法 'getRegion' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:106:35: 第 35 个字符 '{' 后应换行。 [LeftCurly]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:111:25: ',' 后应有空格。 [WhitespaceAfter]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:221:30: '6' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:223:50: '4' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:224:46: '4' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:224:49: '6' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:254:31: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:287:73: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:288:76: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:373:53: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:373:55: '?' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:374:90: ':' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:384:58: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:393:48: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:393:83: '9' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:400:65: '?' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserIdentityService.java:412:51: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/PDFParsingException.java:6:30: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/PDFParsingException.java:10:30: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/PDFParsingException.java:10:46: 参数： cause 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/FieldMappingException.java:6:32: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/FieldMappingException.java:10:32: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/FieldMappingException.java:10:48: 参数： cause 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/FieldMappingException.java:14:32: 参数： cause 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/MarkdownAnalysisException.java:5:36: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/MarkdownAnalysisException.java:5:52: 参数： cause 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/GlobalExceptionHandler.java:18:67: 参数： ex 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/GlobalExceptionHandler.java:27:7: 参数： ex 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/GlobalExceptionHandler.java:40:60: 参数： ex 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/PasswordGenerator.java:3:1: 类 PasswordGenerator 应定义为 final 的。 [FinalClass]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/dto/DTOBuilderTest.java:138:5: 方法 testAssessmentScaleDTOJsonFields 69 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:83:5: 既然类 'MultiTenantIsolationTestSuite' 设计为可继承的，应为可重写的非空方法 'setUp' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:465:33: 参数： code 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:465:46: 参数： name 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:493:37: 参数： username 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:493:54: 参数： email 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:515:51: 参数： tenant 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:515:66: 参数： user 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:516:49: 参数： role 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:531:55: 参数： tenant 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:531:70: 参数： name 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:531:83: 参数： idNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:550:59: 参数： tenant 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:550:74: 参数： subject 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:551:57: 参数： assessor 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/multitenant/MultiTenantIsolationTestSuite.java:551:80: 参数： recordNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/util/TestDataFactory.java:15:1: 工具类不应有 public 或 default 构造器。 [HideUtilityClassConstructor]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/security/JwtTokenProviderBasicTest.java:35: 本行字符数 125个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/integration/PdfScaleParsingIntegrationTest.java:370:46: 参数： filename 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/integration/PdfScaleParsingIntegrationTest.java:370:63: 参数： content 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/PDFParserServiceBasicTest.java:296:45: 参数： filename 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/PDFParserServiceBasicTest.java:296:62: 参数： content 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/PDFParserServiceBasicTest.java:350:51: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:78:39: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:79:27: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:80:36: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:81:40: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:82:40: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:83:40: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:84:22: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:85:28: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:86:22: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:87:27: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:88:39: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:89:49: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:90:50: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:93: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:122:48: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:123:26: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:124:27: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:125:32: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:126:35: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:129: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:134: 本行字符数 124个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:146:42: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:147:32: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:150: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:291: 本行字符数 123个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:307: 本行字符数 122个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:443: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceExtensiveTest.java:448: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceBasicTest.java:252: 本行字符数 123个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceBasicTest.java:260: 本行字符数 125个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceBasicTest.java:268: 本行字符数 123个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceBasicTest.java:276: 本行字符数 123个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceBasicTest.java:279: 本行字符数 123个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceBasicTest.java:286: 本行字符数 127个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceBasicTest.java:390:64: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/pdf/extractor/TableExtractionServiceBasicTest.java:394:71: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/controller/MultiTenantScaleControllerComprehensiveTest.java:116: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/controller/MultiTenantScaleControllerComprehensiveTest.java:300: 本行字符数 122个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/controller/MultiTenantScaleControllerComprehensiveTest.java:320: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/controller/MultiTenantScaleControllerComprehensiveTest.java:356: 本行字符数 123个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/controller/MultiTenantScaleControllerSimpleTest.java:222: 本行字符数 122个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/controller/MultiTenantScaleControllerSimpleTest.java:243: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/AssessmentServiceExtensiveTest.java:151: 本行字符数 139个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/AssessmentServiceExtensiveTest.java:284: 本行字符数 122个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/AssessmentServiceExtensiveTest.java:340: 本行字符数 143个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/AssessmentServiceExtensiveTest.java:355: 本行字符数 143个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/DatabaseServiceTest.java:458:58: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/DatabaseServiceTest.java:459:43: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/DatabaseServiceTest.java:460:84: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/DatabaseServiceTest.java:461:99: '+' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/AIAnalysisServiceComprehensiveTest.java:114: 本行字符数 129个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/AIAnalysisServiceComprehensiveTest.java:328: 本行字符数 138个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/AssessmentServiceTest.java:67: 本行字符数 124个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/scoring/DefaultScoringStrategyTest.java:53: 本行字符数 134个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/JwtTokenServiceTest.java:39: 本行字符数 131个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/DoclingServiceTest.java:155: 本行字符数 133个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/DoclingServiceTest.java:205: 本行字符数 176个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/service/LMStudioServiceComprehensiveTest.java:358: 本行字符数 122个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/exception/GlobalExceptionHandlerTest.java:112: 本行字符数 129个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/exception/GlobalExceptionHandlerTest.java:131: 本行字符数 129个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/exception/GlobalExceptionHandlerTest.java:151: 本行字符数 129个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/exception/GlobalExceptionHandlerTest.java:230: 本行字符数 145个，最多：120个。 [LineLength]
