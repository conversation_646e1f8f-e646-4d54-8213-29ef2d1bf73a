<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码缓存测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .captcha-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .captcha-image {
            border: 2px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .timestamp {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>验证码缓存测试工具</h1>
    
    <div class="test-section">
        <h2>测试目的</h2>
        <div class="info">
            <p>这个页面帮助检测验证码图片的缓存问题：</p>
            <ul>
                <li>检查每次请求是否生成新的验证码图片</li>
                <li>验证浏览器是否缓存了验证码图片</li>
                <li>确认后端样式改变是否生效</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>验证码测试</h2>
        <button onclick="loadCaptcha()">获取新验证码</button>
        <button onclick="loadCaptchaWithTimestamp()">获取验证码(带时间戳)</button>
        <button onclick="clearImages()">清空图片</button>
        <button onclick="forceRefresh()">强制刷新页面</button>
        
        <div id="captcha-info" class="info" style="display: none;">
            <p><strong>Token:</strong> <span id="token"></span></p>
            <p><strong>请求时间:</strong> <span id="request-time"></span></p>
            <p><strong>图片大小:</strong> <span id="image-size"></span></p>
        </div>
        
        <div class="captcha-container" id="captcha-container">
            <!-- 验证码图片将在这里显示 -->
        </div>
        
        <div id="log" style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 0.9em; max-height: 300px; overflow-y: auto;">
            <h3>请求日志:</h3>
        </div>
    </div>

    <script>
        let requestCount = 0;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div class="timestamp">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function loadCaptcha(withTimestamp = false) {
            requestCount++;
            const url = withTimestamp 
                ? `http://localhost:8181/api/captcha/get?t=${Date.now()}`
                : 'http://localhost:8181/api/captcha/get';
                
            log(`#${requestCount} 请求验证码: ${url}`);
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`#${requestCount} 响应成功，Token: ${data.data.token.substring(0, 8)}...`);
                
                displayCaptcha(data.data, requestCount);
                updateInfo(data.data);
                
            } catch (error) {
                log(`#${requestCount} 请求失败: ${error.message}`);
                console.error('验证码请求失败:', error);
            }
        }
        
        function loadCaptchaWithTimestamp() {
            loadCaptcha(true);
        }
        
        function displayCaptcha(data, index) {
            const container = document.getElementById('captcha-container');
            
            const captchaDiv = document.createElement('div');
            captchaDiv.style.border = '2px solid #ddd';
            captchaDiv.style.borderRadius = '4px';
            captchaDiv.style.padding = '10px';
            captchaDiv.style.background = 'white';
            captchaDiv.style.minWidth = '320px';
            
            captchaDiv.innerHTML = `
                <h4>验证码 #${index}</h4>
                <p style="font-size: 0.8em; color: #666;">Token: ${data.token.substring(0, 12)}...</p>
                <div style="position: relative; width: 310px; height: 155px; margin: 10px 0;">
                    <img src="data:image/png;base64,${data.originalImageBase64}" 
                         style="width: 310px; height: 155px; display: block;" 
                         alt="背景图" />
                    <div style="position: absolute; top: 0; left: 0; width: 47px; height: 155px;">
                        <img src="data:image/png;base64,${data.jigsawImageBase64}" 
                             style="width: 47px; height: 155px; display: block;" 
                             alt="滑块" />
                    </div>
                </div>
                <p style="font-size: 0.8em; color: #666;">生成时间: ${new Date().toLocaleTimeString()}</p>
            `;
            
            container.appendChild(captchaDiv);
            
            // 限制显示的验证码数量
            if (container.children.length > 5) {
                container.removeChild(container.firstChild);
            }
        }
        
        function updateInfo(data) {
            document.getElementById('captcha-info').style.display = 'block';
            document.getElementById('token').textContent = data.token;
            document.getElementById('request-time').textContent = new Date().toLocaleString();
            document.getElementById('image-size').textContent = 
                `背景图: ${Math.round(data.originalImageBase64.length * 0.75)} 字节, ` +
                `滑块: ${Math.round(data.jigsawImageBase64.length * 0.75)} 字节`;
        }
        
        function clearImages() {
            document.getElementById('captcha-container').innerHTML = '';
            log('已清空所有验证码图片');
        }
        
        function forceRefresh() {
            // 清除缓存并刷新页面
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            window.location.reload(true);
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            log('页面加载完成，可以开始测试验证码');
            
            // 检查浏览器缓存相关信息
            log(`用户代理: ${navigator.userAgent.substring(0, 50)}...`);
            log(`缓存API支持: ${'caches' in window ? '是' : '否'}`);
            log(`Service Worker支持: ${'serviceWorker' in navigator ? '是' : '否'}`);
        };
    </script>
</body>
</html>