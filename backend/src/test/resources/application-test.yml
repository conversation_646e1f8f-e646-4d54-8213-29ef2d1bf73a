spring:
  application:
    name: elderly-assessment-platform-test

  # 允许Bean定义覆盖，用于测试环境
  main:
    allow-bean-definition-overriding: true

  # 使用H2内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver

  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        '[format_sql]': false
        '[show_sql]': false

  # 禁用Redis、Flyway和安全相关自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration
      - org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
  
  # 禁用Flyway迁移（测试使用ddl-auto）
  flyway:
    enabled: false

# 测试日志配置
minio:
  endpoint: http://localhost:9000
  access-key: test
  secret-key: test
  bucket-name: test-bucket
  secure: false

jwt:
  secret: test-secret-key-for-testing-only-must-be-long-enough
  expiration: 86400000

# 禁用Actuator
management:
  endpoints:
    access:
      default: none

# # 测试日志配置
# logging:
#   level:
#     root: WARN
#     com.assessment: INFO
#     org.springframework: WARN
