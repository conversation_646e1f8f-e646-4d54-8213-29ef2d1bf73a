import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public final class PasswordGenerator {
    
    private PasswordGenerator() {
        // 工具类，隐藏构造函数
    }
    
    public static void main(final String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "123456";
        String encoded = encoder.encode(password);
        System.out.println("Password: " + password);
        System.out.println("BCrypt hash: " + encoded);
        
        // 验证
        boolean matches = encoder.matches(password, encoded);
        System.out.println("Verification: " + matches);
    }
}