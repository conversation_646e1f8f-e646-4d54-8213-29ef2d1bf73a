package com.assessment.controller;

import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.TenantUserMembership;
import com.assessment.controller.config.ControllerTestConfig;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.http.MediaType;

import java.util.ArrayList;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;

/**
 * SystemUserController 基础测试类
 */
@WebMvcTest(SystemUserController.class)
@Import(ControllerTestConfig.class)
@DisplayName("系统用户控制器基础测试")
@SuppressWarnings("removal")
class SystemUserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PlatformUserRepository userRepository;

    @MockBean
    private TenantRepository tenantRepository;

    @MockBean
    private TenantUserMembershipRepository membershipRepository;


    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户列表 - 基础HTTP测试")
    void testGetUsers_HttpEndpoint() throws Exception {
        // 准备数据
        Page<PlatformUser> emptyPage = new PageImpl<>(new ArrayList<>());
        when(userRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/users")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户统计 - 基础HTTP测试")
    void testGetUserStats_HttpEndpoint() throws Exception {
        // 准备数据
        when(userRepository.count()).thenReturn(200L);
        when(userRepository.countByIsActive(true)).thenReturn(180L);
        when(tenantRepository.count()).thenReturn(10L);

        // 执行请求
        mockMvc.perform(get("/api/system/users/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalUsers").value(200))
                .andExpect(jsonPath("$.data.activeUsers").value(180))
                .andExpect(jsonPath("$.data.totalTenants").value(10));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户详情 - 成功")
    void testGetUser_Success() throws Exception {
        // 准备数据
        UUID userId = UUID.randomUUID();
        PlatformUser user = PlatformUser.builder()
            .id(userId)
            .username("testuser")
            .email("<EMAIL>")
            .fullName("Test User")
            .build();
        
        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(membershipRepository.findByUserId(userId.toString())).thenReturn(new ArrayList<>());

        // 执行请求
        mockMvc.perform(get("/api/system/users/" + userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.user.username").value("testuser"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户详情 - 用户不存在")
    void testGetUser_NotFound() throws Exception {
        // 准备数据
        UUID userId = UUID.randomUUID();
        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(get("/api/system/users/" + userId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建用户 - 成功")
    void testCreateUser_Success() throws Exception {
        // 准备数据
        PlatformUser savedUser = PlatformUser.builder()
            .id(UUID.randomUUID())
            .username("newuser")
            .email("<EMAIL>")
            .fullName("New User")
            .build();

        when(userRepository.existsByUsername("newuser")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(userRepository.save(any(PlatformUser.class))).thenReturn(savedUser);

        // 执行请求
        String requestBody = """
            {
                "username": "newuser",
                "email": "<EMAIL>",
                "password": "password123",
                "fullName": "New User"
            }
            """;

        mockMvc.perform(post("/api/system/users")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.username").value("newuser"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建用户 - 用户名已存在")
    void testCreateUser_UsernameExists() throws Exception {
        // 准备数据
        when(userRepository.existsByUsername("existinguser")).thenReturn(true);

        // 执行请求
        String requestBody = """
            {
                "username": "existinguser",
                "email": "<EMAIL>",
                "password": "password123"
            }
            """;

        mockMvc.perform(post("/api/system/users")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("用户名已存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("更新用户 - 成功")
    void testUpdateUser_Success() throws Exception {
        // 准备数据
        UUID userId = UUID.randomUUID();
        PlatformUser existingUser = PlatformUser.builder()
            .id(userId)
            .username("testuser")
            .email("<EMAIL>")
            .fullName("Old Name")
            .build();

        PlatformUser updatedUser = PlatformUser.builder()
            .id(userId)
            .username("testuser")
            .email("<EMAIL>")
            .fullName("New Name")
            .build();

        when(userRepository.findById(userId)).thenReturn(Optional.of(existingUser));
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(userRepository.save(any(PlatformUser.class))).thenReturn(updatedUser);

        // 执行请求
        String requestBody = """
            {
                "email": "<EMAIL>",
                "fullName": "New Name"
            }
            """;

        mockMvc.perform(put("/api/system/users/" + userId)
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("重置密码 - 成功")
    void testResetPassword_Success() throws Exception {
        // 准备数据
        UUID userId = UUID.randomUUID();
        PlatformUser user = PlatformUser.builder()
            .id(userId)
            .username("testuser")
            .email("<EMAIL>")
            .build();

        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(userRepository.save(any(PlatformUser.class))).thenReturn(user);

        // 执行请求
        mockMvc.perform(post("/api/system/users/" + userId + "/reset-password")
                .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value("password123"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("管理租户关系 - 成功")
    void testManageTenantMembership_Success() throws Exception {
        // 准备数据
        UUID userId = UUID.randomUUID();
        UUID tenantId = UUID.randomUUID();
        
        PlatformUser user = PlatformUser.builder().id(userId).build();
        Tenant tenant = Tenant.builder().id(tenantId).build();
        TenantUserMembership membership = new TenantUserMembership();
        
        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(tenantRepository.findById(tenantId)).thenReturn(Optional.of(tenant));
        when(membershipRepository.findByTenantIdAndUserId(tenantId.toString(), userId.toString()))
            .thenReturn(Optional.of(membership));
        when(membershipRepository.save(any(TenantUserMembership.class))).thenReturn(membership);

        // 执行请求
        String requestBody = """
            {
                "role": "ADMIN",
                "isActive": true
            }
            """;

        mockMvc.perform(put("/api/system/users/" + userId + "/tenants/" + tenantId)
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问 - 返回403")
    void testAccessWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/users"))
                .andExpect(status().isForbidden());
    }
}