package com.assessment.controller;

import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import com.assessment.entity.multitenant.PlatformUser;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.mockito.Mockito;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemUserController Web层测试
 * 使用WebMvcTest + 自定义测试配置
 */
@WebMvcTest(SystemUserController.class)
@Import(SystemUserControllerWebTest.TestConfig.class)
@DisplayName("系统用户控制器Web测试")
class SystemUserControllerWebTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private PlatformUserRepository userRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private TenantUserMembershipRepository membershipRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户列表 - 成功")
    void testGetUsers_Success() throws Exception {
        // Arrange
        PlatformUser user = PlatformUser.builder()
            .id(UUID.randomUUID())
            .username("testuser")
            .email("<EMAIL>")
            .build();
        
        Page<PlatformUser> userPage = new PageImpl<>(List.of(user));
        when(userRepository.findAll(any(Pageable.class))).thenReturn(userPage);
        when(membershipRepository.findByUserId(anyString())).thenReturn(new ArrayList<>());

        // Act & Assert
        mockMvc.perform(get("/api/system/users")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.totalElements").value(1));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("搜索用户 - 成功")
    void testSearchUsers_Success() throws Exception {
        // Arrange
        PlatformUser user = PlatformUser.builder()
            .id(UUID.randomUUID())
            .username("testuser")
            .email("<EMAIL>")
            .fullName("Test User")
            .build();
        
        Page<PlatformUser> userPage = new PageImpl<>(List.of(user));
        when(userRepository.findByUsernameContainingIgnoreCaseOrEmailContainingIgnoreCaseOrFullNameContainingIgnoreCase(
                eq("test"), eq("test"), eq("test"), any(Pageable.class)))
                .thenReturn(userPage);
        when(membershipRepository.findByUserId(anyString())).thenReturn(new ArrayList<>());

        // Act & Assert
        mockMvc.perform(get("/api/system/users")
                .param("search", "test")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户统计 - 成功")
    void testGetUserStats_Success() throws Exception {
        // Arrange
        when(userRepository.count()).thenReturn(100L);
        when(userRepository.countByIsActive(true)).thenReturn(90L);
        when(tenantRepository.count()).thenReturn(5L);

        // Act & Assert
        mockMvc.perform(get("/api/system/users/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalUsers").value(100))
                .andExpect(jsonPath("$.data.activeUsers").value(90))
                .andExpect(jsonPath("$.data.totalTenants").value(5));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户详情 - 成功")
    void testGetUser_Success() throws Exception {
        // Arrange
        UUID userId = UUID.randomUUID();
        PlatformUser user = PlatformUser.builder()
            .id(userId)
            .username("testuser")
            .email("<EMAIL>")
            .fullName("Test User")
            .build();
        
        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(membershipRepository.findByUserId(userId.toString())).thenReturn(new ArrayList<>());

        // Act & Assert
        mockMvc.perform(get("/api/system/users/" + userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.user.username").value("testuser"))
                .andExpect(jsonPath("$.data.memberships").isArray());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户详情 - 用户不存在")
    void testGetUser_NotFound() throws Exception {
        // Arrange
        UUID userId = UUID.randomUUID();
        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        // Act & Assert
        mockMvc.perform(get("/api/system/users/" + userId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取用户详情失败: 用户不存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户详情 - 无效ID格式")
    void testGetUser_InvalidIdFormat() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/system/users/invalid-uuid"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("无效的用户ID格式"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建用户 - 成功")
    void testCreateUser_Success() throws Exception {
        // Arrange
        PlatformUser savedUser = PlatformUser.builder()
            .id(UUID.randomUUID())
            .username("newuser")
            .email("<EMAIL>")
            .fullName("New User")
            .build();

        when(userRepository.existsByUsername("newuser")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(passwordEncoder.encode("password123")).thenReturn("encoded_password");
        when(userRepository.save(any(PlatformUser.class))).thenReturn(savedUser);

        // Act & Assert
        String requestBody = """
            {
                "username": "newuser",
                "email": "<EMAIL>",
                "password": "password123",
                "fullName": "New User"
            }
            """;

        mockMvc.perform(post("/api/system/users")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.username").value("newuser"))
                .andExpect(jsonPath("$.data.email").value("<EMAIL>"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建用户 - 用户名已存在")
    void testCreateUser_UsernameExists() throws Exception {
        // Arrange
        when(userRepository.existsByUsername("existinguser")).thenReturn(true);

        // Act & Assert
        String requestBody = """
            {
                "username": "existinguser",
                "email": "<EMAIL>",
                "password": "password123"
            }
            """;

        mockMvc.perform(post("/api/system/users")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("用户名已存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建用户 - 邮箱已存在")
    void testCreateUser_EmailExists() throws Exception {
        // Arrange
        when(userRepository.existsByUsername("newuser")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // Act & Assert
        String requestBody = """
            {
                "username": "newuser",
                "email": "<EMAIL>",
                "password": "password123"
            }
            """;

        mockMvc.perform(post("/api/system/users")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("邮箱已存在"));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问 - 返回403")
    void testAccessWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/users"))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("未认证访问 - 返回401")
    void testUnauthenticatedAccess_Unauthorized() throws Exception {
        mockMvc.perform(get("/api/system/users"))
                .andExpect(status().isUnauthorized());
    }

    @Configuration
    static class TestConfig {
        
        @Bean
        @Primary
        public PlatformUserRepository platformUserRepository() {
            return Mockito.mock(PlatformUserRepository.class);
        }
        
        @Bean
        @Primary
        public TenantRepository tenantRepository() {
            return Mockito.mock(TenantRepository.class);
        }
        
        @Bean
        @Primary
        public TenantUserMembershipRepository tenantUserMembershipRepository() {
            return Mockito.mock(TenantUserMembershipRepository.class);
        }
        
        @Bean
        @Primary
        public PasswordEncoder passwordEncoder() {
            return Mockito.mock(PasswordEncoder.class);
        }
    }
}