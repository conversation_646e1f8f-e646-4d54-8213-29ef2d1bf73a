package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.dto.ApiResponse;
import com.assessment.dto.SystemScaleQueryRequest;
import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 系统量表控制器基础单元测试
 * 专注于测试控制器的核心逻辑
 */
@DisplayName("系统量表控制器基础单元测试")
class SystemScaleControllerBasicTest {

    private SystemScaleController systemScaleController;
    private GlobalScaleRegistryRepository mockScaleRepository;
    private ObjectMapper mockObjectMapper;

    @BeforeEach
    void setUp() {
        mockScaleRepository = mock(GlobalScaleRegistryRepository.class);
        mockObjectMapper = mock(ObjectMapper.class);
        systemScaleController = new SystemScaleController(mockScaleRepository, mockObjectMapper);
    }

    @Test
    @DisplayName("测试获取量表列表 - 基本功能")
    void testGetScales_Basic() {
        // Arrange
        List<GlobalScaleRegistry> scales = Arrays.asList(
            createSimpleMockScale("elderly-assessment", "老年人能力评估量表"),
            createSimpleMockScale("emotion-quick", "情绪快评量表")
        );
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findAll(any(Pageable.class)))
            .thenReturn(scalesPage);

        // Act
        SystemScaleQueryRequest queryRequest = new SystemScaleQueryRequest();
        queryRequest.setPage(0);
        queryRequest.setSize(20);
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemScaleController.getScales(queryRequest);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("content");
        assertThat(data).containsKey("totalElements");
        assertThat(data.get("totalElements")).isEqualTo(2L);
    }

    @Test
    @DisplayName("测试获取量表列表 - 带搜索条件")
    void testGetScales_WithSearch() {
        // Arrange
        List<GlobalScaleRegistry> scales = Arrays.asList(
            createSimpleMockScale("elderly-assessment", "老年人能力评估量表")
        );
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByNameContainingIgnoreCaseOrCodeContainingIgnoreCase(
                anyString(), anyString(), any(Pageable.class)))
            .thenReturn(scalesPage);

        // Act
        SystemScaleQueryRequest queryRequest = new SystemScaleQueryRequest();
        queryRequest.setPage(0);
        queryRequest.setSize(20);
        queryRequest.setSearch("老年人");
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemScaleController.getScales(queryRequest);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取量表列表 - 按状态筛选")
    void testGetScales_FilterByStatus() {
        // Arrange
        List<GlobalScaleRegistry> scales = Arrays.asList(
            createSimpleMockScale("active-scale", "活跃量表")
        );
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByStatus(
                any(GlobalScaleRegistry.ScaleStatus.class), any(Pageable.class)))
            .thenReturn(scalesPage);

        // Act
        SystemScaleQueryRequest queryRequest = new SystemScaleQueryRequest();
        queryRequest.setPage(0);
        queryRequest.setSize(20);
        queryRequest.setStatus("ACTIVE");
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemScaleController.getScales(queryRequest);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取量表详情 - 成功")
    void testGetScale_Success() {
        // Arrange
        String scaleId = UUID.randomUUID().toString();
        GlobalScaleRegistry scale = createSimpleMockScale("test-scale", "测试量表");
        scale.setId(scaleId);

        when(mockScaleRepository.findById(scaleId))
            .thenReturn(Optional.of(scale));

        // Act
        ResponseEntity<ApiResponse<GlobalScaleRegistry>> response = 
            systemScaleController.getScale(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(scale);
    }

    @Test
    @DisplayName("测试获取量表详情 - 无效ID格式")
    void testGetScale_InvalidIdFormat() {
        // Act
        ResponseEntity<ApiResponse<GlobalScaleRegistry>> response = 
            systemScaleController.getScale("invalid-uuid");

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("无效的量表ID格式");
    }

    @Test
    @DisplayName("测试创建量表 - 量表代码已存在")
    void testCreateScale_CodeExists() {
        // Arrange
        SystemScaleController.CreateScaleRequest request = 
            new SystemScaleController.CreateScaleRequest();
        request.setCode("existing-code");
        request.setName("新量表");

        when(mockScaleRepository.existsByCode("existing-code")).thenReturn(true);

        // Act
        ResponseEntity<ApiResponse<GlobalScaleRegistry>> response = 
            systemScaleController.createScale(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("量表代码已存在");
    }

    @Test
    @DisplayName("测试创建量表 - 成功")
    void testCreateScale_Success() {
        // Arrange
        SystemScaleController.CreateScaleRequest request = 
            new SystemScaleController.CreateScaleRequest();
        request.setCode("new-scale");
        request.setName("新量表");

        GlobalScaleRegistry savedScale = createSimpleMockScale("new-scale", "新量表");
        savedScale.setId(UUID.randomUUID().toString());

        when(mockScaleRepository.existsByCode("new-scale")).thenReturn(false);
        when(mockScaleRepository.save(any(GlobalScaleRegistry.class))).thenReturn(savedScale);

        // Act
        ResponseEntity<ApiResponse<GlobalScaleRegistry>> response = 
            systemScaleController.createScale(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(savedScale);
    }

    @Test
    @DisplayName("测试更新量表 - 量表不存在")
    void testUpdateScale_NotFound() {
        // Arrange
        String scaleId = UUID.randomUUID().toString();
        SystemScaleController.UpdateScaleRequest request = 
            new SystemScaleController.UpdateScaleRequest();
        request.setName("更新名称");

        when(mockScaleRepository.findById(scaleId))
            .thenReturn(Optional.empty());

        // Act
        ResponseEntity<ApiResponse<GlobalScaleRegistry>> response = 
            systemScaleController.updateScale(scaleId, request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).contains("量表不存在");
    }

    @Test
    @DisplayName("测试发布量表 - 成功")
    void testPublishScale_Success() {
        // Arrange
        String scaleId = UUID.randomUUID().toString();
        GlobalScaleRegistry scale = createSimpleMockScale("test-scale", "测试量表");
        scale.setId(scaleId);
        scale.setStatus(GlobalScaleRegistry.ScaleStatus.UNDER_REVIEW);

        when(mockScaleRepository.findById(scaleId))
            .thenReturn(Optional.of(scale));
        when(mockScaleRepository.save(any(GlobalScaleRegistry.class)))
            .thenReturn(scale);

        // Act
        ResponseEntity<ApiResponse<GlobalScaleRegistry>> response = 
            systemScaleController.publishScale(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
    }

    @Test
    @DisplayName("测试删除量表 - 成功")
    void testDeleteScale_Success() {
        // Arrange
        String scaleId = UUID.randomUUID().toString();
        GlobalScaleRegistry scale = createSimpleMockScale("test-scale", "测试量表");
        scale.setId(scaleId);

        when(mockScaleRepository.findById(scaleId))
            .thenReturn(Optional.of(scale));

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            systemScaleController.deleteScale(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getMessage()).isEqualTo("量表删除成功");
    }

    @Test
    @DisplayName("测试获取量表统计 - 成功")
    void testGetScaleStats_Success() {
        // Arrange
        when(mockScaleRepository.count()).thenReturn(100L);
        when(mockScaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE)).thenReturn(85L);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemScaleController.getScaleStats();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("totalScales");
        assertThat(data.get("totalScales")).isEqualTo(100L);
    }

    /**
     * 创建简单的模拟量表对象
     */
    private GlobalScaleRegistry createSimpleMockScale(String code, String name) {
        GlobalScaleRegistry scale = new GlobalScaleRegistry();
        scale.setId(UUID.randomUUID().toString());
        scale.setCode(code);
        scale.setName(name);
        scale.setCategory("老年护理");
        scale.setStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE);
        scale.setVersion("1.0.0");
        scale.setCreatedAt(LocalDateTime.now());
        scale.setUpdatedAt(LocalDateTime.now());
        return scale;
    }
}