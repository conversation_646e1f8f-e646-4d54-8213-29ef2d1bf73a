package com.assessment.controller;

import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import com.assessment.entity.multitenant.PlatformUser;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.mockito.Mockito;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemUserController 基础测试类 - 使用SpringBootTest
 */
@SpringBootTest(classes = {SystemUserControllerBasicTest.TestConfig.class})
@AutoConfigureWebMvc
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=none"
})
@DisplayName("系统用户控制器基础测试")
class SystemUserControllerBasicTest {

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private PlatformUserRepository userRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private TenantUserMembershipRepository membershipRepository;


    private MockMvc mockMvc;

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户列表 - 基础HTTP测试")
    void testGetUsers_HttpEndpoint() throws Exception {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
        
        // 准备数据
        Page<PlatformUser> emptyPage = new PageImpl<>(new ArrayList<>());
        when(userRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);
        when(membershipRepository.findByUserId(anyString())).thenReturn(new ArrayList<>());

        // 执行请求
        mockMvc.perform(get("/api/system/users")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户统计 - 基础HTTP测试")
    void testGetUserStats_HttpEndpoint() throws Exception {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
        
        // 准备数据
        when(userRepository.count()).thenReturn(200L);
        when(userRepository.countByIsActive(true)).thenReturn(180L);
        when(tenantRepository.count()).thenReturn(10L);

        // 执行请求
        mockMvc.perform(get("/api/system/users/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalUsers").value(200))
                .andExpect(jsonPath("$.data.activeUsers").value(180))
                .andExpect(jsonPath("$.data.totalTenants").value(10));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户详情 - 成功")
    void testGetUser_Success() throws Exception {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
        
        // 准备数据
        UUID userId = UUID.randomUUID();
        PlatformUser user = PlatformUser.builder()
            .id(userId)
            .username("testuser")
            .email("<EMAIL>")
            .fullName("Test User")
            .build();
        
        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(membershipRepository.findByUserId(userId.toString())).thenReturn(new ArrayList<>());

        // 执行请求
        mockMvc.perform(get("/api/system/users/" + userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.user.username").value("testuser"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户详情 - 用户不存在")
    void testGetUser_NotFound() throws Exception {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
        
        // 准备数据
        UUID userId = UUID.randomUUID();
        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(get("/api/system/users/" + userId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问 - 返回403")
    void testAccessWithoutAdminRole_Forbidden() throws Exception {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
        
        mockMvc.perform(get("/api/system/users"))
                .andExpect(status().isForbidden());
    }

    @Configuration
    static class TestConfig {
        
        @Bean
        @Primary
        public PlatformUserRepository platformUserRepository() {
            return Mockito.mock(PlatformUserRepository.class);
        }
        
        @Bean
        @Primary
        public TenantRepository tenantRepository() {
            return Mockito.mock(TenantRepository.class);
        }
        
        @Bean
        @Primary
        public TenantUserMembershipRepository tenantUserMembershipRepository() {
            return Mockito.mock(TenantUserMembershipRepository.class);
        }
        
    }
}