package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.dto.ApiResponse;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 系统用户控制器简单单元测试
 * 专注于测试控制器的核心逻辑，避免复杂的实体构造
 */
@DisplayName("系统用户控制器简单单元测试")
class SystemUserControllerSimpleTest {

    private SystemUserController systemUserController;
    private PlatformUserRepository mockPlatformUserRepository;
    private TenantRepository mockTenantRepository;
    private TenantUserMembershipRepository mockTenantUserMembershipRepository;
    private PasswordEncoder mockPasswordEncoder;

    @BeforeEach
    void setUp() {
        mockPlatformUserRepository = mock(PlatformUserRepository.class);
        mockTenantRepository = mock(TenantRepository.class);
        mockTenantUserMembershipRepository = mock(TenantUserMembershipRepository.class);
        mockPasswordEncoder = mock(PasswordEncoder.class);
        
        systemUserController = new SystemUserController(
            mockPlatformUserRepository,
            mockTenantRepository,
            mockTenantUserMembershipRepository,
            mockPasswordEncoder
        );
    }

    @Test
    @DisplayName("测试获取用户列表 - 基本功能")
    void testGetUsers_Basic() {
        // Arrange
        List<PlatformUser> users = Arrays.asList(
            createSimpleMockUser("user1", "<EMAIL>"),
            createSimpleMockUser("user2", "<EMAIL>")
        );
        Page<PlatformUser> usersPage = new PageImpl<>(users);

        when(mockPlatformUserRepository.findAll(any(Pageable.class)))
            .thenReturn(usersPage);
        when(mockTenantUserMembershipRepository.findByUserId(anyString()))
            .thenReturn(Arrays.asList());

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemUserController.getUsers(0, 20, null, null, null, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("content");
        assertThat(data).containsKey("totalElements");
        assertThat(data.get("totalElements")).isEqualTo(2L);
    }

    @Test
    @DisplayName("测试获取用户列表 - 带搜索条件")
    void testGetUsers_WithSearch() {
        // Arrange
        List<PlatformUser> users = Arrays.asList(
            createSimpleMockUser("admin", "<EMAIL>")
        );
        Page<PlatformUser> usersPage = new PageImpl<>(users);

        when(mockPlatformUserRepository
            .findByUsernameContainingIgnoreCaseOrEmailContainingIgnoreCaseOrFullNameContainingIgnoreCase(
                anyString(), anyString(), anyString(), any(Pageable.class)))
            .thenReturn(usersPage);
        when(mockTenantUserMembershipRepository.findByUserId(anyString()))
            .thenReturn(Arrays.asList());

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemUserController.getUsers(0, 20, "admin", null, null, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取用户详情 - 成功")
    void testGetUser_Success() {
        // Arrange
        String userId = UUID.randomUUID().toString();
        PlatformUser user = createSimpleMockUser("testuser", "<EMAIL>");
        user.setId(UUID.fromString(userId));

        when(mockPlatformUserRepository.findById(UUID.fromString(userId)))
            .thenReturn(Optional.of(user));
        when(mockTenantUserMembershipRepository.findByUserId(userId))
            .thenReturn(Arrays.asList());

        // Act
        ResponseEntity<ApiResponse<SystemUserController.UserWithMemberships>> response = 
            systemUserController.getUser(userId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        SystemUserController.UserWithMemberships userData = response.getBody().getData();
        assertThat(userData.getUser()).isEqualTo(user);
        assertThat(userData.getMemberships()).isNotNull();
    }

    @Test
    @DisplayName("测试获取用户详情 - 无效ID格式")
    void testGetUser_InvalidIdFormat() {
        // Act
        ResponseEntity<ApiResponse<SystemUserController.UserWithMemberships>> response = 
            systemUserController.getUser("invalid-uuid");

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("无效的用户ID格式");
    }

    @Test
    @DisplayName("测试创建用户 - 用户名已存在")
    void testCreateUser_UsernameExists() {
        // Arrange
        SystemUserController.CreateUserRequest request = new SystemUserController.CreateUserRequest();
        request.setUsername("existinguser");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");

        when(mockPlatformUserRepository.existsByUsername("existinguser")).thenReturn(true);

        // Act
        ResponseEntity<ApiResponse<PlatformUser>> response = 
            systemUserController.createUser(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("用户名已存在");
    }

    @Test
    @DisplayName("测试创建用户 - 邮箱已存在")
    void testCreateUser_EmailExists() {
        // Arrange
        SystemUserController.CreateUserRequest request = new SystemUserController.CreateUserRequest();
        request.setUsername("newuser");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");

        when(mockPlatformUserRepository.existsByUsername("newuser")).thenReturn(false);
        when(mockPlatformUserRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // Act
        ResponseEntity<ApiResponse<PlatformUser>> response = 
            systemUserController.createUser(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("邮箱已存在");
    }

    @Test
    @DisplayName("测试创建用户 - 成功")
    void testCreateUser_Success() {
        // Arrange
        SystemUserController.CreateUserRequest request = new SystemUserController.CreateUserRequest();
        request.setUsername("newuser");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setFullName("New User");

        PlatformUser savedUser = createSimpleMockUser("newuser", "<EMAIL>");
        savedUser.setId(UUID.randomUUID());

        when(mockPlatformUserRepository.existsByUsername("newuser")).thenReturn(false);
        when(mockPlatformUserRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(mockPasswordEncoder.encode("password123")).thenReturn("encoded-password");
        when(mockPlatformUserRepository.save(any(PlatformUser.class))).thenReturn(savedUser);

        // Act
        ResponseEntity<ApiResponse<PlatformUser>> response = 
            systemUserController.createUser(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(savedUser);
    }

    @Test
    @DisplayName("测试更新用户 - 用户不存在")
    void testUpdateUser_UserNotFound() {
        // Arrange
        String userId = UUID.randomUUID().toString();
        SystemUserController.UpdateUserRequest request = new SystemUserController.UpdateUserRequest();
        request.setEmail("<EMAIL>");

        when(mockPlatformUserRepository.findById(UUID.fromString(userId)))
            .thenReturn(Optional.empty());

        // Act  
        ResponseEntity<ApiResponse<PlatformUser>> response = 
            systemUserController.updateUser(userId, request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).contains("用户不存在");
    }

    @Test
    @DisplayName("测试更新用户 - 邮箱冲突")
    void testUpdateUser_EmailConflict() {
        // Arrange
        String userId = UUID.randomUUID().toString();
        PlatformUser existingUser = createSimpleMockUser("testuser", "<EMAIL>");
        existingUser.setId(UUID.fromString(userId));

        SystemUserController.UpdateUserRequest request = new SystemUserController.UpdateUserRequest();
        request.setEmail("<EMAIL>");

        when(mockPlatformUserRepository.findById(UUID.fromString(userId)))
            .thenReturn(Optional.of(existingUser));
        when(mockPlatformUserRepository.existsByEmail("<EMAIL>"))
            .thenReturn(true);

        // Act
        ResponseEntity<ApiResponse<PlatformUser>> response = 
            systemUserController.updateUser(userId, request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("邮箱已被其他用户使用");
    }

    /**
     * 创建简单的模拟用户对象
     */
    private PlatformUser createSimpleMockUser(String username, String email) {
        PlatformUser user = new PlatformUser();
        user.setId(UUID.randomUUID());
        user.setUsername(username);
        user.setEmail(email);
        user.setFullName("Test User");
        user.setPlatformRole(PlatformUser.PlatformRole.USER);
        user.setIsActive(true);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        return user;
    }
}