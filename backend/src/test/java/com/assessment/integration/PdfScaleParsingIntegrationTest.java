package com.assessment.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.assessment.dto.DocumentAnalysisRequest;
import com.assessment.dto.DocumentAnalysisResult;
import com.assessment.service.AIAnalysisService;
import com.assessment.service.DoclingService;
import com.assessment.service.LMStudioService;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;

/**
 * PDF量表解析完整流程集成测试
 * 
 * 测试覆盖：
 * 1. PDF文件上传与处理
 * 2. Docling服务转换（Mock）
 * 3. AI结构分析（Mock）
 * 4. 量表Schema生成
 * 5. 端到端工作流验证
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("PDF量表解析流程集成测试")
@SuppressWarnings("removal")
class PdfScaleParsingIntegrationTest {

    @Autowired
    private MockMvc mockMvc;


    @Autowired
    private DoclingService doclingService;

    @Autowired
    private AIAnalysisService aiAnalysisService;

    @MockBean
    private LMStudioService lmStudioService;

    // Mock external HTTP dependencies
    @MockBean
    private OkHttpClient httpClient;

    @MockBean
    private Call call;

    @MockBean
    private Response response;

    @MockBean
    private ResponseBody responseBody;

    private MockMultipartFile testPdfFile;
    private String mockMarkdownContent;

    @BeforeEach
    void setUp() throws IOException {
        // 准备测试文件
        setupTestPdfFile();
        
        // 准备Mock响应数据
        setupMockResponses();
        
        // 配置Mock行为
        configureMockBehavior();
    }

    @Test
    @DisplayName("完整PDF量表解析工作流 - 成功场景")
    void testCompletePdfScaleParsingWorkflow() throws Exception {
        // Act & Assert - 测试Docling转换端点
        mockMvc.perform(multipart("/api/docling/test")
                .file(testPdfFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.fileName").value("test_scale.pdf"))
                .andExpect(jsonPath("$.data.markdownContent").exists())
                .andExpect(jsonPath("$.data.processingTimeMs").exists());
    }

    @Test
    @DisplayName("AI文档分析流程测试")
    void testAiDocumentAnalysisFlow() {
        // Arrange
        DocumentAnalysisRequest analysisRequest = new DocumentAnalysisRequest();
        analysisRequest.setFileName("test_scale.pdf");
        analysisRequest.setMarkdownContent(mockMarkdownContent);
        analysisRequest.setUseStream(false);

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(analysisRequest);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getTableName()).isNotEmpty();
        assertThat(result.getFields()).isNotEmpty();
        assertThat(result.getParsingTimeMs()).isGreaterThan(0);
    }

    @Test
    @DisplayName("大文件处理能力测试")
    void testLargeFileProcessing() throws Exception {
        // Arrange - 创建较大的测试文件
        MockMultipartFile largeFile = new MockMultipartFile(
            "file",
            "large_scale.pdf",
            "application/pdf",
            createLargePdfContent()
        );

        // Mock Docling响应大文件处理
        when(responseBody.string()).thenReturn(createLargeMarkdownResponse());

        // Act & Assert
        mockMvc.perform(multipart("/api/docling/test")
                .file(largeFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.fileName").value("large_scale.pdf"))
                .andExpect(jsonPath("$.data.processingTimeMs").exists());
    }

    @Test
    @DisplayName("不支持的文件格式处理测试")
    void testUnsupportedFileFormatHandling() throws Exception {
        // Arrange - 创建非PDF文件
        MockMultipartFile txtFile = new MockMultipartFile(
            "file",
            "test.txt",
            "text/plain",
            "This is not a PDF file".getBytes()
        );

        // Act & Assert
        mockMvc.perform(multipart("/api/docling/test")
                .file(txtFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("文件解析失败: 不支持的文件格式"));
    }

    @Test
    @DisplayName("Docling服务不可用时的降级处理测试")
    void testDoclingServiceFallbackHandling() throws Exception {
        // Arrange - Mock Docling服务不可用
        when(call.execute()).thenThrow(new IOException("Connection refused"));
        ReflectionTestUtils.setField(doclingService, "httpClient", httpClient);

        // Act & Assert
        mockMvc.perform(multipart("/api/docling/test")
                .file(testPdfFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("文件解析失败: Docling API调用失败"));
    }

    @Test
    @DisplayName("AI分析服务不可用时的降级处理测试")
    void testAiAnalysisServiceFallbackHandling() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(false);
        
        DocumentAnalysisRequest analysisRequest = new DocumentAnalysisRequest();
        analysisRequest.setFileName("test_scale.pdf");
        analysisRequest.setMarkdownContent(mockMarkdownContent);

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(analysisRequest);

        // Assert - 应该返回降级分析结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("切换到基础降级分析");
        assertThat(result.getTableName()).isNotEmpty();
        assertThat(result.getFields()).isNotEmpty(); // 基础字段应该存在
    }

    @Test
    @DisplayName("并发文件处理能力测试")
    void testConcurrentFileProcessing() throws Exception {
        // Arrange - 准备多个文件
        MockMultipartFile[] files = {
            createTestFile("scale1.pdf", "评估量表1内容"),
            createTestFile("scale2.pdf", "评估量表2内容"),
            createTestFile("scale3.pdf", "评估量表3内容")
        };

        // Act & Assert - 并发处理多个文件
        for (MockMultipartFile file : files) {
            mockMvc.perform(multipart("/api/docling/test")
                    .file(file))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true));
        }
    }

    @Test
    @DisplayName("文件内容验证测试")
    void testFileContentValidation() throws Exception {
        // Arrange - 创建空文件
        MockMultipartFile emptyFile = new MockMultipartFile(
            "file",
            "empty.pdf",
            "application/pdf",
            new byte[0]
        );

        // Act & Assert
        mockMvc.perform(multipart("/api/docling/test")
                .file(emptyFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("文件解析失败: 文件为空或无效"));
    }

    // Helper Methods

    private void setupTestPdfFile() {
        String pdfContent = createRealisticPdfContent();
        testPdfFile = new MockMultipartFile(
            "file",
            "test_scale.pdf",
            "application/pdf",
            pdfContent.getBytes()
        );
    }

    private String createRealisticPdfContent() {
        return """
            %PDF-1.4 Mock Content
            老年人能力评估量表
            
            1. 基本信息
            姓名：_______
            年龄：_______
            性别：_______
            
            2. 日常生活活动能力
            2.1 进食能力
            □ 完全独立 □ 需要帮助 □ 完全依赖
            
            2.2 洗澡能力
            □ 完全独立 □ 需要帮助 □ 完全依赖
            
            3. 认知功能评估
            3.1 记忆力
            □ 正常 □ 轻度下降 □ 中度下降 □ 重度下降
            
            3.2 定向力
            □ 正常 □ 轻度异常 □ 中度异常 □ 重度异常
            """;
    }

    private void setupMockResponses() {
        mockMarkdownContent = """
            # 老年人能力评估量表
            
            ## 1. 基本信息
            - 姓名
            - 年龄
            - 性别
            
            ## 2. 日常生活活动能力
            ### 2.1 进食能力
            - 完全独立
            - 需要帮助
            - 完全依赖
            
            ### 2.2 洗澡能力
            - 完全独立
            - 需要帮助
            - 完全依赖
            
            ## 3. 认知功能评估
            ### 3.1 记忆力
            - 正常
            - 轻度下降
            - 中度下降
            - 重度下降
            """;

        // Removed mockAiAnalysisResult as it was unused
    }

    private void configureMockBehavior() throws IOException {
        // Mock HTTP Client behavior
        when(httpClient.newCall(any())).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        
        // Mock Docling service response
        String doclingResponse = String.format("""
            {
              "success": true,
              "filename": "test_scale.pdf",
              "markdown": "%s",
              "metadata": {
                "page_count": 1,
                "content_length": %d
              }
            }
            """, mockMarkdownContent.replace("\n", "\\n"), mockMarkdownContent.length());
        
        when(responseBody.string()).thenReturn(doclingResponse);
        
        // Mock LM Studio service behavior
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(lmStudioService.getCurrentServerUrl()).thenReturn("http://localhost:1234");
        when(lmStudioService.getCurrentModel()).thenReturn(null);
        
        // Inject mocked HTTP client
        ReflectionTestUtils.setField(doclingService, "httpClient", httpClient);
    }

    private byte[] createLargePdfContent() {
        StringBuilder content = new StringBuilder();
        content.append("%PDF-1.4 Large Mock Content\n");
        
        // 模拟大文件内容
        for (int i = 0; i < 100; i++) {
            content.append("第").append(i + 1).append("部分：评估项目").append(i + 1).append("\n");
            content.append("□ 选项A □ 选项B □ 选项C □ 选项D\n");
        }
        
        return content.toString().getBytes();
    }

    private String createLargeMarkdownResponse() {
        return """
            {
              "success": true,
              "filename": "large_scale.pdf",
              "markdown": "# 大型评估量表\\n\\n## 包含100个评估项目的完整量表内容...",
              "metadata": {
                "page_count": 10,
                "content_length": 5000
              }
            }
            """;
    }

    private MockMultipartFile createTestFile(final String filename, final String content) {
        return new MockMultipartFile(
            "file",
            filename,
            "application/pdf",
            ("%PDF-1.4 Mock\n" + content).getBytes()
        );
    }
}