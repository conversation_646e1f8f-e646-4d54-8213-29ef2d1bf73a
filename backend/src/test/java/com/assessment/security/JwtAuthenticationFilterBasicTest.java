package com.assessment.security;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.never;

import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

/**
 * JWT认证过滤器基础单元测试
 * 测试JWT过滤器的请求处理逻辑
 */
@DisplayName("JWT认证过滤器基础单元测试")
class JwtAuthenticationFilterBasicTest {

    private JwtAuthenticationFilter jwtAuthenticationFilter;
    private JwtTokenProvider mockTokenProvider;
    private UserDetailsService mockUserDetailsService;
    private HttpServletRequest mockRequest;
    private HttpServletResponse mockResponse;
    private FilterChain mockFilterChain;

    @BeforeEach
    void setUp() {
        mockTokenProvider = mock(JwtTokenProvider.class);
        mockUserDetailsService = mock(UserDetailsService.class);
        mockRequest = mock(HttpServletRequest.class);
        mockResponse = mock(HttpServletResponse.class);
        mockFilterChain = mock(FilterChain.class);
        
        jwtAuthenticationFilter = new JwtAuthenticationFilter();
        
        // 使用反射设置私有字段
        try {
            var tokenProviderField = JwtAuthenticationFilter.class.getDeclaredField("tokenProvider");
            tokenProviderField.setAccessible(true);
            tokenProviderField.set(jwtAuthenticationFilter, mockTokenProvider);
            
            var userDetailsServiceField = JwtAuthenticationFilter.class.getDeclaredField("userDetailsService");
            userDetailsServiceField.setAccessible(true);
            userDetailsServiceField.set(jwtAuthenticationFilter, mockUserDetailsService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set mock fields", e);
        }
    }

    @Test
    @DisplayName("测试公共端点跳过JWT验证")
    void testSkipJwtValidationForPublicEndpoints() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/auth/login");
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider, never()).validateToken(anyString());
    }

    @Test
    @DisplayName("测试健康检查端点跳过JWT验证")
    void testSkipJwtValidationForHealthEndpoint() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/health");
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider, never()).validateToken(anyString());
    }

    @Test
    @DisplayName("测试Swagger端点跳过JWT验证")
    void testSkipJwtValidationForSwaggerEndpoint() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/swagger-ui/index.html");
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider, never()).validateToken(anyString());
    }

    @Test
    @DisplayName("测试根路径跳过JWT验证")
    void testSkipJwtValidationForRootPath() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/");
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider, never()).validateToken(anyString());
    }

    @Test
    @DisplayName("测试有效JWT令牌处理")
    void testValidJwtTokenProcessing() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/protected");
        when(mockRequest.getHeader("Authorization")).thenReturn("Bearer valid.jwt.token");
        when(mockTokenProvider.validateToken("valid.jwt.token")).thenReturn(true);
        when(mockTokenProvider.getUsernameFromToken("valid.jwt.token")).thenReturn("testuser");
        
        UserDetails userDetails = new User("testuser", "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        when(mockUserDetailsService.loadUserByUsername("testuser")).thenReturn(userDetails);
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider).validateToken("valid.jwt.token");
        verify(mockTokenProvider).getUsernameFromToken("valid.jwt.token");
        verify(mockUserDetailsService).loadUserByUsername("testuser");
    }

    @Test
    @DisplayName("测试无效JWT令牌处理")
    void testInvalidJwtTokenProcessing() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/protected");
        when(mockRequest.getHeader("Authorization")).thenReturn("Bearer invalid.jwt.token");
        when(mockTokenProvider.validateToken("invalid.jwt.token")).thenReturn(false);
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider).validateToken("invalid.jwt.token");
        verify(mockTokenProvider, never()).getUsernameFromToken(anyString());
        verify(mockUserDetailsService, never()).loadUserByUsername(anyString());
    }

    @Test
    @DisplayName("测试缺少Authorization头处理")
    void testMissingAuthorizationHeader() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/protected");
        when(mockRequest.getHeader("Authorization")).thenReturn(null);
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider, never()).validateToken(anyString());
    }

    @Test
    @DisplayName("测试错误的Authorization头格式")
    void testInvalidAuthorizationHeaderFormat() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/protected");
        when(mockRequest.getHeader("Authorization")).thenReturn("Basic dXNlcjpwYXNz");
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider, never()).validateToken(anyString());
    }

    @Test
    @DisplayName("测试空的Bearer令牌")
    void testEmptyBearerToken() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/protected");
        when(mockRequest.getHeader("Authorization")).thenReturn("Bearer ");
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider, never()).validateToken(anyString());
    }

    @Test
    @DisplayName("测试JWT异常处理")
    void testJwtExceptionHandling() throws Exception {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/protected");
        when(mockRequest.getHeader("Authorization")).thenReturn("Bearer valid.jwt.token");
        when(mockTokenProvider.validateToken("valid.jwt.token"))
            .thenThrow(new io.jsonwebtoken.ExpiredJwtException(null, null, "Token expired"));
        
        // Act
        jwtAuthenticationFilter.doFilterInternal(mockRequest, mockResponse, mockFilterChain);
        
        // Assert
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verify(mockTokenProvider).validateToken("valid.jwt.token");
    }

    @Test
    @DisplayName("测试过滤器类的基本属性")
    void testJwtAuthenticationFilterBasicProperties() {
        // Act & Assert
        assertThat(jwtAuthenticationFilter).isNotNull();
        assertThat(jwtAuthenticationFilter.getClass().getSimpleName()).isEqualTo("JwtAuthenticationFilter");
        assertThat(jwtAuthenticationFilter.getClass().getPackage().getName()).isEqualTo("com.assessment.security");
        
        // 验证Component注解
        assertThat(jwtAuthenticationFilter.getClass().isAnnotationPresent(
            org.springframework.stereotype.Component.class)).isTrue();
    }

    @Test
    @DisplayName("测试过滤器继承关系")
    void testFilterInheritance() {
        // Act & Assert
        assertThat(jwtAuthenticationFilter).isInstanceOf(
            org.springframework.web.filter.OncePerRequestFilter.class);
    }

    @Test
    @DisplayName("测试字符串表示和哈希码")
    void testStringRepresentationAndHashCode() {
        // Act & Assert
        assertThat(jwtAuthenticationFilter.toString()).isNotNull();
        assertThat(jwtAuthenticationFilter.toString()).contains("JwtAuthenticationFilter");
        assertThat(jwtAuthenticationFilter.hashCode()).isNotNull();
    }

    @Test
    @DisplayName("测试实例化")
    void testInstantiation() {
        // Act & Assert
        assertThatCode(() -> new JwtAuthenticationFilter()).doesNotThrowAnyException();
        
        JwtAuthenticationFilter newFilter = new JwtAuthenticationFilter();
        assertThat(newFilter).isNotNull();
        assertThat(newFilter).isInstanceOf(JwtAuthenticationFilter.class);
    }

    @Test
    @DisplayName("测试多个公共端点路径")
    void testMultiplePublicEndpointPaths() throws Exception {
        String[] publicPaths = {
            "/api/auth/register",
            "/api/public/info", 
            "/api/ai/test",
            "/api/docling/health",
            "/api/pdf-import/upload",
            "/actuator/health",
            "/api-docs/swagger-config"
        };
        
        for (int i = 0; i < publicPaths.length; i++) {
            // Arrange - 为每个路径创建新的mock以避免验证冲突
            HttpServletRequest newMockRequest = mock(HttpServletRequest.class);
            HttpServletResponse newMockResponse = mock(HttpServletResponse.class);
            FilterChain newMockFilterChain = mock(FilterChain.class);
            
            when(newMockRequest.getRequestURI()).thenReturn(publicPaths[i]);
            
            // Act
            jwtAuthenticationFilter.doFilterInternal(newMockRequest, newMockResponse, newMockFilterChain);
            
            // Assert
            verify(newMockFilterChain).doFilter(newMockRequest, newMockResponse);
        }
        
        // 验证没有调用令牌验证
        verify(mockTokenProvider, never()).validateToken(anyString());
    }
}