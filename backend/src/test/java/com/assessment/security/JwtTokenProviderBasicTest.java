package com.assessment.security;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.config.JwtProperties;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * JWT Token Provider基础单元测试
 * 测试JWT令牌的生成、验证和解析功能
 */
@DisplayName("JWT Token Provider基础单元测试")
class JwtTokenProviderBasicTest {

    private JwtTokenProvider jwtTokenProvider;
    private JwtProperties mockJwtProperties;

    @BeforeEach
    void setUp() {
        mockJwtProperties = mock(JwtProperties.class);
        jwtTokenProvider = new JwtTokenProvider();
        
        // 设置默认的JWT属性
        when(mockJwtProperties.getSecret())
                .thenReturn("ThisIsAVeryLongAndSecureSecretKeyForJWTTokenGeneration"
                    + "ThatDefinitelyMeetsThe256BitRequirement");
        when(mockJwtProperties.getExpiration()).thenReturn(86400000L); // 24小时
        when(mockJwtProperties.getRefreshExpiration()).thenReturn(604800000L); // 7天
        
        // 使用反射设置属性
        try {
            var field = JwtTokenProvider.class.getDeclaredField("jwtProperties");
            field.setAccessible(true);
            field.set(jwtTokenProvider, mockJwtProperties);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set jwtProperties field", e);
        }
    }

    @Test
    @DisplayName("测试JWT令牌生成")
    void testGenerateToken() {
        // Arrange
        UserDetails userDetails = new User("testuser", "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        Authentication mockAuth = mock(Authentication.class);
        when(mockAuth.getPrincipal()).thenReturn(userDetails);
        
        // Act
        String token = jwtTokenProvider.generateToken(mockAuth);
        
        // Assert
        assertThat(token).isNotNull();
        assertThat(token).isNotEmpty();
        assertThat(token.split("\\.")).hasSize(3); // JWT格式：header.payload.signature
    }

    @Test
    @DisplayName("测试刷新令牌生成")
    void testGenerateRefreshToken() {
        // Arrange
        String username = "testuser";
        
        // Act
        String refreshToken = jwtTokenProvider.generateRefreshToken(username);
        
        // Assert
        assertThat(refreshToken).isNotNull();
        assertThat(refreshToken).isNotEmpty();
        assertThat(refreshToken.split("\\.")).hasSize(3);
    }

    @Test
    @DisplayName("测试从令牌获取用户名")
    void testGetUsernameFromToken() {
        // Arrange
        UserDetails userDetails = new User("testuser", "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        Authentication mockAuth = mock(Authentication.class);
        when(mockAuth.getPrincipal()).thenReturn(userDetails);
        
        String token = jwtTokenProvider.generateToken(mockAuth);
        
        // Act
        String username = jwtTokenProvider.getUsernameFromToken(token);
        
        // Assert
        assertThat(username).isEqualTo("testuser");
    }

    @Test
    @DisplayName("测试有效令牌验证")
    void testValidateValidToken() {
        // Arrange
        UserDetails userDetails = new User("testuser", "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        Authentication mockAuth = mock(Authentication.class);
        when(mockAuth.getPrincipal()).thenReturn(userDetails);
        
        String token = jwtTokenProvider.generateToken(mockAuth);
        
        // Act
        boolean isValid = jwtTokenProvider.validateToken(token);
        
        // Assert
        assertThat(isValid).isTrue();
    }

    @Test
    @DisplayName("测试无效令牌验证")
    void testValidateInvalidToken() {
        // Arrange
        String invalidToken = "invalid.jwt.token";
        
        // Act
        boolean isValid = jwtTokenProvider.validateToken(invalidToken);
        
        // Assert
        assertThat(isValid).isFalse();
    }

    @Test
    @DisplayName("测试空令牌验证")
    void testValidateEmptyToken() {
        // Act & Assert
        assertThat(jwtTokenProvider.validateToken("")).isFalse();
        assertThat(jwtTokenProvider.validateToken(null)).isFalse();
    }

    @Test
    @DisplayName("测试获取JWT过期时间")
    void testGetJwtExpirationMs() {
        // Act
        long expirationMs = jwtTokenProvider.getJwtExpirationMs();
        
        // Assert
        assertThat(expirationMs).isEqualTo(86400000L);
    }

    @Test
    @DisplayName("测试短密钥的默认处理")
    void testShortSecretHandling() {
        // Arrange
        when(mockJwtProperties.getSecret()).thenReturn("short"); // 短密钥
        
        UserDetails userDetails = new User("testuser", "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        Authentication mockAuth = mock(Authentication.class);
        when(mockAuth.getPrincipal()).thenReturn(userDetails);
        
        // Act - 应该使用默认密钥而不抛异常
        String token = jwtTokenProvider.generateToken(mockAuth);
        
        // Assert
        assertThat(token).isNotNull();
        assertThat(token).isNotEmpty();
        assertThat(jwtTokenProvider.validateToken(token)).isTrue();
    }

    @Test
    @DisplayName("测试空密钥的默认处理")
    void testNullSecretHandling() {
        // Arrange
        when(mockJwtProperties.getSecret()).thenReturn(null);
        
        UserDetails userDetails = new User("testuser", "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        Authentication mockAuth = mock(Authentication.class);
        when(mockAuth.getPrincipal()).thenReturn(userDetails);
        
        // Act - 应该使用默认密钥
        String token = jwtTokenProvider.generateToken(mockAuth);
        
        // Assert
        assertThat(token).isNotNull();
        assertThat(token).isNotEmpty();
        assertThat(jwtTokenProvider.validateToken(token)).isTrue();
    }

    @Test
    @DisplayName("测试令牌的一致性")
    void testTokenConsistency() throws InterruptedException {
        // Arrange
        UserDetails userDetails = new User("testuser", "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        Authentication mockAuth = mock(Authentication.class);
        when(mockAuth.getPrincipal()).thenReturn(userDetails);
        
        // Act
        String token1 = jwtTokenProvider.generateToken(mockAuth);
        Thread.sleep(10); // 增加延迟确保时间戳不同
        String token2 = jwtTokenProvider.generateToken(mockAuth);
        
        // Assert - 如果时间戳不同，令牌应该不同；如果相同，说明时间精度问题
        // 主要验证核心功能：令牌有效且用户名正确
        assertThat(jwtTokenProvider.validateToken(token1)).isTrue();
        assertThat(jwtTokenProvider.validateToken(token2)).isTrue();
        assertThat(jwtTokenProvider.getUsernameFromToken(token1)).isEqualTo("testuser");
        assertThat(jwtTokenProvider.getUsernameFromToken(token2)).isEqualTo("testuser");
        
        // 验证令牌格式正确
        assertThat(token1.split("\\.")).hasSize(3);
        assertThat(token2.split("\\.")).hasSize(3);
    }

    @Test
    @DisplayName("测试不同用户的令牌区别")
    void testDifferentUserTokens() {
        // Arrange
        UserDetails user1 = new User("user1", "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        UserDetails user2 = new User("user2", "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        
        Authentication auth1 = mock(Authentication.class);
        Authentication auth2 = mock(Authentication.class);
        when(auth1.getPrincipal()).thenReturn(user1);
        when(auth2.getPrincipal()).thenReturn(user2);
        
        // Act
        String token1 = jwtTokenProvider.generateToken(auth1);
        String token2 = jwtTokenProvider.generateToken(auth2);
        
        // Assert
        assertThat(token1).isNotEqualTo(token2);
        assertThat(jwtTokenProvider.getUsernameFromToken(token1)).isEqualTo("user1");
        assertThat(jwtTokenProvider.getUsernameFromToken(token2)).isEqualTo("user2");
    }

    @Test
    @DisplayName("测试类的基本属性")
    void testJwtTokenProviderBasicProperties() {
        // Act & Assert
        assertThat(jwtTokenProvider).isNotNull();
        assertThat(jwtTokenProvider.getClass().getSimpleName()).isEqualTo("JwtTokenProvider");
        assertThat(jwtTokenProvider.getClass().getPackage().getName()).isEqualTo("com.assessment.security");
        
        // 验证Component注解
        assertThat(jwtTokenProvider.getClass().isAnnotationPresent(
            org.springframework.stereotype.Component.class)).isTrue();
    }

    @Test
    @DisplayName("测试字符串表示和哈希码")
    void testStringRepresentationAndHashCode() {
        // Act & Assert
        assertThat(jwtTokenProvider.toString()).isNotNull();
        assertThat(jwtTokenProvider.toString()).contains("JwtTokenProvider");
        assertThat(jwtTokenProvider.hashCode()).isNotNull();
    }

    @Test
    @DisplayName("测试实例化")
    void testInstantiation() {
        // Act & Assert
        assertThatCode(() -> new JwtTokenProvider()).doesNotThrowAnyException();
        
        JwtTokenProvider newProvider = new JwtTokenProvider();
        assertThat(newProvider).isNotNull();
        assertThat(newProvider).isInstanceOf(JwtTokenProvider.class);
    }
}