package com.assessment.pdf;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.dto.AssessmentScaleDTO;
import com.assessment.exception.PDFParsingException;
import com.assessment.service.DoclingService;
import com.assessment.service.ScaleAnalysisService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.web.multipart.MultipartFile;

/**
 * PDF解析服务基础单元测试
 * 测试PDF文件解析的核心功能
 */
@DisplayName("PDF解析服务基础单元测试")
class PDFParserServiceBasicTest {

    private PDFParserService pdfParserService;
    private DoclingService mockDoclingService;
    private ScaleAnalysisService mockScaleAnalysisService;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockDoclingService = mock(DoclingService.class);
        mockScaleAnalysisService = mock(ScaleAnalysisService.class);
        objectMapper = new ObjectMapper();
        
        pdfParserService = new PDFParserService();
        
        // 使用反射设置私有字段
        try {
            var doclingField = PDFParserService.class.getDeclaredField("doclingService");
            doclingField.setAccessible(true);
            doclingField.set(pdfParserService, mockDoclingService);
            
            var scaleAnalysisField = PDFParserService.class.getDeclaredField("scaleAnalysisService");
            scaleAnalysisField.setAccessible(true);
            scaleAnalysisField.set(pdfParserService, mockScaleAnalysisService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set mock fields", e);
        }
    }

    @Test
    @DisplayName("测试成功解析PDF文件")
    void testParsePDFToAssessmentScale_Success() throws IOException {
        // Arrange
        MultipartFile mockFile = createMockPdfFile("test.pdf", "PDF content".getBytes());
        String mockMarkdown = "# 测试评估量表\n\n这是一个测试量表。";
        AssessmentStructure mockStructure = createMockAssessmentStructure();
        JsonNode mockSchema = objectMapper.createObjectNode();
        JsonNode mockScoringRules = objectMapper.createObjectNode();
        
        when(mockDoclingService.convertPdfToMarkdown(any(File.class))).thenReturn(mockMarkdown);
        when(mockScaleAnalysisService.analyzeMarkdownToStructure(anyString(), anyString()))
            .thenReturn(mockStructure);
        when(mockScaleAnalysisService.generateJsonSchema(any(AssessmentStructure.class)))
            .thenReturn(mockSchema);
        when(mockScaleAnalysisService.generateScoringRulesJson(any(AssessmentStructure.class)))
            .thenReturn(mockScoringRules);
        
        // Act
        AssessmentScaleDTO result = pdfParserService.parsePDFToAssessmentScale(mockFile);
        
        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("测试量表");
        assertThat(result.getCode()).isNotNull();
        assertThat(result.getCategory()).isEqualTo("老年人评估");
        assertThat(result.getVersion()).isEqualTo("1.0");
        assertThat(result.getDescription()).isEqualTo("测试描述");
        assertThat(result.getFormSchema()).isEqualTo(mockSchema);
        assertThat(result.getScoringRules()).isEqualTo(mockScoringRules);
        assertThat(result.getSourcePdfPath()).isNotNull();
        assertThat(result.getEstimatedDuration()).isGreaterThan(0);
        assertThat(result.getMaxScore()).isEqualTo(100);
        assertThat(result.getMinScore()).isEqualTo(0);
    }

    @Test
    @DisplayName("测试空文件名处理")
    void testParsePDFToAssessmentScale_EmptyFilename() {
        // Arrange
        MultipartFile mockFile = createMockPdfFile("", "PDF content".getBytes());
        
        // Act & Assert
        assertThatThrownBy(() -> pdfParserService.parsePDFToAssessmentScale(mockFile))
            .isInstanceOf(PDFParsingException.class)
            .hasMessageContaining("文件名不能为空");
    }

    @Test
    @DisplayName("测试空文件名（null）处理")
    void testParsePDFToAssessmentScale_NullFilename() {
        // Arrange
        MultipartFile mockFile = createMockPdfFile(null, "PDF content".getBytes());
        
        // Act & Assert
        assertThatThrownBy(() -> pdfParserService.parsePDFToAssessmentScale(mockFile))
            .isInstanceOf(PDFParsingException.class)
            .hasMessageContaining("文件名不能为空");
    }

    @Test
    @DisplayName("测试没有扩展名的文件")
    void testParsePDFToAssessmentScale_NoExtension() {
        // Arrange
        MultipartFile mockFile = createMockPdfFile("testfile", "PDF content".getBytes());
        
        // Act & Assert
        assertThatThrownBy(() -> pdfParserService.parsePDFToAssessmentScale(mockFile))
            .isInstanceOf(PDFParsingException.class)
            .hasMessageContaining("文件必须有扩展名");
    }

    @Test
    @DisplayName("测试Docling服务异常处理")
    void testParsePDFToAssessmentScale_DoclingServiceException() throws IOException {
        // Arrange
        MultipartFile mockFile = createMockPdfFile("test.pdf", "PDF content".getBytes());
        
        when(mockDoclingService.convertPdfToMarkdown(any(File.class)))
            .thenThrow(new RuntimeException("Docling conversion failed"));
        
        // Act & Assert
        assertThatThrownBy(() -> pdfParserService.parsePDFToAssessmentScale(mockFile))
            .isInstanceOf(PDFParsingException.class)
            .hasMessageContaining("PDF解析过程中发生运行时错误");
    }

    @Test
    @DisplayName("测试ScaleAnalysisService异常处理")
    void testParsePDFToAssessmentScale_ScaleAnalysisException() throws IOException {
        // Arrange
        MultipartFile mockFile = createMockPdfFile("test.pdf", "PDF content".getBytes());
        String mockMarkdown = "# 测试评估量表";
        
        when(mockDoclingService.convertPdfToMarkdown(any(File.class))).thenReturn(mockMarkdown);
        when(mockScaleAnalysisService.analyzeMarkdownToStructure(anyString(), anyString()))
            .thenThrow(new RuntimeException("Analysis failed"));
        
        // Act & Assert
        assertThatThrownBy(() -> pdfParserService.parsePDFToAssessmentScale(mockFile))
            .isInstanceOf(PDFParsingException.class)
            .hasMessageContaining("PDF解析过程中发生运行时错误");
    }

    @Test
    @DisplayName("测试IO异常处理")
    void testParsePDFToAssessmentScale_IOException() throws IOException {
        // Arrange
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getOriginalFilename()).thenReturn("test.pdf");
        when(mockFile.getInputStream()).thenThrow(new IOException("IO error"));
        
        // Act & Assert
        assertThatThrownBy(() -> pdfParserService.parsePDFToAssessmentScale(mockFile))
            .isInstanceOf(PDFParsingException.class)
            .hasMessageContaining("PDF解析失败");
    }

    @Test
    @DisplayName("测试量表代码生成")
    void testGenerateScaleCode() throws Exception {
        // Arrange - 使用反射调用私有方法
        var method = PDFParserService.class.getDeclaredMethod("generateScaleCode", String.class);
        method.setAccessible(true);
        
        // Act & Assert
        String result1 = (String) method.invoke(pdfParserService, "老年人能力评估");
        assertThat(result1).isEqualTo("老年人能力评估");
        
        String result2 = (String) method.invoke(pdfParserService, "Test Assessment Scale!");
        assertThat(result2).isEqualTo("TESTASSESSMENTSCALE");
        
        String result3 = (String) method.invoke(pdfParserService, "这是一个非常长的量表名称超过二十个字符的测试");
        assertThat(result3).hasSize(20);
    }

    @Test
    @DisplayName("测试评估时长估算")
    void testEstimateAssessmentDuration() throws Exception {
        // Arrange
        AssessmentStructure structure = createMockAssessmentStructure();
        var method = PDFParserService.class.getDeclaredMethod("estimateAssessmentDuration", AssessmentStructure.class);
        method.setAccessible(true);
        
        // Act
        Integer duration = (Integer) method.invoke(pdfParserService, structure);
        
        // Assert
        assertThat(duration).isGreaterThanOrEqualTo(5); // 最小5分钟
        assertThat(duration).isEqualTo(5); // 最小5分钟限制
    }

    @Test
    @DisplayName("测试分类转换")
    void testConvertStringToCategory() throws Exception {
        // Arrange
        var method = PDFParserService.class.getDeclaredMethod("convertStringToCategory", String.class);
        method.setAccessible(true);
        
        // Act & Assert
        assertThat((String) method.invoke(pdfParserService, "elderly")).isEqualTo("老年人评估");
        assertThat((String) method.invoke(pdfParserService, "老年人")).isEqualTo("老年人评估");
        assertThat((String) method.invoke(pdfParserService, "emotional")).isEqualTo("情绪评估");
        assertThat((String) method.invoke(pdfParserService, "情绪")).isEqualTo("情绪评估");
        assertThat((String) method.invoke(pdfParserService, "interrai")).isEqualTo("interRAI评估");
        assertThat((String) method.invoke(pdfParserService, "care")).isEqualTo("护理评估");
        assertThat((String) method.invoke(pdfParserService, "护理")).isEqualTo("护理评估");
        assertThat((String) method.invoke(pdfParserService, "unknown")).isEqualTo("unknown");
        assertThat((String) method.invoke(pdfParserService, (Object) null)).isEqualTo("自定义量表");
        assertThat((String) method.invoke(pdfParserService, "")).isEqualTo("自定义量表");
        assertThat((String) method.invoke(pdfParserService, "   ")).isEqualTo("自定义量表");
    }

    @Test
    @DisplayName("测试类的基本属性")
    void testPDFParserServiceBasicProperties() {
        // Act & Assert
        assertThat(pdfParserService).isNotNull();
        assertThat(pdfParserService.getClass().getSimpleName()).isEqualTo("PDFParserService");
        assertThat(pdfParserService.getClass().getPackage().getName()).isEqualTo("com.assessment.pdf");
        
        // 验证Service注解
        assertThat(pdfParserService.getClass().isAnnotationPresent(
            org.springframework.stereotype.Service.class)).isTrue();
    }

    @Test
    @DisplayName("测试字符串表示和哈希码")
    void testStringRepresentationAndHashCode() {
        // Act & Assert
        assertThat(pdfParserService.toString()).isNotNull();
        assertThat(pdfParserService.toString()).contains("PDFParserService");
        assertThat(pdfParserService.hashCode()).isNotNull();
    }

    @Test
    @DisplayName("测试实例化")
    void testInstantiation() {
        // Act & Assert
        assertThatCode(() -> new PDFParserService()).doesNotThrowAnyException();
        
        PDFParserService newService = new PDFParserService();
        assertThat(newService).isNotNull();
        assertThat(newService).isInstanceOf(PDFParserService.class);
    }

    @Test
    @DisplayName("测试不同文件扩展名")
    void testDifferentFileExtensions() throws IOException {
        // Arrange
        MultipartFile pdfFile = createMockPdfFile("test.pdf", "PDF content".getBytes());
        MultipartFile docxFile = createMockPdfFile("test.docx", "DOCX content".getBytes());
        
        String mockMarkdown = "# 测试";
        AssessmentStructure mockStructure = createMockAssessmentStructure();
        JsonNode mockSchema = objectMapper.createObjectNode();
        JsonNode mockScoringRules = objectMapper.createObjectNode();
        
        when(mockDoclingService.convertPdfToMarkdown(any(File.class))).thenReturn(mockMarkdown);
        when(mockScaleAnalysisService.analyzeMarkdownToStructure(anyString(), anyString()))
            .thenReturn(mockStructure);
        when(mockScaleAnalysisService.generateJsonSchema(any(AssessmentStructure.class)))
            .thenReturn(mockSchema);
        when(mockScaleAnalysisService.generateScoringRulesJson(any(AssessmentStructure.class)))
            .thenReturn(mockScoringRules);
        
        // Act & Assert - PDF文件应该成功
        AssessmentScaleDTO result1 = pdfParserService.parsePDFToAssessmentScale(pdfFile);
        assertThat(result1).isNotNull();
        
        // DOCX文件也应该成功（因为只是扩展名不同）
        AssessmentScaleDTO result2 = pdfParserService.parsePDFToAssessmentScale(docxFile);
        assertThat(result2).isNotNull();
    }

    // 辅助方法
    private MultipartFile createMockPdfFile(final String filename, final byte[] content) {
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getOriginalFilename()).thenReturn(filename);
        when(mockFile.isEmpty()).thenReturn(false);
        when(mockFile.getSize()).thenReturn((long) content.length);
        try {
            when(mockFile.getInputStream()).thenReturn(new ByteArrayInputStream(content));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return mockFile;
    }

    private AssessmentStructure createMockAssessmentStructure() {
        AssessmentMetadata metadata = AssessmentMetadata.builder()
            .title("测试量表")
            .version("1.0")
            .description("测试描述")
            .type("elderly")
            .author("测试作者")
            .build();

        AssessmentSection section1 = AssessmentSection.builder()
            .title("第一部分")
            .questions(Arrays.asList(
                createMockQuestion("问题1"),
                createMockQuestion("问题2")
            ))
            .build();

        AssessmentSection section2 = AssessmentSection.builder()
            .title("第二部分")
            .questions(Arrays.asList(
                createMockQuestion("问题3"),
                createMockQuestion("问题4")
            ))
            .build();

        ScoringRules scoringRules = ScoringRules.builder()
            .maxPossibleScore(100)
            .minPossibleScore(0)
            .passScore(60)
            .scoringMethod("sum")
            .algorithm("simple")
            .build();

        return AssessmentStructure.builder()
            .metadata(metadata)
            .section(section1)
            .section(section2)
            .scoringRules(scoringRules)
            .build();
    }

    private AssessmentQuestion createMockQuestion(final String text) {
        return AssessmentQuestion.builder()
            .text(text)
            .type(QuestionType.SINGLE_CHOICE.name())
            .required(true)
            .build();
    }
}