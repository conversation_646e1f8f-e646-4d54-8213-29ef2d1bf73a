package com.assessment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.assessment.dto.PDFFormatInfo;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * Docling服务测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Docling服务测试")
class DoclingServiceTest {

    @Mock
    private OkHttpClient httpClient;

    @Mock
    private Call call;

    @Mock
    private Response response;

    @Mock
    private ResponseBody responseBody;

    @InjectMocks
    private DoclingService doclingService;

    private MultipartFile testPdfFile;

    @BeforeEach
    void setUp() {
        
        // 设置属性
        ReflectionTestUtils.setField(doclingService, "doclingApiUrl", "http://localhost:8088");
        ReflectionTestUtils.setField(doclingService, "timeoutSeconds", 300);
        ReflectionTestUtils.setField(doclingService, "enabled", true);
        
        // 创建测试文件
        testPdfFile = new MockMultipartFile(
            "file",
            "test.pdf",
            "application/pdf",
            "PDF content".getBytes()
        );
    }

    @Test
    @DisplayName("测试服务正常初始化")
    void testServiceInitialization() {
        // Act
        doclingService.initHttpClient();
        
        // Assert
        assertThat(ReflectionTestUtils.getField(doclingService, "httpClient")).isNotNull();
    }

    @Test
    @DisplayName("测试检查服务状态 - 服务可用")
    void testIsServiceAvailableTrue() throws IOException {
        // Arrange
        when(httpClient.newCall(any())).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        
        ReflectionTestUtils.setField(doclingService, "httpClient", httpClient);

        // Act
        boolean available = doclingService.isAvailable();

        // Assert
        assertThat(available).isTrue();
    }

    @Test
    @DisplayName("测试检查服务状态 - 服务不可用")
    void testIsServiceAvailableFalse() throws IOException {
        // Arrange
        when(httpClient.newCall(any())).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        
        ReflectionTestUtils.setField(doclingService, "httpClient", httpClient);

        // Act
        boolean available = doclingService.isAvailable();

        // Assert
        assertThat(available).isFalse();
    }

    @Test
    @DisplayName("测试检查服务状态 - 网络异常")
    void testIsServiceAvailableException() throws IOException {
        // Arrange
        when(httpClient.newCall(any())).thenReturn(call);
        when(call.execute()).thenThrow(new IOException("网络连接失败"));
        
        ReflectionTestUtils.setField(doclingService, "httpClient", httpClient);

        // Act
        boolean available = doclingService.isAvailable();

        // Assert
        assertThat(available).isFalse();
    }

    @Test
    @DisplayName("测试获取服务信息")
    void testGetServiceInfo() throws IOException {
        // Arrange
        when(httpClient.newCall(any())).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("{\"status\":\"healthy\",\"version\":\"1.0\"}");
        
        ReflectionTestUtils.setField(doclingService, "httpClient", httpClient);

        // Act
        String serviceInfo = doclingService.getServiceInfo();
        
        // Assert
        assertThat(serviceInfo).isNotNull();
        assertThat(serviceInfo).contains("status");
    }

    @Test
    @DisplayName("测试转换PDF为Markdown - 成功场景")
    void testConvertPdfToMarkdown() throws IOException {
        // Arrange
        when(httpClient.newCall(any())).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(
            "{\"success\":true,\"markdown\":\"# Test Content\",\"metadata\":{\"page_count\":1}}");
        
        ReflectionTestUtils.setField(doclingService, "httpClient", httpClient);
        ReflectionTestUtils.setField(doclingService, "enabled", true);

        // Act
        String markdown = doclingService.convertPdfToMarkdown(testPdfFile);
        
        // Assert
        assertThat(markdown).isEqualTo("# Test Content");
    }

    @Test
    @DisplayName("测试转换PDF为Markdown - 空文件")
    void testConvertPDFWithEmptyFile() {
        // Arrange
        MultipartFile emptyFile = new MockMultipartFile(
            "file",
            "empty.pdf",
            "application/pdf",
            new byte[0]
        );
        
        ReflectionTestUtils.setField(doclingService, "enabled", true);

        // Act & Assert
        assertThatThrownBy(() -> doclingService.convertPdfToMarkdown(emptyFile))
            .isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("测试转换PDF为Markdown - 服务不可用")
    void testConvertPDFServiceUnavailable() {
        // Arrange
        ReflectionTestUtils.setField(doclingService, "enabled", false);

        // Act & Assert
        assertThatThrownBy(() -> doclingService.convertPdfToMarkdown(testPdfFile))
            .isInstanceOf(RuntimeException.class)
            .hasMessageContaining("Docling服务不可用");
    }

    @Test
    @DisplayName("测试转换文档为指定格式")
    void testConvertDocumentWithInfo() throws IOException {
        // Arrange
        when(httpClient.newCall(any())).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(
            "{\"success\":true,\"filename\":\"test.pdf\",\"markdown\":\"# Content\","
            + "\"metadata\":{\"page_count\":1,\"content_length\":10}}");
        
        ReflectionTestUtils.setField(doclingService, "httpClient", httpClient);
        ReflectionTestUtils.setField(doclingService, "enabled", true);

        // Act
        PDFFormatInfo result = doclingService.convertDocumentWithInfo(testPdfFile, "markdown");
        
        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getFileName()).isEqualTo("test.pdf");
        assertThat(result.getMarkdownContent()).isEqualTo("# Content");
    }

    @Test
    @DisplayName("测试批量转换PDF")
    void testBatchConvertPdfs() {
        // Arrange
        java.util.List<java.io.File> files = java.util.Arrays.asList(
            new java.io.File("test1.pdf"),
            new java.io.File("test2.pdf")
        );
        
        // Mock the service to avoid actual file operations
        ReflectionTestUtils.setField(doclingService, "enabled", false);

        // Act & Assert - This should not throw exception even with disabled service
        assertThatThrownBy(() -> doclingService.batchConvertPdfs(files))
            .isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("测试获取服务信息 - 服务不可用")
    void testGetServiceInfoUnavailable() {
        // Arrange
        ReflectionTestUtils.setField(doclingService, "enabled", false);

        // Act
        String serviceInfo = doclingService.getServiceInfo();
        
        // Assert
        assertThat(serviceInfo).isEqualTo("Docling服务不可用");
    }
}