package com.assessment.entity;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;

import com.assessment.entity.multitenant.*;
import java.time.LocalDateTime;
import java.time.LocalDate;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 实体类基础单元测试
 * 测试实体类的基本功能和数据完整性
 */
@DisplayName("实体类基础单元测试")
class EntityBasicTest {

    @Test
    @DisplayName("测试Tenant实体类基本功能")
    void testTenantEntityBasicFunctionality() {
        // Arrange & Act
        Tenant tenant = Tenant.builder()
            .code("TEST001")
            .name("测试租户")
            .industry("healthcare")
            .contactPerson("张三")
            .contactEmail("<EMAIL>")
            .contactPhone("13800138000")
            .address("测试地址")
            .maxUsers(100)
            .maxMonthlyAssessments(2000)
            .maxCustomScales(20)
            .maxStorageMb(2048)
            .isTrial(false)
            .build();
        
        // Assert
        assertThat(tenant).isNotNull();
        assertThat(tenant.getCode()).isEqualTo("TEST001");
        assertThat(tenant.getName()).isEqualTo("测试租户");
        assertThat(tenant.getIndustry()).isEqualTo("healthcare");
        assertThat(tenant.getContactPerson()).isEqualTo("张三");
        assertThat(tenant.getContactEmail()).isEqualTo("<EMAIL>");
        assertThat(tenant.getContactPhone()).isEqualTo("13800138000");
        assertThat(tenant.getAddress()).isEqualTo("测试地址");
        assertThat(tenant.getMaxUsers()).isEqualTo(100);
        assertThat(tenant.getMaxMonthlyAssessments()).isEqualTo(2000);
        assertThat(tenant.getMaxCustomScales()).isEqualTo(20);
        assertThat(tenant.getMaxStorageMb()).isEqualTo(2048);
        assertThat(tenant.getIsTrial()).isFalse();
        
        // 测试默认值
        assertThat(tenant.getSubscriptionPlan()).isEqualTo(Tenant.SubscriptionPlan.BASIC);
        assertThat(tenant.getSubscriptionStatus()).isEqualTo(Tenant.SubscriptionStatus.ACTIVE);
        assertThat(tenant.getStatus()).isEqualTo(Tenant.TenantStatus.ACTIVE);
    }

    @Test
    @DisplayName("测试Tenant枚举类型")
    void testTenantEnums() {
        // Test SubscriptionPlan
        assertThat(Tenant.SubscriptionPlan.BASIC.getDisplayName()).isEqualTo("基础版");
        assertThat(Tenant.SubscriptionPlan.BASIC.getDefaultMaxUsers()).isEqualTo(50);
        assertThat(Tenant.SubscriptionPlan.STANDARD.getDefaultMaxMonthlyAssessments()).isEqualTo(2000);
        assertThat(Tenant.SubscriptionPlan.PREMIUM.getDefaultMaxCustomScales()).isEqualTo(50);
        assertThat(Tenant.SubscriptionPlan.ENTERPRISE.getDefaultMaxStorageMb()).isEqualTo(10240);
        
        // Test SubscriptionStatus
        assertThat(Tenant.SubscriptionStatus.ACTIVE.getDisplayName()).isEqualTo("活跃");
        assertThat(Tenant.SubscriptionStatus.SUSPENDED.getDisplayName()).isEqualTo("暂停");
        assertThat(Tenant.SubscriptionStatus.EXPIRED.getDisplayName()).isEqualTo("过期");
        assertThat(Tenant.SubscriptionStatus.CANCELLED.getDisplayName()).isEqualTo("已取消");
        
        // Test TenantStatus
        assertThat(Tenant.TenantStatus.ACTIVE.getDisplayName()).isEqualTo("活跃");
        assertThat(Tenant.TenantStatus.INACTIVE.getDisplayName()).isEqualTo("非活跃");
        assertThat(Tenant.TenantStatus.SUSPENDED.getDisplayName()).isEqualTo("暂停");
        assertThat(Tenant.TenantStatus.DISABLED.getDisplayName()).isEqualTo("禁用");
        assertThat(Tenant.TenantStatus.PENDING.getDisplayName()).isEqualTo("待审核");
        
        // Test QuotaType
        assertThat(Tenant.QuotaType.values()).hasSize(4);
        assertThat(Tenant.QuotaType.valueOf("USERS")).isEqualTo(Tenant.QuotaType.USERS);
        assertThat(Tenant.QuotaType.valueOf("MONTHLY_ASSESSMENTS")).isEqualTo(Tenant.QuotaType.MONTHLY_ASSESSMENTS);
    }

    @Test
    @DisplayName("测试Tenant业务方法")
    void testTenantBusinessMethods() {
        // Arrange - 活跃的试用租户
        Tenant trialTenant = Tenant.builder()
            .status(Tenant.TenantStatus.ACTIVE)
            .subscriptionStatus(Tenant.SubscriptionStatus.ACTIVE)
            .isTrial(true)
            .trialEndDate(LocalDate.now().plusDays(10))
            .subscriptionEndDate(LocalDate.now().plusMonths(1))
            .build();
        
        // Act & Assert
        assertThat(trialTenant.isTrialActive()).isTrue();
        assertThat(trialTenant.isSubscriptionValid()).isTrue();
        assertThat(trialTenant.canUseFeature()).isTrue();
        assertThat(trialTenant.getCurrentMonthUsage()).isEqualTo(0);
        assertThat(trialTenant.isQuotaExceeded(Tenant.QuotaType.USERS)).isFalse();
        
        // Arrange - 过期的试用租户
        Tenant expiredTrialTenant = Tenant.builder()
            .status(Tenant.TenantStatus.ACTIVE)
            .subscriptionStatus(Tenant.SubscriptionStatus.EXPIRED)
            .isTrial(true)
            .trialEndDate(LocalDate.now().minusDays(1))
            .subscriptionEndDate(LocalDate.now().minusDays(1))
            .build();
        
        // Act & Assert
        assertThat(expiredTrialTenant.isTrialActive()).isFalse();
        assertThat(expiredTrialTenant.isSubscriptionValid()).isFalse();
        assertThat(expiredTrialTenant.canUseFeature()).isFalse();
    }

    @Test
    @DisplayName("测试PlatformUser实体类")
    void testPlatformUserEntity() {
        // Arrange & Act
        PlatformUser user = PlatformUser.builder()
            .username("testuser")
            .email("<EMAIL>")
            .passwordHash("hashedPassword")
            .build();
        
        // Assert
        assertThat(user).isNotNull();
        assertThat(user.getUsername()).isEqualTo("testuser");
        assertThat(user.getEmail()).isEqualTo("<EMAIL>");
        assertThat(user.getPasswordHash()).isEqualTo("hashedPassword");
    }

    @Test
    @DisplayName("测试GlobalScaleRegistry实体类")
    void testGlobalScaleRegistryEntity() {
        // Arrange & Act - 只测试基本的构造
        GlobalScaleRegistry registry = GlobalScaleRegistry.builder()
            .code("SCALE001")
            .name("测试量表")
            .category("老年人评估")
            .version("1.0")
            .build();
        
        // Assert - 只验证基本字段
        assertThat(registry).isNotNull();
        assertThat(registry.getCode()).isEqualTo("SCALE001");
        assertThat(registry.getName()).isEqualTo("测试量表");
        assertThat(registry.getCategory()).isEqualTo("老年人评估");
        assertThat(registry.getVersion()).isEqualTo("1.0");
    }

    @Test
    @DisplayName("测试AssessmentSubject实体类")
    void testAssessmentSubjectEntity() {
        // Arrange & Act - 只测试基本的构造
        AssessmentSubject subject = AssessmentSubject.builder()
            .tenantId("tenant123")
            .name("张三")
            .build();
        
        // Assert - 只验证基本字段
        assertThat(subject).isNotNull();
        assertThat(subject.getTenantId()).isEqualTo("tenant123");
        assertThat(subject.getName()).isEqualTo("张三");
    }

    @Test
    @DisplayName("测试实体类实例化")
    void testEntityInstantiation() {
        // Act & Assert
        assertThatCode(() -> Tenant.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> PlatformUser.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> GlobalScaleRegistry.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> AssessmentSubject.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> TenantUserMembership.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> TenantAssessmentRecord.builder().build()).doesNotThrowAnyException();
        
        // 验证实例类型
        assertThat(Tenant.builder().build()).isInstanceOf(Tenant.class);
        assertThat(PlatformUser.builder().build()).isInstanceOf(PlatformUser.class);
        assertThat(GlobalScaleRegistry.builder().build()).isInstanceOf(GlobalScaleRegistry.class);
        assertThat(AssessmentSubject.builder().build()).isInstanceOf(AssessmentSubject.class);
        assertThat(TenantUserMembership.builder().build()).isInstanceOf(TenantUserMembership.class);
        assertThat(TenantAssessmentRecord.builder().build()).isInstanceOf(TenantAssessmentRecord.class);
    }

    @Test
    @DisplayName("测试实体类equals和hashCode")
    void testEntityEqualsAndHashCode() {
        // Arrange
        Tenant tenant1 = Tenant.builder()
            .code("TEST001")
            .name("测试租户")
            .build();
        
        Tenant tenant2 = Tenant.builder()
            .code("TEST002")
            .name("其他租户")
            .build();
        
        // Act & Assert
        assertThat(tenant1).isEqualTo(tenant1); // 反射性
        assertThat(tenant1).isNotEqualTo(tenant2); // 不相等
        assertThat(tenant1).isNotEqualTo(null); // 与null不相等
        assertThat(tenant1).isNotEqualTo("string"); // 与其他类型不相等
        
        // hashCode基本测试
        assertThat(tenant1.hashCode()).isNotZero();
        assertThat(tenant2.hashCode()).isNotZero();
    }

    @Test
    @DisplayName("测试实体类toString方法")
    void testEntityToString() {
        // Arrange
        Tenant tenant = Tenant.builder()
            .code("TEST001")
            .name("测试租户")
            .build();
        
        PlatformUser user = PlatformUser.builder()
            .username("testuser")
            .email("<EMAIL>")
            .build();
        
        // Act & Assert
        assertThat(tenant.toString()).isNotNull();
        assertThat(tenant.toString()).contains("Tenant");
        assertThat(tenant.toString()).contains("TEST001");
        
        assertThat(user.toString()).isNotNull();
        assertThat(user.toString()).contains("PlatformUser");
        assertThat(user.toString()).contains("testuser");
    }

    @Test
    @DisplayName("测试BaseEntity审计字段")
    void testBaseEntityAuditFields() {
        // Arrange
        Tenant tenant = Tenant.builder()
            .code("TEST001")
            .name("测试租户")
            .build();
        
        LocalDateTime now = LocalDateTime.now();
        
        // Act
        tenant.setCreatedBy("testuser");
        tenant.setUpdatedBy("testuser");
        tenant.setCreatedAt(now);
        tenant.setUpdatedAt(now);
        
        // Assert
        assertThat(tenant.getCreatedBy()).isEqualTo("testuser");
        assertThat(tenant.getUpdatedBy()).isEqualTo("testuser");
        assertThat(tenant.getCreatedAt()).isNotNull();
        assertThat(tenant.getUpdatedAt()).isNotNull();
        
        // 验证时间设置成功（基本功能测试）
        assertThat(tenant.getCreatedAt()).isEqualTo(now);
        assertThat(tenant.getUpdatedAt()).isEqualTo(now);
    }

    @Test
    @DisplayName("测试实体类基本属性")
    void testEntityBasicProperties() {
        // Arrange
        Tenant tenant = Tenant.builder().build();
        PlatformUser user = PlatformUser.builder().build();
        
        // Act & Assert
        assertThat(tenant.getClass().getSimpleName()).isEqualTo("Tenant");
        assertThat(tenant.getClass().getPackage().getName()).isEqualTo("com.assessment.entity.multitenant");
        
        assertThat(user.getClass().getSimpleName()).isEqualTo("PlatformUser");
        assertThat(user.getClass().getPackage().getName()).isEqualTo("com.assessment.entity.multitenant");
        
        // 测试实体注解存在
        assertThat(tenant.getClass().isAnnotationPresent(jakarta.persistence.Entity.class)).isTrue();
        assertThat(user.getClass().isAnnotationPresent(jakarta.persistence.Entity.class)).isTrue();
    }

    @Test
    @DisplayName("测试实体类继承关系")
    void testEntityInheritance() {
        // Arrange
        Tenant tenant = Tenant.builder().build();
        
        // Act & Assert - 验证Tenant继承自BaseEntity
        assertThat(tenant).isInstanceOf(BaseEntity.class);
        assertThat(tenant.getClass().getSuperclass()).isEqualTo(BaseEntity.class);
    }

    @Test
    @DisplayName("测试实体类哈希码一致性")
    void testEntityHashCodeConsistency() {
        // Arrange
        Tenant tenant = Tenant.builder()
            .code("TEST001")
            .name("测试租户")
            .build();
        
        // Act & Assert
        int hashCode1 = tenant.hashCode();
        int hashCode2 = tenant.hashCode();
        
        assertThat(hashCode1).isEqualTo(hashCode2);
        assertThat(tenant).isEqualTo(tenant); // 自等性
    }

    @Test
    @DisplayName("测试实体类方法存在性")
    void testEntityMethodExistence() {
        // Arrange
        Class<?> tenantClass = Tenant.class;
        
        // Act & Assert - 验证关键方法存在
        assertThat(tenantClass.getMethods()).anyMatch(
            method -> method.getName().equals("toString") && method.getParameterCount() == 0
        );
        assertThat(tenantClass.getMethods()).anyMatch(
            method -> method.getName().equals("equals") && method.getParameterCount() == 1
        );
        assertThat(tenantClass.getMethods()).anyMatch(
            method -> method.getName().equals("hashCode") && method.getParameterCount() == 0
        );
    }
}