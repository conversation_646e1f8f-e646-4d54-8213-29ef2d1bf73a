package com.assessment.util;

import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.TenantUserMembership;


import java.time.LocalDate;
import java.util.UUID;

/**
 * 测试数据工厂类
 * 提供创建测试实体的便捷方法
 */
public final class TestDataFactory {

    /**
     * 私有构造器，防止实例化
     */
    private TestDataFactory() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 创建测试租户
     */
    public static Tenant createTestTenant(final String code, final String name) {
        Tenant tenant = new Tenant();
        tenant.setId(UUID.randomUUID());
        tenant.setCode(code);
        tenant.setName(name);
        tenant.setIndustry("healthcare");
        tenant.setContactPerson("Test Contact");
        tenant.setContactEmail("test@" + code + ".com");
        tenant.setContactPhone("**********");
        tenant.setSubscriptionPlan(Tenant.SubscriptionPlan.STANDARD);
        tenant.setSubscriptionStatus(Tenant.SubscriptionStatus.ACTIVE);
        tenant.setSubscriptionStartDate(LocalDate.now());
        tenant.setSubscriptionEndDate(LocalDate.now().plusYears(1));
        tenant.setMaxUsers(100);
        tenant.setMaxMonthlyAssessments(5000);
        tenant.setMaxCustomScales(20);
        tenant.setMaxStorageMb(2048);
        tenant.setStatus(Tenant.TenantStatus.ACTIVE);
        tenant.setIsTrial(false);
        return tenant;
    }

    /**
     * 创建默认测试租户
     */
    public static Tenant createDefaultTestTenant() {
        return createTestTenant("TEST001", "Test Hospital");
    }

    /**
     * 创建测试用户
     */
    public static PlatformUser createTestUser(final String username, final String email) {
        PlatformUser user = new PlatformUser();
        user.setId(UUID.randomUUID());
        user.setUsername(username);
        user.setEmail(email);
        user.setPasswordHash("$2a$04$test.password.hash"); // 测试密码哈希
        user.setFirstName("Test");
        user.setLastName("User");
        user.setPhone("**********");
        user.setPlatformRole(PlatformUser.PlatformRole.USER);
        user.setIsActive(true);
        user.setEmailVerified(true);
        return user;
    }

    /**
     * 创建默认测试用户
     */
    public static PlatformUser createDefaultTestUser() {
        return createTestUser("testuser", "<EMAIL>");
    }

    /**
     * 创建租户用户关联
     */
    public static TenantUserMembership createTenantMembership(
            final UUID tenantId, 
            final UUID userId, 
            final String role) {
        TenantUserMembership membership = new TenantUserMembership();
        membership.setId(UUID.randomUUID().toString());
        membership.setTenantId(tenantId.toString());
        membership.setUserId(userId.toString());
        membership.setTenantRole(TenantUserMembership.TenantRole.valueOf(role));
        membership.setDisplayName("Test User");
        membership.setProfessionalTitle("Test Assessor");
        membership.setDepartment("Testing Department");
        membership.setStatus(TenantUserMembership.MembershipStatus.ACTIVE);
        return membership;
    }

    /**
     * 创建管理员成员关系
     */
    public static TenantUserMembership createAdminMembership(final UUID tenantId, final UUID userId) {
        return createTenantMembership(tenantId, userId, "ADMIN");
    }

    /**
     * 创建评估师成员关系
     */
    public static TenantUserMembership createAssessorMembership(final UUID tenantId, final UUID userId) {
        return createTenantMembership(tenantId, userId, "ASSESSOR");
    }

    /**
     * 创建审核员成员关系
     */
    public static TenantUserMembership createReviewerMembership(final UUID tenantId, final UUID userId) {
        return createTenantMembership(tenantId, userId, "REVIEWER");
    }

    /**
     * 创建查看者成员关系
     */
    public static TenantUserMembership createViewerMembership(final UUID tenantId, final UUID userId) {
        return createTenantMembership(tenantId, userId, "VIEWER");
    }
}