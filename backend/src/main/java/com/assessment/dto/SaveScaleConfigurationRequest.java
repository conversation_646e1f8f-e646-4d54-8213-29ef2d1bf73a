package com.assessment.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;

/** 保存量表配置请求DTO */
public class SaveScaleConfigurationRequest {

  /** 量表名称 */
  private String scaleName;

  /** 量表类型 */
  private String scaleType;

  /** 对应的数据库表名 */
  private String tableName;

  /** 表注释 */
  private String tableComment;

  /** 字段列表 */
  private List<ScaleField> fields;

  /** 配置描述 */
  private String description;

  /** 是否启用 */
  private Boolean enabled = true;

  /** 配置元数据 */
  private ConfigurationMetadata metadata;

  public SaveScaleConfigurationRequest() { }

  public SaveScaleConfigurationRequest(SaveScaleConfigurationRequest other) {
    if (other != null) {
      this.scaleName = other.scaleName;
      this.scaleType = other.scaleType;
      this.tableName = other.tableName;
      this.tableComment = other.tableComment;
      this.description = other.description;
      this.enabled = other.enabled;
      this.setFields(other.fields);
      this.setMetadata(other.metadata);
    }
  }

  // Getters and Setters

  public String getScaleName() {
    return scaleName;
  }

  public void setScaleName(String scaleName) {
    this.scaleName = scaleName;
  }

  public String getScaleType() {
    return scaleType;
  }

  public void setScaleType(String scaleType) {
    this.scaleType = scaleType;
  }

  public String getTableName() {
    return tableName;
  }

  public void setTableName(String tableName) {
    this.tableName = tableName;
  }

  public String getTableComment() {
    return tableComment;
  }

  public void setTableComment(String tableComment) {
    this.tableComment = tableComment;
  }

  public List<ScaleField> getFields() {
    return fields == null
        ? null
        : fields.stream().map(ScaleField::new).collect(Collectors.toList());
  }

  public void setFields(List<ScaleField> fields) {
    this.fields =
        fields == null ? null : fields.stream().map(ScaleField::new).collect(Collectors.toList());
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public Boolean getEnabled() {
    return enabled;
  }

  public void setEnabled(Boolean enabled) {
    this.enabled = enabled;
  }

  public ConfigurationMetadata getMetadata() {
    return metadata == null ? null : new ConfigurationMetadata(metadata);
  }

  public void setMetadata(ConfigurationMetadata metadata) {
    this.metadata = metadata == null ? null : new ConfigurationMetadata(metadata);
  }

  @Data
  public static class ScaleField {
    /** 字段名 */
    private String name;

    /** 字段类型 */
    private String type;

    /** 字段长度 */
    private String length;

    /** 是否必填 */
    private Boolean required;

    /** 字段显示名称 */
    private String displayName;

    /** 字段描述 */
    private String description;

    /** 默认值 */
    private String defaultValue;

    /** 字段排序 */
    private Integer sortOrder;

    /** 是否在表单中显示 */
    private Boolean showInForm = true;

    /** 是否在列表中显示 */
    private Boolean showInList = false;

    /** 字段验证规则 */
    private String validationRules;

    public ScaleField() { }

    public ScaleField(ScaleField other) {
      if (other != null) {
        this.name = other.name;
        this.type = other.type;
        this.length = other.length;
        this.required = other.required;
        this.displayName = other.displayName;
        this.description = other.description;
        this.defaultValue = other.defaultValue;
        this.sortOrder = other.sortOrder;
        this.showInForm = other.showInForm;
        this.showInList = other.showInList;
        this.validationRules = other.validationRules;
      }
    }
  }

  public static class ConfigurationMetadata {
    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private String createdAt;

    /** 最后修改人 */
    private String lastModifiedBy;

    /** 最后修改时间 */
    private String lastModifiedAt;

    /** 版本号 */
    private String version = "1.0.0";

    /** 标签 */
    private List<String> tags;

    /** 备注 */
    private String notes;

    public ConfigurationMetadata() { }

    public ConfigurationMetadata(ConfigurationMetadata other) {
      if (other != null) {
        this.createdBy = other.createdBy;
        this.createdAt = other.createdAt;
        this.lastModifiedBy = other.lastModifiedBy;
        this.lastModifiedAt = other.lastModifiedAt;
        this.version = other.version;
        this.notes = other.notes;
        this.setTags(other.tags);
      }
    }

    // Getters and Setters

    public String getCreatedBy() {
      return createdBy;
    }

    public void setCreatedBy(String createdBy) {
      this.createdBy = createdBy;
    }

    public String getCreatedAt() {
      return createdAt;
    }

    public void setCreatedAt(String createdAt) {
      this.createdAt = createdAt;
    }

    public String getLastModifiedBy() {
      return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
      this.lastModifiedBy = lastModifiedBy;
    }

    public String getLastModifiedAt() {
      return lastModifiedAt;
    }

    public void setLastModifiedAt(String lastModifiedAt) {
      this.lastModifiedAt = lastModifiedAt;
    }

    public String getVersion() {
      return version;
    }

    public void setVersion(String version) {
      this.version = version;
    }

    public List<String> getTags() {
      return tags == null ? null : new ArrayList<>(tags);
    }

    public void setTags(List<String> tags) {
      this.tags = tags == null ? null : new ArrayList<>(tags);
    }

    public String getNotes() {
      return notes;
    }

    public void setNotes(String notes) {
      this.notes = notes;
    }
  }
}
