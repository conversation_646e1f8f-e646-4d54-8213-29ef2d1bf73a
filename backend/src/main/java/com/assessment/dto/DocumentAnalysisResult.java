package com.assessment.dto;

import java.util.List;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/** 文档分析结果DTO */
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class DocumentAnalysisResult {

  /** 是否成功 */
  private boolean success;

  /** 消息 */
  private String message;

  /** 量表类型 */
  private String scaleType;

  /** 建议的表名 */
  private String tableName;

  /** 表注释 */
  private String tableComment;

  /** 识别的字段数量 */
  private Integer fieldCount;

  /** 分析置信度 (0-100) */
  private Integer confidence;

  /** 字段列表 */
  private List<DatabaseField> fields;

  /** AI分析的原始输出 */
  private String aiRawResponse;

  /** AI思考过程 */
  private String aiThinkingProcess;

  /** 生成的SQL建表语句 */
  private String generatedSql;

  /** 设计说明 */
  private String designExplanation;

  /** 分析耗时（毫秒） */
  private Long parsingTimeMs;

  // 使用 Builder 模式，移除多参数构造函数以符合 Checkstyle 规范

  // Getters and Setters

  public boolean isSuccess() {
    return success;
  }

  public void setSuccess(boolean success) {
    this.success = success;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getScaleType() {
    return scaleType;
  }

  public void setScaleType(String scaleType) {
    this.scaleType = scaleType;
  }

  public String getTableName() {
    return tableName;
  }

  public void setTableName(String tableName) {
    this.tableName = tableName;
  }

  public String getTableComment() {
    return tableComment;
  }

  public void setTableComment(String tableComment) {
    this.tableComment = tableComment;
  }

  public Integer getFieldCount() {
    return fieldCount;
  }

  public void setFieldCount(Integer fieldCount) {
    this.fieldCount = fieldCount;
  }

  public Integer getConfidence() {
    return confidence;
  }

  public void setConfidence(Integer confidence) {
    this.confidence = confidence;
  }

  public List<DatabaseField> getFields() {
    if (this.fields == null) {
      return null;
    }
    return this.fields.stream().map(DatabaseField::new).collect(Collectors.toList());
  }

  public void setFields(List<DatabaseField> fields) {
    if (fields == null) {
      this.fields = null;
    } else {
      this.fields = fields.stream().map(DatabaseField::new).collect(Collectors.toList());
    }
  }

  public String getAiRawResponse() {
    return aiRawResponse;
  }

  public void setAiRawResponse(String aiRawResponse) {
    this.aiRawResponse = aiRawResponse;
  }

  public String getAiThinkingProcess() {
    return aiThinkingProcess;
  }

  public void setAiThinkingProcess(String aiThinkingProcess) {
    this.aiThinkingProcess = aiThinkingProcess;
  }

  public String getGeneratedSql() {
    return generatedSql;
  }

  public void setGeneratedSql(String generatedSql) {
    this.generatedSql = generatedSql;
  }

  public String getDesignExplanation() {
    return designExplanation;
  }

  public void setDesignExplanation(String designExplanation) {
    this.designExplanation = designExplanation;
  }

  public Long getParsingTimeMs() {
    return parsingTimeMs;
  }

  public void setParsingTimeMs(Long parsingTimeMs) {
    this.parsingTimeMs = parsingTimeMs;
  }

  @Builder
  @AllArgsConstructor
  public static class DatabaseField {
    /** 字段名 */
    private String name;

    /** 字段类型 */
    private String type;

    /** 字段长度 */
    private String length;

    /** 是否允许为空 */
    private Boolean nullable;

    /** 字段注释 */
    private String comment;

    /** 默认值 */
    private String defaultValue;

    /** 是否为主键 */
    private Boolean isPrimaryKey;

    /** 是否为外键 */
    private Boolean isForeignKey;

    /** 外键引用的表 */
    private String foreignKeyTable;

    /** 外键引用的列 */
    private String foreignKeyColumn;

    /** 字段重要性评分 (0-100) */
    private Integer importance;

    /** AI分析对此字段的置信度 (0-100) */
    private Integer confidence;

    // No-args constructor required for Jackson deserialization
    public DatabaseField() { }

    // Copy constructor for defensive copying
    public DatabaseField(DatabaseField other) {
      if (other != null) {
        this.name = other.name;
        this.type = other.type;
        this.length = other.length;
        this.nullable = other.nullable;
        this.comment = other.comment;
        this.defaultValue = other.defaultValue;
        this.isPrimaryKey = other.isPrimaryKey;
        this.isForeignKey = other.isForeignKey;
        this.foreignKeyTable = other.foreignKeyTable;
        this.foreignKeyColumn = other.foreignKeyColumn;
        this.importance = other.importance;
        this.confidence = other.confidence;
      }
    }

    // Getters and Setters for all fields

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public String getType() {
      return type;
    }

    public void setType(String type) {
      this.type = type;
    }

    public String getLength() {
      return length;
    }

    public void setLength(String length) {
      this.length = length;
    }

    public Boolean getNullable() {
      return nullable;
    }

    public void setNullable(Boolean nullable) {
      this.nullable = nullable;
    }

    public String getComment() {
      return comment;
    }

    public void setComment(String comment) {
      this.comment = comment;
    }

    public String getDefaultValue() {
      return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
      this.defaultValue = defaultValue;
    }

    public Boolean getIsPrimaryKey() {
      return isPrimaryKey;
    }

    public void setIsPrimaryKey(Boolean primaryKey) {
      isPrimaryKey = primaryKey;
    }

    public Boolean getIsForeignKey() {
      return isForeignKey;
    }

    public void setIsForeignKey(Boolean foreignKey) {
      isForeignKey = foreignKey;
    }

    public String getForeignKeyTable() {
      return foreignKeyTable;
    }

    public void setForeignKeyTable(String foreignKeyTable) {
      this.foreignKeyTable = foreignKeyTable;
    }

    public String getForeignKeyColumn() {
      return foreignKeyColumn;
    }

    public void setForeignKeyColumn(String foreignKeyColumn) {
      this.foreignKeyColumn = foreignKeyColumn;
    }

    public Integer getImportance() {
      return importance;
    }

    public void setImportance(Integer importance) {
      this.importance = importance;
    }

    public Integer getConfidence() {
      return confidence;
    }

    public void setConfidence(Integer confidence) {
      this.confidence = confidence;
    }
  }
}
