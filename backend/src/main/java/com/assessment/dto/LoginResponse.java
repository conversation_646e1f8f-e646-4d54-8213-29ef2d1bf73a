package com.assessment.dto;

import com.assessment.entity.multitenant.PlatformUser;

public class LoginResponse {
  private String token;
  private String refreshToken;
  private PlatformUser user;
  private long expiresIn;

  public LoginResponse() { }

  public LoginResponse(String token, String refreshToken, PlatformUser user, long expiresIn) {
    this.token = token;
    this.refreshToken = refreshToken;
    this.user = (user != null) ? new PlatformUser(user) : null;
    this.expiresIn = expiresIn;
  }

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public String getRefreshToken() {
    return refreshToken;
  }

  public void setRefreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
  }

  public PlatformUser getUser() {
    return (this.user != null) ? new PlatformUser(this.user) : null;
  }

  public void setUser(PlatformUser user) {
    this.user = (user != null) ? new PlatformUser(user) : null;
  }

  public long getExpiresIn() {
    return expiresIn;
  }

  public void setExpiresIn(long expiresIn) {
    this.expiresIn = expiresIn;
  }

  public static LoginResponse of(String token, String refreshToken, PlatformUser user) {
    return new LoginResponse(token, refreshToken, user, 0);
  }
}
