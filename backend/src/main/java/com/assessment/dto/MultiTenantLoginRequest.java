package com.assessment.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 多租户登录请求DTO 支持租户名+用户名+密码的登录方式 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MultiTenantLoginRequest {

  @NotBlank(message = "租户代码不能为空")
  private String tenantCode;

  @NotBlank(message = "用户名不能为空")
  private String username;

  @NotBlank(message = "密码不能为空")
  private String password;

  // 可选字段：记住登录状态
  @Builder.Default
  private boolean rememberMe = false;

  // 可选字段：客户端信息
  private String clientInfo;

  // 验证码相关字段
  private String captchaToken;
  private String captchaVerification;

  /** 构造完整的用户标识（租户代码_用户名） 用于超级管理员等特殊账户的兼容处理 */
  public String getFullUsername() {
    // 如果是超级管理员或平台管理员，直接返回用户名
    if ("superadmin".equals(username) || "admin".equals(username)) {
      return username;
    }

    // 如果用户名已经包含租户前缀（包含下划线），直接返回
    if (username.contains("_")) {
      return username;
    }

    // 否则构造: 租户代码_用户名
    return tenantCode + "_" + username;
  }

  /** 获取纯用户名（去除租户前缀） */
  public String getPureUsername() {
    if (username.contains("_")) {
      String[] parts = username.split("_", 2);
      return parts.length > 1 ? parts[1] : username;
    }
    return username;
  }
}
