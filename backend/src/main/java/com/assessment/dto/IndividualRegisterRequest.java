package com.assessment.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 个人用户注册请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualRegisterRequest {
    
    // 常量定义，避免魔术数字
    private static final int PHONE_MIN_LENGTH = 11;
    private static final int PHONE_PREFIX_LENGTH = 3;
    private static final int PHONE_SUFFIX_START = 9;
    private static final int EMAIL_PREFIX_LENGTH = 2;

    /**
     * 邮箱地址（开发阶段主要注册方式）
     */
    @NotBlank(message = "邮箱不能为空")
    @Pattern(regexp = "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$", message = "请输入正确的邮箱格式")
    private String email;

    /**
     * 手机号（可选，预留功能）
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号格式")
    private String phone;

    /**
     * 验证码（开发阶段可选）
     */
    private String verificationCode;

    /**
     * 用户姓名
     */
    @NotBlank(message = "姓名不能为空")
    @Size(min = 2, max = 20, message = "姓名长度应在2-20个字符之间")
    private String name;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 50, message = "密码长度应在6-50个字符之间")
    private String password;

    /**
     * 身份证号（可选，用于实名认证）
     */
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "请输入正确的身份证号格式")
    private String idCard;

    /**
     * 性别（可选）
     */
    @Pattern(regexp = "^(MALE|FEMALE|OTHER)$", message = "性别必须是MALE、FEMALE或OTHER")
    private String gender;

    /**
     * 出生日期（可选，格式：yyyy-MM-dd）
     */
    private String birthDate;

    /**
     * 服务套餐类型
     */
    @Builder.Default
    private String serviceType = "FREE"; // FREE, PREMIUM

    /**
     * 注册来源
     */
    private String source; // WEB, MOBILE, WECHAT

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 推广渠道码（可选）
     */
    private String referralCode;

    /**
     * 用户协议确认
     */
    @Builder.Default
    private Boolean agreedToTerms = false;

    /**
     * 隐私政策确认
     */
    @Builder.Default
    private Boolean agreedToPrivacy = false;

    /**
     * 验证注册数据完整性（开发阶段简化版）
     */
    public boolean isValid() {
        // 开发阶段：只要有邮箱、姓名、密码即可
        return email != null && !email.trim().isEmpty()
               && name != null && !name.trim().isEmpty()
               && password != null && !password.trim().isEmpty();
    }

    /**
     * 获取主要标识符（用于登录）
     */
    public String getPrimaryIdentifier() {
        // 开发阶段：邮箱作为主要登录标识
        return email;
    }

    /**
     * 是否启用邮箱作为备用登录方式
     */
    public boolean hasEmailBackup() {
        return email != null && !email.trim().isEmpty();
    }

    /**
     * 是否提供了实名信息
     */
    public boolean hasRealNameInfo() {
        return idCard != null && !idCard.trim().isEmpty();
    }

    /**
     * 获取安全的日志信息（脱敏处理）
     */
    public String getSecureLogInfo() {
        String maskedPhone = phone != null && phone.length() >= PHONE_MIN_LENGTH
            ? phone.substring(0, PHONE_PREFIX_LENGTH) + "****" + phone.substring(PHONE_SUFFIX_START) : "***";
        String maskedEmail = email != null && email.contains("@")
            ? email.substring(0, EMAIL_PREFIX_LENGTH) + "***@" + email.split("@")[1] : "未提供";
        
        return String.format("手机: %s, 邮箱: %s, 姓名: %s, 套餐: %s", 
                           maskedPhone, maskedEmail, name, serviceType);
    }

    /**
     * 获取用户显示名称
     */
    public String getDisplayName() {
        return name;
    }

    /**
     * 转换为用户实体的基础信息
     */
    public String toUserDescription() {
        return String.format("个人用户注册: %s, 服务类型: %s, 注册来源: %s", 
                           name, serviceType, source != null ? source : "WEB");
    }

    @Override
    public String toString() {
        return String.format("IndividualRegisterRequest{phone='%s', name='%s', serviceType='%s', masked=true}", 
                           phone != null ? phone.substring(0, PHONE_PREFIX_LENGTH) + "****" : "null", 
                           name, serviceType);
    }
}