package com.assessment.dto;

import lombok.Data;

/** 系统量表查询请求参数DTO */
@Data
public class SystemScaleQueryRequest {
    
    /** 页码 */
    private int page = 0;
    
    /** 每页大小 */
    private int size = 20;
    
    /** 搜索关键词 */
    private String search;
    
    /** 分类 */
    private String category;
    
    /** 状态 */
    private String status;
    
    /** 可见性 */
    private String visibility;
    
    /** 发布者类型 */
    private String publisherType;
    
    /** 排序字段 */
    private String sortField;
    
    /** 排序顺序 */
    private String sortOrder;
}