package com.assessment.dto;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** PDF格式信息DTO 包含PDF转换的详细信息和状态 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PDFFormatInfo {

  /** 转换是否成功 */
  private boolean success;

  /** 文件名 */
  private String fileName;

  /** 文件名（别名用于兼容） */
  private String name;

  /** 文件大小（字节） */
  private long fileSize;

  /** Markdown内容 */
  private String markdownContent;

  /** 处理耗时（毫秒） */
  private long processingTimeMs;

  /** 错误信息 */
  private String errorMessage;

  /** 解析状态信息 */
  private ParseStatusInfo parseStatus;

  /** 文档元数据 */
  private String metadata;

  /** 转换质量评分 (0-100) */
  private Integer qualityScore;

  /** 提取的表格数量 */
  private Integer tableCount;

  /** 提取的图片数量 */
  private Integer imageCount;

  // 使用 Builder 模式，移除多参数构造函数以符合 Checkstyle 规范

  // --- Safe getter and setter for parseStatus ---

  public ParseStatusInfo getParseStatus() {
    return this.parseStatus == null ? null : new ParseStatusInfo(this.parseStatus);
  }

  public void setParseStatus(ParseStatusInfo parseStatus) {
    this.parseStatus = parseStatus == null ? null : new ParseStatusInfo(parseStatus);
  }
}
