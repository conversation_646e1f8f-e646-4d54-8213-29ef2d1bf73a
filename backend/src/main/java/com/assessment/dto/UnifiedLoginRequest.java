package com.assessment.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * 统一登录请求DTO
 * 支持个人用户和机构用户两种登录方式
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedLoginRequest {
    
    // 常量定义，避免魔术数字
    private static final int MIN_IDENTIFIER_LENGTH = 3;
    private static final int PHONE_PREFIX_LENGTH = 3;
    private static final int PHONE_SUFFIX_LENGTH = 2;
    private static final int EMAIL_PREFIX_LENGTH = 2;

    /**
     * 登录类型枚举
     */
    public enum LoginType {
        INDIVIDUAL,    // 个人用户
        INSTITUTIONAL  // 机构用户
    }

    /**
     * 登录类型
     */
    @NotBlank(message = "登录类型不能为空")
    private String loginType;

    // === 个人用户登录字段 ===
    
    /**
     * 个人用户标识符（手机号或邮箱）
     */
    private String identifier;

    // === 机构用户登录字段 ===
    
    /**
     * 机构用户名
     */
    private String username;

    /**
     * 机构代码
     */
    private String tenantCode;

    // === 通用字段 ===
    
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 验证码（可选，用于安全验证）
     */
    private String verificationCode;

    /**
     * 设备信息（可选，用于设备绑定）
     */
    private String deviceInfo;

    /**
     * 获取登录类型枚举
     */
    public LoginType getLoginTypeEnum() {
        try {
            return LoginType.valueOf(loginType.toUpperCase());
        } catch (Exception e) {
            return LoginType.INDIVIDUAL; // 默认为个人用户
        }
    }

    /**
     * 是否为个人用户登录
     */
    public boolean isIndividualLogin() {
        return LoginType.INDIVIDUAL.equals(getLoginTypeEnum());
    }

    /**
     * 是否为机构用户登录
     */
    public boolean isInstitutionalLogin() {
        return LoginType.INSTITUTIONAL.equals(getLoginTypeEnum());
    }

    /**
     * 获取主要标识符
     * 个人用户返回identifier，机构用户返回username
     */
    public String getPrimaryIdentifier() {
        if (isIndividualLogin()) {
            return identifier;
        } else {
            return username;
        }
    }

    /**
     * 验证请求数据完整性
     */
    public boolean isValid() {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }

        if (isIndividualLogin()) {
            return identifier != null && !identifier.trim().isEmpty();
        } else {
            return username != null && !username.trim().isEmpty()
                   && tenantCode != null && !tenantCode.trim().isEmpty();
        }
    }

    /**
     * 获取安全的日志信息（脱敏处理）
     */
    public String getSecureLogInfo() {
        String maskedIdentifier;
        if (isIndividualLogin()) {
            maskedIdentifier = maskIdentifier(identifier);
        } else {
            maskedIdentifier = maskIdentifier(username) + "@" + tenantCode;
        }
        return String.format("类型: %s, 用户: %s", loginType, maskedIdentifier);
    }

    /**
     * 标识符脱敏处理
     */
    private String maskIdentifier(String inputIdentifier) {
        if (inputIdentifier == null || inputIdentifier.length() <= MIN_IDENTIFIER_LENGTH) {
            return "***";
        }
        
        // 手机号脱敏：显示前3位和后2位
        if (inputIdentifier.matches("^1[3-9]\\d{9}$")) {
            return inputIdentifier.substring(0, PHONE_PREFIX_LENGTH) + "****"
                + inputIdentifier.substring(inputIdentifier.length() - PHONE_SUFFIX_LENGTH);
        }
        
        // 邮箱脱敏：显示前2位和@后的域名
        if (inputIdentifier.contains("@")) {
            String[] parts = inputIdentifier.split("@");
            String localPart = parts[0];
            String maskedLocal = localPart.length() > EMAIL_PREFIX_LENGTH
                ? localPart.substring(0, EMAIL_PREFIX_LENGTH) + "***" : "***";
            return maskedLocal + "@" + parts[1];
        }
        
        // 其他格式：显示前2位和后1位
        return inputIdentifier.substring(0, 2) + "***"
               + inputIdentifier.substring(inputIdentifier.length() - 1);
    }

    /**
     * 转换为多租户登录请求（用于机构用户）
     */
    public com.assessment.dto.MultiTenantLoginRequest toMultiTenantLoginRequest() {
        if (!isInstitutionalLogin()) {
            throw new IllegalStateException("只有机构用户可以转换为MultiTenantLoginRequest");
        }
        
        return com.assessment.dto.MultiTenantLoginRequest.builder()
                .username(username)
                .tenantCode(tenantCode)
                .password(password)
                .build();
    }

    @Override
    public String toString() {
        return String.format("UnifiedLoginRequest{loginType='%s', identifier='%s', masked=true}", 
                           loginType, getSecureLogInfo());
    }
}