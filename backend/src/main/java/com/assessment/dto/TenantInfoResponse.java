package com.assessment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 租户基础信息响应DTO
 * 用于前端显示租户列表和验证租户代码
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "租户基础信息")
public class TenantInfoResponse {

    @Schema(description = "租户代码", example = "PLATFORM")
    private String code;

    @Schema(description = "租户名称", example = "平台管理")
    private String name;

    @Schema(description = "所属行业", example = "系统")
    private String industry;

    @Schema(description = "订阅计划", example = "ENTERPRISE")
    private String subscriptionPlan;

    @Schema(description = "机构层级", example = "系统级")
    private String level;
}