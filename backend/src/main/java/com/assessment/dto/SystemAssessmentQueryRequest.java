package com.assessment.dto;

import lombok.Data;

/** 系统评估记录查询请求参数DTO */
@Data
public class SystemAssessmentQueryRequest {
    
    /** 页码 */
    private int page = 0;
    
    /** 每页大小 */
    private int size = 20;
    
    /** 租户ID */
    private String tenantId;
    
    /** 状态 */
    private String status;
    
    /** 评估员ID */
    private String assessorId;
    
    /** 量表ID */
    private String scaleId;
    
    /** 开始日期 */
    private String startDate;
    
    /** 结束日期 */
    private String endDate;
    
    /** 排序字段 */
    private String sortField;
    
    /** 排序顺序 */
    private String sortOrder;
}