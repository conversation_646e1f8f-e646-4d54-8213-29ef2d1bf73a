package com.assessment.controller;

import com.assessment.dto.IndividualRegisterRequest;
import com.assessment.dto.UnifiedLoginRequest;
import com.assessment.dto.MultiTenantLoginResponse;
import com.assessment.entity.IndividualUser;
import com.assessment.service.IndividualUserService;
import com.assessment.service.MultiTenantAuthService;
import com.assessment.service.UserIdentityService;
import com.assessment.service.VerificationCodeService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一认证控制器
 * 支持个人用户和机构用户的双轨登录系统
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@RestController
@RequestMapping("/api/unified-auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "统一认证", description = "个人用户和机构用户的统一认证API")
public class UnifiedAuthController {

    /** 验证码有效期（秒） */
    private static final int VERIFICATION_CODE_EXPIRY_SECONDS = 300;

    private final IndividualUserService individualUserService;
    private final MultiTenantAuthService multiTenantAuthService;
    private final UserIdentityService userIdentityService;
    private final VerificationCodeService verificationCodeService;

    /**
     * 个人用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "个人用户注册", description = "个人用户通过邮箱注册账号（开发阶段简化版）")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "注册成功"),
        @ApiResponse(responseCode = "400", description = "注册信息有误"),
        @ApiResponse(responseCode = "409", description = "邮箱已被注册")
    })
    public ResponseEntity<Map<String, Object>> registerIndividualUser(
            @Valid @RequestBody IndividualRegisterRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("个人用户注册请求: {}", request.getSecureLogInfo());

        try {
            // 设置注册来源
            request.setSource("WEB");
            request.setDeviceInfo(getUserAgent(httpRequest));
            
            // 开发阶段：自动同意协议
            request.setAgreedToTerms(true);
            request.setAgreedToPrivacy(true);
            
            // 执行注册
            IndividualUser user = individualUserService.registerUser(request);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "注册成功！欢迎使用智能评估平台");
            response.put("userId", user.getId().toString());
            response.put("email", user.getEmail());
            response.put("displayName", user.getEffectiveDisplayName());
            response.put("serviceType", user.getServiceType().name());
            
            log.info("个人用户注册成功: userId={}, email={}", user.getId(), user.getEmail());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("个人用户注册失败: {}", e.getMessage());
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 统一登录接口
     * 自动识别个人用户和机构用户
     */
    @PostMapping("/login")
    @Operation(summary = "统一登录", description = "支持个人用户（邮箱/手机号）和机构用户（用户名@机构代码）的统一登录")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "登录成功"),
        @ApiResponse(responseCode = "400", description = "登录信息有误"),
        @ApiResponse(responseCode = "401", description = "认证失败")
    })
    public ResponseEntity<MultiTenantLoginResponse> unifiedLogin(
            @Valid @RequestBody UnifiedLoginRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("统一登录请求: {}", request.getSecureLogInfo());

        try {
            // 设置设备信息
            request.setDeviceInfo(getUserAgent(httpRequest));
            
            MultiTenantLoginResponse response;
            
            // 根据登录类型路由到不同的认证服务
            if (request.isIndividualLogin()) {
                // 个人用户登录
                response = individualUserService.authenticateIndividualUser(request, httpRequest);
                log.info("个人用户登录成功: {}", userIdentityService.maskIndividualIdentifier(request.getIdentifier()));
                
            } else {
                // 机构用户登录（使用原有的多租户认证）
                response = multiTenantAuthService.authenticate(request.toMultiTenantLoginRequest());
                log.info("机构用户登录成功: {}", request.getSecureLogInfo());
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("统一登录失败: {}, 错误: {}", request.getSecureLogInfo(), e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 检查邮箱可用性
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱可用性", description = "检查邮箱是否已被注册")
    public ResponseEntity<Map<String, Object>> checkEmailAvailability(
            @Parameter(description = "要检查的邮箱地址") @RequestParam String email) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证邮箱格式
            if (!userIdentityService.isValidEmail(email)) {
                response.put("available", false);
                response.put("message", "邮箱格式不正确");
                return ResponseEntity.badRequest().body(response);
            }
            
            boolean available = individualUserService.isEmailAvailable(email);
            response.put("available", available);
            response.put("message", available ? "邮箱可以使用" : "邮箱已被注册");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检查邮箱可用性失败: email={}, error={}", email, e.getMessage());
            response.put("available", false);
            response.put("message", "检查失败，请稍后重试");
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 检查手机号可用性
     */
    @GetMapping("/check-phone")
    @Operation(summary = "检查手机号可用性", description = "检查手机号是否已被注册")
    public ResponseEntity<Map<String, Object>> checkPhoneAvailability(
            @Parameter(description = "要检查的手机号") @RequestParam String phone) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证手机号格式
            if (!userIdentityService.isValidPhone(phone)) {
                response.put("available", false);
                response.put("message", "手机号格式不正确");
                return ResponseEntity.badRequest().body(response);
            }
            
            boolean available = individualUserService.isPhoneAvailable(phone);
            response.put("available", available);
            response.put("message", available ? "手机号可以使用" : "手机号已被注册");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检查手机号可用性失败: phone={}, error={}", phone, e.getMessage());
            response.put("available", false);
            response.put("message", "检查失败，请稍后重试");
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/send-email-verification")
    @Operation(summary = "发送邮箱验证码", description = "发送邮箱验证码用于注册或密码重置")
    public ResponseEntity<Map<String, Object>> sendEmailVerificationCode(
            @RequestParam String email) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证邮箱格式
            if (!userIdentityService.isValidEmail(email)) {
                response.put("success", false);
                response.put("message", "邮箱格式不正确");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 检查是否已有有效验证码
            if (verificationCodeService.hasValidEmailCode(email)) {
                long remainingTime = verificationCodeService.getEmailCodeRemainingTime(email);
                response.put("success", false);
                response.put("message", String.format("验证码已发送，请%d秒后重试", remainingTime));
                return ResponseEntity.badRequest().body(response);
            }
            
            // 生成并存储验证码
            String code = verificationCodeService.generateEmailVerificationCode(email);
            
            // 开发阶段：返回生成的验证码用于测试
            response.put("success", true);
            response.put("message", "验证码已生成，有效期5分钟");
            response.put("code", code); // 开发阶段显示验证码
            response.put("expiresIn", VERIFICATION_CODE_EXPIRY_SECONDS); // 5分钟
            
            log.info("邮箱验证码生成成功: email={}, code={}", email, code);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("发送邮箱验证码失败: email={}, error={}", email, e.getMessage());
            response.put("success", false);
            response.put("message", "发送失败，请稍后重试");
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 发送短信验证码（预留接口）
     */
    @PostMapping("/send-sms-verification")
    @Operation(summary = "发送短信验证码", description = "发送短信验证码（开发阶段暂未实现）")
    public ResponseEntity<Map<String, Object>> sendSmsVerificationCode(
            @RequestParam String phone) {
        
        Map<String, Object> response = new HashMap<>();
        
        // 开发阶段：模拟发送成功
        response.put("success", true);
        response.put("message", "验证码已发送到手机（开发阶段模拟）");
        response.put("mockCode", "123456"); // 开发阶段返回模拟验证码
        
        log.info("模拟发送短信验证码: phone={}", phone);
        return ResponseEntity.ok(response);
    }

    /**
     * 获取登录类型建议
     */
    @PostMapping("/detect-login-type")
    @Operation(summary = "检测登录类型", description = "根据输入标识符自动检测是个人用户还是机构用户")
    public ResponseEntity<Map<String, Object>> detectLoginType(
            @RequestParam String identifier) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            UserIdentityService.IdentifierType type = userIdentityService.detectIdentifierType(identifier);
            
            String loginType;
            String suggestion;
            
            switch (type) {
                case EMAIL:
                case PHONE:
                    loginType = "INDIVIDUAL";
                    suggestion = "个人用户登录";
                    break;
                case INSTITUTIONAL:
                    loginType = "INSTITUTIONAL";
                    suggestion = "机构用户登录";
                    break;
                default:
                    loginType = "INDIVIDUAL";
                    suggestion = "建议作为个人用户登录";
            }
            
            response.put("loginType", loginType);
            response.put("identifierType", type.name());
            response.put("suggestion", suggestion);
            response.put("isValid", true);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检测登录类型失败: identifier={}, error={}", identifier, e.getMessage());
            
            response.put("loginType", "INDIVIDUAL");
            response.put("identifierType", "UNKNOWN");
            response.put("suggestion", "默认个人用户登录");
            response.put("isValid", false);
            
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取用户代理信息
     */
    private String getUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }
}