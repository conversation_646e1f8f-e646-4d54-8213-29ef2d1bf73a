package com.assessment.controller;

import com.assessment.dto.TenantInfoResponse;
import com.assessment.service.TenantCacheService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 租户搜索建议控制器
 * 针对大量租户场景的性能优化版本
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/api/public/tenants")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "租户搜索建议", description = "高性能租户代码搜索和建议API")
public class TenantSuggestionsController {

    private final TenantCacheService tenantCacheService;

    /**
     * 搜索租户建议（支持模糊搜索和分页）
     * 适用于大量租户场景
     */
    @GetMapping("/suggestions")
    @Operation(summary = "租户搜索建议", description = "根据输入关键词搜索租户，支持代码和名称模糊匹配")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "搜索成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器错误")
    })
    public ResponseEntity<List<TenantInfoResponse>> getTenantSuggestions(
            @Parameter(description = "搜索关键词（租户代码或名称）", example = "PLATFORM")
            @RequestParam(value = "query", required = false, defaultValue = "") String query,
            
            @Parameter(description = "返回结果数量限制", example = "10")
            @RequestParam(value = "limit", required = false, defaultValue = "10") int limit) {
        
        try {
            log.debug("租户搜索建议: query={}, limit={}", query, limit);
            
            // 参数验证
            if (limit <= 0 || limit > 50) {
                limit = 10; // 默认限制10个结果
            }
            
            // 使用缓存服务进行搜索
            List<TenantInfoResponse> suggestions = tenantCacheService.searchTenantSuggestions(query, limit);
            
            log.debug("返回 {} 个租户建议", suggestions.size());
            return ResponseEntity.ok(suggestions);
            
        } catch (Exception e) {
            log.error("租户搜索建议失败: query={}, limit={}", query, limit, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 快速验证租户代码（精确匹配）
     */
    @GetMapping("/validate/{tenantCode}")
    @Operation(summary = "快速验证租户代码", description = "精确验证租户代码是否存在且活跃")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "租户存在"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    public ResponseEntity<TenantInfoResponse> validateTenantCode(
            @Parameter(description = "租户代码", example = "PLATFORM")
            @PathVariable String tenantCode) {
        
        try {
            log.debug("验证租户代码: {}", tenantCode);
            
            // 使用缓存服务进行验证
            TenantInfoResponse tenantInfo = tenantCacheService.getTenantInfo(tenantCode);
            
            if (tenantInfo != null) {
                // 记录访问统计
                tenantCacheService.recordTenantAccess(tenantCode.toUpperCase());
                return ResponseEntity.ok(tenantInfo);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("验证租户代码失败: {}", tenantCode, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取热门租户列表
     */
    @GetMapping("/popular")
    @Operation(summary = "获取热门租户", description = "获取访问频率最高的租户列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器错误")
    })
    public ResponseEntity<List<TenantInfoResponse>> getPopularTenants(
            @Parameter(description = "返回数量", example = "10")
            @RequestParam(value = "limit", required = false, defaultValue = "10") int limit) {
        
        try {
            if (limit <= 0 || limit > 50) {
                limit = 10;
            }
            
            List<TenantInfoResponse> popularTenants = tenantCacheService.getPopularTenants(limit);
            return ResponseEntity.ok(popularTenants);
            
        } catch (Exception e) {
            log.error("获取热门租户失败: limit={}", limit, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache/stats")
    @Operation(summary = "获取缓存统计", description = "获取租户缓存的统计信息和性能指标")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器错误")
    })
    public ResponseEntity<Map<String, Object>> getCacheStats() {
        try {
            Map<String, Object> stats = tenantCacheService.getCacheStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取缓存统计失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 预热缓存
     */
    @PostMapping("/cache/warmup")
    @Operation(summary = "预热缓存", description = "主动预热租户缓存，提升后续访问性能")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "预热成功"),
        @ApiResponse(responseCode = "500", description = "预热失败")
    })
    public ResponseEntity<Map<String, String>> warmUpCache() {
        try {
            tenantCacheService.warmUpCache();
            Map<String, String> result = Map.of(
                "status", "success",
                "message", "缓存预热完成",
                "timestamp", String.valueOf(System.currentTimeMillis())
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("缓存预热失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 清除缓存
     */
    @DeleteMapping("/cache")
    @Operation(summary = "清除缓存", description = "清除所有租户相关缓存")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "清除成功"),
        @ApiResponse(responseCode = "500", description = "清除失败")
    })
    public ResponseEntity<Map<String, String>> clearCache(
            @Parameter(description = "租户代码（可选，为空则清除所有）")
            @RequestParam(value = "tenantCode", required = false) String tenantCode) {
        
        try {
            if (tenantCode != null && !tenantCode.trim().isEmpty()) {
                tenantCacheService.evictTenantCache(tenantCode.trim().toUpperCase());
            } else {
                tenantCacheService.evictAllTenantCache();
            }
            
            Map<String, String> result = Map.of(
                "status", "success",
                "message", tenantCode != null ? "租户缓存已清除: " + tenantCode : "所有缓存已清除",
                "timestamp", String.valueOf(System.currentTimeMillis())
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清除缓存失败: tenantCode={}", tenantCode, e);
            return ResponseEntity.internalServerError().build();
        }
    }

}