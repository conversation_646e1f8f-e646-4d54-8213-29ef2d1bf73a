package com.assessment.controller;

import com.assessment.dto.MultiTenantLoginRequest;
import com.assessment.dto.MultiTenantLoginResponse;
import com.assessment.service.MultiTenantAuthService;
import com.assessment.service.SimpleCaptchaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/** 多租户认证控制器 处理租户名+用户名+密码的登录请求 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Tag(name = "多租户认证", description = "多租户登录认证接口")
public class MultiTenantAuthController {

  private final MultiTenantAuthService authService;
  private final SimpleCaptchaService captchaService;

  /** 多租户登录 */
  @PostMapping("/login")
  @Operation(summary = "多租户登录", description = "使用租户代码+用户名+密码进行登录")
  @ApiResponse(responseCode = "200", description = "登录成功")
  @ApiResponse(responseCode = "400", description = "请求参数错误")
  @ApiResponse(responseCode = "401", description = "认证失败")
  public ResponseEntity<MultiTenantLoginResponse> login(
      @Valid @RequestBody MultiTenantLoginRequest request) {
    log.info("多租户登录请求: 租户={}, 用户={}", request.getTenantCode(), request.getUsername());

    try {
      // 验证码验证（如果提供了验证码）
      if (request.getCaptchaToken() != null && !request.getCaptchaToken().isEmpty()) {
        log.info("开始验证登录验证码: token={}", request.getCaptchaToken());
        var captchaResult = captchaService.checkCaptcha(
            request.getCaptchaToken(), 
            null, // pointJson在滑动验证中使用，登录时可能不需要
            request.getCaptchaVerification()
        );
        
        if (!captchaResult.isSuccess()) {
          log.warn("登录验证码验证失败: 租户={}, 用户={}, 原因={}", 
              request.getTenantCode(), request.getUsername(), captchaResult.getMessage());
          throw new RuntimeException("验证码验证失败: " + captchaResult.getMessage());
        }
        log.info("登录验证码验证成功");
      }

      MultiTenantLoginResponse response = authService.authenticate(request);
      log.info(
          "用户登录成功: 租户={}, 用户={}, 角色={}",
          response.getTenantCode(),
          response.getUsername(),
          response.getTenantRole());
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      log.error(
          "用户登录失败: 租户={}, 用户={}, 错误={}",
          request.getTenantCode(),
          request.getUsername(),
          e.getMessage());
      throw e;
    }
  }

  /** 超级管理员登录 */
  @PostMapping("/superadmin/login")
  @Operation(summary = "超级管理员登录", description = "超级管理员专用登录接口")
  @ApiResponse(responseCode = "200", description = "登录成功")
  @ApiResponse(responseCode = "401", description = "认证失败")
  public ResponseEntity<MultiTenantLoginResponse> superAdminLogin(
      @RequestBody Map<String, String> request) {
    String username = request.get("username");
    String password = request.get("password");

    log.info("超级管理员登录请求: 用户={}", username);

    MultiTenantLoginRequest loginRequest = new MultiTenantLoginRequest();
    loginRequest.setTenantCode("platform");
    loginRequest.setUsername(username);
    loginRequest.setPassword(password);

    try {
      MultiTenantLoginResponse response = authService.authenticate(loginRequest);
      log.info("超级管理员登录成功: 用户={}", response.getUsername());
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      log.error("超级管理员登录失败: 用户={}, 错误={}", username, e.getMessage());
      throw e;
    }
  }

  /** 获取当前用户信息 */
  @GetMapping("/me")
  @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
  public ResponseEntity<Map<String, Object>> getCurrentUser() {
    // 这里需要从Security Context中获取当前用户信息
    // 暂时返回示例数据
    Map<String, Object> userInfo = new HashMap<>();
    userInfo.put("message", "需要从JWT Token中解析用户信息");
    return ResponseEntity.ok(userInfo);
  }

  /** 刷新Token */
  @PostMapping("/refresh")
  @Operation(summary = "刷新访问Token", description = "使用刷新Token获取新的访问Token")
  public ResponseEntity<Map<String, Object>> refreshToken(
      @RequestBody Map<String, String> request) {
    String refreshToken = request.get("refreshToken");

    // 这里需要实现Token刷新逻辑
    Map<String, Object> response = new HashMap<>();
    response.put("message", "Token刷新功能待实现");
    response.put("refreshToken", refreshToken);

    return ResponseEntity.ok(response);
  }

  /** 登出 */
  @PostMapping("/logout")
  @Operation(summary = "用户登出", description = "用户登出并清除Token")
  public ResponseEntity<Map<String, String>> logout() {
    // 这里可以实现Token黑名单逻辑
    Map<String, String> response = new HashMap<>();
    response.put("message", "登出成功");
    return ResponseEntity.ok(response);
  }

  /** 获取登录页面配置信息 */
  @GetMapping("/config")
  @Operation(summary = "获取登录配置", description = "获取登录页面所需的配置信息")
  public ResponseEntity<Map<String, Object>> getLoginConfig() {
    Map<String, Object> config = new HashMap<>();

    // 系统信息
    config.put("systemName", "智能评估平台");
    config.put("version", "2.0.0");
    config.put("loginType", "multi-tenant");

    // 登录提示信息
    config.put(
        "loginHints",
        Map.of(
            "tenantCode", "请输入您的机构代码",
            "username", "请输入用户名",
            "password", "请输入密码"));

    // 示例账户信息（开发环境）
    config.put(
        "demoAccounts",
        Map.of(
            "hospital",
                Map.of(
                    "tenantCode",
                    "demo_hospital",
                    "accounts",
                    Map.of(
                        "admin", "demo_hospital_admin / password123",
                        "assessor", "demo_hospital_assessor / password123",
                        "reviewer", "demo_hospital_reviewer / password123")),
            "nursing",
                Map.of(
                    "tenantCode",
                    "demo_nursing",
                    "accounts",
                    Map.of(
                        "admin", "demo_nursing_admin / password123",
                        "assessor", "demo_nursing_assessor / password123",
                        "reviewer", "demo_nursing_reviewer / password123")),
            "superadmin",
                Map.of(
                    "username", "superadmin",
                    "password", "password123")));

    return ResponseEntity.ok(config);
  }
}
