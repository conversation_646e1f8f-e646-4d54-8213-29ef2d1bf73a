package com.assessment.controller;

import com.assessment.dto.MultiTenantLoginRequest;
import com.assessment.dto.MultiTenantLoginResponse;
import com.assessment.service.MultiTenantAuthService;
import com.assessment.service.SimpleCaptchaService;
import com.assessment.service.JwtTokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/** 多租户认证控制器 处理租户名+用户名+密码的登录请求 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Tag(name = "多租户认证", description = "多租户登录认证接口")
public class MultiTenantAuthController {

  private final MultiTenantAuthService authService;
  private final SimpleCaptchaService captchaService;
  private final JwtTokenService jwtTokenService;

  /** 多租户登录 */
  @PostMapping("/login")
  @Operation(summary = "多租户登录", description = "使用租户代码+用户名+密码进行登录")
  @ApiResponse(responseCode = "200", description = "登录成功")
  @ApiResponse(responseCode = "400", description = "请求参数错误")
  @ApiResponse(responseCode = "401", description = "认证失败")
  public ResponseEntity<MultiTenantLoginResponse> login(
      @Valid @RequestBody MultiTenantLoginRequest request) {
    log.info("多租户登录请求: 租户={}, 用户={}", request.getTenantCode(), request.getUsername());

    try {
      // 验证码验证（如果提供了验证码）
      if (request.getCaptchaToken() != null && !request.getCaptchaToken().isEmpty()) {
        log.info("开始验证登录验证码: token={}", request.getCaptchaToken());
        var captchaResult = captchaService.checkCaptcha(
            request.getCaptchaToken(), 
            null, // pointJson在滑动验证中使用，登录时可能不需要
            request.getCaptchaVerification()
        );
        
        if (!captchaResult.isSuccess()) {
          log.warn("登录验证码验证失败: 租户={}, 用户={}, 原因={}", 
              request.getTenantCode(), request.getUsername(), captchaResult.getMessage());
          throw new RuntimeException("验证码验证失败: " + captchaResult.getMessage());
        }
        log.info("登录验证码验证成功");
      }

      MultiTenantLoginResponse response = authService.authenticate(request);
      log.info(
          "用户登录成功: 租户={}, 用户={}, 角色={}",
          response.getTenantCode(),
          response.getUsername(),
          response.getTenantRole());
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      log.error(
          "用户登录失败: 租户={}, 用户={}, 错误={}",
          request.getTenantCode(),
          request.getUsername(),
          e.getMessage());
      throw e;
    }
  }

  /** 超级管理员登录 */
  @PostMapping("/superadmin/login")
  @Operation(summary = "超级管理员登录", description = "超级管理员专用登录接口")
  @ApiResponse(responseCode = "200", description = "登录成功")
  @ApiResponse(responseCode = "401", description = "认证失败")
  public ResponseEntity<MultiTenantLoginResponse> superAdminLogin(
      @RequestBody Map<String, String> request) {
    String username = request.get("username");
    String password = request.get("password");

    log.info("超级管理员登录请求: 用户={}", username);

    MultiTenantLoginRequest loginRequest = new MultiTenantLoginRequest();
    loginRequest.setTenantCode("platform");
    loginRequest.setUsername(username);
    loginRequest.setPassword(password);

    try {
      MultiTenantLoginResponse response = authService.authenticate(loginRequest);
      log.info("超级管理员登录成功: 用户={}", response.getUsername());
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      log.error("超级管理员登录失败: 用户={}, 错误={}", username, e.getMessage());
      throw e;
    }
  }

  /** 获取当前用户信息 */
  @GetMapping("/me")
  @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
  @ApiResponse(responseCode = "200", description = "获取用户信息成功")
  @ApiResponse(responseCode = "401", description = "未授权或Token无效")
  public ResponseEntity<Map<String, Object>> getCurrentUser(HttpServletRequest request) {
    log.info("获取当前用户信息请求");

    try {
      // 从请求头中提取Token
      String token = extractTokenFromRequest(request);
      if (token == null) {
        throw new IllegalArgumentException("未找到访问Token");
      }

      // 验证Token
      if (!jwtTokenService.validateToken(token)) {
        throw new IllegalArgumentException("无效的访问Token");
      }

      // 提取用户信息
      String userId = jwtTokenService.extractUserId(token);
      String username = jwtTokenService.extractUsername(token);
      String tenantId = jwtTokenService.extractTenantId(token);
      String tenantCode = jwtTokenService.extractTenantCode(token);
      String tenantRole = jwtTokenService.extractTenantRole(token);
      boolean isSuperAdmin = jwtTokenService.isSuperAdmin(token);

      // 构建用户信息响应
      Map<String, Object> userInfo = new HashMap<>();
      userInfo.put("success", true);
      userInfo.put("userId", userId);
      userInfo.put("username", username);
      userInfo.put("tenantId", tenantId);
      userInfo.put("tenantCode", tenantCode);
      userInfo.put("tenantRole", tenantRole);
      userInfo.put("isSuperAdmin", isSuperAdmin);

      // 如果是个人用户，添加额外信息
      if (jwtTokenService.isIndividualUserToken(token)) {
        userInfo.put("userType", "INDIVIDUAL");
        userInfo.put("serviceType", jwtTokenService.extractServiceType(token));
        userInfo.put("isPremiumUser", jwtTokenService.extractIsPremiumUser(token));
      } else {
        userInfo.put("userType", "INSTITUTIONAL");
      }

      // 添加Token信息
      userInfo.put("tokenType", jwtTokenService.getTokenType(token));
      userInfo.put("isTokenExpired", jwtTokenService.isTokenExpired(token));

      log.info("成功获取用户信息: userId={}, username={}, tenantCode={}", 
               userId, username, tenantCode);
      
      return ResponseEntity.ok(userInfo);

    } catch (Exception e) {
      log.error("获取用户信息失败: {}", e.getMessage());
      
      Map<String, Object> errorResponse = new HashMap<>();
      errorResponse.put("success", false);
      errorResponse.put("error", "get_user_info_failed");
      errorResponse.put("message", e.getMessage());
      
      return ResponseEntity.status(401).body(errorResponse);
    }
  }

  /**
   * 从请求中提取JWT Token
   */
  private String extractTokenFromRequest(HttpServletRequest request) {
    String bearerToken = request.getHeader("Authorization");
    if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
      return bearerToken.substring(7);
    }
    return null;
  }

  /** 刷新Token */
  @PostMapping("/refresh")
  @Operation(summary = "刷新访问Token", description = "使用刷新Token获取新的访问Token")
  @ApiResponse(responseCode = "200", description = "Token刷新成功")
  @ApiResponse(responseCode = "400", description = "无效的刷新Token")
  public ResponseEntity<Map<String, Object>> refreshToken(
      @RequestBody Map<String, String> request) {
    String refreshToken = request.get("refreshToken");
    
    log.info("Token刷新请求");

    try {
      // 验证刷新Token
      if (refreshToken == null || refreshToken.trim().isEmpty()) {
        throw new IllegalArgumentException("刷新Token不能为空");
      }

      if (!jwtTokenService.validateToken(refreshToken)) {
        throw new IllegalArgumentException("无效的刷新Token");
      }

      // 检查Token类型
      String tokenType = jwtTokenService.getTokenType(refreshToken);
      if (!"refresh".equals(tokenType)) {
        throw new IllegalArgumentException("提供的不是刷新Token");
      }

      // 提取用户信息
      String userId = jwtTokenService.extractUserId(refreshToken);
      String username = jwtTokenService.extractUsername(refreshToken);
      
      log.info("Token刷新: userId={}, username={}", userId, username);

      // 生成新的访问Token（使用现有的认证服务重新获取用户信息）
      // 注意：这里我们重用现有的认证逻辑，确保安全性
      String newAccessToken = authService.generateNewAccessToken(userId);
      
      // 可选：生成新的刷新Token（提高安全性）
      String newRefreshToken = jwtTokenService.generateRefreshToken(userId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("accessToken", newAccessToken);
      response.put("refreshToken", newRefreshToken);
      response.put("tokenType", "Bearer");
      response.put("expiresIn", 3600); // 1小时
      
      log.info("Token刷新成功: userId={}", userId);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      log.error("Token刷新失败: {}", e.getMessage());
      
      Map<String, Object> errorResponse = new HashMap<>();
      errorResponse.put("success", false);
      errorResponse.put("error", "token_refresh_failed");
      errorResponse.put("message", e.getMessage());
      
      return ResponseEntity.badRequest().body(errorResponse);
    }
  }

  /** 登出 */
  @PostMapping("/logout")
  @Operation(summary = "用户登出", description = "用户登出并清除Token")
  public ResponseEntity<Map<String, String>> logout() {
    // 这里可以实现Token黑名单逻辑
    Map<String, String> response = new HashMap<>();
    response.put("message", "登出成功");
    return ResponseEntity.ok(response);
  }

  /** 获取登录页面配置信息 */
  @GetMapping("/config")
  @Operation(summary = "获取登录配置", description = "获取登录页面所需的配置信息")
  public ResponseEntity<Map<String, Object>> getLoginConfig() {
    Map<String, Object> config = new HashMap<>();

    // 系统信息
    config.put("systemName", "智能评估平台");
    config.put("version", "2.0.0");
    config.put("loginType", "multi-tenant");

    // 登录提示信息
    config.put(
        "loginHints",
        Map.of(
            "tenantCode", "请输入您的机构代码",
            "username", "请输入用户名",
            "password", "请输入密码"));

    // 示例账户信息（开发环境）
    config.put(
        "demoAccounts",
        Map.of(
            "hospital",
                Map.of(
                    "tenantCode",
                    "demo_hospital",
                    "accounts",
                    Map.of(
                        "admin", "demo_hospital_admin / password123",
                        "assessor", "demo_hospital_assessor / password123",
                        "reviewer", "demo_hospital_reviewer / password123")),
            "nursing",
                Map.of(
                    "tenantCode",
                    "demo_nursing",
                    "accounts",
                    Map.of(
                        "admin", "demo_nursing_admin / password123",
                        "assessor", "demo_nursing_assessor / password123",
                        "reviewer", "demo_nursing_reviewer / password123")),
            "superadmin",
                Map.of(
                    "username", "superadmin",
                    "password", "password123")));

    return ResponseEntity.ok(config);
  }
}
