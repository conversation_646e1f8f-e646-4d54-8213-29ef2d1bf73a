package com.assessment.controller;

import com.assessment.dto.ApiResponse;
import com.assessment.dto.SystemAssessmentQueryRequest;
import com.assessment.entity.multitenant.TenantAssessmentRecord;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/** 系统评估记录管理控制器 仅系统管理员可访问 */
@Slf4j
@RestController
@RequestMapping("/api/system/assessments")
@RequiredArgsConstructor
@Tag(name = "系统评估记录管理", description = "系统级评估记录管理接口，仅管理员可访问")
@PreAuthorize("hasRole('ADMIN')")
public class SystemAssessmentController {

  /** 分页查询最大记录数 */
  private static final int MAX_PAGE_SIZE = 1000;

  private final TenantAssessmentRecordRepository assessmentRepository;
  private final TenantRepository tenantRepository;
  private final GlobalScaleRegistryRepository scaleRepository;
  private final PlatformUserRepository userRepository;

  /** 获取评估记录列表 */
  @GetMapping
  @Operation(summary = "获取评估记录列表", description = "分页查询所有租户的评估记录")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getAssessments(
      SystemAssessmentQueryRequest queryRequest) {

    try {
      Pageable pageable = buildAssessmentPageable(queryRequest);
      Page<TenantAssessmentRecord> assessmentsPage = queryAssessments(queryRequest, pageable);
      List<AssessmentRecordWithDetails> recordsWithDetails = enrichAssessmentRecords(assessmentsPage);
      Map<String, Object> response = buildAssessmentResponse(assessmentsPage, recordsWithDetails);

      return ResponseEntity.ok(ApiResponse.success(response));
    } catch (RuntimeException e) {
      log.error("获取评估记录列表失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取评估记录列表失败: " + e.getMessage()));
    }
  }

  private Pageable buildAssessmentPageable(SystemAssessmentQueryRequest queryRequest) {
    Sort sort = Sort.by(Sort.Direction.DESC, "assessmentDate");
    if (queryRequest.getSortField() != null && !queryRequest.getSortField().isEmpty()) {
      Sort.Direction direction = "ASC".equalsIgnoreCase(queryRequest.getSortOrder())
          ? Sort.Direction.ASC : Sort.Direction.DESC;
      sort = Sort.by(direction, queryRequest.getSortField());
    }
    return PageRequest.of(queryRequest.getPage(), queryRequest.getSize(), sort);
  }

  private Page<TenantAssessmentRecord> queryAssessments(SystemAssessmentQueryRequest queryRequest, Pageable pageable) {
    String tenantId = queryRequest.getTenantId();
    if (tenantId != null && !tenantId.trim().isEmpty()) {
      return queryAssessmentsByTenant(queryRequest, pageable);
    } else {
      return assessmentRepository.findAll(pageable);
    }
  }

  private Page<TenantAssessmentRecord> queryAssessmentsByTenant(SystemAssessmentQueryRequest queryRequest,
                                                               Pageable pageable) {
    String tenantId = queryRequest.getTenantId();
    String status = queryRequest.getStatus();
    String assessorId = queryRequest.getAssessorId();
    String scaleId = queryRequest.getScaleId();

    if (status != null && !status.trim().isEmpty()) {
      return assessmentRepository.findByTenantIdAndStatus(tenantId, status, pageable);
    } else if (assessorId != null && !assessorId.trim().isEmpty()) {
      return assessmentRepository.findByTenantIdAndAssessorId(tenantId, assessorId, pageable);
    } else if (scaleId != null && !scaleId.trim().isEmpty()) {
      return assessmentRepository.findByTenantIdAndScaleId(tenantId, scaleId, pageable);
    } else {
      return assessmentRepository.findByTenantId(tenantId, pageable);
    }
  }

  private List<AssessmentRecordWithDetails> enrichAssessmentRecords(Page<TenantAssessmentRecord> assessmentsPage) {
    return assessmentsPage.getContent().stream()
        .map(this::enrichAssessmentRecord)
        .collect(Collectors.toList());
  }

  private Map<String, Object> buildAssessmentResponse(Page<TenantAssessmentRecord> assessmentsPage,
                                                     List<AssessmentRecordWithDetails> recordsWithDetails) {
    return Map.of(
        "content", recordsWithDetails,
        "totalElements", assessmentsPage.getTotalElements(),
        "totalPages", assessmentsPage.getTotalPages(),
        "size", assessmentsPage.getSize(),
        "number", assessmentsPage.getNumber());
  }

  /** 获取评估记录详情 */
  @GetMapping("/{id}")
  @Operation(summary = "获取评估记录详情", description = "根据ID获取评估记录详细信息")
  public ResponseEntity<ApiResponse<AssessmentRecordWithDetails>> getAssessment(
      @PathVariable String id) {
    try {
      TenantAssessmentRecord record =
          assessmentRepository.findById(id).orElseThrow(() -> new RuntimeException("评估记录不存在"));
      AssessmentRecordWithDetails recordWithDetails = enrichAssessmentRecord(record);
      return ResponseEntity.ok(ApiResponse.success(recordWithDetails));
    } catch (RuntimeException e) {
      log.error("获取评估记录详情失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取评估记录详情失败: " + e.getMessage()));
    }
  }

  /** 审核评估记录 */
  @PostMapping("/{id}/review")
  @Operation(summary = "审核评估记录", description = "审核指定评估记录")
  public ResponseEntity<ApiResponse<TenantAssessmentRecord>> reviewAssessment(
      @PathVariable String id, @Valid @RequestBody ReviewRequest request) {
    try {
      TenantAssessmentRecord record =
          assessmentRepository.findById(id).orElseThrow(() -> new RuntimeException("评估记录不存在"));
      if (request.getApproved()) {
        record.approve(request.getReviewNotes(), request.getReviewerId());
      } else {
        record.reject(request.getReviewNotes(), request.getReviewerId());
      }
      TenantAssessmentRecord updatedRecord = assessmentRepository.save(record);
      log.info("评估记录审核完成: ID={}, 结果={}", id, request.getApproved() ? "通过" : "拒绝");
      return ResponseEntity.ok(ApiResponse.success(updatedRecord, "审核完成"));
    } catch (RuntimeException e) {
      log.error("审核评估记录失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("审核失败: " + e.getMessage()));
    }
  }

  /** 批量审核评估记录 */
  @PostMapping("/batch-review")
  @Operation(summary = "批量审核评估记录", description = "批量审核多个评估记录")
  public ResponseEntity<ApiResponse<String>> batchReviewAssessments(
      @Valid @RequestBody BatchReviewRequest request) {
    try {
      List<String> recordIds = request.getRecordIds();
      if (recordIds == null || recordIds.isEmpty()) {
        return ResponseEntity.badRequest().body(ApiResponse.error("记录ID列表不能为空"));
      }
      List<TenantAssessmentRecord> records = assessmentRepository.findAllById(recordIds);
      if (records.size() != recordIds.size()) {
        return ResponseEntity.badRequest().body(ApiResponse.error("部分记录不存在"));
      }
      for (TenantAssessmentRecord record : records) {
        if (request.getApproved()) {
          record.approve(request.getReviewNotes(), request.getReviewerId());
        } else {
          record.reject(request.getReviewNotes(), request.getReviewerId());
        }
      }
      assessmentRepository.saveAll(records);
      log.info("批量审核完成: 记录数={}, 结果={}", records.size(), request.getApproved() ? "通过" : "拒绝");
      return ResponseEntity.ok(ApiResponse.success("", "批量审核完成"));
    } catch (RuntimeException e) {
      log.error("批量审核失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("批量审核失败: " + e.getMessage()));
    }
  }

  /** 归档评估记录 */
  @PostMapping("/{id}/archive")
  @Operation(summary = "归档评估记录", description = "将评估记录归档")
  public ResponseEntity<ApiResponse<TenantAssessmentRecord>> archiveAssessment(
      @PathVariable String id) {
    try {
      TenantAssessmentRecord record =
          assessmentRepository.findById(id).orElseThrow(() -> new RuntimeException("评估记录不存在"));
      record.archive();
      TenantAssessmentRecord archivedRecord = assessmentRepository.save(record);
      log.info("评估记录归档成功: ID={}", id);
      return ResponseEntity.ok(ApiResponse.success(archivedRecord, "归档成功"));
    } catch (RuntimeException e) {
      log.error("归档评估记录失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("归档失败: " + e.getMessage()));
    }
  }

  /** 删除评估记录 */
  @DeleteMapping("/{id}")
  @Operation(summary = "删除评估记录", description = "删除指定评估记录")
  public ResponseEntity<ApiResponse<String>> deleteAssessment(@PathVariable String id) {
    try {
      if (!assessmentRepository.existsById(id)) {
        throw new RuntimeException("评估记录不存在");
      }
      assessmentRepository.deleteById(id);
      log.info("评估记录删除成功: ID={}", id);
      return ResponseEntity.ok(ApiResponse.success("", "评估记录删除成功"));
    } catch (RuntimeException e) {
      log.error("删除评估记录失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("删除失败: " + e.getMessage()));
    }
  }

  /** 获取评估统计信息 */
  @GetMapping("/stats")
  @Operation(summary = "获取评估统计", description = "获取评估记录统计信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getAssessmentStats(
      @RequestParam(required = false) String tenantId,
      @RequestParam(required = false) String startDate,
      @RequestParam(required = false) String endDate) {
    try {
      LocalDateTime start =
          startDate != null
              ? LocalDateTime.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME)
              : LocalDateTime.now().minusYears(1);
      LocalDateTime end =
          endDate != null
              ? LocalDateTime.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME)
              : LocalDateTime.now();
      Map<String, Object> stats;
      if (tenantId != null && !tenantId.trim().isEmpty()) {
        stats = calculateTenantStats(tenantId, start, end);
      } else {
        stats = calculateGlobalStats(start, end);
      }
      return ResponseEntity.ok(ApiResponse.success(stats));
    } catch (RuntimeException e) {
      log.error("获取评估统计失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取评估统计失败: " + e.getMessage()));
    }
  }

  /** 获取待审核记录 */
  @GetMapping("/pending-review")
  @Operation(summary = "获取待审核记录", description = "获取所有待审核的评估记录")
  public ResponseEntity<ApiResponse<List<AssessmentRecordWithDetails>>> getPendingReviewRecords(
      @RequestParam(required = false) String tenantId) {
    try {
      List<TenantAssessmentRecord> pendingRecords;
      Pageable pageable = PageRequest.of(0, MAX_PAGE_SIZE, Sort.by(Sort.Direction.ASC, "createdAt"));
      if (tenantId != null && !tenantId.trim().isEmpty()) {
        pendingRecords =
            assessmentRepository
                .findByTenantIdAndStatus(
                    tenantId, TenantAssessmentRecord.RecordStatus.SUBMITTED.name(), pageable)
                .getContent();
      } else {
        pendingRecords =
            assessmentRepository.findAllByStatus(
                TenantAssessmentRecord.RecordStatus.SUBMITTED.name(), pageable);
      }
      List<AssessmentRecordWithDetails> recordsWithDetails =
          pendingRecords.stream().map(this::enrichAssessmentRecord).collect(Collectors.toList());
      return ResponseEntity.ok(ApiResponse.success(recordsWithDetails));
    } catch (RuntimeException e) {
      log.error("获取待审核记录失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取待审核记录失败: " + e.getMessage()));
    }
  }

  private AssessmentRecordWithDetails enrichAssessmentRecord(TenantAssessmentRecord record) {
    TenantInfo tenantInfo =
        tenantRepository
            .findById(UUID.fromString(record.getTenantId()))
            .map(t -> new TenantInfo(t.getId().toString(), t.getName(), t.getCode()))
            .orElse(new TenantInfo(record.getTenantId(), "未知租户", null));

    ScaleInfo scaleInfo =
        scaleRepository
            .findById(record.getScaleId())
            .map(s -> new ScaleInfo(s.getId(), s.getName(), s.getCode(), s.getCategory()))
            .orElse(new ScaleInfo(record.getScaleId(), "未知量表", null, null));

    UserInfo assessorInfo =
        userRepository
            .findById(UUID.fromString(record.getAssessorId()))
            .map(u -> new UserInfo(u.getId().toString(), u.getUsername(), u.getFullName()))
            .orElse(new UserInfo(record.getAssessorId(), "未知评估员", null));

    UserInfo reviewerInfo =
        record.getReviewerId() != null
            ? userRepository
                .findById(UUID.fromString(record.getReviewerId()))
                .map(u -> new UserInfo(u.getId().toString(), u.getUsername(), u.getFullName()))
                .orElse(new UserInfo(record.getReviewerId(), "未知审核员", null))
            : null;

    return new AssessmentRecordWithDetails(
        record, tenantInfo, scaleInfo, assessorInfo, reviewerInfo);
  }

  private Map<String, Object> calculateTenantStats(
      String tenantId, LocalDateTime start, LocalDateTime end) {
    long total = assessmentRepository.countByTenantId(tenantId);
    long completed =
        assessmentRepository.countByTenantIdAndStatus(
            tenantId, TenantAssessmentRecord.RecordStatus.APPROVED.name());
    long pending =
        assessmentRepository.countByTenantIdAndStatus(
            tenantId, TenantAssessmentRecord.RecordStatus.SUBMITTED.name());
    long periodTotal =
        assessmentRepository.countByTenantIdAndAssessmentDateBetween(tenantId, start, end);
    return Map.of(
        "total", total,
        "completed", completed,
        "pending", pending,
        "periodTotal", periodTotal);
  }

  private Map<String, Object> calculateGlobalStats(LocalDateTime start, LocalDateTime end) {
    long total = assessmentRepository.count();
    long completed =
        assessmentRepository.countByStatus(TenantAssessmentRecord.RecordStatus.APPROVED.name());
    long pending =
        assessmentRepository.countByStatus(TenantAssessmentRecord.RecordStatus.SUBMITTED.name());
    long periodTotal = assessmentRepository.countByAssessmentDateBetween(start, end);
    return Map.of(
        "total", total,
        "completed", completed,
        "pending", pending,
        "periodTotal", periodTotal);
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ReviewRequest {
    private Boolean approved;
    private String reviewNotes;
    private String reviewerId;
  }

  @Data
  @NoArgsConstructor
  public static class BatchReviewRequest {
    private List<String> recordIds;
    private Boolean approved;
    private String reviewNotes;
    private String reviewerId;

    public List<String> getRecordIds() {
      return recordIds == null ? null : List.copyOf(recordIds);
    }

    public void setRecordIds(List<String> recordIds) {
      this.recordIds = recordIds == null ? null : List.copyOf(recordIds);
    }
  }

  @NoArgsConstructor
  public static class AssessmentRecordWithDetails {
    private TenantAssessmentRecord record;
    private TenantInfo tenantInfo;
    private ScaleInfo scaleInfo;
    private UserInfo assessorInfo;
    private UserInfo reviewerInfo;

    public AssessmentRecordWithDetails(
        TenantAssessmentRecord record,
        TenantInfo tenantInfo,
        ScaleInfo scaleInfo,
        UserInfo assessorInfo,
        UserInfo reviewerInfo) {
      this.record = new TenantAssessmentRecord(record);
      this.tenantInfo = new TenantInfo(tenantInfo);
      this.scaleInfo = new ScaleInfo(scaleInfo);
      this.assessorInfo = new UserInfo(assessorInfo);
      this.reviewerInfo = reviewerInfo == null ? null : new UserInfo(reviewerInfo);
    }

    public TenantAssessmentRecord getRecord() {
      return new TenantAssessmentRecord(this.record);
    }

    public void setRecord(TenantAssessmentRecord record) {
      this.record = new TenantAssessmentRecord(record);
    }

    public TenantInfo getTenantInfo() {
      return new TenantInfo(this.tenantInfo);
    }

    public void setTenantInfo(TenantInfo tenantInfo) {
      this.tenantInfo = new TenantInfo(tenantInfo);
    }

    public ScaleInfo getScaleInfo() {
      return new ScaleInfo(this.scaleInfo);
    }

    public void setScaleInfo(ScaleInfo scaleInfo) {
      this.scaleInfo = new ScaleInfo(scaleInfo);
    }

    public UserInfo getAssessorInfo() {
      return new UserInfo(this.assessorInfo);
    }

    public void setAssessorInfo(UserInfo assessorInfo) {
      this.assessorInfo = new UserInfo(assessorInfo);
    }

    public UserInfo getReviewerInfo() {
      return new UserInfo(this.reviewerInfo);
    }

    public void setReviewerInfo(UserInfo reviewerInfo) {
      this.reviewerInfo = new UserInfo(reviewerInfo);
    }
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class TenantInfo {
    private String id;
    private String name;
    private String code;

    public TenantInfo(TenantInfo other) {
      if (other != null) {
        this.id = other.id;
        this.name = other.name;
        this.code = other.code;
      }
    }
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ScaleInfo {
    private String id;
    private String name;
    private String code;
    private String category;

    public ScaleInfo(ScaleInfo other) {
      if (other != null) {
        this.id = other.id;
        this.name = other.name;
        this.code = other.code;
        this.category = other.category;
      }
    }
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class UserInfo {
    private String id;
    private String username;
    private String fullName;

    public UserInfo(UserInfo other) {
      if (other != null) {
        this.id = other.id;
        this.username = other.username;
        this.fullName = other.fullName;
      }
    }
  }
}
