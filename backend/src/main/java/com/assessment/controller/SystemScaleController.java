package com.assessment.controller;

import com.assessment.dto.ApiResponse;
import com.assessment.dto.SystemScaleQueryRequest;
import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** 系统量表管理控制器 仅系统管理员可访问 */
@Slf4j
@RestController
@RequestMapping("/api/system/scales")
@Tag(name = "系统量表管理", description = "系统级量表管理接口，仅管理员可访问")
@PreAuthorize("hasRole('ADMIN')")
public class SystemScaleController {

  private final GlobalScaleRegistryRepository scaleRepository;
  private final ObjectMapper objectMapper;

  public SystemScaleController(
      GlobalScaleRegistryRepository scaleRepository, ObjectMapper objectMapper) {
    this.scaleRepository = scaleRepository;
    this.objectMapper = objectMapper.copy();
  }

  /** 获取量表列表 */
  @GetMapping
  @Operation(summary = "获取量表列表", description = "分页查询所有量表")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getScales(
      SystemScaleQueryRequest queryRequest) {

    try {
      // 构建排序
      Sort sort = Sort.by(Sort.Direction.DESC, "updatedAt");
      if (queryRequest.getSortField() != null && !queryRequest.getSortField().isEmpty()) {
        Sort.Direction direction =
            "ASC".equalsIgnoreCase(queryRequest.getSortOrder()) ? Sort.Direction.ASC : Sort.Direction.DESC;
        sort = Sort.by(direction, queryRequest.getSortField());
      }

      Pageable pageable = PageRequest.of(queryRequest.getPage(), queryRequest.getSize(), sort);
      Page<GlobalScaleRegistry> scalesPage;

      // 根据条件查询
      if (queryRequest.getSearch() != null && !queryRequest.getSearch().trim().isEmpty()) {
        scalesPage =
            scaleRepository.findByNameContainingIgnoreCaseOrCodeContainingIgnoreCase(
                queryRequest.getSearch(), queryRequest.getSearch(), pageable);
      } else if (queryRequest.getCategory() != null && !queryRequest.getCategory().trim().isEmpty()) {
        scalesPage = scaleRepository.findByCategory(queryRequest.getCategory(), pageable);
      } else if (queryRequest.getStatus() != null && !queryRequest.getStatus().trim().isEmpty()) {
        GlobalScaleRegistry.ScaleStatus scaleStatus =
            GlobalScaleRegistry.ScaleStatus.valueOf(queryRequest.getStatus());
        scalesPage = scaleRepository.findByStatus(scaleStatus, pageable);
      } else {
        scalesPage = scaleRepository.findAll(pageable);
      }

      Map<String, Object> response =
          Map.of(
              "content", scalesPage.getContent(),
              "totalElements", scalesPage.getTotalElements(),
              "totalPages", scalesPage.getTotalPages(),
              "size", scalesPage.getSize(),
              "number", scalesPage.getNumber());

      return ResponseEntity.ok(ApiResponse.success(response));
    } catch (Exception e) {
      log.error("获取量表列表失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取量表列表失败: " + e.getMessage()));
    }
  }

  /** 获取量表详情 */
  @GetMapping("/{id}")
  @Operation(summary = "获取量表详情", description = "根据ID获取量表详细信息")
  public ResponseEntity<ApiResponse<GlobalScaleRegistry>> getScale(@PathVariable String id) {
    try {
      GlobalScaleRegistry scale =
          scaleRepository.findById(id).orElseThrow(() -> new RuntimeException("量表不存在"));

      return ResponseEntity.ok(ApiResponse.success(scale));
    } catch (Exception e) {
      log.error("获取量表详情失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取量表详情失败: " + e.getMessage()));
    }
  }

  /** 创建量表 */
  @PostMapping
  @Operation(summary = "创建量表", description = "创建新的评估量表")
  public ResponseEntity<ApiResponse<GlobalScaleRegistry>> createScale(
      @Valid @RequestBody CreateScaleRequest request) {
    try {
      if (scaleRepository.existsByCode(request.getCode())) {
        return ResponseEntity.badRequest().body(ApiResponse.error("量表代码已存在"));
      }

      GlobalScaleRegistry scale = buildScaleFromRequest(request);
      GlobalScaleRegistry savedScale = scaleRepository.save(scale);
      log.info("量表创建成功: {}", savedScale.getCode());

      return ResponseEntity.ok(ApiResponse.success(savedScale, "量表创建成功"));
    } catch (IllegalArgumentException e) {
      log.error("创建量表失败 - 参数错误", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("创建量表失败: " + e.getMessage()));
    } catch (DataAccessException e) {
      log.error("创建量表失败 - 数据访问错误", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("创建量表失败: " + e.getMessage()));
    } catch (Exception e) {
      log.error("创建量表失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("创建量表失败: " + e.getMessage()));
    }
  }

  private GlobalScaleRegistry buildScaleFromRequest(CreateScaleRequest request) throws Exception {
    JsonNode formSchema = objectMapper.readTree(request.getFormSchema());
    JsonNode scoringRules = objectMapper.readTree(request.getScoringRules());
    JsonNode validationRules = parseOptionalJson(request.getValidationRules());
    JsonNode reportTemplate = parseOptionalJson(request.getReportTemplate());

    GlobalScaleRegistry scale = GlobalScaleRegistry.builder()
        .code(request.getCode())
        .name(request.getName())
        .version(request.getVersion() != null ? request.getVersion() : "1.0.0")
        .category(request.getCategory())
        .industryTags(serializeArrayField(request.getIndustryTags()))
        .keywords(serializeArrayField(request.getKeywords()))
        .formSchema(formSchema)
        .scoringRules(scoringRules)
        .validationRules(validationRules)
        .reportTemplate(reportTemplate)
        .publisherType(request.getPublisherType() != null
            ? request.getPublisherType() : GlobalScaleRegistry.PublisherType.PLATFORM)
        .publisherId(request.getPublisherId())
        .visibility(request.getVisibility() != null
            ? request.getVisibility() : GlobalScaleRegistry.Visibility.PUBLIC)
        .price(request.getPrice() != null ? request.getPrice() : BigDecimal.ZERO)
        .isOfficial(request.getIsOfficial() != null ? request.getIsOfficial() : Boolean.TRUE)
        .isVerified(true)
        .status(GlobalScaleRegistry.ScaleStatus.ACTIVE)
        .build();

    LocalDateTime now = LocalDateTime.now();
    scale.setCreatedAt(now);
    scale.setUpdatedAt(now);
    scale.setPublishedAt(now);

    return scale;
  }

  private JsonNode parseOptionalJson(String jsonString) throws Exception {
    if (jsonString != null && !jsonString.trim().isEmpty()) {
      return objectMapper.readTree(jsonString);
    }
    return null;
  }

  private String serializeArrayField(String[] array) throws Exception {
    return array != null ? objectMapper.writeValueAsString(array) : null;
  }

  /** 更新量表 */
  @PutMapping("/{id}")
  @Operation(summary = "更新量表", description = "更新量表信息")
  public ResponseEntity<ApiResponse<GlobalScaleRegistry>> updateScale(
      @PathVariable String id, @Valid @RequestBody UpdateScaleRequest request) {
    try {
      GlobalScaleRegistry scale = scaleRepository.findById(id)
          .orElseThrow(() -> new RuntimeException("量表不存在"));

      if (isCodeConflict(scale, request.getCode())) {
        return ResponseEntity.badRequest().body(ApiResponse.error("量表代码已被其他量表使用"));
      }

      updateScaleFromRequest(scale, request);
      GlobalScaleRegistry updatedScale = scaleRepository.save(scale);
      log.info("量表更新成功: {}", updatedScale.getCode());

      return ResponseEntity.ok(ApiResponse.success(updatedScale, "量表更新成功"));
    } catch (IllegalArgumentException e) {
      log.error("更新量表失败 - 参数错误: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("更新量表失败: " + e.getMessage()));
    } catch (DataAccessException e) {
      log.error("更新量表失败 - 数据访问错误: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("更新量表失败: " + e.getMessage()));
    } catch (Exception e) {
      log.error("更新量表失败: {}", id, e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("更新量表失败: " + e.getMessage()));
    }
  }

  private boolean isCodeConflict(GlobalScaleRegistry scale, String newCode) {
    return !scale.getCode().equals(newCode) && scaleRepository.existsByCode(newCode);
  }

  private void updateScaleFromRequest(GlobalScaleRegistry scale, UpdateScaleRequest request) throws Exception {
    updateBasicInfo(scale, request);
    updateJsonFields(scale, request);
    updatePublisherInfo(scale, request);
    scale.setUpdatedAt(LocalDateTime.now());
  }

  private void updateBasicInfo(GlobalScaleRegistry scale, UpdateScaleRequest request) throws Exception {
    scale.setCode(request.getCode());
    scale.setName(request.getName());
    scale.setVersion(request.getVersion());
    scale.setCategory(request.getCategory());
    scale.setIndustryTags(serializeArrayField(request.getIndustryTags()));
    scale.setKeywords(serializeArrayField(request.getKeywords()));
  }

  private void updateJsonFields(GlobalScaleRegistry scale, UpdateScaleRequest request) throws Exception {
    if (request.getFormSchema() != null) {
      scale.setFormSchema(objectMapper.readTree(request.getFormSchema()));
    }
    if (request.getScoringRules() != null) {
      scale.setScoringRules(objectMapper.readTree(request.getScoringRules()));
    }
    if (request.getValidationRules() != null && !request.getValidationRules().trim().isEmpty()) {
      scale.setValidationRules(objectMapper.readTree(request.getValidationRules()));
    }
    if (request.getReportTemplate() != null && !request.getReportTemplate().trim().isEmpty()) {
      scale.setReportTemplate(objectMapper.readTree(request.getReportTemplate()));
    }
  }

  private void updatePublisherInfo(GlobalScaleRegistry scale, UpdateScaleRequest request) {
    if (request.getPublisherType() != null) {
      scale.setPublisherType(request.getPublisherType());
    }
    if (request.getPublisherId() != null) {
      scale.setPublisherId(request.getPublisherId());
    }
    if (request.getVisibility() != null) {
      scale.setVisibility(request.getVisibility());
    }
    if (request.getPrice() != null) {
      scale.setPrice(request.getPrice());
    }
    if (request.getIsOfficial() != null) {
      scale.setIsOfficial(request.getIsOfficial());
    }
  }

  /** 删除量表 */
  @DeleteMapping("/{id}")
  @Operation(summary = "删除量表", description = "删除指定量表")
  public ResponseEntity<ApiResponse<String>> deleteScale(@PathVariable String id) {
    try {
      GlobalScaleRegistry scale =
          scaleRepository.findById(id).orElseThrow(() -> new RuntimeException("量表不存在"));

      scaleRepository.delete(scale);
      log.info("量表删除成功: {}", scale.getCode());

      return ResponseEntity.ok(ApiResponse.success("", "量表删除成功"));
    } catch (RuntimeException e) {
      log.error("删除量表失败 - 业务错误: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("删除量表失败: " + e.getMessage()));
    } catch (Exception e) {
      log.error("删除量表失败: {}", id, e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("删除量表失败: " + e.getMessage()));
    }
  }

  /** 发布量表 */
  @PostMapping("/{id}/publish")
  @Operation(summary = "发布量表", description = "发布量表到公开市场")
  public ResponseEntity<ApiResponse<GlobalScaleRegistry>> publishScale(@PathVariable String id) {
    try {
      GlobalScaleRegistry scale =
          scaleRepository.findById(id).orElseThrow(() -> new RuntimeException("量表不存在"));

      scale.publish();
      GlobalScaleRegistry publishedScale = scaleRepository.save(scale);
      log.info("量表发布成功: {}", publishedScale.getCode());

      return ResponseEntity.ok(ApiResponse.success(publishedScale, "量表发布成功"));
    } catch (RuntimeException e) {
      log.error("发布量表失败 - 业务错误: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("发布量表失败: " + e.getMessage()));
    } catch (Exception e) {
      log.error("发布量表失败: {}", id, e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("发布量表失败: " + e.getMessage()));
    }
  }

  /** 弃用量表 */
  @PostMapping("/{id}/deprecate")
  @Operation(summary = "弃用量表", description = "将量表标记为已弃用")
  public ResponseEntity<ApiResponse<GlobalScaleRegistry>> deprecateScale(@PathVariable String id) {
    try {
      GlobalScaleRegistry scale =
          scaleRepository.findById(id).orElseThrow(() -> new RuntimeException("量表不存在"));

      scale.deprecate();
      GlobalScaleRegistry deprecatedScale = scaleRepository.save(scale);
      log.info("量表弃用成功: {}", deprecatedScale.getCode());

      return ResponseEntity.ok(ApiResponse.success(deprecatedScale, "量表弃用成功"));
    } catch (RuntimeException e) {
      log.error("弃用量表失败 - 业务错误: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("弃用量表失败: " + e.getMessage()));
    } catch (Exception e) {
      log.error("弃用量表失败: {}", id, e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("弃用量表失败: " + e.getMessage()));
    }
  }

  /** 获取量表统计信息 */
  @GetMapping("/stats")
  @Operation(summary = "获取量表统计", description = "获取量表统计信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getScaleStats() {
    try {
      long totalScales = scaleRepository.count();
      long activeScales = scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE);
      long publicScales = scaleRepository.countByVisibility(GlobalScaleRegistry.Visibility.PUBLIC);
      long officialScales = scaleRepository.countByIsOfficial(true);

      List<Object[]> categoryStats = scaleRepository.countByCategory();
      List<Object[]> publisherStats = scaleRepository.countByPublisherType();

      Map<String, Object> stats =
          Map.of(
              "totalScales", totalScales,
              "activeScales", activeScales,
              "publicScales", publicScales,
              "officialScales", officialScales,
              "categoryStats", categoryStats,
              "publisherStats", publisherStats);

      return ResponseEntity.ok(ApiResponse.success(stats));
    } catch (DataAccessException e) {
      log.error("获取量表统计失败 - 数据访问错误", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("获取统计信息失败: " + e.getMessage()));
    } catch (Exception e) {
      log.error("获取量表统计失败", e);
      return ResponseEntity.internalServerError()
          .body(ApiResponse.error("获取统计信息失败: " + e.getMessage()));
    }
  }

  /** 获取所有分类 */
  @GetMapping("/categories")
  @Operation(summary = "获取量表分类", description = "获取所有可用的量表分类")
  public ResponseEntity<ApiResponse<List<String>>> getCategories() {
    try {
      List<String> categories = scaleRepository.findDistinctCategories();
      return ResponseEntity.ok(ApiResponse.success(categories));
    } catch (Exception e) {
      log.error("获取量表分类失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取分类失败: " + e.getMessage()));
    }
  }

  // DTO类
  public static class CreateScaleRequest {
    private String code;
    private String name;
    private String version;
    private String category;
    private String[] industryTags;
    private String[] keywords;
    private String formSchema; // JSON字符串
    private String scoringRules; // JSON字符串
    private String validationRules; // JSON字符串
    private String reportTemplate; // JSON字符串
    private GlobalScaleRegistry.PublisherType publisherType;
    private String publisherId;
    private GlobalScaleRegistry.Visibility visibility;
    private BigDecimal price;
    private Boolean isOfficial;

    // Getters and Setters
    public String getCode() {
      return code;
    }

    public void setCode(String code) {
      this.code = code;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public String getVersion() {
      return version;
    }

    public void setVersion(String version) {
      this.version = version;
    }

    public String getCategory() {
      return category;
    }

    public void setCategory(String category) {
      this.category = category;
    }

    public String[] getIndustryTags() {
      return industryTags == null ? null : industryTags.clone();
    }

    public void setIndustryTags(String[] industryTags) {
      this.industryTags = industryTags == null ? null : industryTags.clone();
    }

    public String[] getKeywords() {
      return keywords == null ? null : keywords.clone();
    }

    public void setKeywords(String[] keywords) {
      this.keywords = keywords == null ? null : keywords.clone();
    }

    public String getFormSchema() {
      return formSchema;
    }

    public void setFormSchema(String formSchema) {
      this.formSchema = formSchema;
    }

    public String getScoringRules() {
      return scoringRules;
    }

    public void setScoringRules(String scoringRules) {
      this.scoringRules = scoringRules;
    }

    public String getValidationRules() {
      return validationRules;
    }

    public void setValidationRules(String validationRules) {
      this.validationRules = validationRules;
    }

    public String getReportTemplate() {
      return reportTemplate;
    }

    public void setReportTemplate(String reportTemplate) {
      this.reportTemplate = reportTemplate;
    }

    public GlobalScaleRegistry.PublisherType getPublisherType() {
      return publisherType;
    }

    public void setPublisherType(GlobalScaleRegistry.PublisherType publisherType) {
      this.publisherType = publisherType;
    }

    public String getPublisherId() {
      return publisherId;
    }

    public void setPublisherId(String publisherId) {
      this.publisherId = publisherId;
    }

    public GlobalScaleRegistry.Visibility getVisibility() {
      return visibility;
    }

    public void setVisibility(GlobalScaleRegistry.Visibility visibility) {
      this.visibility = visibility;
    }

    public BigDecimal getPrice() {
      return price;
    }

    public void setPrice(BigDecimal price) {
      this.price = price;
    }

    public Boolean getIsOfficial() {
      return isOfficial;
    }

    public void setIsOfficial(Boolean isOfficial) {
      this.isOfficial = isOfficial;
    }
  }

  public static class UpdateScaleRequest extends CreateScaleRequest {
    // 继承所有创建请求的字段，用于更新操作
  }
}
