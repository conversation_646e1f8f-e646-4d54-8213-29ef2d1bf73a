package com.assessment.controller;

import com.assessment.entity.multitenant.Tenant;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.dto.TenantInfoResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 租户信息控制器
 * 提供租户基础信息查询，用于前端动态加载
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/api/public/tenants")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "租户信息", description = "公开的租户基础信息API")
public class TenantInfoController {

    private final TenantRepository tenantRepository;

    /**
     * 获取所有活跃租户的基础信息
     * 用于前端登录页面的机构代码提示
     */
    @GetMapping("/info")
    @Operation(summary = "获取租户基础信息", description = "获取所有活跃租户的代码、名称、行业等基础信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器错误")
    })
    public ResponseEntity<List<TenantInfoResponse>> getTenantInfo() {
        try {
            log.debug("获取活跃租户基础信息");
            
            List<Tenant> activeTenants = tenantRepository.findByStatus(Tenant.TenantStatus.ACTIVE);
            
            List<TenantInfoResponse> tenantInfoList = activeTenants.stream()
                .map(tenant -> TenantInfoResponse.builder()
                    .code(tenant.getCode())
                    .name(tenant.getName())
                    .industry(tenant.getIndustry())
                    .subscriptionPlan(tenant.getSubscriptionPlan().toString())
                    .level(determineTenantLevel(tenant))
                    .build())
                .collect(Collectors.toList());
            
            log.debug("返回 {} 个活跃租户信息", tenantInfoList.size());
            return ResponseEntity.ok(tenantInfoList);
            
        } catch (Exception e) {
            log.error("获取租户信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 检查租户代码是否存在
     */
    @GetMapping("/check/{tenantCode}")
    @Operation(summary = "检查租户代码", description = "检查指定的租户代码是否存在且活跃")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "租户存在"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    public ResponseEntity<TenantInfoResponse> checkTenantCode(@PathVariable String tenantCode) {
        try {
            log.debug("检查租户代码: {}", tenantCode);
            
            Optional<Tenant> tenantOpt = tenantRepository.findByCode(tenantCode);
            Tenant tenant = tenantOpt.isPresent() && tenantOpt.get().getStatus() == Tenant.TenantStatus.ACTIVE 
                ? tenantOpt.get() : null;
            
            if (tenant != null) {
                TenantInfoResponse tenantInfo = TenantInfoResponse.builder()
                    .code(tenant.getCode())
                    .name(tenant.getName())
                    .industry(tenant.getIndustry())
                    .subscriptionPlan(tenant.getSubscriptionPlan().toString())
                    .level(determineTenantLevel(tenant))
                    .build();
                
                return ResponseEntity.ok(tenantInfo);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("检查租户代码失败: {}", tenantCode, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据租户代码规律确定层级
     */
    private String determineTenantLevel(Tenant tenant) {
        String code = tenant.getCode();
        String industry = tenant.getIndustry();
        
        if ("PLATFORM".equals(code)) {
            return "系统级";
        }
        
        if ("政府机构".equals(industry)) {
            if (code.endsWith("_HQ")) {
                return "省级";
            } else if (code.contains("_") && code.split("_").length == 2) {
                return "市级";
            } else if (code.split("_").length > 2) {
                return "区县级";
            }
        }
        
        switch (industry) {
            case "医疗机构":
                return "医院";
            case "养老机构":
                return "养老院";
            case "保险公司":
                return "保险公司";
            default:
                return "机构级";
        }
    }
}