package com.assessment.controller;

import com.assessment.dto.ApiResponse;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.TenantUserMembership;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/** 系统用户管理控制器 仅系统管理员可访问 */
@Slf4j
@RestController
@RequestMapping("/api/system/users")
@RequiredArgsConstructor
@Tag(name = "系统用户管理", description = "系统级用户管理接口，仅管理员可访问")
@PreAuthorize("hasRole('ADMIN')")
public class SystemUserController {

  private final PlatformUserRepository platformUserRepository;
  private final TenantRepository tenantRepository;
  private final TenantUserMembershipRepository tenantUserMembershipRepository;
  private final PasswordEncoder passwordEncoder;

  /** 获取用户列表 */
  @GetMapping
  @Operation(summary = "获取用户列表", description = "分页查询所有平台用户")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getUsers(
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "20") int size,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String tenantId,
      @RequestParam(required = false) String role,
      @RequestParam(required = false) String sortField,
      @RequestParam(required = false) String sortOrder) {

    try {
      // 构建排序
      Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
      if (sortField != null && !sortField.isEmpty()) {
        Sort.Direction direction =
            "ASC".equalsIgnoreCase(sortOrder) ? Sort.Direction.ASC : Sort.Direction.DESC;
        sort = Sort.by(direction, sortField);
      }

      Pageable pageable = PageRequest.of(page, size, sort);
      Page<PlatformUser> usersPage;

      // 根据条件查询
      if (search != null && !search.trim().isEmpty()) {
        usersPage =
            platformUserRepository
                .findByUsernameContainingIgnoreCaseOrEmailContainingIgnoreCaseOrFullNameContainingIgnoreCase(
                    search, search, search, pageable);
      } else {
        usersPage = platformUserRepository.findAll(pageable);
      }

      // 为每个用户添加租户关系信息
      List<UserWithMemberships> usersWithMemberships =
          usersPage.getContent().stream()
              .map(
                  user -> {
                    List<TenantUserMembership> memberships =
                        tenantUserMembershipRepository.findByUserId(user.getId().toString());
                    return new UserWithMemberships(user, memberships);
                  })
              .toList();

      Map<String, Object> response =
          Map.of(
              "content", usersWithMemberships,
              "totalElements", usersPage.getTotalElements(),
              "totalPages", usersPage.getTotalPages(),
              "size", usersPage.getSize(),
              "number", usersPage.getNumber());

      return ResponseEntity.ok(ApiResponse.success(response));
    } catch (RuntimeException e) {
      log.error("获取用户列表失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取用户列表失败: " + e.getMessage()));
    }
  }

  /** 获取用户详情 */
  @GetMapping("/{id}")
  @Operation(summary = "获取用户详情", description = "根据ID获取用户详细信息")
  public ResponseEntity<ApiResponse<UserWithMemberships>> getUser(@PathVariable String id) {
    try {
      UUID userId = UUID.fromString(id);
      PlatformUser user =
          platformUserRepository.findById(userId).orElseThrow(() -> new RuntimeException("用户不存在"));

      List<TenantUserMembership> memberships =
          tenantUserMembershipRepository.findByUserId(user.getId().toString());
      UserWithMemberships userWithMemberships = new UserWithMemberships(user, memberships);

      return ResponseEntity.ok(ApiResponse.success(userWithMemberships));
    } catch (IllegalArgumentException e) {
      return ResponseEntity.badRequest().body(ApiResponse.error("无效的用户ID格式"));
    } catch (RuntimeException e) {
      log.error("获取用户详情失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取用户详情失败: " + e.getMessage()));
    }
  }

  /** 创建用户 */
  @PostMapping
  @Operation(summary = "创建用户", description = "创建新的平台用户")
  public ResponseEntity<ApiResponse<PlatformUser>> createUser(
      @Valid @RequestBody CreateUserRequest request) {
    try {
      // 检查用户名是否已存在
      if (platformUserRepository.existsByUsername(request.getUsername())) {
        return ResponseEntity.badRequest().body(ApiResponse.error("用户名已存在"));
      }

      // 检查邮箱是否已存在
      if (platformUserRepository.existsByEmail(request.getEmail())) {
        return ResponseEntity.badRequest().body(ApiResponse.error("邮箱已存在"));
      }

      // 创建用户
      PlatformUser user =
          PlatformUser.builder()
              .username(request.getUsername())
              .email(request.getEmail())
              .passwordHash(passwordEncoder.encode(request.getPassword()))
              .fullName(request.getFullName())
              .firstName(request.getFirstName())
              .lastName(request.getLastName())
              .phone(request.getPhone())
              .avatarUrl(request.getAvatarUrl())
              .platformRole(
                  request.getPlatformRole() != null
                      ? request.getPlatformRole()
                      : PlatformUser.PlatformRole.USER)
              .isActive(true)
              .build();

      user.setCreatedAt(LocalDateTime.now());
      user.setUpdatedAt(LocalDateTime.now());

      PlatformUser savedUser = platformUserRepository.save(user);
      log.info("用户创建成功: {}", savedUser.getUsername());

      return ResponseEntity.ok(ApiResponse.success(savedUser, "用户创建成功"));
    } catch (RuntimeException e) {
      log.error("创建用户失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("创建用户失败: " + e.getMessage()));
    }
  }

  /** 更新用户 */
  @PutMapping("/{id}")
  @Operation(summary = "更新用户", description = "更新用户信息")
  public ResponseEntity<ApiResponse<PlatformUser>> updateUser(
      @PathVariable String id, @Valid @RequestBody UpdateUserRequest request) {
    try {
      UUID userId = UUID.fromString(id);
      PlatformUser user =
          platformUserRepository.findById(userId).orElseThrow(() -> new RuntimeException("用户不存在"));

      // 检查邮箱冲突
      if (!user.getEmail().equals(request.getEmail())
          && platformUserRepository.existsByEmail(request.getEmail())) {
        return ResponseEntity.badRequest().body(ApiResponse.error("邮箱已被其他用户使用"));
      }

      // 更新用户信息
      user.setEmail(request.getEmail());
      user.setFullName(request.getFullName());
      user.setFirstName(request.getFirstName());
      user.setLastName(request.getLastName());
      user.setPhone(request.getPhone());
      user.setAvatarUrl(request.getAvatarUrl());

      if (request.getPlatformRole() != null) {
        user.setPlatformRole(request.getPlatformRole());
      }
      if (request.getIsActive() != null) {
        user.setIsActive(request.getIsActive());
      }

      user.setUpdatedAt(LocalDateTime.now());

      PlatformUser updatedUser = platformUserRepository.save(user);
      log.info("用户更新成功: {}", updatedUser.getUsername());

      return ResponseEntity.ok(ApiResponse.success(updatedUser, "用户更新成功"));
    } catch (IllegalArgumentException e) {
      return ResponseEntity.badRequest().body(ApiResponse.error("无效的用户ID格式"));
    } catch (RuntimeException e) {
      log.error("更新用户失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("更新用户失败: " + e.getMessage()));
    }
  }

  /** 重置用户密码 */
  @PostMapping("/{id}/reset-password")
  @Operation(summary = "重置用户密码", description = "重置用户密码为默认密码")
  public ResponseEntity<ApiResponse<String>> resetPassword(@PathVariable String id) {
    try {
      UUID userId = UUID.fromString(id);
      PlatformUser user =
          platformUserRepository.findById(userId).orElseThrow(() -> new RuntimeException("用户不存在"));

      // 重置为默认密码
      String defaultPassword = "password123";
      user.setPasswordHash(passwordEncoder.encode(defaultPassword));
      user.setUpdatedAt(LocalDateTime.now());

      platformUserRepository.save(user);
      log.info("用户密码重置成功: {}", user.getUsername());

      return ResponseEntity.ok(
          ApiResponse.success(defaultPassword, "密码重置成功，新密码为: " + defaultPassword));
    } catch (IllegalArgumentException e) {
      return ResponseEntity.badRequest().body(ApiResponse.error("无效的用户ID格式"));
    } catch (RuntimeException e) {
      log.error("重置用户密码失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("重置用户密码失败: " + e.getMessage()));
    }
  }

  /** 管理用户租户关系 */
  @PutMapping("/{userId}/tenants/{tenantId}")
  @Operation(summary = "管理用户租户关系", description = "添加或更新用户在租户中的角色和权限")
  public ResponseEntity<ApiResponse<TenantUserMembership>> manageTenantMembership(
      @PathVariable String userId,
      @PathVariable String tenantId,
      @Valid @RequestBody TenantMembershipRequest request) {
    try {
      validateUserAndTenant(userId, tenantId);
      TenantUserMembership membership = getOrCreateMembership(userId, tenantId);
      updateMembershipFromRequest(membership, request);
      TenantUserMembership savedMembership = tenantUserMembershipRepository.save(membership);

      log.info("用户 {} 在租户 {} 的关系已更新", userId, tenantId);
      return ResponseEntity.ok(ApiResponse.success(savedMembership, "用户租户关系更新成功"));
    } catch (IllegalArgumentException e) {
      return ResponseEntity.badRequest().body(ApiResponse.error("无效的用户或租户ID格式"));
    } catch (RuntimeException e) {
      log.error("管理用户租户关系失败: u={}, t={}", userId, tenantId, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("管理用户租户关系失败: " + e.getMessage()));
    }
  }

  private void validateUserAndTenant(String userId, String tenantId) {
    platformUserRepository
        .findById(UUID.fromString(userId))
        .orElseThrow(() -> new RuntimeException("用户不存在"));
    tenantRepository
        .findById(UUID.fromString(tenantId))
        .orElseThrow(() -> new RuntimeException("租户不存在"));
  }

  private TenantUserMembership getOrCreateMembership(String userId, String tenantId) {
    return tenantUserMembershipRepository
        .findByTenantIdAndUserId(tenantId, userId)
        .orElseGet(() -> {
          TenantUserMembership newMembership = new TenantUserMembership();
          newMembership.setUserId(userId);
          newMembership.setTenantId(tenantId);
          newMembership.setJoinedAt(LocalDateTime.now());
          return newMembership;
        });
  }

  private void updateMembershipFromRequest(TenantUserMembership membership, TenantMembershipRequest request) {
    if (request.getRole() != null) {
      membership.setTenantRole(request.getRole());
    }

    if (request.getPermissions() != null) {
      ObjectMapper mapper = new ObjectMapper();
      JsonNode permissionsNode = mapper.valueToTree(List.of(request.getPermissions()));
      membership.setPermissions(permissionsNode);
    } else {
      membership.setPermissions(null);
    }

    if (request.getIsActive() != null) {
      membership.setStatus(
          request.getIsActive()
              ? TenantUserMembership.MembershipStatus.ACTIVE
              : TenantUserMembership.MembershipStatus.INACTIVE);
    }

    membership.updateLastActiveTime();
  }

  /** 移除用户租户关系 */
  @DeleteMapping("/{userId}/tenants/{tenantId}")
  @Operation(summary = "移除用户租户关系", description = "从租户中移除用户")
  public ResponseEntity<ApiResponse<String>> removeTenantMembership(
      @PathVariable String userId, @PathVariable String tenantId) {
    try {
      TenantUserMembership membership =
          tenantUserMembershipRepository
              .findByTenantIdAndUserId(tenantId, userId)
              .orElseThrow(() -> new RuntimeException("用户不属于该租户"));

      tenantUserMembershipRepository.delete(membership);
      log.info("已将用户 {} 从租户 {} 中移除", userId, tenantId);

      return ResponseEntity.ok(ApiResponse.success(null, "用户已从租户中移除"));
    } catch (IllegalArgumentException e) {
      return ResponseEntity.badRequest().body(ApiResponse.error("无效的用户或租户ID格式"));
    } catch (RuntimeException e) {
      log.error("移除用户租户关系失败: u={}, t={}", userId, tenantId, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("移除用户租户关系失败: " + e.getMessage()));
    }
  }

  /** 获取用户统计信息 */
  @GetMapping("/stats")
  @Operation(summary = "获取用户统计", description = "获取用户统计信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getUserStats() {
    try {
      long totalUsers = platformUserRepository.count();
      long activeUsers = platformUserRepository.countByIsActive(true);
      long totalTenants = tenantRepository.count();

      Map<String, Object> stats =
          Map.of(
              "totalUsers", totalUsers,
              "activeUsers", activeUsers,
              "totalTenants", totalTenants);
      return ResponseEntity.ok(ApiResponse.success(stats));
    } catch (RuntimeException e) {
      log.error("获取用户统计失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取用户统计失败: " + e.getMessage()));
    }
  }

  // --- Nested DTOs ---
  @Data
  public static class CreateUserRequest {
    private String username;
    private String email;
    private String password;
    private String fullName;
    private String firstName;
    private String lastName;
    private String phone;
    private String avatarUrl;
    private PlatformUser.PlatformRole platformRole;
  }

  @Data
  public static class UpdateUserRequest {
    private String email;
    private String fullName;
    private String firstName;
    private String lastName;
    private String phone;
    private String avatarUrl;
    private PlatformUser.PlatformRole platformRole;
    private Boolean isActive;
  }

  @Data
  public static class TenantMembershipRequest {
    private TenantUserMembership.TenantRole role;
    private String[] permissions;
    private Boolean isActive;

    public String[] getPermissions() {
      return permissions == null ? null : permissions.clone();
    }

    public void setPermissions(String[] permissions) {
      this.permissions = permissions == null ? null : permissions.clone();
    }
  }

  @Data
  public static class UserWithMemberships {
    private PlatformUser user;
    private List<TenantUserMembership> memberships;

    public UserWithMemberships(PlatformUser user, List<TenantUserMembership> memberships) {
      this.setUser(user);
      this.setMemberships(memberships);
    }

    public PlatformUser getUser() {
      return this.user == null ? null : new PlatformUser(this.user);
    }

    public void setUser(PlatformUser user) {
      this.user = user == null ? null : new PlatformUser(user);
    }

    public List<TenantUserMembership> getMemberships() {
      return this.memberships == null ? Collections.emptyList() : new ArrayList<>(this.memberships);
    }

    public void setMemberships(List<TenantUserMembership> memberships) {
      this.memberships =
          memberships == null ? Collections.emptyList() : new ArrayList<>(memberships);
    }
  }
}
