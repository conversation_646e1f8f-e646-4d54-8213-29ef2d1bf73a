package com.assessment.controller;

import com.assessment.service.TenantHierarchyService;
import com.assessment.service.TenantHierarchyService.TenantHierarchyNode;
import com.assessment.service.TenantHierarchyService.PermissionType;
import com.assessment.service.TenantHierarchyService.PermissionScope;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 租户层级管理控制器
 * 处理多级组织架构的租户关系管理
 */
@Slf4j
@RestController
@RequestMapping("/api/tenant-hierarchy")
@RequiredArgsConstructor
@Tag(name = "租户层级管理", description = "多级组织架构的租户关系管理API")
public class TenantHierarchyController {

    private final TenantHierarchyService tenantHierarchyService;

    @GetMapping("/tree/{tenantId}")
    @Operation(summary = "获取租户层级树", description = "获取指定租户的完整组织架构树")
    @PreAuthorize("hasAuthority('ROLE_SUPER_ADMIN') or hasPermission(#tenantId, 'TENANT', 'READ')")
    public ResponseEntity<Map<String, Object>> getTenantHierarchyTree(
            @Parameter(description = "租户ID") @PathVariable String tenantId) {
        
        log.info("获取租户层级树: tenantId={}", tenantId);
        
        try {
            List<TenantHierarchyNode> tree = tenantHierarchyService.getTenantHierarchyTree(tenantId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", tree);
            result.put("message", "获取租户层级树成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取租户层级树失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("data", null);
            result.put("message", "获取租户层级树失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    @GetMapping("/{tenantId}/descendants")
    @Operation(summary = "获取下级租户", description = "获取指定租户的所有下级租户ID")
    @PreAuthorize("hasAuthority('ROLE_SUPER_ADMIN') or hasPermission(#tenantId, 'TENANT', 'READ')")
    public ResponseEntity<Map<String, Object>> getDescendantTenants(
            @Parameter(description = "租户ID") @PathVariable String tenantId,
            @Parameter(description = "最大深度") @RequestParam(required = false) Integer maxDepth) {
        
        log.info("获取下级租户: tenantId={}, maxDepth={}", tenantId, maxDepth);
        
        try {
            List<String> descendants = tenantHierarchyService.getDescendantTenantIds(tenantId, maxDepth);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", descendants);
            result.put("count", descendants.size());
            result.put("message", "获取下级租户成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取下级租户失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("data", null);
            result.put("message", "获取下级租户失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    @GetMapping("/{tenantId}/ancestors")
    @Operation(summary = "获取上级租户", description = "获取指定租户的所有上级租户ID")
    @PreAuthorize("hasAuthority('ROLE_SUPER_ADMIN') or hasPermission(#tenantId, 'TENANT', 'READ')")
    public ResponseEntity<Map<String, Object>> getAncestorTenants(
            @Parameter(description = "租户ID") @PathVariable String tenantId) {
        
        log.info("获取上级租户: tenantId={}", tenantId);
        
        try {
            List<String> ancestors = tenantHierarchyService.getAncestorTenantIds(tenantId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", ancestors);
            result.put("count", ancestors.size());
            result.put("message", "获取上级租户成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取上级租户失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("data", null);
            result.put("message", "获取上级租户失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    @GetMapping("/{tenantId}/accessible")
    @Operation(summary = "获取可访问租户", description = "获取当前租户可以访问数据的所有租户ID")
    @PreAuthorize("hasAuthority('ROLE_SUPER_ADMIN') or hasPermission(#tenantId, 'TENANT', 'READ')")
    public ResponseEntity<Map<String, Object>> getAccessibleTenants(
            @Parameter(description = "租户ID") @PathVariable String tenantId,
            @Parameter(description = "权限类型") @RequestParam(defaultValue = "READ") String permissionType,
            @Parameter(description = "权限范围") @RequestParam(defaultValue = "ALL") String permissionScope) {
        
        log.info("获取可访问租户: tenantId={}, type={}, scope={}", tenantId, permissionType, permissionScope);
        
        try {
            PermissionType type = PermissionType.valueOf(permissionType.toUpperCase());
            PermissionScope scope = PermissionScope.valueOf(permissionScope.toUpperCase());
            
            List<String> accessibleTenants = tenantHierarchyService.getAccessibleTenantIds(tenantId, type, scope);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", accessibleTenants);
            result.put("count", accessibleTenants.size());
            result.put("message", "获取可访问租户成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取可访问租户失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("data", null);
            result.put("message", "获取可访问租户失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    @GetMapping("/{tenantId}/breadcrumb")
    @Operation(summary = "获取租户面包屑", description = "获取租户的层级导航路径")
    @PreAuthorize("hasAuthority('ROLE_SUPER_ADMIN') or hasPermission(#tenantId, 'TENANT', 'READ')")
    public ResponseEntity<Map<String, Object>> getTenantBreadcrumb(
            @Parameter(description = "租户ID") @PathVariable String tenantId) {
        
        log.info("获取租户面包屑: tenantId={}", tenantId);
        
        try {
            List<Map<String, Object>> breadcrumb = tenantHierarchyService.getTenantBreadcrumb(tenantId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", breadcrumb);
            result.put("message", "获取租户面包屑成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取租户面包屑失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("data", null);
            result.put("message", "获取租户面包屑失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    @PostMapping("/{sourceTenantId}/permissions/{targetTenantId}")
    @Operation(summary = "授予数据权限", description = "授予源租户访问目标租户数据的权限")
    @PreAuthorize("hasAuthority('ROLE_SUPER_ADMIN')")
    public ResponseEntity<Map<String, Object>> grantDataPermission(
            @Parameter(description = "源租户ID") @PathVariable String sourceTenantId,
            @Parameter(description = "目标租户ID") @PathVariable String targetTenantId,
            @Parameter(description = "权限类型") @RequestParam String permissionType,
            @Parameter(description = "权限范围") @RequestParam String permissionScope,
            @Parameter(description = "授权人ID") @RequestParam String grantedBy) {
        
        log.info("授予数据权限: source={}, target={}, type={}, scope={}, grantedBy={}", 
                sourceTenantId, targetTenantId, permissionType, permissionScope, grantedBy);
        
        try {
            PermissionType type = PermissionType.valueOf(permissionType.toUpperCase());
            PermissionScope scope = PermissionScope.valueOf(permissionScope.toUpperCase());
            
            tenantHierarchyService.grantDataPermission(sourceTenantId, targetTenantId, type, scope, grantedBy);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "授予数据权限成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("授予数据权限失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "授予数据权限失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    @DeleteMapping("/{sourceTenantId}/permissions/{targetTenantId}")
    @Operation(summary = "撤销数据权限", description = "撤销源租户访问目标租户数据的权限")
    @PreAuthorize("hasAuthority('ROLE_SUPER_ADMIN')")
    public ResponseEntity<Map<String, Object>> revokeDataPermission(
            @Parameter(description = "源租户ID") @PathVariable String sourceTenantId,
            @Parameter(description = "目标租户ID") @PathVariable String targetTenantId,
            @Parameter(description = "权限类型") @RequestParam String permissionType,
            @Parameter(description = "权限范围") @RequestParam String permissionScope) {
        
        log.info("撤销数据权限: source={}, target={}, type={}, scope={}", 
                sourceTenantId, targetTenantId, permissionType, permissionScope);
        
        try {
            PermissionType type = PermissionType.valueOf(permissionType.toUpperCase());
            PermissionScope scope = PermissionScope.valueOf(permissionScope.toUpperCase());
            
            tenantHierarchyService.revokeDataPermission(sourceTenantId, targetTenantId, type, scope);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "撤销数据权限成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("撤销数据权限失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "撤销数据权限失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    @PutMapping("/{tenantId}/parent/{newParentId}")
    @Operation(summary = "更新租户层级", description = "变更租户的上级组织")
    @PreAuthorize("hasAuthority('ROLE_SUPER_ADMIN')")
    public ResponseEntity<Map<String, Object>> updateTenantHierarchy(
            @Parameter(description = "租户ID") @PathVariable String tenantId,
            @Parameter(description = "新父租户ID") @PathVariable String newParentId) {
        
        log.info("更新租户层级: tenantId={}, newParentId={}", tenantId, newParentId);
        
        try {
            // 检查层级关系的有效性
            if (!tenantHierarchyService.isValidHierarchy(newParentId, tenantId)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "无效的层级关系，会形成循环引用");
                return ResponseEntity.badRequest().body(result);
            }
            
            tenantHierarchyService.updateTenantHierarchy(tenantId, newParentId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "更新租户层级成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新租户层级失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "更新租户层级失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    @PostMapping("/permissions/check")
    @Operation(summary = "检查数据权限", description = "检查源租户是否有权限访问目标租户的数据")
    @PreAuthorize("hasAuthority('ROLE_SUPER_ADMIN') or hasAnyRole('ROLE_TENANT_ADMIN')")
    public ResponseEntity<Map<String, Object>> checkDataPermission(
            @Parameter(description = "权限检查请求") @RequestBody Map<String, String> request) {
        
        String sourceTenantId = request.get("sourceTenantId");
        String targetTenantId = request.get("targetTenantId");
        String permissionType = request.getOrDefault("permissionType", "READ");
        String permissionScope = request.getOrDefault("permissionScope", "ALL");
        
        log.info("检查数据权限: source={}, target={}, type={}, scope={}", 
                sourceTenantId, targetTenantId, permissionType, permissionScope);
        
        try {
            PermissionType type = PermissionType.valueOf(permissionType.toUpperCase());
            PermissionScope scope = PermissionScope.valueOf(permissionScope.toUpperCase());
            
            boolean hasPermission = tenantHierarchyService.hasDataPermission(
                    sourceTenantId, targetTenantId, type, scope);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("hasPermission", hasPermission);
            result.put("message", hasPermission ? "有权限访问" : "无权限访问");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查数据权限失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("hasPermission", false);
            result.put("message", "检查数据权限失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }
}