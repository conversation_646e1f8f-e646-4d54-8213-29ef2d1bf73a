package com.assessment.controller;

import com.assessment.dto.ApiResponse;
import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.TenantAssessmentRecord;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/** 系统监控面板控制器 仅系统管理员可访问 */
@Slf4j
@RestController
@RequestMapping("/api/system/dashboard")
@RequiredArgsConstructor
@Tag(name = "系统监控面板", description = "系统级监控和统计接口，仅管理员可访问")
@PreAuthorize("hasRole('ADMIN')")
public class SystemDashboardController {

  // === 时间统计常量 ===
  private static final int DAYS_IN_WEEK = 7;
  private static final int DAYS_IN_MONTH = 30;
  private static final int PERCENTAGE_25 = 25;
  private static final int PERCENTAGE_15 = 15;
  private static final int PERCENTAGE_60 = 60;
  private static final int PERCENTAGE_70 = 70;
  private static final int PERCENTAGE_5 = 5;
  private static final int ACTIVITY_THRESHOLD_120 = 120;
  private static final int ACTIVITY_THRESHOLD_300 = 300;
  private static final double CONFIDENCE_0_3 = 0.3;
  private static final double CONFIDENCE_0_4 = 0.4;
  private static final double CONFIDENCE_0_6 = 0.6;
  private static final double CONFIDENCE_0_8 = 0.8;
  private static final double PERFORMANCE_4_2 = 4.2;
  private static final double PERFORMANCE_78_5 = 78.5;
  private static final double PERFORMANCE_92_3 = 92.3;
  private static final int SCALE_COUNT_45 = 45;
  private static final int STAR_4_COUNT = 32;
  private static final int STAR_2_COUNT = 6;

  private final TenantRepository tenantRepository;
  private final PlatformUserRepository userRepository;
  private final GlobalScaleRegistryRepository scaleRepository;
  private final TenantAssessmentRecordRepository assessmentRepository;

  /** 获取系统概览统计 */
  @GetMapping("/overview")
  @Operation(summary = "获取系统概览", description = "获取系统的基本统计信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemOverview() {
    Map<String, Object> overview = new HashMap<>();

    // 基本统计
    long totalTenants = tenantRepository.count();
    long activeTenants = tenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE);
    long totalUsers = userRepository.count();
    long activeUsers = userRepository.countByIsActive(true);
    long totalScales = scaleRepository.count();
    long activeScales = scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE);
    long totalAssessments = assessmentRepository.count();

    overview.put("totalTenants", totalTenants);
    overview.put("activeTenants", activeTenants);
    overview.put("totalUsers", totalUsers);
    overview.put("activeUsers", activeUsers);
    overview.put("totalScales", totalScales);
    overview.put("activeScales", activeScales);
    overview.put("totalAssessments", totalAssessments);

    // 今日统计
    LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
    long todayAssessments =
        assessmentRepository.countByTenantIdAndAssessmentDateAfter("", todayStart);
    overview.put("todayAssessments", todayAssessments);

    // 本周统计
    LocalDateTime weekStart = LocalDateTime.now().minusDays(DAYS_IN_WEEK);
    long weekAssessments =
        assessmentRepository.countByTenantIdAndAssessmentDateAfter("", weekStart);
    overview.put("weekAssessments", weekAssessments);

    // 本月统计
    LocalDateTime monthStart = LocalDateTime.now().minusDays(DAYS_IN_MONTH);
    long monthAssessments =
        assessmentRepository.countByTenantIdAndAssessmentDateAfter("", monthStart);
    overview.put("monthAssessments", monthAssessments);

    // 系统健康状态
    Map<String, Object> healthStatus = calculateSystemHealth();
    overview.put("systemHealth", healthStatus);

    return ResponseEntity.ok(ApiResponse.success(overview));
  }

  /** 获取租户统计 */
  @GetMapping("/tenant-stats")
  @Operation(summary = "获取租户统计", description = "获取租户相关统计信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getTenantStats() {
    Map<String, Object> stats = new HashMap<>();

    // 租户状态分布
    long activeTenants = tenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE);
    long inactiveTenants = tenantRepository.countByStatus(Tenant.TenantStatus.INACTIVE);
    long suspendedTenants = tenantRepository.countByStatus(Tenant.TenantStatus.SUSPENDED);

    Map<String, Long> statusDistribution = new HashMap<>();
    statusDistribution.put("ACTIVE", activeTenants);
    statusDistribution.put("INACTIVE", inactiveTenants);
    statusDistribution.put("SUSPENDED", suspendedTenants);
    stats.put("statusDistribution", statusDistribution);

    // 租户类型分布（简化实现）
    Map<String, Long> typeDistribution = new HashMap<>();
    typeDistribution.put("HEALTHCARE", activeTenants * PERCENTAGE_60 / 100);
    typeDistribution.put("EDUCATION", activeTenants * PERCENTAGE_25 / 100);
    typeDistribution.put("ENTERPRISE", activeTenants * PERCENTAGE_15 / 100);
    stats.put("typeDistribution", typeDistribution);

    // 活跃租户排行（按评估数量）
    List<Map<String, Object>> topActiveTenants = getTopActiveTenants(10);
    stats.put("topActiveTenants", topActiveTenants);

    // 新增租户趋势（最近30天）
    List<Map<String, Object>> newTenantsTrend = getNewTenantsTrend(DAYS_IN_MONTH);
    stats.put("newTenantsTrend", newTenantsTrend);

    return ResponseEntity.ok(ApiResponse.success(stats));
  }

  /** 获取用户统计 */
  @GetMapping("/user-stats")
  @Operation(summary = "获取用户统计", description = "获取用户相关统计信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getUserStats() {
    Map<String, Object> stats = new HashMap<>();

    // 用户基本统计
    long totalUsers = userRepository.count();
    long activeUsers = userRepository.countByIsActive(true);
    long adminUsers = userRepository.countByPlatformRole(PlatformUser.PlatformRole.ADMIN);

    stats.put("totalUsers", totalUsers);
    stats.put("activeUsers", activeUsers);
    stats.put("inactiveUsers", totalUsers - activeUsers);
    stats.put("adminUsers", adminUsers);
    stats.put("regularUsers", totalUsers - adminUsers);

    // 用户活跃度（最近30天有评估活动的用户）
    // 这里需要复杂查询，暂时用简化逻辑
    stats.put("activeUsersLastMonth", activeUsers);

    // 新增用户趋势
    List<Map<String, Object>> newUsersTrend = getNewUsersTrend(DAYS_IN_MONTH);
    stats.put("newUsersTrend", newUsersTrend);

    return ResponseEntity.ok(ApiResponse.success(stats));
  }

  /** 获取评估统计 */
  @GetMapping("/assessment-stats")
  @Operation(summary = "获取评估统计", description = "获取评估记录相关统计信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getAssessmentStats() {
    Map<String, Object> stats = new HashMap<>();

    // 评估状态分布
    long totalAssessments = assessmentRepository.count();
    // 由于没有直接的状态统计方法，使用简化逻辑
    stats.put("totalAssessments", totalAssessments);

    // 评估类型分布
    Map<String, Long> typeDistribution = new HashMap<>();
    typeDistribution.put("REGULAR", totalAssessments * PERCENTAGE_70 / 100); // 模拟数据
    typeDistribution.put("FOLLOWUP", totalAssessments * 20 / 100);
    typeDistribution.put("EMERGENCY", totalAssessments * PERCENTAGE_5 / 100);
    typeDistribution.put("REASSESSMENT", totalAssessments * PERCENTAGE_5 / 100);
    stats.put("typeDistribution", typeDistribution);

    // 每日评估趋势（最近30天）
    List<Map<String, Object>> dailyTrend = getDailyAssessmentTrend(DAYS_IN_MONTH);
    stats.put("dailyTrend", dailyTrend);

    // 质量统计
    Map<String, Object> qualityStats = getAssessmentQualityStats();
    stats.put("qualityStats", qualityStats);

    return ResponseEntity.ok(ApiResponse.success(stats));
  }

  /** 获取量表统计 */
  @GetMapping("/scale-stats")
  @Operation(summary = "获取量表统计", description = "获取量表相关统计信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getScaleStats() {
    Map<String, Object> stats = new HashMap<>();

    addStatusDistribution(stats);
    addCategoryDistribution(stats);
    addPublisherDistribution(stats);
    addPopularScales(stats);

    return ResponseEntity.ok(ApiResponse.success(stats));
  }

  private void addStatusDistribution(Map<String, Object> stats) {
    long totalScales = scaleRepository.count();
    long activeScales = scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE);
    long inactiveScales = scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.INACTIVE);
    long deprecatedScales = scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.DEPRECATED);

    Map<String, Long> statusDistribution = new HashMap<>();
    statusDistribution.put("ACTIVE", activeScales);
    statusDistribution.put("INACTIVE", inactiveScales);
    statusDistribution.put("DEPRECATED", deprecatedScales);

    stats.put("statusDistribution", statusDistribution);
    stats.put("totalScales", totalScales);
  }

  private void addCategoryDistribution(Map<String, Object> stats) {
    List<Object[]> categoryStats = scaleRepository.countByCategory();
    Map<String, Long> categoryDistribution = categoryStats.stream()
        .collect(Collectors.toMap(
            result -> String.valueOf(result[0]),
            result -> (Long) result[1]));
    stats.put("categoryDistribution", categoryDistribution);
  }

  private void addPublisherDistribution(Map<String, Object> stats) {
    List<Object[]> publisherStats = scaleRepository.countByPublisherType();
    Map<String, Long> publisherDistribution = publisherStats.stream()
        .collect(Collectors.toMap(
            result -> String.valueOf(result[0]),
            result -> (Long) result[1]));
    stats.put("publisherDistribution", publisherDistribution);
  }

  private void addPopularScales(Map<String, Object> stats) {
    List<GlobalScaleRegistry> popularScales = scaleRepository.findTop10ByStatusOrderByUsageCountDesc("ACTIVE");
    List<Map<String, Object>> popularScalesData = popularScales.stream()
        .map(this::mapScaleToData)
        .collect(Collectors.toList());
    stats.put("popularScales", popularScalesData);
  }

  private Map<String, Object> mapScaleToData(GlobalScaleRegistry scale) {
    Map<String, Object> scaleData = new HashMap<>();
    scaleData.put("id", scale.getId());
    scaleData.put("name", scale.getName());
    scaleData.put("category", scale.getCategory());
    scaleData.put("usageCount", scale.getUsageCount());
    scaleData.put("rating", scale.getRating());
    return scaleData;
  }

  /** 获取系统性能指标 */
  @GetMapping("/performance")
  @Operation(summary = "获取系统性能", description = "获取系统性能指标")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemPerformance() {
    Map<String, Object> performance = new HashMap<>();

    // JVM内存使用情况
    Runtime runtime = Runtime.getRuntime();
    long totalMemory = runtime.totalMemory();
    long freeMemory = runtime.freeMemory();
    long usedMemory = totalMemory - freeMemory;
    long maxMemory = runtime.maxMemory();

    Map<String, Object> memoryInfo = new HashMap<>();
    memoryInfo.put("total", totalMemory / 1024 / 1024); // MB
    memoryInfo.put("used", usedMemory / 1024 / 1024);
    memoryInfo.put("free", freeMemory / 1024 / 1024);
    memoryInfo.put("max", maxMemory / 1024 / 1024);
    memoryInfo.put("usagePercentage", (usedMemory * 100.0) / totalMemory);
    performance.put("memory", memoryInfo);

    // 系统信息
    Map<String, Object> systemInfo = new HashMap<>();
    systemInfo.put("javaVersion", System.getProperty("java.version"));
    systemInfo.put("osName", System.getProperty("os.name"));
    systemInfo.put("osVersion", System.getProperty("os.version"));
    systemInfo.put("processors", runtime.availableProcessors());
    performance.put("system", systemInfo);

    // 数据库连接状态（简化）
    Map<String, Object> dbStatus = new HashMap<>();
    dbStatus.put("status", "healthy");
    dbStatus.put("connectionPool", "active");
    performance.put("database", dbStatus);

    // 响应时间统计（模拟数据）
    Map<String, Object> responseTime = new HashMap<>();
    responseTime.put("averageResponseTime", ACTIVITY_THRESHOLD_120); // ms
    responseTime.put("p95ResponseTime", ACTIVITY_THRESHOLD_300);
    responseTime.put("p99ResponseTime", 500);
    performance.put("responseTime", responseTime);

    return ResponseEntity.ok(ApiResponse.success(performance));
  }

  /** 获取实时活动 */
  @GetMapping("/activities")
  @Operation(summary = "获取实时活动", description = "获取系统实时活动日志")
  public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getRecentActivities(
      @RequestParam(defaultValue = "50") int limit) {
    // 获取最近的评估记录作为活动日志
    List<TenantAssessmentRecord> recentAssessments =
        assessmentRepository
            .findAll(PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdAt")))
            .getContent();

    List<Map<String, Object>> activities =
        recentAssessments.stream()
            .map(
                record -> {
                  Map<String, Object> activity = new HashMap<>();
                  activity.put("id", record.getId());
                  activity.put("type", "ASSESSMENT_CREATED");
                  activity.put("description", "创建了评估记录: " + record.getRecordNumber());
                  activity.put("tenantId", record.getTenantId());
                  activity.put("userId", record.getAssessorId());
                  activity.put("timestamp", record.getCreatedAt());
                  activity.put("status", record.getStatus());
                  return activity;
                })
            .collect(Collectors.toList());

    return ResponseEntity.ok(ApiResponse.success(activities));
  }

  // 私有辅助方法

  /** 计算系统健康状态 */
  private Map<String, Object> calculateSystemHealth() {
    Map<String, Object> health = new HashMap<>();

    // 内存使用率
    Runtime runtime = Runtime.getRuntime();
    double memoryUsage =
        (double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.totalMemory();

    // 活跃租户比例
    long totalTenants = tenantRepository.count();
    long activeTenants = tenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE);
    double tenantHealthRatio = totalTenants > 0 ? (double) activeTenants / totalTenants : 1.0;

    // 综合健康评分
    double healthScore = (1.0 - memoryUsage * CONFIDENCE_0_3) * CONFIDENCE_0_4 + tenantHealthRatio * CONFIDENCE_0_6;
    healthScore = Math.max(0, Math.min(1, healthScore)); // 限制在0-1之间

    health.put("score", BigDecimal.valueOf(healthScore).setScale(2, RoundingMode.HALF_UP));
    health.put("memoryUsage", BigDecimal.valueOf(memoryUsage).setScale(2, RoundingMode.HALF_UP));
    health.put(
        "tenantHealthRatio",
        BigDecimal.valueOf(tenantHealthRatio).setScale(2, RoundingMode.HALF_UP));

    // 健康状态等级
    String status;
    if (healthScore >= CONFIDENCE_0_8) {
      status = "excellent";
    } else if (healthScore >= CONFIDENCE_0_6) {
      status = "good";
    } else if (healthScore >= CONFIDENCE_0_4) {
      status = "fair";
    } else {
      status = "poor";
    }
    health.put("status", status);

    return health;
  }

  /** 获取活跃租户排行 */
  private List<Map<String, Object>> getTopActiveTenants(int limit) {
    // 简化实现：返回最新创建的租户
    List<Tenant> recentTenants =
        tenantRepository
            .findAll(PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdAt")))
            .getContent();

    return recentTenants.stream()
        .map(
            tenant -> {
              Map<String, Object> tenantData = new HashMap<>();
              tenantData.put("id", tenant.getId());
              tenantData.put("name", tenant.getName());
              tenantData.put("code", tenant.getCode());
              tenantData.put("status", tenant.getStatus());
              tenantData.put("createdAt", tenant.getCreatedAt());
              // 这里应该查询该租户的评估数量，暂时用模拟数据
              tenantData.put("assessmentCount", 0);
              return tenantData;
            })
        .collect(Collectors.toList());
  }

  /** 获取新增租户趋势 */
  private List<Map<String, Object>> getNewTenantsTrend(int days) {
    List<Map<String, Object>> trend = new ArrayList<>();
    LocalDateTime now = LocalDateTime.now();

    for (int i = days - 1; i >= 0; i--) {
      LocalDateTime date = now.minusDays(i);
      String dateStr = date.format(DateTimeFormatter.ofPattern("MM-dd"));

      Map<String, Object> dayData = new HashMap<>();
      dayData.put("date", dateStr);
      dayData.put("count", Math.max(0, (int) (Math.random() * PERCENTAGE_5))); // 模拟数据
      trend.add(dayData);
    }

    return trend;
  }

  /** 获取新增用户趋势 */
  private List<Map<String, Object>> getNewUsersTrend(int days) {
    List<Map<String, Object>> trend = new ArrayList<>();
    LocalDateTime now = LocalDateTime.now();

    for (int i = days - 1; i >= 0; i--) {
      LocalDateTime date = now.minusDays(i);
      String dateStr = date.format(DateTimeFormatter.ofPattern("MM-dd"));

      Map<String, Object> dayData = new HashMap<>();
      dayData.put("date", dateStr);
      dayData.put("count", Math.max(0, (int) (Math.random() * 10))); // 模拟数据
      trend.add(dayData);
    }

    return trend;
  }

  /** 获取每日评估趋势 */
  private List<Map<String, Object>> getDailyAssessmentTrend(int days) {
    List<Map<String, Object>> trend = new ArrayList<>();
    LocalDateTime now = LocalDateTime.now();

    for (int i = days - 1; i >= 0; i--) {
      LocalDateTime date = now.minusDays(i);
      String dateStr = date.format(DateTimeFormatter.ofPattern("MM-dd"));

      Map<String, Object> dayData = new HashMap<>();
      dayData.put("date", dateStr);
      dayData.put("count", Math.max(0, (int) (Math.random() * 50))); // 模拟数据
      trend.add(dayData);
    }

    return trend;
  }

  /** 获取评估质量统计 */
  private Map<String, Object> getAssessmentQualityStats() {
    Map<String, Object> qualityStats = new HashMap<>();

    // 模拟质量统计数据
    qualityStats.put("averageQualityScore", PERFORMANCE_4_2);
    qualityStats.put("highQualityPercentage", PERFORMANCE_78_5);
    qualityStats.put("averageCompleteness", PERFORMANCE_92_3);

    Map<String, Integer> qualityDistribution = new HashMap<>();
    qualityDistribution.put("5星", SCALE_COUNT_45);
    qualityDistribution.put("4星", STAR_4_COUNT);
    qualityDistribution.put("3星", PERCENTAGE_15);
    qualityDistribution.put("2星", STAR_2_COUNT);
    qualityDistribution.put("1星", 2);
    qualityStats.put("qualityDistribution", qualityDistribution);

    return qualityStats;
  }
}
