package com.assessment.controller;

import com.assessment.dto.ApiResponse;
import com.assessment.dto.DocumentAnalysisRequest;
import com.assessment.dto.DocumentAnalysisResult;
import com.assessment.service.AIAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/** AI文档结构分析控制器 使用LM Studio本地模型进行文档结构智能分析 */
@RestController
@RequestMapping("/api/ai")
@CrossOrigin(
    origins = {
      "http://localhost:5273",
      "http://localhost:5274",
      "${app.frontend.urls:http://localhost:3000}"
    })
@RequiredArgsConstructor
@Slf4j
@Tag(name = "AI Analysis", description = "AI文档结构分析接口")
public class AIAnalysisController {

  /** 最大文档内容长度 */
  private static final int MAX_CONTENT_LENGTH = 100000;
  
  /** SSE连接超时时间 - 10分钟 */
  private static final long SSE_TIMEOUT_10_MINUTES = 600000L;
  
  /** SSE连接超时时间 - 1分钟 */
  private static final long SSE_TIMEOUT_1_MINUTE = 60000L;
  
  /** 最大重试次数阈值 */
  private static final int MAX_RETRY_THRESHOLD = 5;

  private final AIAnalysisService aiAnalysisService;

  /** 分析文档结构并生成数据库建议 */
  @PostMapping("/analyze-document-structure")
  @Operation(summary = "AI分析文档结构", description = "使用AI模型分析文档内容，生成数据库表结构建议")
  public ResponseEntity<ApiResponse<DocumentAnalysisResult>> analyzeDocumentStructure(
      @RequestBody DocumentAnalysisRequest request) {

    log.info("开始AI分析文档结构: {}", request.getFileName());

    try {
      // 验证请求参数
      if (request.getMarkdownContent() == null || request.getMarkdownContent().trim().isEmpty()) {
        return ResponseEntity.badRequest().body(ApiResponse.error("文档内容不能为空"));
      }

      if (request.getMarkdownContent().length() > MAX_CONTENT_LENGTH) {
        return ResponseEntity.badRequest().body(ApiResponse.error("文档内容过长，请限制在100KB以内"));
      }

      // 调用AI分析服务
      DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(request);

      log.info(
          "AI分析完成: 表名={}, 字段数={}, 置信度={}",
          result.getTableName(),
          result.getFields().size(),
          result.getConfidence());

      return ResponseEntity.ok(ApiResponse.success(result));

    } catch (Exception e) {
      log.error("AI分析文档结构失败", e);
      return ResponseEntity.ok(ApiResponse.error("AI分析失败: " + e.getMessage()));
    }
  }

  /** 获取AI分析服务状态 */
  @GetMapping("/status")
  @Operation(summary = "获取AI服务状态")
  public ResponseEntity<ApiResponse<String>> getAIStatus() {
    try {
      boolean isAvailable = aiAnalysisService.isAIServiceAvailable();
      String status = isAvailable ? "AI服务正常" : "AI服务不可用";
      return ResponseEntity.ok(ApiResponse.success(status));
    } catch (Exception e) {
      log.error("检查AI服务状态失败", e);
      return ResponseEntity.ok(ApiResponse.error("检查AI服务状态失败: " + e.getMessage()));
    }
  }

  /** 获取当前LM Studio模型信息 */
  @GetMapping("/model-info")
  @Operation(summary = "获取当前LM Studio模型信息")
  public ResponseEntity<ApiResponse<java.util.Map<String, Object>>> getModelInfo() {
    try {
      java.util.Map<String, Object> modelInfo = aiAnalysisService.getCurrentModelInfo();
      return ResponseEntity.ok(ApiResponse.success(modelInfo));
    } catch (Exception e) {
      log.error("获取模型信息失败", e);
      return ResponseEntity.ok(ApiResponse.error("获取模型信息失败: " + e.getMessage()));
    }
  }

  /** 流式分析文档结构 - Server-Sent Events */
  @PostMapping(
      value = "/analyze-document-structure-stream",
      produces = MediaType.TEXT_EVENT_STREAM_VALUE)
  @Operation(summary = "流式AI分析文档结构", description = "使用SSE实时返回AI分析过程")
  public SseEmitter analyzeDocumentStructureStream(@RequestBody DocumentAnalysisRequest request) {
    log.info("开始流式AI分析文档结构: {}", request.getFileName());

    SseEmitter emitter = new SseEmitter(SSE_TIMEOUT_10_MINUTES); // 10分钟超时

    try {
      // 验证请求参数
      if (request.getMarkdownContent() == null || request.getMarkdownContent().trim().isEmpty()) {
        emitter.send(SseEmitter.event().name("error").data("文档内容不能为空"));
        emitter.complete();
        return emitter;
      }

      if (request.getMarkdownContent().length() > MAX_CONTENT_LENGTH) {
        emitter.send(SseEmitter.event().name("error").data("文档内容过长，请限制在100KB以内"));
        emitter.complete();
        return emitter;
      }

      // 立即发送测试事件
      emitter.send(SseEmitter.event().name("test").data("连接建立成功"));

      // 异步执行AI分析
      aiAnalysisService.analyzeDocumentWithStream(request, emitter);

    } catch (IOException e) {
      log.error("流式AI分析IO异常", e);
      try {
        emitter.send(SseEmitter.event().name("error").data("IO异常: " + e.getMessage()));
        emitter.completeWithError(e);
      } catch (IOException ex) {
        log.error("发送错误事件失败", ex);
      }
    } catch (Exception e) {
      log.error("流式AI分析失败", e);
      try {
        emitter.send(SseEmitter.event().name("error").data("分析失败: " + e.getMessage()));
        emitter.completeWithError(e);
      } catch (IOException ex) {
        log.error("发送错误事件失败", ex);
      }
    }

    return emitter;
  }

  /** 与AI模型进行对话 */
  @PostMapping("/chat")
  @Operation(summary = "与AI模型对话", description = "支持基于文档内容的对话交互")
  public ResponseEntity<ApiResponse<String>> chatWithAI(
      @RequestBody java.util.Map<String, String> request) {
    try {
      String message = request.get("message");
      String context = request.get("context"); // Markdown内容作为上下文

      if (message == null || message.trim().isEmpty()) {
        return ResponseEntity.badRequest().body(ApiResponse.error("消息内容不能为空"));
      }

      String response = aiAnalysisService.chatWithAI(message, context);
      return ResponseEntity.ok(ApiResponse.success(response));
    } catch (Exception e) {
      log.error("AI对话失败", e);
      return ResponseEntity.ok(ApiResponse.error("AI对话失败: " + e.getMessage()));
    }
  }

  /** 测试SSE功能 */
  @GetMapping(value = "/test-sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
  @Operation(summary = "测试SSE功能")
  public SseEmitter testSSE() {
    log.info("开始测试SSE功能");
    SseEmitter emitter = new SseEmitter(SSE_TIMEOUT_1_MINUTE);

    try {
      // 立即发送测试消息
      emitter.send(SseEmitter.event().name("test").data("SSE连接成功"));

      // 使用ScheduledExecutorService代替Thread.sleep循环
      ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
      AtomicInteger counter = new AtomicInteger(1);

      Runnable task =
          () -> {
            try {
              int current = counter.get();
              if (current <= MAX_RETRY_THRESHOLD) {
                emitter.send(SseEmitter.event().name("count").data("计数: " + current));
                counter.incrementAndGet();
              } else {
                emitter.send(SseEmitter.event().name("complete").data("测试完成"));
                emitter.complete();
                scheduler.shutdown();
              }
            } catch (IOException e) {
              log.error("SSE测试IO异常", e);
              emitter.completeWithError(e);
              scheduler.shutdown();
            } catch (Exception e) {
              log.error("SSE测试失败", e);
              emitter.completeWithError(e);
              scheduler.shutdown();
            }
          };

      scheduler.scheduleAtFixedRate(task, 1, 1, TimeUnit.SECONDS);

    } catch (IOException e) {
      log.error("SSE测试初始化IO异常", e);
      emitter.completeWithError(e);
    } catch (Exception e) {
      log.error("SSE测试初始化失败", e);
      emitter.completeWithError(e);
    }

    return emitter;
  }
}
