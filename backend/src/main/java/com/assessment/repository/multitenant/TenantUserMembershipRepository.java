package com.assessment.repository.multitenant;

import com.assessment.entity.multitenant.TenantUserMembership;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/** 租户用户关联数据访问层 */
@Repository
public interface TenantUserMembershipRepository
    extends JpaRepository<TenantUserMembership, String> {

  /** 根据租户ID和用户ID查找关联关系 */
  @Query(value = "SELECT * FROM tenant_user_memberships "
          + "WHERE tenant_id = CAST(:tenantId AS UUID) "
          + "AND user_id = CAST(:userId AS UUID)",
      nativeQuery = true)
  Optional<TenantUserMembership> findByTenantIdAndUserId(
      @Param("tenantId") String tenantId, @Param("userId") String userId);

  /** 根据租户ID查找所有用户关联 */
  List<TenantUserMembership> findByTenantId(String tenantId);

  /** 根据用户ID查找所有租户关联 */
  List<TenantUserMembership> findByUserId(String userId);

  /** 根据租户ID和角色查找用户 */
  List<TenantUserMembership> findByTenantIdAndTenantRole(
      String tenantId, TenantUserMembership.TenantRole tenantRole);

  /** 根据租户ID和状态查找用户 */
  List<TenantUserMembership> findByTenantIdAndStatus(
      String tenantId, TenantUserMembership.MembershipStatus status);

  /** 查找租户中的活跃用户 */
  @Query(
      value = "SELECT * FROM tenant_user_memberships WHERE tenant_id = CAST(:tenantId AS UUID) AND status = 'ACTIVE'",
      nativeQuery = true)
  List<TenantUserMembership> findActiveMembersByTenantId(@Param("tenantId") String tenantId);

  /** 统计租户中的用户数量 */
  long countByTenantId(String tenantId);

  /** 统计租户中活跃用户数量 */
  long countByTenantIdAndStatus(String tenantId, TenantUserMembership.MembershipStatus status);

  /** 检查用户是否属于租户 */
  boolean existsByTenantIdAndUserId(String tenantId, String userId);

  /** 检查用户在租户中是否有特定角色 */
  boolean existsByTenantIdAndUserIdAndTenantRole(
      String tenantId, String userId, TenantUserMembership.TenantRole tenantRole);
}
