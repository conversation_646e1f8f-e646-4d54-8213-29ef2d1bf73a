package com.assessment.repository.multitenant;

import com.assessment.entity.multitenant.GlobalScaleRegistry;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/** 全局量表注册中心Repository 管理平台级别的评估量表 */
@Repository
public interface GlobalScaleRegistryRepository extends JpaRepository<GlobalScaleRegistry, String> {

  /** 根据量表代码查找 */
  Optional<GlobalScaleRegistry> findByCode(String code);

  /** 查找所有公开的活跃量表 */
  @Query(
      "SELECT g FROM GlobalScaleRegistry g WHERE g.visibility = 'PUBLIC' AND g.status = 'ACTIVE'")
  List<GlobalScaleRegistry> findPublicActiveScales();

  /** 分页查找量表 */
  @Query(
      "SELECT g FROM GlobalScaleRegistry g WHERE "
          + "(:category IS NULL OR g.category = :category) AND "
          + "(:visibility IS NULL OR g.visibility = :visibility) AND "
          + "(:status IS NULL OR g.status = :status)")
  Page<GlobalScaleRegistry> findWithFilters(
      @Param("category") String category,
      @Param("visibility") String visibility,
      @Param("status") String status,
      Pageable pageable);

  /** 根据行业标签查找量表 */
  @Query(
      value =
          "SELECT * FROM global_scale_registry g WHERE :industryTag = ANY(g.industry_tags) AND g.status = 'ACTIVE'",
      nativeQuery = true)
  List<GlobalScaleRegistry> findByIndustryTag(@Param("industryTag") String industryTag);

  /** 根据关键词搜索量表 */
  @Query(
      value =
          "SELECT * FROM global_scale_registry g WHERE "
              + "(LOWER(g.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR "
              + "LOWER(g.code) LIKE LOWER(CONCAT('%', :keyword, '%')) OR "
              + ":keyword = ANY(g.keywords)) AND g.status = 'ACTIVE'",
      nativeQuery = true)
  List<GlobalScaleRegistry> searchByKeyword(@Param("keyword") String keyword);

  /** 查找官方量表 */
  List<GlobalScaleRegistry> findByIsOfficialTrueAndStatus(String status);

  /** 查找租户发布的量表 */
  List<GlobalScaleRegistry> findByPublisherTypeAndPublisherId(
      String publisherType, String publisherId);

  /** 更新使用次数 */
  @Query("UPDATE GlobalScaleRegistry g SET g.usageCount = g.usageCount + 1 WHERE g.id = :id")
  void incrementUsageCount(@Param("id") String id);

  /** 检查量表代码是否已存在 */
  boolean existsByCode(String code);

  /** 获取热门量表（按使用次数排序） */
  List<GlobalScaleRegistry> findTop10ByStatusOrderByUsageCountDesc(String status);

  /** 获取高评分量表 */
  @Query("SELECT g FROM GlobalScaleRegistry g WHERE g.rating >= :minRating "
      + "AND g.ratingCount >= :minRatingCount AND g.status = 'ACTIVE' "
      + "ORDER BY g.rating DESC")
  List<GlobalScaleRegistry> findHighRatedScales(
      @Param("minRating") Double minRating, @Param("minRatingCount") Integer minRatingCount);

  /** 根据名称、可见性和状态查找量表 */
  Page<GlobalScaleRegistry> findByNameContainingAndVisibilityAndStatus(
      String name,
      GlobalScaleRegistry.Visibility visibility,
      GlobalScaleRegistry.ScaleStatus status,
      Pageable pageable);

  /** 根据分类、可见性和状态查找量表 */
  Page<GlobalScaleRegistry> findByCategoryAndVisibilityAndStatus(
      String category,
      GlobalScaleRegistry.Visibility visibility,
      GlobalScaleRegistry.ScaleStatus status,
      Pageable pageable);

  /** 根据可见性和状态查找量表 */
  Page<GlobalScaleRegistry> findByVisibilityAndStatus(
      GlobalScaleRegistry.Visibility visibility,
      GlobalScaleRegistry.ScaleStatus status,
      Pageable pageable);

  /** 获取所有不同的分类 */
  @Query("SELECT DISTINCT g.category FROM GlobalScaleRegistry g WHERE g.status = 'ACTIVE'")
  List<String> findDistinctCategories();

  /** 根据名称或代码模糊查询 */
  Page<GlobalScaleRegistry> findByNameContainingIgnoreCaseOrCodeContainingIgnoreCase(
      String name, String code, Pageable pageable);

  /** 根据分类查找量表 */
  Page<GlobalScaleRegistry> findByCategory(String category, Pageable pageable);

  /** 根据状态查找量表 */
  Page<GlobalScaleRegistry> findByStatus(GlobalScaleRegistry.ScaleStatus status, Pageable pageable);

  /** 根据状态统计量表数量 */
  long countByStatus(GlobalScaleRegistry.ScaleStatus status);

  /** 根据可见性统计量表数量 */
  long countByVisibility(GlobalScaleRegistry.Visibility visibility);

  /** 根据是否官方统计量表数量 */
  long countByIsOfficial(boolean isOfficial);

  /** 按分类统计量表数量 */
  @Query("SELECT g.category, COUNT(g) FROM GlobalScaleRegistry g GROUP BY g.category")
  List<Object[]> countByCategory();

  /** 按发布者类型统计量表数量 */
  @Query("SELECT g.publisherType, COUNT(g) FROM GlobalScaleRegistry g GROUP BY g.publisherType")
  List<Object[]> countByPublisherType();
}
