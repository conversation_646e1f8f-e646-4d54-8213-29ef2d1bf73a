package com.assessment.pdf.extractor;

import com.assessment.pdf.AssessmentTable;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;

/** 表格提取服务 从PDF中识别和提取表格数据 */
@Service
@Slf4j
public class TableExtractionService {

  private static final Pattern SCORE_PATTERN = Pattern.compile(".*[分值得点].*");
  private static final Pattern OPTION_PATTERN = Pattern.compile(".*[选项答案].*");
  private static final Pattern CRITERIA_PATTERN = Pattern.compile(".*[标准等级级别].*");

  /** 从PDF中提取表格数据 */
  public List<AssessmentTable> extractTables(final PDDocument document) {
    List<AssessmentTable> tables = new ArrayList<>();

    try {
      log.debug("开始提取表格，PDF页数: {}", document.getNumberOfPages());

      // 简化的表格提取逻辑，从文本中识别表格模式
      PDFTextStripper stripper = new PDFTextStripper();
      stripper.getText(document);

      // 按页面提取
      for (int pageNum = 1; pageNum <= document.getNumberOfPages(); pageNum++) {
        stripper.setStartPage(pageNum);
        stripper.setEndPage(pageNum);
        String pageText = stripper.getText(document);

        List<AssessmentTable> pageTables = extractTablesFromText(pageText, pageNum);
        tables.addAll(pageTables);
      }

      log.info("表格提取完成，共提取{}个表格", tables.size());

    } catch (Exception e) {
      log.error("表格提取失败: {}", e.getMessage(), e);
    }

    return tables;
  }

  /** 从文本中提取表格 */
  private List<AssessmentTable> extractTablesFromText(final String pageText, final int pageNumber) {
    List<AssessmentTable> tables = new ArrayList<>();

    try {
      // 按行分割文本
      String[] lines = pageText.split("\\n");
      List<List<String>> tableRows = new ArrayList<>();
      boolean inTable = false;

      for (String line : lines) {
        line = line.trim();
        if (line.isEmpty()) continue;

        // 检测表格开始 - 包含多个制表符或空格分隔的列
        if (isTableRow(line)) {
          if (!inTable) {
            inTable = true;
            tableRows = new ArrayList<>();
          }

          // 分割行为列
          List<String> columns = splitTableRow(line);
          if (!columns.isEmpty()) {
            tableRows.add(columns);
          }
        } else {
          // 表格结束
          if (inTable && tableRows.size() > 1) {
            AssessmentTable table = createTableFromRows(tableRows, pageNumber);
            if (table != null) {
              tables.add(table);
            }
          }
          inTable = false;
          tableRows.clear();
        }
      }

      // 处理文件末尾的表格
      if (inTable && tableRows.size() > 1) {
        AssessmentTable table = createTableFromRows(tableRows, pageNumber);
        if (table != null) {
          tables.add(table);
        }
      }

    } catch (Exception e) {
      log.warn("从文本提取表格失败: {}", e.getMessage());
    }

    return tables;
  }

  /** 从表格行创建评估表格对象 */
  private AssessmentTable createTableFromRows(final List<List<String>> rows, final int pageNumber) {
    if (rows.size() < 2) {
      return null; // 至少需要标题和一行数据
    }

    // 识别表格类型
    AssessmentTable.TableType type = identifyTableType(rows);
    if (type == AssessmentTable.TableType.UNKNOWN) {
      return null;
    }

    // 提取表格标题
    String title = extractTableTitle(rows);

    return AssessmentTable.builder()
        .type(type)
        .title(title)
        .headers(rows.get(0))
        .data(rows.subList(1, rows.size()))
        .pageNumber(pageNumber)
        .build();
  }

  /** 检查是否为表格行 */
  private boolean isTableRow(final String line) {
    // 简单的表格行检测：包含制表符或多个空格分隔的内容
    return line.contains("\t") || line.split("\\s{2,}").length > 1 || line.split("\\|").length > 1;
  }

  /** 分割表格行为列 */
  private List<String> splitTableRow(final String line) {
    List<String> columns;

    // 尝试不同的分割方式
    if (line.contains("\t")) {
      // 制表符分割
      columns = Arrays.asList(line.split("\t"));
    } else if (line.contains("|")) {
      // 管道符分割
      columns = Arrays.asList(line.split("\\|"));
    } else {
      // 多个空格分割
      columns = Arrays.asList(line.split("\\s{2,}"));
    }

    // 清理列内容
    return columns.stream()
        .map(String::trim)
        .filter(col -> !col.isEmpty())
        .collect(Collectors.toList());
  }

  /** 识别表格类型 */
  private AssessmentTable.TableType identifyTableType(final List<List<String>> rows) {
    String headerText = String.join(" ", rows.get(0)).toLowerCase(java.util.Locale.ROOT);
    String allText =
        rows.stream()
            .flatMap(List::stream)
            .collect(Collectors.joining(" "))
            .toLowerCase(java.util.Locale.ROOT);

    // 评分标准表
    if (SCORE_PATTERN.matcher(headerText).matches()
        || headerText.contains("分数")
        || headerText.contains("得分")
        || headerText.contains("评分")) {
      return AssessmentTable.TableType.SCORE_CRITERIA;
    }

    // 问题选项表
    if (OPTION_PATTERN.matcher(headerText).matches()
        || headerText.contains("选项")
        || headerText.contains("答案")
        || allText.contains("a.")
        || allText.contains("①")
        || allText.contains("1)")
        || allText.contains("（1）")) {
      return AssessmentTable.TableType.QUESTION_OPTIONS;
    }

    // 评分规则表
    if (CRITERIA_PATTERN.matcher(headerText).matches()
        || headerText.contains("等级")
        || headerText.contains("级别")
        || headerText.contains("标准")
        || headerText.contains("规则")) {
      return AssessmentTable.TableType.SCORING_RULES;
    }

    // 评估项目表
    if (headerText.contains("项目")
        || headerText.contains("内容")
        || headerText.contains("指标")
        || headerText.contains("维度")) {
      return AssessmentTable.TableType.ASSESSMENT_ITEMS;
    }

    return AssessmentTable.TableType.UNKNOWN;
  }

  /** 提取表格标题 */
  private String extractTableTitle(final List<List<String>> rows) {
    // 简单的标题提取逻辑
    if (!rows.isEmpty() && !rows.get(0).isEmpty()) {
      String firstCell = rows.get(0).get(0);
      if (firstCell.length() > 2 && firstCell.length() < 50) {
        return firstCell;
      }
    }
    return "未知表格";
  }

  /** 验证表格数据质量 */
  public boolean validateTableQuality(final AssessmentTable table) {
    if (table == null || table.getData() == null) {
      return false;
    }

    // 检查数据完整性
    int expectedColumns = table.getHeaders().size();
    for (List<String> row : table.getData()) {
      if (row.size() != expectedColumns) {
        log.warn("表格数据列数不一致: 期望{}, 实际{}", expectedColumns, row.size());
        return false;
      }
    }

    // 检查空值比例
    long totalCells = table.getData().size() * expectedColumns;
    long emptyCells =
        table.getData().stream()
            .flatMap(List::stream)
            .mapToLong(cell -> cell.trim().isEmpty() ? 1 : 0)
            .sum();

    double emptyRatio = (double) emptyCells / totalCells;
    if (emptyRatio > 0.5) {
      log.warn("表格空值比例过高: {}%", emptyRatio * 100);
      return false;
    }

    return true;
  }
}
