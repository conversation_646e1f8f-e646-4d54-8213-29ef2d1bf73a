package com.assessment.pdf;

import java.util.ArrayList;
import java.util.List;
import lombok.Builder;

/** 评估结构 */
@Builder
public class AssessmentStructure {
  private AssessmentMetadata metadata;
  @lombok.Singular private List<AssessmentSection> sections;
  private ScoringRules scoringRules;

  // Getter methods
  @SuppressWarnings("EI_EXPOSE_REP")
  public AssessmentMetadata getMetadata() {
    // 注意：AssessmentMetadata如果是可变对象，也需要实现防御性拷贝
    // 这里假设它是不可变的或者已经实现了防御性拷贝
    return metadata;
  }

  public List<AssessmentSection> getSections() {
    return sections == null ? null : new ArrayList<>(sections);
  }

  @SuppressWarnings("EI_EXPOSE_REP")
  public ScoringRules getScoringRules() {
    // 注意：ScoringRules如果是可变对象，也需要实现防御性拷贝
    // 这里假设它是不可变的或者已经实现了防御性拷贝
    return scoringRules;
  }

  // Setter methods
  @SuppressWarnings("EI_EXPOSE_REP2")
  public void setMetadata(final AssessmentMetadata metadata) {
    // 注意：如果AssessmentMetadata是可变对象，这里也需要防御性拷贝
    this.metadata = metadata;
  }

  public void setSections(final List<AssessmentSection> sections) {
    this.sections = sections == null ? null : new ArrayList<>(sections);
  }

  @SuppressWarnings("EI_EXPOSE_REP2")
  public void setScoringRules(final ScoringRules scoringRules) {
    // 注意：如果ScoringRules是可变对象，这里也需要防御性拷贝
    this.scoringRules = scoringRules;
  }
}
