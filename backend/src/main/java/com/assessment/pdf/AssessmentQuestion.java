package com.assessment.pdf;

import java.util.ArrayList;
import java.util.List;
import lombok.Builder;

/** 评估问题 */
@Builder
public class AssessmentQuestion {
  private String id;
  private String title;
  private String text;
  private String type;
  private boolean required;
  private int order;
  @Builder.Default private List<String> options = new ArrayList<>();

  public void addOption(final String option) {
    if (options == null) {
      options = new ArrayList<>();
    }
    options.add(option);
  }

  // Getter methods
  public String getId() {
    return id;
  }

  public String getTitle() {
    return title;
  }

  public String getText() {
    return text != null ? text : title;
  }

  public String getType() {
    return type;
  }

  public boolean isRequired() {
    return required;
  }

  public int getOrder() {
    return order;
  }

  public List<String> getOptions() {
    return this.options;
  }

  // Setter methods
  public void setId(final String id) {
    this.id = id;
  }

  public void setTitle(final String title) {
    this.title = title;
  }

  public void setText(final String text) {
    this.text = text;
  }

  public void setType(final String type) {
    this.type = type;
  }

  public void setRequired(final boolean required) {
    this.required = required;
  }

  public void setOrder(final int order) {
    this.order = order;
  }

  public void setOptions(final List<String> options) {
    this.options = options;
  }
}
