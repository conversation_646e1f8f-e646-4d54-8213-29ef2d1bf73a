package com.assessment.pdf;

import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/** 评估章节 */
@Data
@Builder
public class AssessmentSection {
  private String id;
  private String title;
  private String description;
  private int order;
  @Builder.Default private List<AssessmentQuestion> questions = new ArrayList<>();

  // 手动提供getter和setter方法以实现防御性拷贝
  public List<AssessmentQuestion> getQuestions() {
    return questions == null ? null : new ArrayList<>(questions);
  }

  public void setQuestions(final List<AssessmentQuestion> questions) {
    this.questions = questions == null ? null : new ArrayList<>(questions);
  }

  public void addQuestion(final AssessmentQuestion question) {
    if (questions == null) {
      questions = new ArrayList<>();
    }
    questions.add(question);
  }
}
