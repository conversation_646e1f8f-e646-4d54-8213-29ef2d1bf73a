package com.assessment.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * IP地址获取工具类
 * 支持从HTTP请求中获取真实客户端IP地址
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-24
 */
@Slf4j
public final class IpUtils {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
    private static final int PRIVATE_IP_RANGE_START = 16;
    private static final int PRIVATE_IP_RANGE_END = 31;
    
    private IpUtils() {
        throw new IllegalStateException("工具类不能实例化");
    }

    /**
     * 获取客户端真实IP地址
     * 支持代理、负载均衡等场景
     */
    public static String getClientIp(final HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest为空，返回默认IP");
            return LOCALHOST_IPV4;
        }

        String ip = tryGetIpFromProxyHeaders(request);
        if (isValidIp(ip)) {
            return ip;
        }

        // 最后使用getRemoteAddr()方法获取
        ip = request.getRemoteAddr();
        if (isValidIp(ip)) {
            return ip;
        }

        log.warn("无法获取客户端IP，返回默认值");
        return LOCALHOST_IPV4;
    }

    /**
     * 尝试从代理头中获取IP
     */
    private static String tryGetIpFromProxyHeaders(final HttpServletRequest request) {
        // 定义要检查的头部列表
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        for (String headerName : headerNames) {
            String ip = getIpFromHeader(request, headerName);
            if (isValidIp(ip)) {
                // X-Forwarded-For和HTTP_X_FORWARDED_FOR可能包含多个IP，取第一个
                if ("X-Forwarded-For".equals(headerName) || "HTTP_X_FORWARDED_FOR".equals(headerName)) {
                    return ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        return null;
    }

    /**
     * 从请求头中获取IP
     */
    private static String getIpFromHeader(final HttpServletRequest request, final String headerName) {
        String ip = request.getHeader(headerName);
        log.debug("从请求头 {} 获取IP: {}", headerName, ip);
        return ip;
    }

    /**
     * 验证IP地址是否有效
     */
    private static boolean isValidIp(final String ip) {
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            return false;
        }
        
        // 过滤内网IP和本地IP（可根据需要调整）
        if (LOCALHOST_IPV4.equals(ip) || LOCALHOST_IPV6.equals(ip)) {
            log.debug("检测到本地IP: {}", ip);
            return true;  // 开发环境允许本地IP
        }
        
        return true;
    }

    /**
     * 判断是否为内网IP
     */
    public static boolean isInternalIp(final String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        try {
            // 127.x.x.x (本地回环)
            if (ip.startsWith("127.")) {
                return true;
            }
            
            // 10.x.x.x (A类私有地址)
            if (ip.startsWith("10.")) {
                return true;
            }
            
            // 172.16.x.x - 172.31.x.x (B类私有地址)
            if (ip.startsWith("172.")) {
                String[] parts = ip.split("\\.");
                if (parts.length >= 2) {
                    int secondOctet = Integer.parseInt(parts[1]);
                    if (secondOctet >= PRIVATE_IP_RANGE_START && secondOctet <= PRIVATE_IP_RANGE_END) {
                        return true;
                    }
                }
            }
            
            // 192.168.x.x (C类私有地址)
            if (ip.startsWith("192.168.")) {
                return true;
            }
            
            // IPv6本地地址
            if (LOCALHOST_IPV6.equals(ip) || "::1".equals(ip)) {
                return true;
            }
            
        } catch (Exception e) {
            log.error("判断内网IP时发生错误: {}", e.getMessage());
        }
        
        return false;
    }

    /**
     * 获取用户代理信息
     */
    public static String getUserAgent(final HttpServletRequest request) {
        if (request == null) {
            return "Unknown";
        }
        
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null ? userAgent : "Unknown";
    }

    /**
     * 获取请求来源（Referer）
     */
    public static String getReferer(final HttpServletRequest request) {
        if (request == null) {
            return "Unknown";
        }
        
        String referer = request.getHeader("Referer");
        return referer != null ? referer : "Direct";
    }
}