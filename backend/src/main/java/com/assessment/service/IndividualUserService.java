package com.assessment.service;

import com.assessment.dto.IndividualRegisterRequest;
import com.assessment.dto.UnifiedLoginRequest;
import com.assessment.dto.MultiTenantLoginResponse;
import com.assessment.entity.IndividualUser;
import com.assessment.repository.IndividualUserRepository;
import com.assessment.util.IpUtils;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 个人用户服务
 * 负责个人用户的注册、登录、管理等功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class IndividualUserService {

    /** 免费用户月度评估次数限制 */
    private static final int FREE_USER_MONTHLY_LIMIT = 5;

    /** Token过期时间（秒） */
    private static final int TOKEN_EXPIRY_SECONDS = 3600;

    private final IndividualUserRepository individualUserRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenService jwtTokenService;
    private final UserIdentityService userIdentityService;
    private final VerificationCodeService verificationCodeService;

    /**
     * 个人用户注册
     */
    @Transactional
    public IndividualUser registerUser(IndividualRegisterRequest request) {
        log.info("个人用户注册开始: {}", request.getSecureLogInfo());

        // 1. 验证注册数据
        validateRegistrationRequest(request);

        // 2. 检查邮箱是否已存在
        if (individualUserRepository.existsByEmail(request.getEmail())) {
            throw new BadCredentialsException("该邮箱已被注册，请使用其他邮箱或直接登录");
        }

        // 3. 检查手机号是否已存在（如果提供了手机号）
        if (request.getPhone() != null && !request.getPhone().trim().isEmpty()) {
            if (individualUserRepository.existsByPhone(request.getPhone())) {
                throw new BadCredentialsException("该手机号已被注册，请使用其他手机号");
            }
        }

        // 4. 创建用户实体
        IndividualUser user = IndividualUser.builder()
                .email(request.getEmail().toLowerCase().trim())
                .phone(request.getPhone())
                .realName(request.getName().trim())
                .displayName(request.getName().trim())
                .passwordHash(passwordEncoder.encode(request.getPassword()))
                .status(IndividualUser.AccountStatus.ACTIVE)
                .serviceType(determineServiceType(request.getServiceType()))
                .registrationSource(determineRegistrationSource(request.getSource()))
                .referralCode(request.getReferralCode())
                .gender(parseGender(request.getGender()))
                .birthDate(request.getBirthDate())
                .idCard(request.getIdCard())
                .emailVerified(false) // 开发阶段先设为false，后续可以发送验证邮件
                .phoneVerified(request.getPhone() != null)
                .loginCount(0)
                .monthlyAssessmentCount(0)
                .assessmentCountResetAt(LocalDateTime.now())
                .build();

        // 5. 确认用户协议和隐私政策
        if (Boolean.TRUE.equals(request.getAgreedToTerms())) {
            user.acceptTerms();
        }
        if (Boolean.TRUE.equals(request.getAgreedToPrivacy())) {
            user.acceptPrivacy();
        }

        // 6. 保存用户
        user = individualUserRepository.save(user);
        log.info("个人用户注册成功: id={}, email={}", user.getId(), user.getEmail());

        return user;
    }

    /**
     * 个人用户登录认证
     */
    public MultiTenantLoginResponse authenticateIndividualUser(final UnifiedLoginRequest request,
                                                            final HttpServletRequest httpRequest) {
        log.info("个人用户登录尝试: {}", request.getSecureLogInfo());

        // 1. 查找用户
        Optional<IndividualUser> userOpt = individualUserRepository.findByEmailOrPhone(request.getIdentifier());
        if (userOpt.isEmpty()) {
            throw new BadCredentialsException("用户不存在，请检查邮箱/手机号或先注册账号");
        }

        IndividualUser user = userOpt.get();

        // 2. 检查账户状态
        if (!IndividualUser.AccountStatus.ACTIVE.equals(user.getStatus())) {
            throw new BadCredentialsException("账户已被禁用，请联系客服");
        }

        // 3. 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPasswordHash())) {
            throw new BadCredentialsException("密码错误，请重新输入");
        }

        // 4. 更新登录信息
        user.updateLoginInfo(getClientIp(httpRequest));
        individualUserRepository.save(user);

        // 5. 生成JWT Token
        String accessToken = jwtTokenService.generateIndividualUserToken(user);
        String refreshToken = jwtTokenService.generateRefreshToken(user.getId().toString());

        // 6. 构建登录响应
        return buildIndividualLoginResponse(user, accessToken, refreshToken);
    }

    /**
     * 检查邮箱是否可用
     */
    public boolean isEmailAvailable(String email) {
        return !individualUserRepository.existsByEmail(email.toLowerCase().trim());
    }

    /**
     * 检查手机号是否可用
     */
    public boolean isPhoneAvailable(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return true;
        }
        return !individualUserRepository.existsByPhone(phone.trim());
    }

    /**
     * 根据ID查找用户
     */
    public Optional<IndividualUser> findById(UUID userId) {
        return individualUserRepository.findByIdAndStatus(userId, IndividualUser.AccountStatus.ACTIVE);
    }

    /**
     * 根据邮箱查找用户
     */
    public Optional<IndividualUser> findByEmail(String email) {
        return individualUserRepository.findByEmail(email.toLowerCase().trim());
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public IndividualUser updateUserInfo(UUID userId, String realName, String displayName, 
                                       IndividualUser.Gender gender, String birthDate) {
        IndividualUser user = findById(userId)
                .orElseThrow(() -> new BadCredentialsException("用户不存在"));

        if (realName != null && !realName.trim().isEmpty()) {
            user.setRealName(realName.trim());
        }
        if (displayName != null && !displayName.trim().isEmpty()) {
            user.setDisplayName(displayName.trim());
        }
        if (gender != null) {
            user.setGender(gender);
        }
        if (birthDate != null && !birthDate.trim().isEmpty()) {
            user.setBirthDate(birthDate.trim());
        }

        return individualUserRepository.save(user);
    }

    /**
     * 修改密码
     */
    @Transactional
    public void changePassword(UUID userId, String oldPassword, String newPassword) {
        IndividualUser user = findById(userId)
                .orElseThrow(() -> new BadCredentialsException("用户不存在"));

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPasswordHash())) {
            throw new BadCredentialsException("原密码错误");
        }

        // 设置新密码
        user.setPasswordHash(passwordEncoder.encode(newPassword));
        individualUserRepository.save(user);

        log.info("用户密码修改成功: userId={}", userId);
    }

    /**
     * 重置密码（通过邮箱验证）
     */
    @Transactional
    public void resetPassword(String email, String newPassword, String verificationCode) {
        // 验证邮箱验证码
        if (!verificationCodeService.verifyEmailCode(email, verificationCode)) {
            throw new BadCredentialsException("验证码错误或已过期");
        }
        
        IndividualUser user = findByEmail(email)
                .orElseThrow(() -> new BadCredentialsException("用户不存在"));

        user.setPasswordHash(passwordEncoder.encode(newPassword));
        individualUserRepository.save(user);

        log.info("用户密码重置成功: email={}", email);
    }

    /**
     * 升级用户服务套餐
     */
    @Transactional
    public void upgradeUserService(UUID userId, IndividualUser.ServiceType targetType, LocalDateTime expiresAt) {
        IndividualUser user = findById(userId)
                .orElseThrow(() -> new BadCredentialsException("用户不存在"));

        switch (targetType) {
            case PREMIUM:
                user.upgradeToPremium(expiresAt);
                break;
            case PRO:
                user.upgradeToPro(expiresAt);
                break;
            default:
                throw new IllegalArgumentException("不支持的服务类型: " + targetType);
        }

        individualUserRepository.save(user);
        log.info("用户服务升级成功: userId={}, serviceType={}", userId, targetType);
    }

    /**
     * 增加用户评估次数
     */
    @Transactional
    public void incrementAssessmentCount(UUID userId) {
        IndividualUser user = findById(userId)
                .orElseThrow(() -> new BadCredentialsException("用户不存在"));

        user.incrementAssessmentCount();
        individualUserRepository.save(user);
    }

    /**
     * 检查用户是否可以进行评估
     */
    public boolean canUserPerformAssessment(UUID userId) {
        return findById(userId)
                .map(IndividualUser::canPerformAssessment)
                .orElse(false);
    }

    /**
     * 获取用户月度评估剩余次数
     */
    public int getRemainingAssessmentCount(UUID userId) {
        return findById(userId)
                .map(user -> {
                    if (user.isPremiumUser() && user.isSubscriptionValid()) {
                        return Integer.MAX_VALUE; // 付费用户无限制
                    }
                    return Math.max(0, FREE_USER_MONTHLY_LIMIT - user.getMonthlyAssessmentCount()); // 免费用户限制
                })
                .orElse(0);
    }

    /**
     * 验证注册请求
     */
    private void validateRegistrationRequest(IndividualRegisterRequest request) {
        if (!request.isValid()) {
            throw new BadCredentialsException("注册信息不完整，请检查必填字段");
        }

        // 验证邮箱格式
        if (!userIdentityService.isValidEmail(request.getEmail())) {
            throw new BadCredentialsException("邮箱格式不正确");
        }

        // 验证手机号格式（如果提供了）
        if (request.getPhone() != null && !request.getPhone().trim().isEmpty()) {
            if (!userIdentityService.isValidPhone(request.getPhone())) {
                throw new BadCredentialsException("手机号格式不正确");
            }
        }

        // 验证码验证（开发阶段可选）
        if (request.getVerificationCode() != null && !request.getVerificationCode().trim().isEmpty()) {
            if (!verificationCodeService.verifyEmailCode(request.getEmail(), request.getVerificationCode())) {
                throw new BadCredentialsException("验证码错误或已过期");
            }
        }
        // 注意：开发阶段允许不提供验证码，生产环境建议强制验证
    }

    /**
     * 确定服务类型
     */
    private IndividualUser.ServiceType determineServiceType(String serviceType) {
        if (serviceType == null) {
            return IndividualUser.ServiceType.FREE;
        }
        try {
            return IndividualUser.ServiceType.valueOf(serviceType.toUpperCase());
        } catch (IllegalArgumentException e) {
            return IndividualUser.ServiceType.FREE;
        }
    }

    /**
     * 确定注册来源
     */
    private IndividualUser.RegistrationSource determineRegistrationSource(String source) {
        if (source == null) {
            return IndividualUser.RegistrationSource.WEB;
        }
        try {
            return IndividualUser.RegistrationSource.valueOf(source.toUpperCase());
        } catch (IllegalArgumentException e) {
            return IndividualUser.RegistrationSource.WEB;
        }
    }

    /**
     * 解析性别
     */
    private IndividualUser.Gender parseGender(String gender) {
        if (gender == null || gender.trim().isEmpty()) {
            return null;
        }
        try {
            return IndividualUser.Gender.valueOf(gender.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest httpRequest) {
        if (httpRequest != null) {
            return IpUtils.getClientIp(httpRequest);
        }
        return "127.0.0.1";
    }

    /**
     * 构建个人用户登录响应
     */
    private MultiTenantLoginResponse buildIndividualLoginResponse(IndividualUser user, 
                                                                String accessToken, 
                                                                String refreshToken) {
        return MultiTenantLoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(TOKEN_EXPIRY_SECONDS) // 1小时
                .userId(user.getId().toString())
                .username(user.getEmail())
                .email(user.getEmail())
                .firstName(user.getRealName())
                .lastName("")
                .displayName(user.getEffectiveDisplayName())
                .tenantId(null) // 个人用户无租户概念
                .tenantCode("INDIVIDUAL")
                .tenantName("个人用户")
                .tenantRole(user.getServiceType().name())
                .platformRole(null)
                .isActive(IndividualUser.AccountStatus.ACTIVE.equals(user.getStatus()))
                .isSuperAdmin(false)
                .lastLoginAt(user.getLastLoginAt())
                .permissions(getIndividualUserPermissions(user))
                .accessibleTenantIds(List.of("INDIVIDUAL"))
                .build();
    }

    /**
     * 获取个人用户权限列表
     */
    private List<String> getIndividualUserPermissions(IndividualUser user) {
        if (user.isPremiumUser() && user.isSubscriptionValid()) {
            return Arrays.asList(
                "assessment:create", "assessment:view", "assessment:export",
                "report:view", "report:export", "history:view",
                "profile:edit", "data:backup"
            );
        } else {
            return Arrays.asList(
                "assessment:create_limited", "assessment:view", 
                "report:view_basic", "history:view_limited",
                "profile:edit"
            );
        }
    }

}