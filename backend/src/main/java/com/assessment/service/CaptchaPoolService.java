package com.assessment.service;

import com.assessment.dto.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 验证码池服务
 * 预生成验证码池，避免高并发下的性能瓶颈
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CaptchaPoolService {

    private final StringRedisTemplate stringRedisTemplate;
    private final SimpleCaptchaService simpleCaptchaService;
    
    // 配置常量
    private static final String POOL_KEY_PREFIX = "captcha:pool:";
    private static final String POOL_STATS_KEY = "captcha:pool:stats";
    private static final int INITIAL_POOL_SIZE = 100;  // 初始池大小
    private static final int CRITICAL_THRESHOLD = 50;  // 临界阈值，生成500个
    private static final int LOW_THRESHOLD = 200;      // 低水位阈值，生成2000个
    private static final int CRITICAL_REPLENISH = 500; // 临界补充数量
    private static final int NORMAL_REPLENISH = 2000;  // 正常补充数量
    private static final int BATCH_SIZE = 100;         // 批量生成数量
    private static final int TTL_MINUTES = 5;          // 验证码有效期
    
    private final AtomicBoolean isReplenishing = new AtomicBoolean(false);

    /**
     * 启动时预热验证码池
     */
    @PostConstruct
    public void initializePool() {
        log.info("🚀 初始化验证码池...");
        replenishPool(INITIAL_POOL_SIZE);
        log.info("✅ 验证码池初始化完成，预生成 {} 个验证码", INITIAL_POOL_SIZE);
    }

    /**
     * 从池中获取验证码
     */
    public ApiResponse<Map<String, Object>> getCaptchaFromPool() {
        try {
            // 获取池中的一个验证码
            Set<String> poolKeys = stringRedisTemplate.keys(POOL_KEY_PREFIX + "*");
            
            if (poolKeys == null || poolKeys.isEmpty()) {
                log.warn("⚠️ 验证码池为空，降级到实时生成");
                updatePoolStats("pool_empty", 1);
                return simpleCaptchaService.generateCaptcha();
            }
            
            // 随机选择一个验证码
            String selectedKey = poolKeys.iterator().next();
            String captchaData = stringRedisTemplate.opsForValue().get(selectedKey);
            
            if (captchaData == null) {
                log.warn("⚠️ 验证码数据为空，重试获取");
                // 清理无效key并重试
                stringRedisTemplate.delete(selectedKey);
                return getCaptchaFromPool();
            }
            
            // 删除已使用的验证码
            stringRedisTemplate.delete(selectedKey);
            
            // 更新统计
            updatePoolStats("served_from_pool", 1);
            
            // 检查是否需要补充
            checkAndTriggerReplenishment();
            
            // 解析验证码数据
            @SuppressWarnings("unchecked")
            Map<String, Object> result = new ObjectMapper().readValue(captchaData, Map.class);
            
            log.debug("✅ 从池中获取验证码成功，剩余: {}", poolKeys.size() - 1);
            return ApiResponse.success(result, "");
            
        } catch (Exception e) {
            log.error("❌ 从验证码池获取验证码失败", e);
            updatePoolStats("pool_error", 1);
            // 降级到实时生成
            return simpleCaptchaService.generateCaptcha();
        }
    }

    /**
     * 检查并触发池补充
     */
    private void checkAndTriggerReplenishment() {
        int currentSize = getCurrentPoolSize();
        
        if (currentSize <= CRITICAL_THRESHOLD) {
            log.warn("🚨 验证码池临界状态 ({}个)，触发紧急补充{}个！当前数量: {}", 
                CRITICAL_THRESHOLD, CRITICAL_REPLENISH, currentSize);
            updatePoolStats("critical_level", 1);
            asyncReplenishPool(CRITICAL_REPLENISH);
        } else if (currentSize <= LOW_THRESHOLD) {
            log.info("📈 验证码池低水位 ({}个)，触发补充{}个。当前数量: {}", 
                LOW_THRESHOLD, NORMAL_REPLENISH, currentSize);
            asyncReplenishPool(NORMAL_REPLENISH);
        }
    }

    /**
     * 异步补充验证码池
     */
    @Async
    public void asyncReplenishPool(int targetCount) {
        replenishPool(targetCount);
    }

    /**
     * 补充验证码池
     */
    private void replenishPool(int targetGenerate) {
        if (!isReplenishing.compareAndSet(false, true)) {
            log.debug("⏭️ 验证码池正在补充中，跳过本次任务");
            return;
        }
        
        try {
            int currentSize = getCurrentPoolSize();
            
            if (targetGenerate <= 0) {
                log.debug("✅ 无需补充验证码池，当前: {}个", currentSize);
                return;
            }
            
            log.info("🔄 开始补充验证码池，当前: {}个，需生成: {}个", currentSize, targetGenerate);
            
            int generated = 0;
            long startTime = System.currentTimeMillis();
            
            // 批量生成验证码
            while (generated < targetGenerate) {
                int batchSize = Math.min(BATCH_SIZE, targetGenerate - generated);
                
                for (int i = 0; i < batchSize; i++) {
                    try {
                        generateAndStoreCaptcha();
                        generated++;
                    } catch (Exception e) {
                        log.error("❌ 生成验证码失败", e);
                    }
                }
                
                // 批次间短暂暂停，避免CPU过载
                if (generated < targetGenerate) {
                    Thread.sleep(10);
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            updatePoolStats("generated_count", generated);
            updatePoolStats("last_replenish_time", System.currentTimeMillis());
            
            log.info("✅ 验证码池补充完成，生成: {}个，耗时: {}ms，当前总数: {}", 
                generated, duration, getCurrentPoolSize());
                
        } catch (Exception e) {
            log.error("❌ 验证码池补充失败", e);
            updatePoolStats("replenish_error", 1);
        } finally {
            isReplenishing.set(false);
        }
    }

    /**
     * 生成单个验证码并存储到池中
     */
    private void generateAndStoreCaptcha() throws Exception {
        // 生成验证码
        ApiResponse<Map<String, Object>> response = simpleCaptchaService.generateCaptcha();
        
        if (!response.isSuccess() || response.getData() == null) {
            throw new RuntimeException("验证码生成失败");
        }
        
        // 获取token作为key
        String token = (String) response.getData().get("token");
        if (token == null) {
            throw new RuntimeException("验证码token为空");
        }
        
        // 序列化验证码数据
        String captchaData = new ObjectMapper().writeValueAsString(response.getData());
        
        // 存储到Redis池中，设置过期时间
        String poolKey = POOL_KEY_PREFIX + token;
        stringRedisTemplate.opsForValue().set(poolKey, captchaData, TTL_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取当前池大小
     */
    public int getCurrentPoolSize() {
        Set<String> poolKeys = stringRedisTemplate.keys(POOL_KEY_PREFIX + "*");
        return poolKeys != null ? poolKeys.size() : 0;
    }

    /**
     * 获取池统计信息
     */
    public Map<String, Object> getPoolStats() {
        Map<Object, Object> stats = stringRedisTemplate.opsForHash().entries(POOL_STATS_KEY);
        int currentSize = getCurrentPoolSize();
        
        Map<String, Object> result = new HashMap<>();
        result.put("currentSize", currentSize);
        result.put("initialPoolSize", INITIAL_POOL_SIZE);
        result.put("lowThreshold", LOW_THRESHOLD);
        result.put("criticalThreshold", CRITICAL_THRESHOLD);
        result.put("normalReplenish", NORMAL_REPLENISH);
        result.put("criticalReplenish", CRITICAL_REPLENISH);
        result.put("poolStatus", getPoolStatus(currentSize));
        result.put("isReplenishing", isReplenishing.get());
        result.put("servedFromPool", stats.getOrDefault("served_from_pool", "0"));
        result.put("generatedCount", stats.getOrDefault("generated_count", "0"));
        result.put("poolEmptyCount", stats.getOrDefault("pool_empty", "0"));
        result.put("poolErrorCount", stats.getOrDefault("pool_error", "0"));
        result.put("lastReplenishTime", stats.getOrDefault("last_replenish_time", "0"));
        return result;
    }

    /**
     * 获取池状态
     */
    private String getPoolStatus(int currentSize) {
        if (currentSize <= CRITICAL_THRESHOLD) {
            return "CRITICAL";
        } else if (currentSize <= LOW_THRESHOLD) {
            return "LOW";
        } else if (currentSize >= NORMAL_REPLENISH) {
            return "FULL";
        } else {
            return "NORMAL";
        }
    }

    /**
     * 更新池统计
     */
    private void updatePoolStats(String key, Object value) {
        try {
            if (value instanceof Number) {
                stringRedisTemplate.opsForHash().increment(POOL_STATS_KEY, key, ((Number) value).longValue());
            } else {
                stringRedisTemplate.opsForHash().put(POOL_STATS_KEY, key, value.toString());
            }
            // 设置统计数据1天过期
            stringRedisTemplate.expire(POOL_STATS_KEY, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.debug("更新池统计失败: {}", e.getMessage());
        }
    }

    /**
     * 定时清理过期验证码和池维护
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行
    public void maintenanceTask() {
        try {
            int currentSize = getCurrentPoolSize();
            log.debug("🔧 验证码池维护检查，当前大小: {}", currentSize);
            
            // 如果池大小异常小，立即补充
            if (currentSize < CRITICAL_THRESHOLD) {
                log.warn("⚠️ 定时检查发现池大小异常 ({}), 立即补充{}个", currentSize, CRITICAL_REPLENISH);
                asyncReplenishPool(CRITICAL_REPLENISH);
            }
            
        } catch (Exception e) {
            log.error("❌ 验证码池维护任务失败", e);
        }
    }

    /**
     * 清空验证码池（管理接口）
     */
    public void clearPool() {
        try {
            Set<String> poolKeys = stringRedisTemplate.keys(POOL_KEY_PREFIX + "*");
            if (poolKeys != null && !poolKeys.isEmpty()) {
                stringRedisTemplate.delete(poolKeys);
                log.info("🗑️ 验证码池已清空，删除 {} 个验证码", poolKeys.size());
            }
        } catch (Exception e) {
            log.error("❌ 清空验证码池失败", e);
        }
    }
}