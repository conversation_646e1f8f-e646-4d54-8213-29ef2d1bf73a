package com.assessment.service;

import com.assessment.dto.PDFFormatInfo;
import com.assessment.dto.ParseStatusInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/** Docling Docker服务集成 负责与Docling Docker容器通信，进行文档转换 */
@Service
@Slf4j
public class DoclingService {

  private static final int DEFAULT_CONNECT_TIMEOUT = 30;

  @Value("${docling.service.url:http://localhost:8088}")
  private String doclingApiUrl;

  @Value("${docling.service.timeout:300}")
  private int timeoutSeconds;

  @Value("${docling.service.enabled:true}")
  private boolean enabled;

  private OkHttpClient httpClient;
  private final ObjectMapper objectMapper;

  public DoclingService() {
    this.objectMapper = new ObjectMapper();
  }

  /**
   * 初始化HTTP客户端
   * 配置连接超时、写入超时和读取超时参数
   */
  @PostConstruct
  public void initHttpClient() {
    this.httpClient =
        new OkHttpClient.Builder()
            .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(timeoutSeconds, TimeUnit.SECONDS)
            .readTimeout(timeoutSeconds, TimeUnit.SECONDS)
            .build();
  }

  /** 检查Docling服务是否可用 */
  public boolean isAvailable() {
    if (!enabled) {
      log.debug("Docling服务已禁用");
      return false;
    }

    try {
      Request request = new Request.Builder().url(doclingApiUrl + "/health").get().build();

      try (Response response = httpClient.newCall(request).execute()) {
        boolean isHealthy = response.isSuccessful();
        log.debug("Docling健康检查: {}", isHealthy ? "可用" : "不可用");
        return isHealthy;
      }
    } catch (Exception e) {
      log.warn("Docling健康检查失败: {}", e.getMessage());
      return false;
    }
  }

  /** 转换PDF文件为Markdown */
  public String convertPdfToMarkdown(MultipartFile file) {
    if (!isAvailable()) {
      throw new RuntimeException("Docling服务不可用");
    }

    try {
      // 创建临时文件
      Path tempFile = Files.createTempFile("docling-", ".pdf");
      file.transferTo(tempFile.toFile());

      try {
        return convertPdfToMarkdown(tempFile.toFile());
      } finally {
        // 清理临时文件
        Files.deleteIfExists(tempFile);
      }
    } catch (IOException e) {
      throw new RuntimeException("文件处理失败: " + e.getMessage(), e);
    }
  }

  /** 转换PDF文件为Markdown */
  public String convertPdfToMarkdown(File pdfFile) {
    if (!pdfFile.exists()) {
      throw new RuntimeException("PDF文件不存在: " + pdfFile.getPath());
    }

    try {
      RequestBody fileBody = RequestBody.create(pdfFile, MediaType.parse("application/pdf"));

      RequestBody requestBody =
          new MultipartBody.Builder()
              .setType(MultipartBody.FORM)
              .addFormDataPart("file", pdfFile.getName(), fileBody)
              .addFormDataPart("output_format", "markdown")
              .build();

      Request request =
          new Request.Builder().url(doclingApiUrl + "/convert").post(requestBody).build();

      log.info("开始转换PDF文件: {}, 大小: {} bytes", pdfFile.getName(), pdfFile.length());

      try (Response response = httpClient.newCall(request).execute()) {
        if (!response.isSuccessful()) {
          throw new RuntimeException("Docling转换失败，HTTP状态: " + response.code());
        }

        ResponseBody body = response.body();
        if (body == null) {
          throw new RuntimeException("响应体为空");
        }
        String responseBody = body.string();
        JsonNode jsonResponse = objectMapper.readTree(responseBody);

        if (!jsonResponse.get("success").asBoolean()) {
          String error = jsonResponse.get("error").asText();
          throw new RuntimeException("Docling转换失败: " + error);
        }

        String markdown = jsonResponse.get("markdown").asText();
        JsonNode metadata = jsonResponse.get("metadata");

        log.info(
            "PDF转换成功: {} -> {} 字符, {} 页",
            pdfFile.getName(),
            markdown.length(),
            metadata.get("page_count").asInt());

        return markdown;
      }
    } catch (IOException e) {
      throw new RuntimeException("Docling API调用失败: " + e.getMessage(), e);
    }
  }

  /** 转换PDF文件为Markdown并返回详细信息 */
  public PDFFormatInfo convertPdfToMarkdownWithInfo(MultipartFile file) {
    return convertDocumentWithInfo(file, "markdown");
  }

  /** 转换文档为指定格式并返回详细信息 */
  public PDFFormatInfo convertDocumentWithInfo(MultipartFile file, String outputFormat) {
    if (!isAvailable()) {
      throw new RuntimeException("Docling服务不可用");
    }

    try {
      // 根据文件类型确定后缀
      String originalFilename = file.getOriginalFilename();
      String suffix =
          originalFilename != null
              ? originalFilename.substring(originalFilename.lastIndexOf('.'))
              : ".tmp";

      // 创建临时文件
      Path tempFile = Files.createTempFile("docling-", suffix);
      file.transferTo(tempFile.toFile());

      try {
        return convertDocumentWithInfo(tempFile.toFile(), outputFormat);
      } finally {
        // 清理临时文件
        Files.deleteIfExists(tempFile);
      }
    } catch (IOException e) {
      throw new RuntimeException("文件处理失败: " + e.getMessage(), e);
    }
  }

  /** 转换文档为指定格式并返回详细信息 */
  public PDFFormatInfo convertDocumentWithInfo(File documentFile, String outputFormat) {
    long startTime = System.currentTimeMillis();

    // 根据文件扩展名确定正确的MIME类型
    String mimeType = getMimeTypeFromFile(documentFile);

    try {
      RequestBody fileBody = RequestBody.create(documentFile, MediaType.parse(mimeType));

      RequestBody requestBody =
          new MultipartBody.Builder()
              .setType(MultipartBody.FORM)
              .addFormDataPart("file", documentFile.getName(), fileBody)
              .addFormDataPart("output_format", outputFormat)
              .build();

      Request request =
          new Request.Builder().url(doclingApiUrl + "/convert").post(requestBody).build();

      log.info(
          "开始转换文档: {}, 大小: {} bytes, 输出格式: {}",
          documentFile.getName(),
          documentFile.length(),
          outputFormat);

      try (Response response = httpClient.newCall(request).execute()) {
        long processingTime = System.currentTimeMillis() - startTime;

        if (!response.isSuccessful()) {
          return PDFFormatInfo.builder()
              .fileName(documentFile.getName())
              .fileSize(documentFile.length())
              .processingTimeMs(processingTime)
              .errorMessage("HTTP错误: " + response.code())
              .parseStatus(ParseStatusInfo.builder().errorCount(1).build())
              .build();
        }

        ResponseBody body = response.body();
        if (body == null) {
          return PDFFormatInfo.builder()
              .fileName(documentFile.getName())
              .fileSize(documentFile.length())
              .processingTimeMs(processingTime)
              .errorMessage("响应体为空")
              .parseStatus(ParseStatusInfo.builder().errorCount(1).build())
              .build();
        }
        String responseBody = body.string();
        JsonNode jsonResponse = objectMapper.readTree(responseBody);

        boolean success = jsonResponse.get("success").asBoolean();
        String filename = jsonResponse.get("filename").asText();

        if (success) {
          // 根据输出格式获取相应的内容字段
          String content = getContentByFormat(jsonResponse, outputFormat);
          JsonNode metadata = jsonResponse.get("metadata");
          int pageCount = metadata.get("page_count").asInt();
          int contentLength = metadata.get("content_length").asInt();

          log.info(
              "文档转换成功: {} -> {} 字符, {} 页, 耗时: {}ms, 格式: {}",
              filename,
              contentLength,
              pageCount,
              processingTime,
              outputFormat);

          return PDFFormatInfo.builder()
              .fileName(filename)
              .fileSize(documentFile.length())
              .markdownContent(content) // 通用内容字段，可能是markdown、html或json
              .processingTimeMs(processingTime)
              .parseStatus(
                  ParseStatusInfo.builder()
                      .totalPages(pageCount)
                      .processedPages(pageCount)
                      .successfulPages(pageCount)
                      .errorCount(0)
                      .contentLength(contentLength)
                      .build())
              .build();
        } else {
          String error = jsonResponse.get("error").asText();
          log.error("文档转换失败: {} - {}", filename, error);

          return PDFFormatInfo.builder()
              .fileName(filename)
              .fileSize(documentFile.length())
              .processingTimeMs(processingTime)
              .errorMessage(error)
              .parseStatus(ParseStatusInfo.builder().errorCount(1).build())
              .build();
        }
      }
    } catch (IOException | RuntimeException e) {
      long processingTime = System.currentTimeMillis() - startTime;
      log.error("Docling API调用异常: {}", e.getMessage(), e);

      return PDFFormatInfo.builder()
          .fileName(documentFile.getName())
          .fileSize(documentFile.length())
          .processingTimeMs(processingTime)
          .errorMessage("API调用异常: " + e.getMessage())
          .parseStatus(ParseStatusInfo.builder().errorCount(1).build())
          .build();
    }
  }

  /** 转换PDF文件为Markdown并返回详细信息 */
  public PDFFormatInfo convertPdfToMarkdownWithInfo(File pdfFile) {
    return convertDocumentWithInfo(pdfFile, "markdown");
  }

  /** 根据文件获取MIME类型 */
  private String getMimeTypeFromFile(File file) {
    String filename = file.getName().toLowerCase();
    if (filename.endsWith(".pdf")) return "application/pdf";
    if (filename.endsWith(".docx"))
      return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    if (filename.endsWith(".xlsx"))
      return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    if (filename.endsWith(".html") || filename.endsWith(".htm")) return "text/html";
    if (filename.endsWith(".png")) return "image/png";
    if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")) return "image/jpeg";
    if (filename.endsWith(".gif")) return "image/gif";
    if (filename.endsWith(".bmp")) return "image/bmp";
    if (filename.endsWith(".tiff")) return "image/tiff";
    return "application/octet-stream"; // 默认类型
  }

  /** 根据输出格式获取内容 */
  private String getContentByFormat(JsonNode jsonResponse, String outputFormat) {
    switch (outputFormat.toLowerCase()) {
      case "html":
        return jsonResponse.has("html") ? jsonResponse.get("html").asText() : "";
      case "json":
        return jsonResponse.has("json")
            ? jsonResponse.get("json").toString()
            : jsonResponse.toString();
      case "markdown":
      default:
        return jsonResponse.has("markdown") ? jsonResponse.get("markdown").asText() : "";
    }
  }

  /** 批量转换PDF文件 */
  public java.util.List<PDFFormatInfo> batchConvertPdfs(java.util.List<File> pdfFiles) {
    return pdfFiles.stream().map(this::convertPdfToMarkdownWithInfo).toList();
  }

  /** 获取Docling服务信息 */
  public String getServiceInfo() {
    if (!isAvailable()) {
      return "Docling服务不可用";
    }

    try {
      Request request = new Request.Builder().url(doclingApiUrl + "/health").get().build();

      try (Response response = httpClient.newCall(request).execute()) {
        if (response.isSuccessful()) {
          ResponseBody body = response.body();
          if (body == null) {
            return "响应体为空";
          }
          return body.string();
        } else {
          return "服务不可用，HTTP状态: " + response.code();
        }
      }
    } catch (IOException | RuntimeException e) {
      return "获取服务信息失败: " + e.getMessage();
    }
  }
}
