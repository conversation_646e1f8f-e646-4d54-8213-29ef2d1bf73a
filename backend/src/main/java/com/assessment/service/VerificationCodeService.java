package com.assessment.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务
 * 支持邮箱验证码的生成、存储和验证
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-24
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VerificationCodeService {

    private final RedisTemplate<String, Object> redisTemplate;
    
    // 验证码配置
    private static final int CODE_LENGTH = 6;
    private static final long CODE_EXPIRE_MINUTES = 5;
    private static final String EMAIL_CODE_PREFIX = "email_verification_code:";
    private static final String PHONE_CODE_PREFIX = "phone_verification_code:";
    
    // 字符集：数字和字母组合
    private static final String CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * 生成邮箱验证码
     */
    public String generateEmailVerificationCode(String email) {
        String code = generateCode();
        String key = EMAIL_CODE_PREFIX + email.toLowerCase();
        
        // 存储到Redis，设置5分钟过期
        redisTemplate.opsForValue().set(key, code, Duration.ofMinutes(CODE_EXPIRE_MINUTES));
        
        log.info("邮箱验证码已生成: email={}, code={}", email, code);
        return code;
    }

    /**
     * 生成手机验证码
     */
    public String generatePhoneVerificationCode(String phone) {
        String code = generateCode();
        String key = PHONE_CODE_PREFIX + phone;
        
        // 存储到Redis，设置5分钟过期
        redisTemplate.opsForValue().set(key, code, Duration.ofMinutes(CODE_EXPIRE_MINUTES));
        
        log.info("手机验证码已生成: phone={}, code={}", phone, code);
        return code;
    }

    /**
     * 验证邮箱验证码
     */
    public boolean verifyEmailCode(String email, String inputCode) {
        if (email == null || inputCode == null) {
            return false;
        }
        
        String key = EMAIL_CODE_PREFIX + email.toLowerCase();
        String storedCode = (String) redisTemplate.opsForValue().get(key);
        
        if (storedCode == null) {
            log.warn("邮箱验证码不存在或已过期: email={}", email);
            return false;
        }
        
        boolean isValid = storedCode.equalsIgnoreCase(inputCode.trim());
        
        if (isValid) {
            // 验证成功后删除验证码
            redisTemplate.delete(key);
            log.info("邮箱验证码验证成功: email={}", email);
        } else {
            log.warn("邮箱验证码验证失败: email={}, expected={}, actual={}", 
                    email, storedCode, inputCode);
        }
        
        return isValid;
    }

    /**
     * 验证手机验证码
     */
    public boolean verifyPhoneCode(String phone, String inputCode) {
        if (phone == null || inputCode == null) {
            return false;
        }
        
        String key = PHONE_CODE_PREFIX + phone;
        String storedCode = (String) redisTemplate.opsForValue().get(key);
        
        if (storedCode == null) {
            log.warn("手机验证码不存在或已过期: phone={}", phone);
            return false;
        }
        
        boolean isValid = storedCode.equalsIgnoreCase(inputCode.trim());
        
        if (isValid) {
            // 验证成功后删除验证码
            redisTemplate.delete(key);
            log.info("手机验证码验证成功: phone={}", phone);
        } else {
            log.warn("手机验证码验证失败: phone={}, expected={}, actual={}", 
                    phone, storedCode, inputCode);
        }
        
        return isValid;
    }

    /**
     * 检查验证码是否存在（用于防止重复发送）
     */
    public boolean hasValidEmailCode(String email) {
        String key = EMAIL_CODE_PREFIX + email.toLowerCase();
        return redisTemplate.hasKey(key);
    }

    /**
     * 检查手机验证码是否存在
     */
    public boolean hasValidPhoneCode(String phone) {
        String key = PHONE_CODE_PREFIX + phone;
        return redisTemplate.hasKey(key);
    }

    /**
     * 获取验证码剩余有效时间（秒）
     */
    public long getEmailCodeRemainingTime(String email) {
        String key = EMAIL_CODE_PREFIX + email.toLowerCase();
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 获取手机验证码剩余有效时间（秒）
     */
    public long getPhoneCodeRemainingTime(String phone) {
        String key = PHONE_CODE_PREFIX + phone;
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 生成6位数字字母组合验证码
     */
    private String generateCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = RANDOM.nextInt(CHARACTERS.length());
            code.append(CHARACTERS.charAt(index));
        }
        
        return code.toString();
    }

    /**
     * 清除邮箱验证码（用于管理员操作）
     */
    public void clearEmailCode(String email) {
        String key = EMAIL_CODE_PREFIX + email.toLowerCase();
        redisTemplate.delete(key);
        log.info("邮箱验证码已清除: email={}", email);
    }

    /**
     * 清除手机验证码（用于管理员操作）
     */
    public void clearPhoneCode(String phone) {
        String key = PHONE_CODE_PREFIX + phone;
        redisTemplate.delete(key);
        log.info("手机验证码已清除: phone={}", phone);
    }
}