package com.assessment.service;

import com.assessment.dto.ExecuteDDLRequest;
import com.assessment.dto.ExecuteDDLResult;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** 数据库操作服务 */
@Service
@Slf4j
public class DatabaseService {

  private final JdbcTemplate jdbcTemplate;

  public DatabaseService(JdbcTemplate jdbcTemplate) {
    if (jdbcTemplate != null && jdbcTemplate.getDataSource() != null) {
      this.jdbcTemplate = new JdbcTemplate(jdbcTemplate.getDataSource());
    } else {
      throw new IllegalArgumentException("JdbcTemplate and its DataSource cannot be null");
    }
  }

  /** 执行DDL语句创建表 */
  @Transactional
  public ExecuteDDLResult executeDDL(ExecuteDDLRequest request) {
    long startTime = System.currentTimeMillis();
    List<String> errors = new ArrayList<>();
    List<String> warnings = new ArrayList<>();
    boolean overwrittenExisting = false;
    String backupTableName = null;

    try {
      log.info("开始执行DDL: 表名={}", request.getTableName());

      // 检查表是否存在
      boolean tableExists = tableExists(request.getTableName());

      if (tableExists) {
        if (request.getOverwriteExisting() != null && request.getOverwriteExisting()) {
          // 如果需要备份
          if (request.getOptions() != null && request.getOptions().isBackupExistingTable()) {
            backupTableName = createBackupTable(request.getTableName());
            warnings.add("已备份原表为: " + backupTableName);
          }

          // 删除已存在的表
          dropTable(request.getTableName());
          overwrittenExisting = true;
          warnings.add("已覆盖已存在的表: " + request.getTableName());
        } else {
          throw new RuntimeException("表 " + request.getTableName() + " 已存在，请选择覆盖或修改表名");
        }
      }

      // 执行DDL语句
      log.debug("执行SQL: {}", request.getSql());
      jdbcTemplate.execute(request.getSql());

      long executionTime = System.currentTimeMillis() - startTime;

      log.info("DDL执行成功: 表名={}, 耗时={}ms", request.getTableName(), executionTime);

      return ExecuteDDLResult.builder()
          .success(true)
          .message("表创建成功")
          .tableName(request.getTableName())
          .executedSql(request.getSql())
          .executionTimeMs(executionTime)
          .executionTime(LocalDateTime.now())
          .affectedRows(1)
          .errors(errors)
          .warnings(warnings)
          .overwrittenExistingTable(overwrittenExisting)
          .backupTableName(backupTableName)
          .build();

    } catch (Exception e) {
      long executionTime = System.currentTimeMillis() - startTime;
      errors.add(e.getMessage());

      log.error("DDL执行失败: 表名={}", request.getTableName(), e);

      return ExecuteDDLResult.builder()
          .success(false)
          .message("DDL执行失败: " + e.getMessage())
          .tableName(request.getTableName())
          .executedSql(request.getSql())
          .executionTimeMs(executionTime)
          .executionTime(LocalDateTime.now())
          .affectedRows(0)
          .errors(errors)
          .warnings(warnings)
          .overwrittenExistingTable(overwrittenExisting)
          .backupTableName(backupTableName)
          .build();
    }
  }

  /** 检查表是否存在 */
  public boolean tableExists(String tableName) {
    try {
      String sql =
          "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ? AND table_schema = CURRENT_SCHEMA()";
      Integer count =
          jdbcTemplate.queryForObject(sql, Integer.class, tableName.toLowerCase(Locale.ROOT));
      return count != null && count > 0;
    } catch (Exception e) {
      log.error("检查表存在性失败: {}", tableName, e);
      return false;
    }
  }

  /** 获取表结构信息 */
  public Map<String, Object> getTableStructure(String tableName) {
    try {
      Map<String, Object> result = new HashMap<>();

      // 获取表信息
      String tableInfoSql = "SELECT table_comment FROM information_schema.tables "
          + "WHERE table_name = ? AND table_schema = CURRENT_SCHEMA()";
      String tableComment =
          jdbcTemplate.queryForObject(
              tableInfoSql, String.class, tableName.toLowerCase(Locale.ROOT));
      result.put("tableName", tableName);
      result.put("tableComment", tableComment);

      // 获取列信息
      String columnsSql =
          """
                SELECT
                    column_name,
                    data_type,
                    character_maximum_length,
                    is_nullable,
                    column_default,
                    column_comment
                FROM information_schema.columns
                WHERE table_name = ? AND table_schema = CURRENT_SCHEMA()
                ORDER BY ordinal_position
                """;

      List<Map<String, Object>> columns =
          jdbcTemplate.queryForList(columnsSql, tableName.toLowerCase(Locale.ROOT));
      result.put("columns", columns);
      result.put("columnCount", columns.size());

      return result;

    } catch (Exception e) {
      log.error("获取表结构失败: {}", tableName, e);
      throw new RuntimeException("获取表结构失败: " + e.getMessage());
    }
  }

  /** 创建备份表 */
  private String createBackupTable(String tableName) {
    String backupTableName = tableName + "_backup_" + System.currentTimeMillis();
    try {
      String sql = "CREATE TABLE " + backupTableName + " AS SELECT * FROM " + tableName;
      jdbcTemplate.execute(sql);
      log.info("已创建备份表: {}", backupTableName);
      return backupTableName;
    } catch (Exception e) {
      log.error("创建备份表失败: {}", tableName, e);
      throw new RuntimeException("创建备份表失败: " + e.getMessage());
    }
  }

  /** 删除表 */
  private void dropTable(String tableName) {
    try {
      String sql = "DROP TABLE IF EXISTS " + tableName + " CASCADE";
      jdbcTemplate.execute(sql);
      log.info("已删除表: {}", tableName);
    } catch (Exception e) {
      log.error("删除表失败: {}", tableName, e);
      throw new RuntimeException("删除表失败: " + e.getMessage());
    }
  }
}
