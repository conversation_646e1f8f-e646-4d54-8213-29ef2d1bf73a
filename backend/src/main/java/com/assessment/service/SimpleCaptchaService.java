
package com.assessment.service;

import com.assessment.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.SecureRandom;
import java.time.Duration;
import java.util.*;
import java.util.List;

/**
 * AjCaptcha兼容的滑动验证码服务
 * 基于模板系统实现的专业滑动拼图验证码
 * 
 * <AUTHOR>
 * @version 2.0 (AjCaptcha Compatible)
 * @since 2025-06-24
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SimpleCaptchaService {
    
    static {
        // 设置Java AWT headless模式，允许在没有图形界面的环境中生成图片
        System.setProperty("java.awt.headless", "true");
    }

    private final RedisTemplate<String, Object> redisTemplate;
    
    // === AjCaptcha 标准配置 ===
    private static final String CAPTCHA_PREFIX = "simple_captcha:";
    private static final int CAPTCHA_WIDTH = 310;
    private static final int CAPTCHA_HEIGHT = 155;
    private static final int PIECE_WIDTH = 47;
    private static final int PIECE_HEIGHT = 155; // 模板高度与背景一致
    private static final int TOLERANCE = 15; // 允许的误差像素 (平衡用户体验与安全性)
    
    // === 时间配置常量 ===
    private static final int CAPTCHA_EXPIRE_MINUTES = 5; // 验证码过期时间（分钟）
    private static final long VERIFICATION_EXPIRE_MS = 600000L; // 验证过期时间（毫秒，10分钟）
    private static final long MILLIS_TO_MINUTES = 60000L; // 毫秒转分钟的除数
    
    // === 颜色配置常量 ===
    private static final int COLOR_R_240 = 240;
    private static final int COLOR_G_248 = 248;
    private static final int COLOR_B_230 = 230;
    private static final int COLOR_B_250 = 250;
    
    // === 透明度和图像处理常量 ===
    private static final float TRANSPARENCY_THRESHOLD = 0.95f; // 透明度阈值
    private static final int BLOCK_SIZE_8 = 8;
    private static final int NOISE_ITERATIONS_3 = 3;
    private static final int MAX_COLOR_VALUE = 256;
    private static final int TOLERANCE_5 = 5;
    private static final int NOISE_RANGE_30 = 30;
    private static final int VALIDATION_RETRY_THRESHOLD = 9;
    
    // === 扩展颜色和图像常量 ===
    private static final int COLOR_WHITE = 255;
    private static final int COLOR_ALPHA_60 = 60;
    private static final int COLOR_ALPHA_30 = 30;
    private static final int FONT_SIZE_8 = 8;
    private static final int MODULO_10000 = 10000;
    private static final int TIMESTAMP_OFFSET_X = 60;
    private static final int TIMESTAMP_OFFSET_Y = 8;
    
    // === 模板系统配置 ===
    @Value("${captcha.template.path:classpath*:templates/sliding-block/*.png}")
    private String templatePath;
    
    private final List<BufferedImage> puzzleTemplates = new ArrayList<>();
    private final SecureRandom random = new SecureRandom();
    
    /**
     * 启动时加载所有拼图模板到内存缓存
     */
    @PostConstruct
    public void initializePuzzleTemplates() {
        try {
            log.info("🔧 开始初始化AjCaptcha拼图模板系统...");
            
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources(templatePath);
            
            if (resources.length == 0) {
                log.warn("⚠️ 未找到拼图模板文件，路径: {}", templatePath);
                return;
            }
            
            int loadedCount = 0;
            for (Resource resource : resources) {
                try (InputStream inputStream = resource.getInputStream()) {
                    BufferedImage template = ImageIO.read(inputStream);
                    if (template != null) {
                        // 验证模板尺寸
                        if (template.getWidth() == PIECE_WIDTH && template.getHeight() == PIECE_HEIGHT) {
                            puzzleTemplates.add(template);
                            loadedCount++;
                            log.debug("✅ 加载模板: {}", resource.getFilename());
                        } else {
                            log.warn("⚠️ 模板尺寸不符合标准 {}: {}x{}, 期望: {}x{}", 
                                resource.getFilename(), template.getWidth(), template.getHeight(), 
                                PIECE_WIDTH, PIECE_HEIGHT);
                        }
                    }
                } catch (Exception e) {
                    log.error("❌ 加载模板失败: {}, 错误: {}", resource.getFilename(), e.getMessage());
                }
            }
            
            log.info("🎯 拼图模板系统初始化完成: 成功加载 {} 个模板", loadedCount);
            
            if (puzzleTemplates.isEmpty()) {
                log.error("❌ 致命错误: 没有可用的拼图模板！请检查模板文件");
            }
            
        } catch (Exception e) {
            log.error("❌ 初始化拼图模板系统失败", e);
        }
    }
    
    /**
     * 生成滑动验证码
     */
    public ApiResponse<Map<String, Object>> generateCaptcha() {
        try {
            log.info("开始生成验证码");
            String token = UUID.randomUUID().toString();
            
            // 生成随机滑块位置（AjCaptcha标准）
            int minX = 50; // 最小X坐标
            int maxX = CAPTCHA_WIDTH - PIECE_WIDTH; // 310 - 47 = 263 (最大X坐标)
            int x = random.nextInt(maxX - minX + 1) + minX; // [50, 263] 范围
            int y = 0; // 模板系统：拼图块高度与背景一致，固定Y坐标为0
            
            // 创建背景图
            BufferedImage backgroundImage = createBackgroundImage();
            
            // 使用模板系统创建滑块图片和背景缺口
            BufferedImage pieceImage = createPieceImageWithTemplate(backgroundImage, x, y);
            
            // 将图片转为Base64
            String backgroundBase64 = imageToBase64(backgroundImage);
            String pieceBase64 = imageToBase64(pieceImage);
            
            // 生成验证密钥
            String secretKey = generateSecretKey();
            
            // 存储验证信息到Redis
            Map<String, Object> captchaInfo = new HashMap<>();
            captchaInfo.put("x", x);
            captchaInfo.put("y", y);
            captchaInfo.put("secretKey", secretKey);
            captchaInfo.put("timestamp", System.currentTimeMillis());
            
            redisTemplate.opsForValue().set(
                CAPTCHA_PREFIX + token, 
                captchaInfo, 
                Duration.ofMinutes(CAPTCHA_EXPIRE_MINUTES)
            );
            
            // 返回验证码数据
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("originalImageBase64", backgroundBase64);
            result.put("jigsawImageBase64", pieceBase64);
            result.put("secretKey", secretKey);
            result.put("result", false);
            result.put("x", x); // 添加X坐标到响应中 (仅用于测试，生产环境不应暴露)
            result.put("y", y); // 添加Y坐标到响应中
            
            log.info("验证码生成成功，token: {}, 滑块位置: ({}, {})", token, x, y);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("生成验证码失败: {}", e.getMessage(), e);
            return ApiResponse.error("验证码生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 校验滑动验证码
     */
    public ApiResponse<Map<String, Object>> checkCaptcha(String token, String pointJson, String verification) {
        try {
            if (token == null || verification == null) {
                return ApiResponse.error("参数不完整");
            }
            
            // 从Redis获取验证信息
            Object captchaObj = redisTemplate.opsForValue().get(CAPTCHA_PREFIX + token);
            if (captchaObj == null) {
                return ApiResponse.error("验证码已过期或无效");
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> captchaInfo = (Map<String, Object>) captchaObj;
            
            // 验证密钥
            String storedSecretKey = (String) captchaInfo.get("secretKey");
            if (!verification.equals(storedSecretKey)) {
                return ApiResponse.error("验证密钥错误");
            }
            
            // 如果没有提供pointJson（登录场景），只验证token和verification的匹配性
            if (pointJson == null || pointJson.trim().isEmpty()) {
                log.info("🔑 登录场景验证: 检查验证码是否已通过滑动验证");
                
                // 检查验证码是否已经通过滑动验证
                Boolean verified = (Boolean) captchaInfo.get("verified");
                if (verified == null || !verified) {
                    log.warn("❌ 登录验证失败: 验证码尚未通过滑动验证, token: {}", token);
                    return ApiResponse.error("验证码尚未完成滑动验证");
                }
                
                // 检查验证时间（防止过期）
                Long verifiedAt = (Long) captchaInfo.get("verifiedAt");
                if (verifiedAt != null) {
                    long timeDiff = System.currentTimeMillis() - verifiedAt;
                    if (timeDiff > VERIFICATION_EXPIRE_MS) { // 10分钟过期
                        log.warn("❌ 登录验证失败: 验证码已过期, token: {}, 验证时间: {}分钟前", 
                            token, timeDiff / MILLIS_TO_MINUTES);
                        redisTemplate.delete(CAPTCHA_PREFIX + token);
                        return ApiResponse.error("验证码已过期，请重新验证");
                    }
                }
                
                Map<String, Object> result = new HashMap<>();
                result.put("result", true);
                result.put("loginMode", true); // 标识这是登录模式验证
                
                // 登录验证成功，删除Redis中的数据（防止重复使用）
                redisTemplate.delete(CAPTCHA_PREFIX + token);
                log.info("✅ 登录验证码校验成功，token: {}", token);
                return ApiResponse.success(result, "");
            }
            
            // 标准滑动验证码场景：解析滑动位置
            String cleanPointJson = pointJson.replace("{", "").replace("}", "").replace("\"", "");
            String[] pairs = cleanPointJson.split(",");
            int userX = 0;
            
            for (String pair : pairs) {
                String[] kv = pair.split(":");
                if (kv.length == 2 && "x".equals(kv[0].trim())) {
                    userX = Integer.parseInt(kv[1].trim());
                    break;
                }
            }
            
            // 获取正确位置
            int correctX = (Integer) captchaInfo.get("x");
            
            // 验证位置是否正确（允许一定误差）
            int diff = Math.abs(userX - correctX);
            boolean isValid = diff <= TOLERANCE;
            
            // 详细调试日志
            log.info("🔍 验证码校验详情 - Token: {}", token);
            log.info("📊 位置对比: 用户X={}, 正确X={}, 差值={}, 容差={}, 结果={}", 
                userX, correctX, diff, TOLERANCE, isValid ? "通过" : "失败");
            log.info("🔑 验证数据: pointJson={}, verification={}", pointJson, verification);
            
            Map<String, Object> result = new HashMap<>();
            result.put("result", isValid);
            result.put("userX", userX);
            result.put("correctX", correctX);
            result.put("diff", diff);
            result.put("tolerance", TOLERANCE);
            
            if (isValid) {
                // 验证成功，标记为已验证状态，但不删除（登录时需要再次验证）
                captchaInfo.put("verified", true);
                captchaInfo.put("verifiedAt", System.currentTimeMillis());
                redisTemplate.opsForValue().set(
                    CAPTCHA_PREFIX + token, 
                    captchaInfo, 
                    Duration.ofMinutes(10) // 延长有效期，为登录验证预留时间
                );
                log.info("✅ 验证码校验成功，token: {}, 用户位置: {}, 正确位置: {}", token, userX, correctX);
                // 验证成功时不返回message，让前端显示纯图标
                return ApiResponse.success(result, "");
            } else {
                log.warn("❌ 验证码校验失败，token: {}, 用户位置: {}, 正确位置: {}, 差值: {}", token, userX, correctX, diff);
                String errorMessage = "验证失败，请重试";
                result.put("message", errorMessage);
                return ApiResponse.success(result, errorMessage);
            }
            
        } catch (Exception e) {
            log.error("校验验证码失败", e);
            return ApiResponse.error("验证码校验失败");
        }
    }
    
    /**
     * 创建背景图片 - 使用AjCaptcha专业背景图片
     */
    private BufferedImage createBackgroundImage() {
        try {
            // 随机选择一张背景图片（支持多张图片轮询）
            String[] backgroundFiles = {
                "bg_chinese.png",
                "1.png", 
                "2.png",
                "3.png", 
                "4.png",
                "5.png",
                "6.png"
            };
            String selectedBg = backgroundFiles[random.nextInt(backgroundFiles.length)];
            log.info("🎨 随机选择背景图片: {}", selectedBg);
            
            // 从classpath加载背景图片
            try (var inputStream = getClass().getClassLoader().getResourceAsStream("images/" + selectedBg)) {
                if (inputStream != null) {
                    BufferedImage originalBg = ImageIO.read(inputStream);
                    
                    // 调整到目标尺寸
                    BufferedImage resizedBg = new BufferedImage(CAPTCHA_WIDTH, CAPTCHA_HEIGHT,
                        BufferedImage.TYPE_INT_RGB);
                    Graphics2D g = resizedBg.createGraphics();
                    g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, 
                        RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                    g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                    
                    // 缩放并居中绘制背景图片
                    g.drawImage(originalBg, 0, 0, CAPTCHA_WIDTH, CAPTCHA_HEIGHT, null);
                    
                    // 添加轻微的图片优化效果
                    addImageEnhancement(resizedBg);

                    // 添加随机干扰增强安全性
                    addRandomInterference(resizedBg);

                    g.dispose();
                    log.info("✅ 成功加载背景图片: {} ({}x{})", selectedBg, CAPTCHA_WIDTH, CAPTCHA_HEIGHT);
                    return resizedBg;
                }
            }
        } catch (Exception e) {
            log.warn("⚠️ 加载背景图片失败，使用默认背景: {}", e.getMessage());
        }
        
        // 如果加载失败，使用默认的彩色背景
        return createDefaultBackgroundImage();
    }
    
    /**
     * 创建默认背景图片（备用方案）
     */
    private BufferedImage createDefaultBackgroundImage() {
        BufferedImage image = new BufferedImage(CAPTCHA_WIDTH, CAPTCHA_HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 高质量渲染设置
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        
        // 创建专业的渐变背景
        GradientPaint gradient = new GradientPaint(
            0, 0, new Color(COLOR_R_240, COLOR_G_248, 255), // 淡蓝色
            CAPTCHA_WIDTH, CAPTCHA_HEIGHT, new Color(COLOR_B_230, COLOR_B_230, COLOR_B_250) // 淡紫色
        );
        g.setPaint(gradient);
        g.fillRect(0, 0, CAPTCHA_WIDTH, CAPTCHA_HEIGHT);
        
        // 添加轻微的纹理
        addDefaultTexturePattern(g);

        g.dispose();

        // 添加随机干扰增强安全性
        addRandomInterference(image);

        return image;
    }
    
    /**
     * 添加图片增强效果
     */
    private void addImageEnhancement(BufferedImage image) {
        Graphics2D g = image.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 添加轻微的亮度调整
        g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, TRANSPARENCY_THRESHOLD));
        
        // 添加时间戳确保图片唯一性
        g.setColor(new Color(COLOR_WHITE, COLOR_WHITE, COLOR_WHITE, COLOR_ALPHA_60));
        g.setFont(new Font("Arial", Font.PLAIN, FONT_SIZE_8));
        String timestamp = String.valueOf(System.currentTimeMillis() % MODULO_10000);
        g.drawString("AJ:" + timestamp, CAPTCHA_WIDTH - TIMESTAMP_OFFSET_X, CAPTCHA_HEIGHT - TIMESTAMP_OFFSET_Y);
        
        g.dispose();
    }
    
    /**
     * 添加默认纹理图案
     */
    private void addDefaultTexturePattern(Graphics2D g) {
        g.setColor(new Color(COLOR_WHITE, COLOR_WHITE, COLOR_WHITE, COLOR_ALPHA_30));
        g.setStroke(new BasicStroke(TRANSPARENCY_THRESHOLD / 2));
        
        // 添加对角线纹理
        for (int i = 0; i < CAPTCHA_WIDTH + CAPTCHA_HEIGHT; i += 20) {
            g.drawLine(i, 0, i - CAPTCHA_HEIGHT, CAPTCHA_HEIGHT);
        }
    }
    
    /**
     * 基于AjCaptcha模板系统创建拼图块和背景缺口
     * 采用官方模板切图算法，确保专业视觉效果
     */
    private BufferedImage createPieceImageWithTemplate(BufferedImage backgroundImage, int x, int y) {
        if (puzzleTemplates.isEmpty()) {
            log.error("💥 严重错误：没有可用的拼图模板！");
            throw new RuntimeException("拼图模板未加载，无法生成验证码");
        }
        
        // 随机选择一个拼图模板
        BufferedImage template = puzzleTemplates.get(random.nextInt(puzzleTemplates.size()));
        log.debug("🎯 选择模板: {}x{}", template.getWidth(), template.getHeight());
        
        // 使用AjCaptcha标准切图算法
        return cutByTemplate(backgroundImage, template, x, y);
    }
    
    /**
     * AjCaptcha标准模板切图算法
     * 参考官方BlockPuzzleCaptchaServiceImpl.cutByTemplate()实现
     * 
     * @param originalImage 原始背景图
     * @param templateImage 拼图模板
     * @param x 切图X坐标
     * @param y 切图Y坐标（固定为0，模板高度与背景一致）
     * @return 切出的拼图块图像
     */
    private BufferedImage cutByTemplate(BufferedImage originalImage, BufferedImage templateImage, int x, int y) {
        try {
            int templateWidth = templateImage.getWidth();
            int templateHeight = templateImage.getHeight();
            
            // 创建新的拼图图像（透明背景）
            BufferedImage newJigsawImage = new BufferedImage(templateWidth, templateHeight, 
                BufferedImage.TYPE_INT_ARGB);
            Graphics2D graphics = newJigsawImage.createGraphics();
            
            // 配置高质量渲染
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            
            // 设置透明背景
            graphics.setComposite(AlphaComposite.getInstance(AlphaComposite.CLEAR));
            graphics.fillRect(0, 0, templateWidth, templateHeight);
            graphics.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
            
            // === 核心切图逻辑：按模板像素逐一处理 ===
            // 临时数组遍历用于高斯模糊存周边像素值
            int[][] matrix = new int[NOISE_ITERATIONS_3][NOISE_ITERATIONS_3];
            int[] values = new int[VALIDATION_RETRY_THRESHOLD];
            
            // 遍历模板的每个像素
            for (int i = 0; i < templateWidth; i++) {
                for (int j = 0; j < templateHeight; j++) {
                    // 获取模板当前像素的RGB值
                    int rgb = templateImage.getRGB(i, j);
                    
                    // AjCaptcha标准：如果模板像素不是透明色(rgb < 0)，则复制背景像素
                    if (rgb < 0) {
                        // 将背景图对应位置的像素复制到拼图块
                        newJigsawImage.setRGB(i, j, originalImage.getRGB(x + i, y + j));
                        
                        // 对原图的抠图区域应用高斯模糊效果
                        readPixel(originalImage, x + i, y + j, values);
                        fillMatrix(matrix, values);
                        originalImage.setRGB(x + i, y + j, avgMatrix(matrix));
                    }
                    
                    // 防止数组越界判断
                    if (i == (templateWidth - 1) || j == (templateHeight - 1)) {
                        continue;
                    }
                    
                    // 边缘检测和描边处理
                    int rightRgb = templateImage.getRGB(i + 1, j);
                    int downRgb = templateImage.getRGB(i, j + 1);
                    
                    // 判断是否为轮廓边界点，如果是则设置为白色边框
                    if ((rgb >= 0 && rightRgb < 0) || (rgb < 0 && rightRgb >= 0)
                        || (rgb >= 0 && downRgb < 0) || (rgb < 0 && downRgb >= 0)) {
                        newJigsawImage.setRGB(i, j, Color.WHITE.getRGB());
                        originalImage.setRGB(x + i, y + j, Color.WHITE.getRGB());
                    }
                }
            }
            
            graphics.dispose();
            
            log.debug("✅ 模板切图完成: 位置({}, {}), 尺寸: {}x{}", x, y, templateWidth, templateHeight);
            return newJigsawImage;
            
        } catch (Exception e) {
            log.error("❌ 模板切图失败", e);
            throw new RuntimeException("拼图生成失败: " + e.getMessage());
        }
    }
    
    // === AjCaptcha标准高斯模糊算法 ===
    
    /**
     * 读取指定像素周围3x3区域的像素值
     * AjCaptcha标准算法
     */
    private static void readPixel(BufferedImage img, int x, int y, int[] pixels) {
        int xStart = x - 1;
        int yStart = y - 1;
        int current = 0;
        for (int i = xStart; i < NOISE_ITERATIONS_3 + xStart; i++) {
            for (int j = yStart; j < NOISE_ITERATIONS_3 + yStart; j++) {
                int tx = i;
                if (tx < 0) {
                    tx = -tx;
                } else if (tx >= img.getWidth()) {
                    tx = x;
                }
                int ty = j;
                if (ty < 0) {
                    ty = -ty;
                } else if (ty >= img.getHeight()) {
                    ty = y;
                }
                pixels[current++] = img.getRGB(tx, ty);
            }
        }
    }
    
    /**
     * 将像素值数组填充到3x3矩阵
     */
    private static void fillMatrix(int[][] matrix, int[] values) {
        int filled = 0;
        for (int i = 0; i < matrix.length; i++) {
            int[] x = matrix[i];
            for (int j = 0; j < x.length; j++) {
                x[j] = values[filled++];
            }
        }
    }
    
    /**
     * 计算3x3矩阵的平均颜色值（高斯模糊效果）
     */
    private static int avgMatrix(int[][] matrix) {
        int r = 0;
        int g = 0;
        int b = 0;
        for (int i = 0; i < matrix.length; i++) {
            int[] x = matrix[i];
            for (int j = 0; j < x.length; j++) {
                if (j == 1) { // 跳过中心像素
                    continue;
                }
                Color c = new Color(x[j]);
                r += c.getRed();
                g += c.getGreen();
                b += c.getBlue();
            }
        }
        return new Color(r / BLOCK_SIZE_8, g / BLOCK_SIZE_8, b / BLOCK_SIZE_8).getRGB();
    }
    
    // 注意：以下旧的程序生成方法已被模板系统取代，不再需要
    // - createPuzzleShapePath(): 用模板取代程序生成的形状
    // - applyGaussianBlur(): 模板切图算法已包含高斯模糊效果
    
    /**
     * 增强随机干扰点生成 - 参考AjCaptcha防机器学习
     */
    private void addRandomInterference(BufferedImage image) {
        Graphics2D g = image.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 添加随机噪点
        for (int i = 0; i < 20; i++) {
            int x = random.nextInt(image.getWidth());
            int y = random.nextInt(image.getHeight());
            g.setColor(new Color(
                random.nextInt(MAX_COLOR_VALUE), 
                random.nextInt(MAX_COLOR_VALUE), 
                random.nextInt(MAX_COLOR_VALUE), 
                50 + random.nextInt(100)
            ));
            g.fillOval(x, y, 2 + random.nextInt(NOISE_ITERATIONS_3), 2 + random.nextInt(NOISE_ITERATIONS_3));
        }
        
        // 添加随机线条
        for (int i = 0; i < TOLERANCE_5; i++) {
            g.setColor(new Color(COLOR_WHITE, COLOR_WHITE, COLOR_WHITE, NOISE_RANGE_30 + random.nextInt(50)));
            g.setStroke(new BasicStroke(1.0f + random.nextFloat()));
            g.drawLine(
                random.nextInt(image.getWidth()), 
                random.nextInt(image.getHeight()),
                random.nextInt(image.getWidth()), 
                random.nextInt(image.getHeight())
            );
        }
        
        g.dispose();
    }
    
    /**
     * 图片转Base64
     */
    private String imageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        byte[] imageBytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);
    }
    
    /**
     * 生成验证密钥
     */
    private String generateSecretKey() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}