package com.assessment.service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.assessment.config.LMStudioConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/** LM Studio服务管理类 负责LM Studio服务的连接管理、模型切换、健康检查等 */
@Service
@Slf4j
public class LMStudioService {

  private final LMStudioConfig lmStudioConfig;
  private final RestTemplate restTemplate;
  private final ObjectMapper objectMapper = new ObjectMapper();
  private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

  public LMStudioService(LMStudioConfig lmStudioConfig, RestTemplate restTemplate) {
    this.lmStudioConfig = new LMStudioConfig(lmStudioConfig);
    this.restTemplate = restTemplate;
  }

  /** 当前活跃的服务URL */
  private volatile String currentServerUrl;

  /** 当前活跃的模型信息 */
  private volatile LMStudioConfig.ModelInfo currentModel;

  /** 可用模型列表缓存 */
  private final Map<String, List<LMStudioConfig.ModelInfo>> availableModelsCache =
      new ConcurrentHashMap<>();

  /** 服务健康状态缓存 */
  private final Map<String, Boolean> serverHealthCache = new ConcurrentHashMap<>();

  /**
   * 初始化LM Studio服务管理器
   * 设置服务URL并进行健康检查
   */
  @PostConstruct
  public void initialize() {
    log.info("初始化LM Studio服务管理器");

    // 初始化当前服务URL - 强制使用正确的IP
    currentServerUrl = "http://*************:1234";
    log.info("强制设置LM Studio服务地址为: {}", currentServerUrl);

    // 检查服务可用性并选择最佳模型
    initializeBestServer();

    // 启动定期健康检查
    startHealthCheck();

    log.info(
        "LM Studio服务管理器初始化完成 - 当前服务: {}, 当前模型: {}",
        getCurrentServerUrl(),
        getCurrentModel() != null ? getCurrentModel().getDisplayName() : "未选择");
  }

  /** 获取当前服务地址 */
  public String getCurrentServerUrl() {
    return currentServerUrl;
  }

  /** 获取当前模型信息 */
  public LMStudioConfig.ModelInfo getCurrentModel() {
    return this.currentModel == null ? null : new LMStudioConfig.ModelInfo(this.currentModel);
  }

  /** 获取当前模型的详细信息（包含服务器信息） */
  public Map<String, Object> getCurrentModelInfo() {
    Map<String, Object> info = new HashMap<>();

    if (currentModel != null) {
      info.put("id", currentModel.getId());
      info.put("displayName", currentModel.getDisplayName());
      info.put("description", currentModel.getDescription());
      info.put("priority", currentModel.getPriority());
      info.put("capabilities", currentModel.getCapabilities());
    }

    info.put("serverUrl", currentServerUrl);
    info.put("serverHealth", isServerHealthy(currentServerUrl));
    info.put("availableModels", getAvailableModels());
    info.put("lastUpdated", System.currentTimeMillis());

    return info;
  }

  /** 检查服务是否可用 */
  public boolean isServiceAvailable() {
    return isServerHealthy(currentServerUrl) && currentModel != null;
  }

  /** 获取可用模型列表 */
  public List<LMStudioConfig.ModelInfo> getAvailableModels() {
    String cacheKey = currentServerUrl;
    List<LMStudioConfig.ModelInfo> cached = availableModelsCache.get(cacheKey);

    if (cached != null && !cached.isEmpty()) {
      return cached;
    }

    return refreshAvailableModels();
  }

  /** 刷新可用模型列表 */
  public List<LMStudioConfig.ModelInfo> refreshAvailableModels() {
    List<LMStudioConfig.ModelInfo> models = new ArrayList<>();

    try {
      String url = currentServerUrl + "/v1/models";

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      HttpEntity<String> entity = new HttpEntity<>(headers);

      ResponseEntity<String> response =
          restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

      if (response == null) {
        log.error("RestTemplate.exchange 返回了 null，URL: {}", url);
        return models;
      }

      if (response.getStatusCode() == HttpStatus.OK) {
        JsonNode responseJson = objectMapper.readTree(response.getBody());
        JsonNode data = responseJson.get("data");

        if (data != null && data.isArray()) {
          for (JsonNode modelNode : data) {
            String modelId = modelNode.get("id").asText();

            // 跳过排除的模型
            if (lmStudioConfig.isModelExcluded(modelId)) {
              log.debug("跳过排除的模型: {}", modelId);
              continue;
            }

            // 动态创建模型信息
            int priority = lmStudioConfig.getModelPriority(modelId);
            List<String> capabilities = lmStudioConfig.inferModelCapabilities(modelId);
            String displayName = lmStudioConfig.generateDisplayName(modelId);

            LMStudioConfig.ModelInfo modelInfo =
                new LMStudioConfig.ModelInfo(
                    modelId,
                    displayName,
                    generateModelDescription(modelId, capabilities),
                    priority,
                    capabilities);

            models.add(modelInfo);
            log.debug("发现模型: {} (优先级: {}, 能力: {})", displayName, priority, capabilities);
          }
        }
      }

      // 按优先级排序
      models.sort(Comparator.comparing(LMStudioConfig.ModelInfo::getPriority));

      // 缓存结果
      availableModelsCache.put(currentServerUrl, models);

      log.info("刷新模型列表成功，发现 {} 个可用模型", models.size());

    } catch (RestClientException e) {
      log.error("刷新模型列表失败 - 网络请求错误: {}", e.getMessage());
    } catch (JsonProcessingException e) {
      log.error("刷新模型列表失败 - JSON解析错误: {}", e.getMessage());
    } catch (RuntimeException e) {
      log.error("刷新模型列表失败 - 运行时错误: {}", e.getMessage());
    }

    return models;
  }

  /** 切换到指定模型 */
  public boolean switchToModel(String modelId) {
    List<LMStudioConfig.ModelInfo> availableModels = getAvailableModels();

    LMStudioConfig.ModelInfo targetModel =
        availableModels.stream().filter(m -> m.getId().equals(modelId)).findFirst().orElse(null);

    if (targetModel != null) {
      currentModel = targetModel;
      log.info("已切换到模型: {} ({})", targetModel.getDisplayName(), targetModel.getId());
      return true;
    }

    log.warn("未找到指定模型: {}", modelId);
    return false;
  }

  /** 切换服务器 */
  public boolean switchToServer(String serverUrl) {
    if (isServerHealthy(serverUrl)) {
      currentServerUrl = serverUrl;
      availableModelsCache.clear(); // 清除模型缓存
      initializeBestModel(); // 重新选择最佳模型
      log.info("已切换到服务器: {}", serverUrl);
      return true;
    }

    log.warn("无法切换到不健康的服务器: {}", serverUrl);
    return false;
  }

  /** 自动切换到最佳可用服务器 */
  public boolean autoSwitchToBestServer() {
    List<String> allServers = new ArrayList<>();
    allServers.add(lmStudioConfig.getServer().getPrimaryUrl());
    allServers.addAll(lmStudioConfig.getServer().getBackupUrls());

    for (String serverUrl : allServers) {
      if (isServerHealthy(serverUrl)) {
        return switchToServer(serverUrl);
      }
    }

    log.error("没有找到健康的LM Studio服务器");
    return false;
  }

  /** 检查服务器健康状态 */
  private boolean isServerHealthy(String serverUrl) {
    Boolean cached = serverHealthCache.get(serverUrl);
    if (cached != null) {
      return cached;
    }

    try {
      String url = serverUrl + "/v1/models";
      ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
      boolean healthy = response.getStatusCode() == HttpStatus.OK;
      serverHealthCache.put(serverUrl, healthy);
      return healthy;
    } catch (RestClientException e) {
      log.debug("服务器健康检查失败 - {}: {}", serverUrl, e.getMessage());
      serverHealthCache.put(serverUrl, false);
      return false;
    } catch (RuntimeException e) {
      log.debug("服务器健康检查异常 - {}: {}", serverUrl, e.getMessage());
      serverHealthCache.put(serverUrl, false);
      return false;
    }
  }

  /** 初始化最佳服务器 */
  private void initializeBestServer() {
    if (!autoSwitchToBestServer()) {
      log.warn("使用默认服务器配置: {}", currentServerUrl);
    }
    initializeBestModel();
  }

  /** 初始化最佳模型 */
  private void initializeBestModel() {
    List<LMStudioConfig.ModelInfo> availableModels = refreshAvailableModels();

    if (!availableModels.isEmpty()) {
      // 选择优先级最高的模型
      LMStudioConfig.ModelInfo selectedModel =
          availableModels.stream()
              .min(Comparator.comparing(LMStudioConfig.ModelInfo::getPriority))
              .orElse(availableModels.get(0));

      currentModel = selectedModel;
      log.info("已选择模型: {} (优先级: {})", selectedModel.getDisplayName(), selectedModel.getPriority());
    } else {
      log.warn("没有发现可用的模型");
    }
  }

  /** 启动健康检查 */
  private void startHealthCheck() {
    int interval = lmStudioConfig.getServer().getHealthCheckInterval();

    scheduler.scheduleAtFixedRate(
        () -> {
          try {
            // 清除健康状态缓存
            serverHealthCache.clear();

            // 检查当前服务器健康状态
            if (!isServerHealthy(currentServerUrl)
                && lmStudioConfig.getAutoSwitch().getServerFallbackEnabled()) {
              log.warn("当前服务器不健康，尝试自动切换");
              autoSwitchToBestServer();
            }

            // 刷新模型列表
            if (isServerHealthy(currentServerUrl)) {
              refreshAvailableModels();
            }

          } catch (Exception e) {
            log.error("健康检查异常: {}", e.getMessage());
          }
        },
        interval,
        interval,
        TimeUnit.SECONDS);
  }

  /** 生成模型描述 */
  private String generateModelDescription(String modelId, List<String> capabilities) {
    StringBuilder description = new StringBuilder();

    if (capabilities.contains("code")) {
      description.append("代码生成和分析");
    } else if (capabilities.contains("reasoning")) {
      description.append("推理和逻辑分析");
    } else if (capabilities.contains("chinese")) {
      description.append("中文对话优化");
    } else {
      description.append("通用对话模型");
    }

    // 根据模型名称添加特色描述
    String lowerModelId = modelId.toLowerCase();
    if (lowerModelId.contains("r1")) {
      description.append("，具备强化学习能力");
    } else if (lowerModelId.contains("coder")) {
      description.append("，专业编程助手");
    } else if (lowerModelId.matches(".*[0-9]+b.*")) {
      description.append("，大参数量模型");
    }

    return description.toString();
  }

  // API配置相关方法已移除，所有模型参数由LM Studio管理
}
