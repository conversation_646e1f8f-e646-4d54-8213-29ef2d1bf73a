package com.assessment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/** AI分析服务配置 */
@Configuration
@ConfigurationProperties(prefix = "ai")
public class AIProperties {

  /** LM Studio配置 */
  private LmStudio lmstudio = new LmStudio();

  /** AI分析配置 */
  private Analysis analysis = new Analysis();

  public LmStudio getLmstudio() {
    return lmstudio == null ? null : new LmStudio(this.lmstudio);
  }

  public void setLmstudio(final LmStudio lmstudio) {
    this.lmstudio = lmstudio == null ? null : new LmStudio(lmstudio);
  }

  public Analysis getAnalysis() {
    return analysis == null ? null : new Analysis(this.analysis);
  }

  public void setAnalysis(final Analysis analysis) {
    this.analysis = analysis == null ? null : new Analysis(analysis);
  }

  @Data
  public static class LmStudio {
    /** LM Studio服务URL */
    private String url = "http://*************:1234";

    /** 模型名称 */
    private String model = "deepseek/deepseek-r1-0528-qwen3-8b";

    public LmStudio() { }

    public LmStudio(final LmStudio other) {
      if (other != null) {
        this.url = other.url;
        this.model = other.model;
      }
    }
  }

  @Data
  public static class Analysis {
    /** 分析超时时间（毫秒） */
    private Integer timeout = 60000;

    /** 最大内容长度 */
    private Integer maxContentLength = 100000;

    /** 是否启用AI分析 */
    private Boolean enabled = true;

    public Analysis() { }

    public Analysis(final Analysis other) {
      if (other != null) {
        this.timeout = other.timeout;
        this.maxContentLength = other.maxContentLength;
        this.enabled = other.enabled;
      }
    }
  }
}
