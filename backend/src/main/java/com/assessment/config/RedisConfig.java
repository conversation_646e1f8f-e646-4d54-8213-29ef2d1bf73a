package com.assessment.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import lombok.extern.slf4j.Slf4j;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis缓存配置
 * 支持多种缓存策略和超大规模数据缓存
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2025-06-25
 */
@Configuration
@EnableCaching
@Slf4j
public class RedisConfig {

    /**
     * 标准RedisTemplate配置
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(final RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 配置ObjectMapper
        ObjectMapper objectMapper = createObjectMapper();
        
        // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer =
            new Jackson2JsonRedisSerializer<>(objectMapper, Object.class);

        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        
        // 开启事务支持
        template.setEnableTransactionSupport(true);
        template.afterPropertiesSet();

        log.info("✅ RedisTemplate配置完成");
        return template;
    }

    /**
     * StringRedisTemplate配置
     * 用于简单的字符串缓存和计数器
     */
    @Bean
    public StringRedisTemplate stringRedisTemplate(final RedisConnectionFactory connectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(connectionFactory);
        template.setEnableTransactionSupport(true);
        
        log.info("✅ StringRedisTemplate配置完成");
        return template;
    }

    /**
     * 缓存管理器配置
     * 支持多级缓存和不同的过期策略
     */
    @Bean
    @Primary
    public CacheManager cacheManager(final RedisConnectionFactory factory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = createCacheConfiguration(Duration.ofMinutes(30));
        
        // 特定缓存配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 租户信息缓存 - 24小时过期
        cacheConfigurations.put("tenants", createCacheConfiguration(Duration.ofHours(24)));
        
        // 搜索结果缓存 - 30分钟过期
        cacheConfigurations.put("tenant-search", createCacheConfiguration(Duration.ofMinutes(30)));
        
        // 热门租户缓存 - 1小时过期
        cacheConfigurations.put("tenant-popular", createCacheConfiguration(Duration.ofHours(1)));
        
        // 统计数据缓存 - 10分钟过期
        cacheConfigurations.put("tenant-stats", createCacheConfiguration(Duration.ofMinutes(10)));
        
        // 快速验证缓存 - 5分钟过期
        cacheConfigurations.put("tenant-validation", createCacheConfiguration(Duration.ofMinutes(5)));

        CacheManager cacheManager = RedisCacheManager.builder(factory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .transactionAware() // 支持事务
            .build();
        
        log.info("✅ Redis缓存管理器配置完成，支持 {} 种缓存策略", cacheConfigurations.size() + 1);
        return cacheManager;
    }

    /**
     * ObjectMapper配置
     */
    @Bean
    public ObjectMapper redisObjectMapper() {
        return createObjectMapper();
    }

    // ========== 私有辅助方法 ==========

    /**
     * 创建缓存配置
     */
    private RedisCacheConfiguration createCacheConfiguration(final Duration ttl) {
        ObjectMapper objectMapper = createObjectMapper();
        
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(ttl)
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(
                new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(
                new Jackson2JsonRedisSerializer<>(objectMapper, Object.class)))
            .disableCachingNullValues() // 不缓存null值
            .computePrefixWith(cacheName -> "assessment:cache:" + cacheName + ":"); // 统一缓存键前缀
    }

    /**
     * 创建ObjectMapper
     */
    private ObjectMapper createObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.registerModule(new JavaTimeModule()); // 支持Java 8时间类型
        // 注意：不使用 activateDefaultTyping 避免类型信息污染API响应
        return objectMapper;
    }
}