package com.assessment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/** Docling PDF转换服务配置 */
@Configuration
@ConfigurationProperties(prefix = "docling")
public class DoclingProperties {

  /** Docling服务配置 */
  private Service service = new Service();

  public Service getService() {
    return this.service == null ? null : new Service(this.service);
  }

  public void setService(final Service service) {
    this.service = service == null ? null : new Service(service);
  }

  @Data
  public static class Service {
    /** Docling服务URL */
    private String url = "http://localhost:8088";

    /** 请求超时时间（秒） */
    private Integer timeout = 60;

    /** 是否启用Docling服务 */
    private Boolean enabled = true;

    public Service() { }

    public Service(final Service other) {
      if (other != null) {
        this.url = other.url;
        this.timeout = other.timeout;
        this.enabled = other.enabled;
      }
    }
  }
}
