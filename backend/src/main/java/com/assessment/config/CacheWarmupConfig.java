package com.assessment.config;

import com.assessment.service.TenantCacheService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * 缓存预热配置
 * 应用启动时自动预热关键缓存数据
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class CacheWarmupConfig {

    private final TenantCacheService tenantCacheService;

    /**
     * 缓存预热命令行运行器
     * 在应用启动完成后执行缓存预热
     */
    @Bean
    @Order(100) // 确保在其他初始化之后执行
    public CommandLineRunner cacheWarmupRunner() {
        return args -> {
            try {
                log.info("🚀 开始应用启动缓存预热...");
                
                // 异步执行缓存预热，避免阻塞应用启动
                new Thread(() -> {
                    try {
                        // 等待应用完全启动
                        Thread.sleep(5000);
                        
                        log.info("🔥 执行租户缓存预热...");
                        tenantCacheService.warmUpCache();
                        
                        log.info("✅ 应用启动缓存预热完成");
                    } catch (Exception e) {
                        log.error("❌ 应用启动缓存预热失败", e);
                    }
                }, "cache-warmup-thread").start();
                
            } catch (Exception e) {
                log.error("❌ 启动缓存预热线程失败", e);
            }
        };
    }
}