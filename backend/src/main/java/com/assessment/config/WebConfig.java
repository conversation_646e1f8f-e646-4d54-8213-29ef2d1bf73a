package com.assessment.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Web相关配置类
 * 提供RestTemplate等Web客户端组件配置
 */
@Configuration
public class WebConfig {

    /**
     * 配置RestTemplate Bean
     * 用于LM Studio服务等外部API调用
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}