package com.assessment;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ConfigurationPropertiesScan("com.assessment.config")
@EnableAsync
@EnableScheduling
public class AssessmentApplication {
  public static void main(final String[] args) {
    SpringApplication.run(AssessmentApplication.class, args);
  }
}
