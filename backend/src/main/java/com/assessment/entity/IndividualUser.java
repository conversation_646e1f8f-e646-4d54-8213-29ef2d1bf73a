package com.assessment.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 个人用户实体
 * 独立于机构用户体系，支持B2C业务模式
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@Entity
@Table(name = "individual_users")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualUser {

    // === 业务常量 ===
    private static final int FREE_USER_MONTHLY_LIMIT = 5;

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    /**
     * 邮箱地址（主要登录标识）
     */
    @Column(unique = true, nullable = false, length = 255)
    private String email;

    /**
     * 手机号（备用登录方式，预留）
     */
    @Column(unique = true, length = 20)
    private String phone;

    /**
     * 用户真实姓名
     */
    @Column(nullable = false, length = 50)
    private String realName;

    /**
     * 显示名称（可以是昵称）
     */
    @Column(length = 50)
    private String displayName;

    /**
     * 密码hash
     */
    @Column(nullable = false, length = 255)
    private String passwordHash;

    /**
     * 账户状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private AccountStatus status = AccountStatus.ACTIVE;

    /**
     * 服务套餐类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private ServiceType serviceType = ServiceType.FREE;

    /**
     * 性别
     */
    @Enumerated(EnumType.STRING)
    private Gender gender;

    /**
     * 出生日期
     */
    private String birthDate;

    /**
     * 身份证号（加密存储，用于实名认证）
     */
    private String idCard;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 注册来源
     */
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private RegistrationSource registrationSource = RegistrationSource.WEB;

    /**
     * 推广渠道码
     */
    private String referralCode;

    /**
     * 邮箱验证状态
     */
    @Builder.Default
    private Boolean emailVerified = false;

    /**
     * 手机验证状态
     */
    @Builder.Default
    private Boolean phoneVerified = false;

    /**
     * 用户协议确认时间
     */
    private LocalDateTime termsAcceptedAt;

    /**
     * 隐私政策确认时间
     */
    private LocalDateTime privacyAcceptedAt;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginAt;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 登录次数
     */
    @Builder.Default
    private Integer loginCount = 0;

    /**
     * 本月评估次数（用于免费版限制）
     */
    @Builder.Default
    private Integer monthlyAssessmentCount = 0;

    /**
     * 评估次数重置时间
     */
    private LocalDateTime assessmentCountResetAt;

    /**
     * 付费订阅到期时间
     */
    private LocalDateTime subscriptionExpiresAt;

    /**
     * 创建时间
     */
    @CreationTimestamp
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    /**
     * 账户状态枚举
     */
    public enum AccountStatus {
        ACTIVE,    // 活跃
        INACTIVE,  // 未激活
        SUSPENDED, // 暂停
        DELETED    // 已删除
    }

    /**
     * 服务套餐类型枚举
     */
    public enum ServiceType {
        FREE,     // 免费版
        PREMIUM,  // 付费版
        PRO       // 专业版
    }

    /**
     * 性别枚举
     */
    public enum Gender {
        MALE,   // 男性
        FEMALE, // 女性
        OTHER   // 其他
    }

    /**
     * 注册来源枚举
     */
    public enum RegistrationSource {
        WEB,     // 网页端
        MOBILE,  // 移动端
        WECHAT,  // 微信
        API      // API接口
    }

    /**
     * 检查是否为付费用户
     */
    public boolean isPremiumUser() {
        return ServiceType.PREMIUM.equals(serviceType) || ServiceType.PRO.equals(serviceType);
    }

    /**
     * 检查订阅是否有效
     */
    public boolean isSubscriptionValid() {
        if (!isPremiumUser()) {
            return false;
        }
        return subscriptionExpiresAt == null || subscriptionExpiresAt.isAfter(LocalDateTime.now());
    }

    /**
     * 检查是否可以进行评估（免费版有次数限制）
     */
    public boolean canPerformAssessment() {
        if (isPremiumUser() && isSubscriptionValid()) {
            return true; // 付费用户无限制
        }
        
        // 免费用户检查月度限制
        return monthlyAssessmentCount < FREE_USER_MONTHLY_LIMIT; // 免费版每月5次
    }

    /**
     * 获取显示名称（优先显示名称，其次真实姓名）
     */
    public String getEffectiveDisplayName() {
        return displayName != null && !displayName.trim().isEmpty() ? displayName : realName;
    }

    /**
     * 更新登录信息
     */
    public void updateLoginInfo(final String loginIp) {
        this.lastLoginAt = LocalDateTime.now();
        this.lastLoginIp = loginIp;
        this.loginCount = this.loginCount + 1;
    }

    /**
     * 重置月度评估次数（每月1号调用）
     */
    public void resetMonthlyAssessmentCount() {
        this.monthlyAssessmentCount = 0;
        this.assessmentCountResetAt = LocalDateTime.now();
    }

    /**
     * 增加评估次数
     */
    public void incrementAssessmentCount() {
        this.monthlyAssessmentCount = this.monthlyAssessmentCount + 1;
    }

    /**
     * 验证邮箱
     */
    public void verifyEmail() {
        this.emailVerified = true;
    }

    /**
     * 验证手机
     */
    public void verifyPhone() {
        this.phoneVerified = true;
    }

    /**
     * 确认用户协议
     */
    public void acceptTerms() {
        this.termsAcceptedAt = LocalDateTime.now();
    }

    /**
     * 确认隐私政策
     */
    public void acceptPrivacy() {
        this.privacyAcceptedAt = LocalDateTime.now();
    }

    /**
     * 升级到付费版
     */
    public void upgradeToPremium(final LocalDateTime expiresAt) {
        this.serviceType = ServiceType.PREMIUM;
        this.subscriptionExpiresAt = expiresAt;
    }

    /**
     * 升级到专业版
     */
    public void upgradeToPro(final LocalDateTime expiresAt) {
        this.serviceType = ServiceType.PRO;
        this.subscriptionExpiresAt = expiresAt;
    }

    /**
     * 获取用户描述信息
     */
    public String getUserDescription() {
        return String.format("个人用户: %s (%s), 套餐: %s, 状态: %s", 
                           getEffectiveDisplayName(), email, serviceType, status);
    }

    @Override
    public String toString() {
        return String.format("IndividualUser{id=%s, email='%s', realName='%s', serviceType=%s, status=%s}", 
                           id, email, realName, serviceType, status);
    }
}