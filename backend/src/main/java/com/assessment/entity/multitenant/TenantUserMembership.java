package com.assessment.entity.multitenant;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/** 租户用户关联实体 管理用户在各租户中的角色和权限 */
@Entity
@Table(
    name = "tenant_user_memberships",
    uniqueConstraints = @UniqueConstraint(columnNames = {"tenantId", "userId"}),
    indexes = {
      @Index(name = "idx_tenant_memberships_tenant", columnList = "tenantId"),
      @Index(name = "idx_tenant_memberships_user", columnList = "userId"),
      @Index(name = "idx_tenant_memberships_role", columnList = "tenantId,tenantRole")
    })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantUserMembership {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(length = 36)
  private String id;

  @Column(nullable = false, length = 36)
  private String tenantId;

  // 暂时注释掉关联关系，避免类型转换问题
  // @ManyToOne(fetch = FetchType.LAZY)
  // @JoinColumn(name = "tenantId", insertable = false, updatable = false)
  // private Tenant tenant;

  @Column(nullable = false, length = 36)
  private String userId;

  // 暂时注释掉关联关系，避免类型转换问题
  // @ManyToOne(fetch = FetchType.LAZY)
  // @JoinColumn(name = "userId", insertable = false, updatable = false)
  // private PlatformUser user;

  // 租户内角色和权限
  @Enumerated(EnumType.STRING)
  @Column(nullable = false, length = 50)
  private TenantRole tenantRole;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT")
  @Builder.Default
  private JsonNode permissions = null;

  // 个人信息（租户级别）
  @Column(length = 100)
  private String displayName;

  @Column(length = 100)
  private String professionalTitle;

  @Column(length = 100)
  private String licenseNumber;

  @Column(length = 100)
  private String department;

  // 状态管理
  @Enumerated(EnumType.STRING)
  @Column(length = 20, nullable = false)
  @Builder.Default
  private MembershipStatus status = MembershipStatus.ACTIVE;

  @Column(nullable = false)
  @Builder.Default
  private LocalDateTime joinedAt = LocalDateTime.now();

  private LocalDateTime lastActiveAt;

  /** Copy constructor for defensive copying */
  public TenantUserMembership(final TenantUserMembership other) {
    if (other != null) {
      this.id = other.id;
      this.tenantId = other.tenantId;
      // this.tenant = other.tenant; // Shallow copy is ok for LAZY loaded entity
      this.userId = other.userId;
      // this.user = other.user; // Shallow copy is ok for LAZY loaded entity
      this.tenantRole = other.tenantRole;
      this.permissions = other.permissions != null ? other.permissions.deepCopy() : null;
      this.displayName = other.displayName;
      this.professionalTitle = other.professionalTitle;
      this.licenseNumber = other.licenseNumber;
      this.department = other.department;
      this.status = other.status;
      this.joinedAt = other.joinedAt;
      this.lastActiveAt = other.lastActiveAt;
    }
  }

  /** 租户角色枚举 */
  public enum TenantRole {
    ADMIN(
        "管理员",
        new String[] {
          "USER_MANAGE", "SCALE_MANAGE", "ASSESSMENT_ALL", "REPORT_ALL", "TENANT_CONFIG"
        }),
    SUPERVISOR(
        "督导员", new String[] {"ASSESSMENT_READ", "ASSESSMENT_REVIEW", "REPORT_READ", "USER_READ"}),
    ASSESSOR(
        "评估员",
        new String[] {
          "ASSESSMENT_CREATE", "ASSESSMENT_READ", "ASSESSMENT_UPDATE", "ASSESSMENT_SUBMIT"
        }),
    REVIEWER("审核员", new String[] {"ASSESSMENT_READ", "ASSESSMENT_REVIEW", "REPORT_READ"}),
    VIEWER("查看员", new String[] {"ASSESSMENT_READ", "REPORT_READ"});

    private final String displayName;
    private final String[] defaultPermissions;

    TenantRole(final String displayName, final String[] defaultPermissions) {
      this.displayName = displayName;
      this.defaultPermissions = defaultPermissions;
    }

    public String getDisplayName() {
      return displayName;
    }

    public String[] getDefaultPermissions() {
      return defaultPermissions;
    }
  }

  /** 成员身份状态枚举 */
  public enum MembershipStatus {
    ACTIVE("活跃"),
    INACTIVE("非活跃"),
    SUSPENDED("暂停"),
    PENDING("待激活");

    private final String displayName;

    MembershipStatus(final String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 业务方法

  /** 检查是否有特定权限 */
  public boolean hasPermission(final String permission) {
    // 检查角色默认权限
    for (String defaultPerm : tenantRole.getDefaultPermissions()) {
      if (defaultPerm.equals(permission)) {
        return true;
      }
    }

    // 检查自定义权限
    if (permissions != null && permissions.isArray()) {
      for (var permNode : permissions) {
        if (permission.equals(permNode.asText())) {
          return true;
        }
      }
    }

    return false;
  }

  /** 检查是否为管理员 */
  public boolean isAdmin() {
    return tenantRole == TenantRole.ADMIN;
  }

  /** 检查是否可以管理用户 */
  public boolean canManageUsers() {
    return hasPermission("USER_MANAGE");
  }

  /** 检查是否可以管理量表 */
  public boolean canManageScales() {
    return hasPermission("SCALE_MANAGE");
  }

  /** 检查是否可以进行评估 */
  public boolean canAssess() {
    return hasPermission("ASSESSMENT_CREATE") || hasPermission("ASSESSMENT_ALL");
  }

  /** 检查是否可以审核评估 */
  public boolean canReview() {
    return hasPermission("ASSESSMENT_REVIEW") || hasPermission("ASSESSMENT_ALL");
  }

  /** 更新最后活跃时间 */
  public void updateLastActiveTime() {
    this.lastActiveAt = LocalDateTime.now();
  }

  /** 激活成员身份 */
  public void activate() {
    this.status = MembershipStatus.ACTIVE;
  }

  /** 停用成员身份 */
  public void deactivate() {
    this.status = MembershipStatus.INACTIVE;
  }

  /** 暂停成员身份 */
  public void suspend() {
    this.status = MembershipStatus.SUSPENDED;
  }

  /** 检查成员身份是否有效 */
  public boolean isValid() {
    return status == MembershipStatus.ACTIVE;
  }
}
