package com.assessment.entity.multitenant;

import com.assessment.entity.BaseEntity;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/** SaaS租户实体 代表平台中的一个租户（机构） */
@Entity
@Table(
    name = "tenants",
    indexes = {
      @Index(name = "idx_tenants_code", columnList = "code"),
      @Index(name = "idx_tenants_industry", columnList = "industry"),
      @Index(name = "idx_tenants_status", columnList = "status"),
      @Index(name = "idx_tenants_subscription", columnList = "subscriptionPlan,subscriptionStatus")
    })
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Tenant extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(length = 36)
  private java.util.UUID id;

  /** 租户代码（唯一标识） */
  @Column(nullable = false, unique = true, length = 50)
  private String code;

  /** 租户名称 */
  @Column(nullable = false, length = 200)
  private String name;

  /** 租户描述 */
  @Column(columnDefinition = "TEXT")
  private String description;

  /** Logo链接 */
  @Column(length = 500)
  private String logoUrl;

  /** 所属行业 */
  @Column(length = 50)
  private String industry;

  // 联系信息
  @Column(length = 100)
  private String contactPerson;

  @Column(length = 100)
  private String contactEmail;

  @Column(length = 50)
  private String contactPhone;

  @Column(columnDefinition = "TEXT")
  private String address;

  // 服务配置
  @Enumerated(EnumType.STRING)
  @Column(length = 20, nullable = false)
  @Builder.Default
  private SubscriptionPlan subscriptionPlan = SubscriptionPlan.BASIC;

  @Enumerated(EnumType.STRING)
  @Column(length = 20, nullable = false)
  @Builder.Default
  private SubscriptionStatus subscriptionStatus = SubscriptionStatus.ACTIVE;

  private LocalDate subscriptionStartDate;

  private LocalDate subscriptionEndDate;

  private java.time.LocalDateTime subscriptionExpiresAt;

  // 功能配额
  @Column(nullable = false)
  @Builder.Default
  private Integer maxUsers = 50;

  @Column(nullable = false)
  @Builder.Default
  private Integer maxMonthlyAssessments = 1000;

  @Column(nullable = false)
  @Builder.Default
  private Integer maxCustomScales = 10;

  @Column(nullable = false)
  @Builder.Default
  private Integer maxStorageMb = 1024;

  // 定制配置
  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT")
  @Builder.Default
  private JsonNode customConfig = null;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT")
  @Builder.Default
  private JsonNode brandingConfig = null;

  // 状态管理
  @Enumerated(EnumType.STRING)
  @Column(length = 20, nullable = false)
  @Builder.Default
  private TenantStatus status = TenantStatus.ACTIVE;

  @Column(nullable = false)
  @Builder.Default
  private Boolean isTrial = false;

  private LocalDate trialEndDate;

  // 审计字段继承自BaseEntity

  /** 订阅计划枚举 */
  public enum SubscriptionPlan {
    BASIC("基础版", 50, 1000, 10, 1024),
    STANDARD("标准版", 100, 2000, 20, 2048),
    PREMIUM("高级版", 200, 5000, 50, 5120),
    ENTERPRISE("企业版", 500, 10000, 100, 10240);

    private final String displayName;
    private final int defaultMaxUsers;
    private final int defaultMaxMonthlyAssessments;
    private final int defaultMaxCustomScales;
    private final int defaultMaxStorageMb;

    SubscriptionPlan(
        final String displayName,
        final int defaultMaxUsers,
        final int defaultMaxMonthlyAssessments,
        final int defaultMaxCustomScales,
        final int defaultMaxStorageMb) {
      this.displayName = displayName;
      this.defaultMaxUsers = defaultMaxUsers;
      this.defaultMaxMonthlyAssessments = defaultMaxMonthlyAssessments;
      this.defaultMaxCustomScales = defaultMaxCustomScales;
      this.defaultMaxStorageMb = defaultMaxStorageMb;
    }

    public String getDisplayName() {
      return displayName;
    }

    public int getDefaultMaxUsers() {
      return defaultMaxUsers;
    }

    public int getDefaultMaxMonthlyAssessments() {
      return defaultMaxMonthlyAssessments;
    }

    public int getDefaultMaxCustomScales() {
      return defaultMaxCustomScales;
    }

    public int getDefaultMaxStorageMb() {
      return defaultMaxStorageMb;
    }
  }

  /** 订阅状态枚举 */
  public enum SubscriptionStatus {
    ACTIVE("活跃"),
    SUSPENDED("暂停"),
    EXPIRED("过期"),
    CANCELLED("已取消");

    private final String displayName;

    SubscriptionStatus(final String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  /** 租户状态枚举 */
  public enum TenantStatus {
    ACTIVE("活跃"),
    INACTIVE("非活跃"),
    SUSPENDED("暂停"),
    DISABLED("禁用"),
    PENDING("待审核");

    private final String displayName;

    TenantStatus(final String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 业务方法

  /** 检查是否为试用租户 */
  public boolean isTrialActive() {
    return isTrial && trialEndDate != null && trialEndDate.isAfter(LocalDate.now());
  }

  /** 检查订阅是否有效 */
  public boolean isSubscriptionValid() {
    return subscriptionStatus == SubscriptionStatus.ACTIVE
        && (subscriptionEndDate == null || subscriptionEndDate.isAfter(LocalDate.now()));
  }

  /** 检查是否可以使用功能 */
  public boolean canUseFeature() {
    return status == TenantStatus.ACTIVE && (isSubscriptionValid() || isTrialActive());
  }

  /** 获取当月已使用的评估次数 */
  public int getCurrentMonthUsage() {
    // 这个方法需要在Service层实现，这里只是占位
    return 0;
  }

  /** 检查是否达到配额限制 */
  public boolean isQuotaExceeded(final QuotaType quotaType) {
    // 这个方法需要在Service层实现具体逻辑
    return false;
  }

  /** 配额类型枚举 */
  public enum QuotaType {
    USERS,
    MONTHLY_ASSESSMENTS,
    CUSTOM_SCALES,
    STORAGE
  }
}
