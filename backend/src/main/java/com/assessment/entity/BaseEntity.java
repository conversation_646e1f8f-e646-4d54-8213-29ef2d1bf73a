package com.assessment.entity;

import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import java.time.LocalDateTime;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/** 基础实体类 包含通用的审计字段 */
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseEntity {

  /** 创建时间 */
  @CreatedDate
  @Column(nullable = false, updatable = false)
  private LocalDateTime createdAt;

  /** 更新时间 */
  @LastModifiedDate
  @Column(nullable = false)
  private LocalDateTime updatedAt;

  /** 创建人 */
  @Column(length = 100)
  private String createdBy;

  /** 更新人 */
  @Column(length = 100)
  private String updatedBy;

  // 手动提供getter和setter方法以实现防御性拷贝
  public LocalDateTime getCreatedAt() {
    return createdAt == null ? null : LocalDateTime.from(createdAt);
  }

  public void setCreatedAt(final LocalDateTime createdAt) {
    this.createdAt = createdAt == null ? null : LocalDateTime.from(createdAt);
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt == null ? null : LocalDateTime.from(updatedAt);
  }

  public void setUpdatedAt(final LocalDateTime updatedAt) {
    this.updatedAt = updatedAt == null ? null : LocalDateTime.from(updatedAt);
  }

  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(final String createdBy) {
    this.createdBy = createdBy;
  }

  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(final String updatedBy) {
    this.updatedBy = updatedBy;
  }

  @PrePersist
  protected void onCreate() {
    LocalDateTime now = LocalDateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  @PreUpdate
  protected void onUpdate() {
    updatedAt = LocalDateTime.now();
  }
}
