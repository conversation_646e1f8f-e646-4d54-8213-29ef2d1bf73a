package com.assessment.security;

import com.assessment.constants.SecurityConstants;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

  @Autowired private JwtTokenProvider tokenProvider;

  @Autowired private UserDetailsService userDetailsService;

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {
    // Skip JWT validation for public endpoints
    String path = request.getRequestURI();
    if (path.startsWith("/api/auth/")
        || path.startsWith("/api/public/")
        || path.startsWith("/api/ai/")
        || path.startsWith("/api/docling/")
        || path.startsWith("/api/pdf-import/")
        || path.startsWith("/api/captcha/")
        || path.startsWith("/api/health")
        || path.startsWith("/swagger-ui")
        || path.startsWith("/api-docs")
        || path.startsWith("/actuator/")
        || path.equals("/")) {
      filterChain.doFilter(request, response);
      return;
    }

    try {
      String jwt = getJwtFromRequest(request);

      if (StringUtils.hasText(jwt) && tokenProvider.validateToken(jwt)) {
        String username = tokenProvider.getUsernameFromToken(jwt);

        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(
                userDetails, null, userDetails.getAuthorities());
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

        SecurityContextHolder.getContext().setAuthentication(authentication);
      }
    } catch (io.jsonwebtoken.ExpiredJwtException ex) {
      logger.error("JWT token is expired", ex);
    } catch (io.jsonwebtoken.UnsupportedJwtException | io.jsonwebtoken.MalformedJwtException ex) {
      logger.error("JWT token format is invalid", ex);
    } catch (io.jsonwebtoken.security.SignatureException ex) {
      logger.error("JWT signature validation failed", ex);
    } catch (IllegalArgumentException ex) {
      logger.error("JWT token compact of handler are invalid", ex);
      // } catch (Exception ex) {
      //   logger.error("Could not set user authentication in security context", ex);
    }

    filterChain.doFilter(request, response);
  }

  private String getJwtFromRequest(HttpServletRequest request) {
    String bearerToken = request.getHeader("Authorization");
    if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
      return bearerToken.substring(SecurityConstants.BEARER_PREFIX_LENGTH);
    }
    return null;
  }
}
