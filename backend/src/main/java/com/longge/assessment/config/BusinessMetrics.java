package com.longge.assessment.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 业务监控指标组件
 * 定义和管理业务相关的监控指标
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BusinessMetrics {

    private final MeterRegistry meterRegistry;

    // 计数器指标
    private Counter loginSuccessCounter;
    private Counter loginFailureCounter;
    private Counter assessmentCreatedCounter;
    private Counter assessmentCompletedCounter;
    private Counter elderlyRegisteredCounter;
    private Counter scaleUsedCounter;
    private Counter auditLogCreatedCounter;
    private Counter apiErrorCounter;
    private Counter exportOperationCounter;
    private Counter importOperationCounter;

    // 计时器指标  
    private Timer assessmentDurationTimer;
    private Timer apiResponseTimer;
    private Timer loginDurationTimer;
    private Timer databaseQueryTimer;
    private Timer pdfProcessingTimer;

    // 计量器指标（使用AtomicLong支持动态更新）
    private final AtomicLong activeUsersGauge = new AtomicLong(0);
    private final AtomicLong totalElderly = new AtomicLong(0);
    private final AtomicLong totalAssessments = new AtomicLong(0);
    private final AtomicLong totalScales = new AtomicLong(0);
    private final AtomicLong pendingAssessments = new AtomicLong(0);
    private final AtomicLong completedAssessments = new AtomicLong(0);
    private final AtomicLong systemErrorCount = new AtomicLong(0);
    private final AtomicLong memoryUsage = new AtomicLong(0);
    private final AtomicLong cpuUsage = new AtomicLong(0);

    @PostConstruct
    public void initMetrics() {
        log.info("初始化业务监控指标...");

        // 初始化计数器
        initCounters();
        
        // 初始化计时器
        initTimers();
        
        // 初始化计量器
        initGauges();

        log.info("业务监控指标初始化完成");
    }

    /**
     * 初始化计数器指标
     */
    private void initCounters() {
        // 登录相关指标
        loginSuccessCounter = Counter.builder("user.login.success")
                .description("用户登录成功次数")
                .tag("type", "authentication")
                .register(meterRegistry);

        loginFailureCounter = Counter.builder("user.login.failure")
                .description("用户登录失败次数")
                .tag("type", "authentication")
                .register(meterRegistry);

        // 评估相关指标
        assessmentCreatedCounter = Counter.builder("assessment.created")
                .description("创建评估次数")
                .tag("type", "business")
                .register(meterRegistry);

        assessmentCompletedCounter = Counter.builder("assessment.completed")
                .description("完成评估次数")
                .tag("type", "business")
                .register(meterRegistry);

        // 老人管理指标
        elderlyRegisteredCounter = Counter.builder("elderly.registered")
                .description("注册老人数量")
                .tag("type", "business")
                .register(meterRegistry);

        // 量表使用指标
        scaleUsedCounter = Counter.builder("scale.used")
                .description("量表使用次数")
                .tag("type", "business")
                .register(meterRegistry);

        // 审计日志指标
        auditLogCreatedCounter = Counter.builder("audit.log.created")
                .description("审计日志创建次数")
                .tag("type", "security")
                .register(meterRegistry);

        // API错误指标
        apiErrorCounter = Counter.builder("api.error")
                .description("API错误次数")
                .tag("type", "system")
                .register(meterRegistry);

        // 导出导入操作指标
        exportOperationCounter = Counter.builder("operation.export")
                .description("导出操作次数")
                .tag("type", "operation")
                .register(meterRegistry);

        importOperationCounter = Counter.builder("operation.import")
                .description("导入操作次数")
                .tag("type", "operation")
                .register(meterRegistry);
    }

    /**
     * 初始化计时器指标
     */
    private void initTimers() {
        // 评估完成时间
        assessmentDurationTimer = Timer.builder("assessment.duration")
                .description("评估完成用时")
                .tag("type", "business")
                .register(meterRegistry);

        // API响应时间
        apiResponseTimer = Timer.builder("api.response.time")
                .description("API响应时间")
                .tag("type", "performance")
                .register(meterRegistry);

        // 登录耗时
        loginDurationTimer = Timer.builder("user.login.duration")
                .description("用户登录耗时")
                .tag("type", "authentication")
                .register(meterRegistry);

        // 数据库查询耗时
        databaseQueryTimer = Timer.builder("database.query.duration")
                .description("数据库查询耗时")
                .tag("type", "performance")
                .register(meterRegistry);

        // PDF处理耗时
        pdfProcessingTimer = Timer.builder("pdf.processing.duration")
                .description("PDF处理耗时")
                .tag("type", "business")
                .register(meterRegistry);
    }

    /**
     * 初始化计量器指标
     */
    private void initGauges() {
        // 在线用户数
        Gauge.builder("user.active.count", activeUsersGauge, AtomicLong::get)
                .description("当前活跃用户数")
                .tag("type", "business")
                .register(meterRegistry);

        // 老人总数
        Gauge.builder("elderly.total.count", totalElderly, AtomicLong::get)
                .description("老人总数")
                .tag("type", "business")
                .register(meterRegistry);

        // 评估总数
        Gauge.builder("assessment.total.count", totalAssessments, AtomicLong::get)
                .description("评估总数")
                .tag("type", "business")
                .register(meterRegistry);

        // 量表总数
        Gauge.builder("scale.total.count", totalScales, AtomicLong::get)
                .description("量表总数")
                .tag("type", "business")
                .register(meterRegistry);

        // 待处理评估数
        Gauge.builder("assessment.pending.count", pendingAssessments, AtomicLong::get)
                .description("待处理评估数")
                .tag("type", "business")
                .register(meterRegistry);

        // 已完成评估数
        Gauge.builder("assessment.completed.count", completedAssessments, AtomicLong::get)
                .description("已完成评估数")
                .tag("type", "business")
                .register(meterRegistry);

        // 系统错误计数
        Gauge.builder("system.error.count", systemErrorCount, AtomicLong::get)
                .description("系统错误计数")
                .tag("type", "system")
                .register(meterRegistry);

        // 内存使用率
        Gauge.builder("system.memory.usage", memoryUsage, AtomicLong::get)
                .description("系统内存使用率")
                .tag("type", "system")
                .register(meterRegistry);

        // CPU使用率
        Gauge.builder("system.cpu.usage", cpuUsage, AtomicLong::get)
                .description("系统CPU使用率")
                .tag("type", "system")
                .register(meterRegistry);
    }

    // 公共方法：计数器操作

    /**
     * 记录登录成功
     */
    public void recordLoginSuccess() {
        loginSuccessCounter.increment();
    }

    /**
     * 记录登录失败
     */
    public void recordLoginFailure() {
        loginFailureCounter.increment();
    }

    /**
     * 记录创建评估
     */
    public void recordAssessmentCreated() {
        assessmentCreatedCounter.increment();
    }

    /**
     * 记录完成评估
     */
    public void recordAssessmentCompleted() {
        assessmentCompletedCounter.increment();
    }

    /**
     * 记录注册老人
     */
    public void recordElderlyRegistered() {
        elderlyRegisteredCounter.increment();
    }

    /**
     * 记录量表使用
     */
    public void recordScaleUsed() {
        scaleUsedCounter.increment();
    }

    /**
     * 记录审计日志创建
     */
    public void recordAuditLogCreated() {
        auditLogCreatedCounter.increment();
    }

    /**
     * 记录API错误
     */
    public void recordApiError() {
        apiErrorCounter.increment();
    }

    /**
     * 记录导出操作
     */
    public void recordExportOperation() {
        exportOperationCounter.increment();
    }

    /**
     * 记录导入操作
     */
    public void recordImportOperation() {
        importOperationCounter.increment();
    }

    // 公共方法：计时器操作

    /**
     * 获取评估耗时计时器
     */
    public Timer.Sample startAssessmentTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 停止评估计时并记录
     */
    public void stopAssessmentTimer(final Timer.Sample sample) {
        sample.stop(assessmentDurationTimer);
    }

    /**
     * 获取API响应时间计时器
     */
    public Timer.Sample startApiTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 停止API计时并记录
     */
    public void stopApiTimer(final Timer.Sample sample) {
        sample.stop(apiResponseTimer);
    }

    /**
     * 获取登录耗时计时器
     */
    public Timer.Sample startLoginTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 停止登录计时并记录
     */
    public void stopLoginTimer(final Timer.Sample sample) {
        sample.stop(loginDurationTimer);
    }

    /**
     * 获取数据库查询耗时计时器
     */
    public Timer.Sample startDatabaseTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 停止数据库计时并记录
     */
    public void stopDatabaseTimer(final Timer.Sample sample) {
        sample.stop(databaseQueryTimer);
    }

    /**
     * 获取PDF处理耗时计时器
     */
    public Timer.Sample startPdfProcessingTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 停止PDF处理计时并记录
     */
    public void stopPdfProcessingTimer(final Timer.Sample sample) {
        sample.stop(pdfProcessingTimer);
    }

    // 公共方法：计量器操作

    /**
     * 设置活跃用户数
     */
    public void setActiveUsers(final long count) {
        activeUsersGauge.set(count);
    }

    /**
     * 增加活跃用户数
     */
    public void incrementActiveUsers() {
        activeUsersGauge.incrementAndGet();
    }

    /**
     * 减少活跃用户数
     */
    public void decrementActiveUsers() {
        activeUsersGauge.decrementAndGet();
    }

    /**
     * 设置老人总数
     */
    public void setTotalElderly(final long count) {
        totalElderly.set(count);
    }

    /**
     * 设置评估总数
     */
    public void setTotalAssessments(final long count) {
        totalAssessments.set(count);
    }

    /**
     * 设置量表总数
     */
    public void setTotalScales(final long count) {
        totalScales.set(count);
    }

    /**
     * 设置待处理评估数
     */
    public void setPendingAssessments(final long count) {
        pendingAssessments.set(count);
    }

    /**
     * 设置已完成评估数
     */
    public void setCompletedAssessments(final long count) {
        completedAssessments.set(count);
    }

    /**
     * 增加系统错误计数
     */
    public void incrementSystemErrors() {
        systemErrorCount.incrementAndGet();
    }

    /**
     * 设置内存使用率
     */
    public void setMemoryUsage(final long usage) {
        memoryUsage.set(usage);
    }

    /**
     * 设置CPU使用率
     */
    public void setCpuUsage(final long usage) {
        cpuUsage.set(usage);
    }

    // 工具方法

    /**
     * 记录带标签的计数器
     */
    public void recordCounterWithTags(final String name, final String description, 
                                    final String... tags) {
        Counter.builder(name)
                .description(description)
                .tags(tags)
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录带标签的计时器
     */
    public void recordTimerWithTags(final String name, final String description, 
                                  final long duration, final String... tags) {
        Timer.builder(name)
                .description(description)
                .tags(tags)
                .register(meterRegistry)
                .record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
    }

    /**
     * 获取当前指标注册表
     */
    public MeterRegistry getMeterRegistry() {
        return meterRegistry;
    }
}