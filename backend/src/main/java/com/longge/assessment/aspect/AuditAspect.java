package com.longge.assessment.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.longge.assessment.annotation.Audited;
import com.longge.assessment.entity.AuditLog;
import com.longge.assessment.service.AuditLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 审计切面
 * 拦截被@Audited注解标记的方法，自动记录审计日志
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class AuditAspect {

    private final AuditLogService auditLogService;
    private final ObjectMapper objectMapper;
    private final ExpressionParser parser = new SpelExpressionParser();

    /**
     * 环绕通知，处理审计日志记录
     */
    @Around("@annotation(audited)")
    public Object auditMethod(final ProceedingJoinPoint joinPoint, final Audited audited) throws Throwable {
        LocalDateTime startTime = LocalDateTime.now();
        Object result = null;
        Throwable exception = null;
        long duration = 0;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            duration = java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
            
            // 记录成功的审计日志
            if (!audited.successOnly()) {
                recordAuditLog(joinPoint, audited, result, null, duration, true);
            } else {
                recordAuditLog(joinPoint, audited, result, null, duration, true);
            }
            
            return result;
        } catch (Throwable ex) {
            exception = ex;
            duration = java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
            
            // 记录失败的审计日志
            if (!audited.successOnly()) {
                recordAuditLog(joinPoint, audited, result, exception, duration, false);
            }
            
            throw ex;
        }
    }

    /**
     * 记录审计日志
     */
    private void recordAuditLog(final ProceedingJoinPoint joinPoint, final Audited audited,
                               final Object result, final Throwable exception, 
                               final long duration, final boolean success) {
        try {
            // 获取当前用户信息
            UserInfo userInfo = getCurrentUserInfo();
            
            // 创建SpEL评估上下文
            EvaluationContext context = createEvaluationContext(joinPoint.getArgs(), result, exception);
            
            // 提取资源信息
            String resourceId = evaluateExpression(audited.resourceId(), context);
            String resourceName = evaluateExpression(audited.resourceName(), context);
            String description = evaluateExpression(audited.description(), context);
            
            // 提取操作详情
            String operationDetails = extractOperationDetails(audited, joinPoint.getArgs(), result, exception);
            
            // 构建审计日志
            AuditLog.AuditLogBuilder builder = AuditLog.builder()
                    .userId(userInfo.getUserId())
                    .userName(userInfo.getUserName())
                    .tenantId(userInfo.getTenantId())
                    .operationType(audited.operationType())
                    .resourceType(audited.resourceType())
                    .resourceId(resourceId)
                    .resourceName(resourceName)
                    .operationDescription(description)
                    .operationDetails(operationDetails)
                    .operationResult(success ? AuditLog.OperationResult.SUCCESS : AuditLog.OperationResult.FAILURE)
                    .riskLevel(audited.riskLevel())
                    .sensitive(audited.sensitive())
                    .businessModule(StringUtils.hasText(audited.businessModule()) ? audited.businessModule() : getBusinessModule(joinPoint))
                    .duration(duration);
            
            // 设置错误信息
            if (!success && exception != null) {
                builder.errorMessage(exception.getMessage());
            }
            
            // 记录审计日志
            auditLogService.recordAuditLog(builder.build());
            
        } catch (Exception e) {
            log.error("记录审计日志失败", e);
        }
    }

    /**
     * 获取当前用户信息
     */
    private UserInfo getCurrentUserInfo() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null && authentication.isAuthenticated()) {
            String userId = authentication.getName();
            String userName = getUserDisplayName(authentication);
            String tenantId = getTenantId(authentication);
            
            return new UserInfo(userId, userName, tenantId);
        }
        
        // 如果没有认证信息，返回系统用户
        return new UserInfo("system", "系统", "system");
    }

    /**
     * 获取用户显示名称
     */
    private String getUserDisplayName(final Authentication authentication) {
        Object details = authentication.getDetails();
        if (details instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> detailsMap = (Map<String, Object>) details;
            Object displayName = detailsMap.get("displayName");
            if (displayName != null) {
                return displayName.toString();
            }
        }
        
        // 如果没有显示名称，使用用户名
        return authentication.getName();
    }

    /**
     * 获取租户ID
     */
    private String getTenantId(final Authentication authentication) {
        Object details = authentication.getDetails();
        if (details instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> detailsMap = (Map<String, Object>) details;
            Object tenantId = detailsMap.get("tenantId");
            if (tenantId != null) {
                return tenantId.toString();
            }
        }
        
        // 如果没有租户信息，返回默认值
        return "default";
    }

    /**
     * 创建SpEL评估上下文
     */
    private EvaluationContext createEvaluationContext(final Object[] args, final Object result, final Throwable exception) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        
        // 设置方法参数
        context.setVariable("args", args);
        
        // 设置返回值
        if (result != null) {
            context.setVariable("result", result);
        }
        
        // 设置异常
        if (exception != null) {
            context.setVariable("exception", exception);
        }
        
        // 设置当前时间
        context.setVariable("now", LocalDateTime.now());
        
        return context;
    }

    /**
     * 评估SpEL表达式
     */
    private String evaluateExpression(final String expression, final EvaluationContext context) {
        if (!StringUtils.hasText(expression)) {
            return null;
        }
        
        try {
            // 如果不是SpEL表达式，直接返回
            if (!expression.contains("#{") && !expression.contains("${")) {
                return expression;
            }
            
            Expression expr = parser.parseExpression(expression);
            Object value = expr.getValue(context);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.warn("评估SpEL表达式失败: {}", expression, e);
            return expression;
        }
    }

    /**
     * 提取操作详情
     */
    private String extractOperationDetails(final Audited audited, final Object[] args, 
                                         final Object result, final Throwable exception) {
        try {
            // 使用自定义详情提取器
            if (audited.detailsExtractor() != Audited.DefaultAuditDetailsExtractor.class) {
                Audited.AuditDetailsExtractor extractor = audited.detailsExtractor().getDeclaredConstructor().newInstance();
                return extractor.extractDetails(args, result, exception);
            }
            
            // 默认详情提取逻辑
            Map<String, Object> details = new HashMap<>();
            
            // 记录方法参数
            if (audited.recordArgs() && args != null && args.length > 0) {
                Map<String, Object> argsMap = new HashMap<>();
                for (int i = 0; i < args.length; i++) {
                    final int currentIndex = i; // 创建final变量用于lambda表达式
                    // 检查是否需要排除此参数
                    if (Arrays.stream(audited.excludeArgs()).anyMatch(excludeIndex -> excludeIndex == currentIndex)) {
                        argsMap.put("arg" + i, "[EXCLUDED]");
                    } else {
                        argsMap.put("arg" + i, maskSensitiveData(args[i]));
                    }
                }
                details.put("arguments", argsMap);
            }
            
            // 记录返回值
            if (audited.recordResult() && result != null) {
                details.put("result", maskSensitiveData(result));
            }
            
            // 记录异常信息
            if (exception != null) {
                Map<String, Object> exceptionInfo = new HashMap<>();
                exceptionInfo.put("type", exception.getClass().getSimpleName());
                exceptionInfo.put("message", exception.getMessage());
                details.put("exception", exceptionInfo);
            }
            
            return details.isEmpty() ? null : objectMapper.writeValueAsString(details);
            
        } catch (Exception e) {
            log.warn("提取操作详情失败", e);
            return null;
        }
    }

    /**
     * 掩码敏感数据
     */
    private Object maskSensitiveData(final Object data) {
        if (data == null) {
            return null;
        }
        
        // 如果是字符串且可能是敏感信息，进行掩码处理
        if (data instanceof String) {
            String str = (String) data;
            String lowerStr = str.toLowerCase();
            
            // 检查是否包含敏感关键词
            if (lowerStr.contains("password") || lowerStr.contains("token") || 
                lowerStr.contains("secret") || lowerStr.contains("key")) {
                return "[MASKED]";
            }
            
            // 如果字符串很长，可能包含敏感信息，进行截断
            if (str.length() > 1000) {
                return str.substring(0, 1000) + "...[TRUNCATED]";
            }
        }
        
        return data;
    }

    /**
     * 获取业务模块名称
     */
    private String getBusinessModule(final ProceedingJoinPoint joinPoint) {
        try {
            String className = joinPoint.getTarget().getClass().getSimpleName();
            
            // 根据类名推断业务模块
            if (className.toLowerCase().contains("assessment")) {
                return "评估管理";
            } else if (className.toLowerCase().contains("elderly")) {
                return "老人管理";
            } else if (className.toLowerCase().contains("scale")) {
                return "量表管理";
            } else if (className.toLowerCase().contains("user")) {
                return "用户管理";
            } else if (className.toLowerCase().contains("tenant")) {
                return "租户管理";
            } else if (className.toLowerCase().contains("auth")) {
                return "认证授权";
            }
            
            return "其他";
        } catch (Exception e) {
            return "未知";
        }
    }

    /**
     * 用户信息内部类
     */
    private static class UserInfo {
        private final String userId;
        private final String userName;
        private final String tenantId;

        public UserInfo(final String userId, final String userName, final String tenantId) {
            this.userId = userId;
            this.userName = userName;
            this.tenantId = tenantId;
        }

        public String getUserId() {
            return userId;
        }

        public String getUserName() {
            return userName;
        }

        public String getTenantId() {
            return tenantId;
        }
    }
}