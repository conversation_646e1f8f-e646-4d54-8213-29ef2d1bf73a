package com.longge.assessment.annotation;

import com.longge.assessment.entity.AuditLog;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 审计注解
 * 用于标记需要进行审计记录的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Audited {

    /**
     * 操作类型
     */
    AuditLog.OperationType operationType();

    /**
     * 资源类型
     * 如：ASSESSMENT, ELDERLY, SCALE, USER等
     */
    String resourceType();

    /**
     * 操作描述模板
     * 支持SpEL表达式，可以使用方法参数和返回值
     * 例如："创建评估：#{args[0].title}"
     */
    String description();

    /**
     * 资源ID表达式
     * 支持SpEL表达式，用于提取资源ID
     * 例如："#{args[0].id}" 或 "#{result.id}"
     */
    String resourceId() default "";

    /**
     * 资源名称表达式
     * 支持SpEL表达式，用于提取资源名称
     * 例如："{@args[0].name}" 或 "#{result.name}"
     */
    String resourceName() default "";

    /**
     * 风险级别
     */
    AuditLog.RiskLevel riskLevel() default AuditLog.RiskLevel.LOW;

    /**
     * 是否敏感操作
     */
    boolean sensitive() default false;

    /**
     * 业务模块
     */
    String businessModule() default "";

    /**
     * 是否记录方法参数
     */
    boolean recordArgs() default false;

    /**
     * 是否记录返回值
     */
    boolean recordResult() default false;

    /**
     * 是否仅在操作成功时记录
     * 如果为false，成功和失败都会记录
     */
    boolean successOnly() default false;

    /**
     * 需要排除记录的参数索引
     * 用于排除敏感参数，如密码等
     */
    int[] excludeArgs() default {};

    /**
     * 自定义操作详情提取器类
     * 实现 AuditDetailsExtractor 接口
     */
    Class<? extends AuditDetailsExtractor> detailsExtractor() default DefaultAuditDetailsExtractor.class;

    /**
     * 审计详情提取器接口
     */
    interface AuditDetailsExtractor {
        /**
         * 提取操作详情
         *
         * @param args 方法参数
         * @param result 方法返回值
         * @param exception 异常信息（如果有）
         * @return 操作详情JSON字符串
         */
        String extractDetails(Object[] args, Object result, Throwable exception);
    }

    /**
     * 默认的审计详情提取器
     */
    class DefaultAuditDetailsExtractor implements AuditDetailsExtractor {
        @Override
        public String extractDetails(final Object[] args, final Object result, final Throwable exception) {
            // 默认不提取详情
            return null;
        }
    }
}