package com.longge.assessment.entity;

import com.assessment.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 审计日志实体
 * 记录系统中的关键操作，用于合规性要求和问题追踪
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "audit_logs", indexes = {
    @Index(name = "idx_audit_user_id", columnList = "userId"),
    @Index(name = "idx_audit_tenant_id", columnList = "tenantId"),
    @Index(name = "idx_audit_operation_time", columnList = "operationTime"),
    @Index(name = "idx_audit_resource_type", columnList = "resourceType"),
    @Index(name = "idx_audit_operation_type", columnList = "operationType")
})
public class AuditLog extends BaseEntity {

    /**
     * 审计日志ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;

    /**
     * 操作用户ID
     */
    @Column(nullable = false)
    private String userId;

    /**
     * 操作用户名称
     */
    @Column(nullable = false, length = 100)
    private String userName;

    /**
     * 租户ID
     */
    @Column(nullable = false)
    private String tenantId;

    /**
     * 操作类型
     * CREATE: 创建操作
     * UPDATE: 更新操作  
     * DELETE: 删除操作
     * VIEW: 查看操作
     * LOGIN: 登录操作
     * LOGOUT: 登出操作
     * EXPORT: 导出操作
     * IMPORT: 导入操作
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private OperationType operationType;

    /**
     * 操作的资源类型
     * 如：ASSESSMENT, ELDERLY, SCALE, USER等
     */
    @Column(nullable = false, length = 50)
    private String resourceType;

    /**
     * 操作的资源ID
     */
    private String resourceId;

    /**
     * 操作的资源名称
     */
    @Column(length = 200)
    private String resourceName;

    /**
     * 操作描述
     */
    @Column(nullable = false, length = 500)
    private String operationDescription;

    /**
     * 操作详情（JSON格式）
     * 记录具体的操作内容，如修改前后的数据对比
     */
    @Lob
    private String operationDetails;

    /**
     * 操作时间
     */
    @Column(nullable = false)
    private LocalDateTime operationTime;

    /**
     * 操作结果
     * SUCCESS: 操作成功
     * FAILURE: 操作失败
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10)
    private OperationResult operationResult;

    /**
     * 错误信息（如果操作失败）
     */
    @Column(length = 1000)
    private String errorMessage;

    /**
     * 客户端IP地址
     */
    @Column(length = 45)
    private String clientIp;

    /**
     * 用户代理信息
     */
    @Column(length = 500)
    private String userAgent;

    /**
     * 会话ID
     */
    @Column(length = 100)
    private String sessionId;

    /**
     * 请求URI
     */
    @Column(length = 500)
    private String requestUri;

    /**
     * 请求方法
     */
    @Column(length = 10)
    private String requestMethod;

    /**
     * 操作耗时（毫秒）
     */
    private Long duration;

    /**
     * 风险级别
     * LOW: 低风险
     * MEDIUM: 中等风险
     * HIGH: 高风险
     * CRITICAL: 严重风险
     */
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private RiskLevel riskLevel;

    /**
     * 是否敏感操作
     */
    @Column(nullable = false)
    private Boolean sensitive = false;

    /**
     * 业务模块
     */
    @Column(length = 50)
    private String businessModule;

    /**
     * 扩展字段（JSON格式）
     */
    @Lob
    private String extendedInfo;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE("创建"),
        UPDATE("更新"),
        DELETE("删除"),
        VIEW("查看"),
        LOGIN("登录"),
        LOGOUT("登出"),
        EXPORT("导出"),
        IMPORT("导入"),
        DOWNLOAD("下载"),
        UPLOAD("上传"),
        EXECUTE("执行"),
        APPROVE("审批"),
        REJECT("拒绝");

        private final String description;

        OperationType(final String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 操作结果枚举
     */
    public enum OperationResult {
        SUCCESS("成功"),
        FAILURE("失败");

        private final String description;

        OperationResult(final String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 风险级别枚举
     */
    public enum RiskLevel {
        LOW("低风险"),
        MEDIUM("中等风险"),
        HIGH("高风险"),
        CRITICAL("严重风险");

        private final String description;

        RiskLevel(final String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 构造函数
     */
    public AuditLog() {
        this.operationTime = LocalDateTime.now();
        this.operationResult = OperationResult.SUCCESS;
        this.riskLevel = RiskLevel.LOW;
        this.sensitive = false;
    }

    /**
     * 构建器模式的静态方法
     */
    public static AuditLogBuilder builder() {
        return new AuditLogBuilder();
    }

    /**
     * 审计日志构建器
     */
    public static class AuditLogBuilder {
        private final AuditLog auditLog = new AuditLog();

        public AuditLogBuilder userId(final String userId) {
            auditLog.setUserId(userId);
            return this;
        }

        public AuditLogBuilder userName(final String userName) {
            auditLog.setUserName(userName);
            return this;
        }

        public AuditLogBuilder tenantId(final String tenantId) {
            auditLog.setTenantId(tenantId);
            return this;
        }

        public AuditLogBuilder operationType(final OperationType operationType) {
            auditLog.setOperationType(operationType);
            return this;
        }

        public AuditLogBuilder resourceType(final String resourceType) {
            auditLog.setResourceType(resourceType);
            return this;
        }

        public AuditLogBuilder resourceId(final String resourceId) {
            auditLog.setResourceId(resourceId);
            return this;
        }

        public AuditLogBuilder resourceName(final String resourceName) {
            auditLog.setResourceName(resourceName);
            return this;
        }

        public AuditLogBuilder operationDescription(final String operationDescription) {
            auditLog.setOperationDescription(operationDescription);
            return this;
        }

        public AuditLogBuilder operationDetails(final String operationDetails) {
            auditLog.setOperationDetails(operationDetails);
            return this;
        }

        public AuditLogBuilder operationResult(final OperationResult operationResult) {
            auditLog.setOperationResult(operationResult);
            return this;
        }

        public AuditLogBuilder errorMessage(final String errorMessage) {
            auditLog.setErrorMessage(errorMessage);
            return this;
        }

        public AuditLogBuilder clientIp(final String clientIp) {
            auditLog.setClientIp(clientIp);
            return this;
        }

        public AuditLogBuilder userAgent(final String userAgent) {
            auditLog.setUserAgent(userAgent);
            return this;
        }

        public AuditLogBuilder sessionId(final String sessionId) {
            auditLog.setSessionId(sessionId);
            return this;
        }

        public AuditLogBuilder requestUri(final String requestUri) {
            auditLog.setRequestUri(requestUri);
            return this;
        }

        public AuditLogBuilder requestMethod(final String requestMethod) {
            auditLog.setRequestMethod(requestMethod);
            return this;
        }

        public AuditLogBuilder duration(final Long duration) {
            auditLog.setDuration(duration);
            return this;
        }

        public AuditLogBuilder riskLevel(final RiskLevel riskLevel) {
            auditLog.setRiskLevel(riskLevel);
            return this;
        }

        public AuditLogBuilder sensitive(final Boolean sensitive) {
            auditLog.setSensitive(sensitive);
            return this;
        }

        public AuditLogBuilder businessModule(final String businessModule) {
            auditLog.setBusinessModule(businessModule);
            return this;
        }

        public AuditLogBuilder extendedInfo(final String extendedInfo) {
            auditLog.setExtendedInfo(extendedInfo);
            return this;
        }

        public AuditLog build() {
            return auditLog;
        }
    }
}