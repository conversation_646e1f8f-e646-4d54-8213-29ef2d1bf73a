package com.longge.assessment.repository;

import com.longge.assessment.entity.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志数据访问接口
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, String>, JpaSpecificationExecutor<AuditLog> {

    /**
     * 根据用户ID查询审计日志
     */
    Page<AuditLog> findByUserIdOrderByOperationTimeDesc(String userId, Pageable pageable);

    /**
     * 根据租户ID查询审计日志
     */
    Page<AuditLog> findByTenantIdOrderByOperationTimeDesc(String tenantId, Pageable pageable);

    /**
     * 根据资源类型查询审计日志
     */
    Page<AuditLog> findByResourceTypeOrderByOperationTimeDesc(String resourceType, Pageable pageable);

    /**
     * 根据资源ID查询审计日志
     */
    List<AuditLog> findByResourceIdOrderByOperationTimeDesc(String resourceId);

    /**
     * 根据操作类型查询审计日志
     */
    Page<AuditLog> findByOperationTypeOrderByOperationTimeDesc(AuditLog.OperationType operationType, Pageable pageable);

    /**
     * 根据操作结果查询审计日志
     */
    Page<AuditLog> findByOperationResultOrderByOperationTimeDesc(AuditLog.OperationResult operationResult, Pageable pageable);

    /**
     * 根据风险级别查询审计日志
     */
    Page<AuditLog> findByRiskLevelOrderByOperationTimeDesc(AuditLog.RiskLevel riskLevel, Pageable pageable);

    /**
     * 查询敏感操作日志
     */
    Page<AuditLog> findBySensitiveTrueOrderByOperationTimeDesc(Pageable pageable);

    /**
     * 根据时间范围查询审计日志
     */
    Page<AuditLog> findByOperationTimeBetweenOrderByOperationTimeDesc(
            LocalDateTime startTime, 
            LocalDateTime endTime, 
            Pageable pageable
    );

    /**
     * 根据用户和时间范围查询审计日志
     */
    Page<AuditLog> findByUserIdAndOperationTimeBetweenOrderByOperationTimeDesc(
            String userId, 
            LocalDateTime startTime, 
            LocalDateTime endTime, 
            Pageable pageable
    );

    /**
     * 根据租户和时间范围查询审计日志
     */
    Page<AuditLog> findByTenantIdAndOperationTimeBetweenOrderByOperationTimeDesc(
            String tenantId, 
            LocalDateTime startTime, 
            LocalDateTime endTime, 
            Pageable pageable
    );

    /**
     * 查询用户的登录日志
     */
    @Query("SELECT a FROM AuditLog a WHERE a.userId = :userId AND a.operationType IN ('LOGIN', 'LOGOUT') ORDER BY a.operationTime DESC")
    List<AuditLog> findUserLoginLogs(@Param("userId") String userId, Pageable pageable);

    /**
     * 统计用户操作次数
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.userId = :userId AND a.operationTime >= :startTime")
    Long countUserOperations(@Param("userId") String userId, @Param("startTime") LocalDateTime startTime);

    /**
     * 统计失败操作次数
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.operationResult = 'FAILURE' AND a.operationTime >= :startTime")
    Long countFailedOperations(@Param("startTime") LocalDateTime startTime);

    /**
     * 统计高风险操作次数
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.riskLevel IN ('HIGH', 'CRITICAL') AND a.operationTime >= :startTime")
    Long countHighRiskOperations(@Param("startTime") LocalDateTime startTime);

    /**
     * 查询最近的操作日志
     */
    List<AuditLog> findTop10ByOrderByOperationTimeDesc();

    /**
     * 根据IP地址查询操作日志
     */
    Page<AuditLog> findByClientIpOrderByOperationTimeDesc(String clientIp, Pageable pageable);

    /**
     * 查询特定时间范围内的敏感操作
     */
    @Query("SELECT a FROM AuditLog a WHERE a.sensitive = true AND a.operationTime BETWEEN :startTime AND :endTime ORDER BY a.operationTime DESC")
    List<AuditLog> findSensitiveOperations(
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 按操作类型统计
     */
    @Query("SELECT a.operationType, COUNT(a) FROM AuditLog a WHERE a.operationTime >= :startTime GROUP BY a.operationType")
    List<Object[]> statisticsByOperationType(@Param("startTime") LocalDateTime startTime);

    /**
     * 按资源类型统计
     */
    @Query("SELECT a.resourceType, COUNT(a) FROM AuditLog a WHERE a.operationTime >= :startTime GROUP BY a.resourceType")
    List<Object[]> statisticsByResourceType(@Param("startTime") LocalDateTime startTime);

    /**
     * 按用户统计操作次数
     */
    @Query("SELECT a.userId, a.userName, COUNT(a) FROM AuditLog a WHERE a.operationTime >= :startTime GROUP BY a.userId, a.userName ORDER BY COUNT(a) DESC")
    List<Object[]> statisticsByUser(@Param("startTime") LocalDateTime startTime, Pageable pageable);

    /**
     * 按时间分组统计（按小时）
     */
    @Query(value = "SELECT DATE_FORMAT(operation_time, '%Y-%m-%d %H:00:00') as hour, COUNT(*) as count " +
           "FROM audit_logs " +
           "WHERE operation_time >= :startTime " +
           "GROUP BY DATE_FORMAT(operation_time, '%Y-%m-%d %H:00:00') " +
           "ORDER BY hour", nativeQuery = true)
    List<Object[]> statisticsByHour(@Param("startTime") LocalDateTime startTime);

    /**
     * 按时间分组统计（按天）
     */
    @Query(value = "SELECT DATE(operation_time) as day, COUNT(*) as count " +
           "FROM audit_logs " +
           "WHERE operation_time >= :startTime " +
           "GROUP BY DATE(operation_time) " +
           "ORDER BY day", nativeQuery = true)
    List<Object[]> statisticsByDay(@Param("startTime") LocalDateTime startTime);

    /**
     * 删除指定时间之前的审计日志（用于日志清理）
     */
    void deleteByOperationTimeBefore(LocalDateTime beforeTime);

    /**
     * 统计指定时间范围内的日志数量
     */
    Long countByOperationTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询异常IP操作（同一IP在短时间内大量操作）
     */
    @Query(value = "SELECT client_ip, COUNT(*) as count " +
           "FROM audit_logs " +
           "WHERE operation_time >= :startTime " +
           "GROUP BY client_ip " +
           "HAVING COUNT(*) > :threshold " +
           "ORDER BY count DESC", nativeQuery = true)
    List<Object[]> findSuspiciousIpOperations(
            @Param("startTime") LocalDateTime startTime, 
            @Param("threshold") Integer threshold
    );

    /**
     * 查询用户异常操作（用户在短时间内失败操作过多）
     */
    @Query("SELECT a.userId, a.userName, COUNT(a) FROM AuditLog a " +
           "WHERE a.operationResult = 'FAILURE' AND a.operationTime >= :startTime " +
           "GROUP BY a.userId, a.userName " +
           "HAVING COUNT(a) > :threshold " +
           "ORDER BY COUNT(a) DESC")
    List<Object[]> findUsersWithExcessiveFailures(
            @Param("startTime") LocalDateTime startTime, 
            @Param("threshold") Integer threshold
    );
}