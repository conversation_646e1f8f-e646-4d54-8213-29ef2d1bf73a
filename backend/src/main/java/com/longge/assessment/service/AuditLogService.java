package com.longge.assessment.service;

import com.longge.assessment.entity.AuditLog;
import com.longge.assessment.repository.AuditLogRepository;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 审计日志服务
 * 提供审计日志的记录、查询和统计功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuditLogService {

    private final AuditLogRepository auditLogRepository;

    /**
     * 异步记录审计日志
     * 
     * @param auditLog 审计日志对象
     */
    @Async
    @Transactional
    public void recordAuditLog(final AuditLog auditLog) {
        try {
            // 自动填充请求信息
            fillRequestInfo(auditLog);
            
            // 保存审计日志
            auditLogRepository.save(auditLog);
            
            log.debug("审计日志记录成功: {}", auditLog.getOperationDescription());
        } catch (Exception e) {
            log.error("记录审计日志失败", e);
            // 审计日志记录失败不应该影响业务流程
        }
    }

    /**
     * 记录操作成功的审计日志
     */
    @Async
    public void recordSuccess(final String userId, final String userName, final String tenantId,
                             final AuditLog.OperationType operationType, final String resourceType,
                             final String resourceId, final String resourceName,
                             final String operationDescription) {
        recordSuccess(userId, userName, tenantId, operationType, resourceType, resourceId, 
                     resourceName, operationDescription, null, null);
    }

    /**
     * 记录操作成功的审计日志（带详情）
     */
    @Async
    public void recordSuccess(final String userId, final String userName, final String tenantId,
                             final AuditLog.OperationType operationType, final String resourceType,
                             final String resourceId, final String resourceName,
                             final String operationDescription, final String operationDetails,
                             final AuditLog.RiskLevel riskLevel) {
        AuditLog auditLog = AuditLog.builder()
                .userId(userId)
                .userName(userName)
                .tenantId(tenantId)
                .operationType(operationType)
                .resourceType(resourceType)
                .resourceId(resourceId)
                .resourceName(resourceName)
                .operationDescription(operationDescription)
                .operationDetails(operationDetails)
                .operationResult(AuditLog.OperationResult.SUCCESS)
                .riskLevel(riskLevel != null ? riskLevel : AuditLog.RiskLevel.LOW)
                .build();

        recordAuditLog(auditLog);
    }

    /**
     * 记录操作失败的审计日志
     */
    @Async
    public void recordFailure(final String userId, final String userName, final String tenantId,
                             final AuditLog.OperationType operationType, final String resourceType,
                             final String resourceId, final String resourceName,
                             final String operationDescription, final String errorMessage) {
        recordFailure(userId, userName, tenantId, operationType, resourceType, resourceId,
                     resourceName, operationDescription, errorMessage, null, null);
    }

    /**
     * 记录操作失败的审计日志（带详情）
     */
    @Async
    public void recordFailure(final String userId, final String userName, final String tenantId,
                             final AuditLog.OperationType operationType, final String resourceType,
                             final String resourceId, final String resourceName,
                             final String operationDescription, final String errorMessage,
                             final String operationDetails, final AuditLog.RiskLevel riskLevel) {
        AuditLog auditLog = AuditLog.builder()
                .userId(userId)
                .userName(userName)
                .tenantId(tenantId)
                .operationType(operationType)
                .resourceType(resourceType)
                .resourceId(resourceId)
                .resourceName(resourceName)
                .operationDescription(operationDescription)
                .operationDetails(operationDetails)
                .operationResult(AuditLog.OperationResult.FAILURE)
                .errorMessage(errorMessage)
                .riskLevel(riskLevel != null ? riskLevel : AuditLog.RiskLevel.MEDIUM)
                .build();

        recordAuditLog(auditLog);
    }

    /**
     * 记录登录审计日志
     */
    @Async
    public void recordLogin(final String userId, final String userName, final String tenantId, 
                           final boolean success, final String errorMessage) {
        AuditLog.OperationType operationType = AuditLog.OperationType.LOGIN;
        AuditLog.OperationResult operationResult = success ? 
                AuditLog.OperationResult.SUCCESS : AuditLog.OperationResult.FAILURE;
        
        AuditLog auditLog = AuditLog.builder()
                .userId(userId)
                .userName(userName)
                .tenantId(tenantId)
                .operationType(operationType)
                .resourceType("USER")
                .resourceId(userId)
                .resourceName(userName)
                .operationDescription(success ? "用户登录成功" : "用户登录失败")
                .operationResult(operationResult)
                .errorMessage(errorMessage)
                .riskLevel(success ? AuditLog.RiskLevel.LOW : AuditLog.RiskLevel.MEDIUM)
                .sensitive(true)
                .businessModule("认证")
                .build();

        recordAuditLog(auditLog);
    }

    /**
     * 记录登出审计日志
     */
    @Async
    public void recordLogout(final String userId, final String userName, final String tenantId) {
        AuditLog auditLog = AuditLog.builder()
                .userId(userId)
                .userName(userName)
                .tenantId(tenantId)
                .operationType(AuditLog.OperationType.LOGOUT)
                .resourceType("USER")
                .resourceId(userId)
                .resourceName(userName)
                .operationDescription("用户登出")
                .operationResult(AuditLog.OperationResult.SUCCESS)
                .riskLevel(AuditLog.RiskLevel.LOW)
                .sensitive(true)
                .businessModule("认证")
                .build();

        recordAuditLog(auditLog);
    }

    /**
     * 分页查询审计日志
     */
    public Page<AuditLog> findAuditLogs(final AuditLogQueryParams queryParams, final Pageable pageable) {
        Specification<AuditLog> spec = buildSpecification(queryParams);
        return auditLogRepository.findAll(spec, pageable);
    }

    /**
     * 根据用户查询审计日志
     */
    public Page<AuditLog> findByUserId(final String userId, final Pageable pageable) {
        return auditLogRepository.findByUserIdOrderByOperationTimeDesc(userId, pageable);
    }

    /**
     * 根据租户查询审计日志
     */
    public Page<AuditLog> findByTenantId(final String tenantId, final Pageable pageable) {
        return auditLogRepository.findByTenantIdOrderByOperationTimeDesc(tenantId, pageable);
    }

    /**
     * 根据资源查询审计日志
     */
    public List<AuditLog> findByResourceId(final String resourceId) {
        return auditLogRepository.findByResourceIdOrderByOperationTimeDesc(resourceId);
    }

    /**
     * 查询敏感操作日志
     */
    public Page<AuditLog> findSensitiveOperations(final Pageable pageable) {
        return auditLogRepository.findBySensitiveTrueOrderByOperationTimeDesc(pageable);
    }

    /**
     * 查询失败操作日志
     */
    public Page<AuditLog> findFailedOperations(final Pageable pageable) {
        return auditLogRepository.findByOperationResultOrderByOperationTimeDesc(
                AuditLog.OperationResult.FAILURE, pageable);
    }

    /**
     * 查询高风险操作日志
     */
    public Page<AuditLog> findHighRiskOperations(final Pageable pageable) {
        return auditLogRepository.findByRiskLevelOrderByOperationTimeDesc(
                AuditLog.RiskLevel.HIGH, pageable);
    }

    /**
     * 统计用户操作次数
     */
    public Long countUserOperations(final String userId, final LocalDateTime startTime) {
        return auditLogRepository.countUserOperations(userId, startTime);
    }

    /**
     * 统计失败操作次数
     */
    public Long countFailedOperations(final LocalDateTime startTime) {
        return auditLogRepository.countFailedOperations(startTime);
    }

    /**
     * 统计高风险操作次数
     */
    public Long countHighRiskOperations(final LocalDateTime startTime) {
        return auditLogRepository.countHighRiskOperations(startTime);
    }

    /**
     * 获取操作类型统计
     */
    public List<Map<String, Object>> getOperationTypeStatistics(final LocalDateTime startTime) {
        List<Object[]> results = auditLogRepository.statisticsByOperationType(startTime);
        List<Map<String, Object>> statistics = new ArrayList<>();
        
        for (Object[] result : results) {
            Map<String, Object> stat = Map.of(
                "operationType", result[0],
                "count", result[1]
            );
            statistics.add(stat);
        }
        
        return statistics;
    }

    /**
     * 获取资源类型统计
     */
    public List<Map<String, Object>> getResourceTypeStatistics(final LocalDateTime startTime) {
        List<Object[]> results = auditLogRepository.statisticsByResourceType(startTime);
        List<Map<String, Object>> statistics = new ArrayList<>();
        
        for (Object[] result : results) {
            Map<String, Object> stat = Map.of(
                "resourceType", result[0],
                "count", result[1]
            );
            statistics.add(stat);
        }
        
        return statistics;
    }

    /**
     * 获取用户操作统计
     */
    public List<Map<String, Object>> getUserOperationStatistics(final LocalDateTime startTime, 
                                                               final Pageable pageable) {
        List<Object[]> results = auditLogRepository.statisticsByUser(startTime, pageable);
        List<Map<String, Object>> statistics = new ArrayList<>();
        
        for (Object[] result : results) {
            Map<String, Object> stat = Map.of(
                "userId", result[0],
                "userName", result[1],
                "count", result[2]
            );
            statistics.add(stat);
        }
        
        return statistics;
    }

    /**
     * 获取异常IP操作统计
     */
    public List<Map<String, Object>> getSuspiciousIpOperations(final LocalDateTime startTime, 
                                                              final Integer threshold) {
        List<Object[]> results = auditLogRepository.findSuspiciousIpOperations(startTime, threshold);
        List<Map<String, Object>> statistics = new ArrayList<>();
        
        for (Object[] result : results) {
            Map<String, Object> stat = Map.of(
                "clientIp", result[0],
                "count", result[1]
            );
            statistics.add(stat);
        }
        
        return statistics;
    }

    /**
     * 获取用户异常操作统计
     */
    public List<Map<String, Object>> getUsersWithExcessiveFailures(final LocalDateTime startTime, 
                                                                  final Integer threshold) {
        List<Object[]> results = auditLogRepository.findUsersWithExcessiveFailures(startTime, threshold);
        List<Map<String, Object>> statistics = new ArrayList<>();
        
        for (Object[] result : results) {
            Map<String, Object> stat = Map.of(
                "userId", result[0],
                "userName", result[1],
                "failureCount", result[2]
            );
            statistics.add(stat);
        }
        
        return statistics;
    }

    /**
     * 清理过期的审计日志
     */
    @Transactional
    public void cleanupExpiredLogs(final LocalDateTime beforeTime) {
        try {
            auditLogRepository.deleteByOperationTimeBefore(beforeTime);
            log.info("清理审计日志成功，清理时间点之前: {}", beforeTime);
        } catch (Exception e) {
            log.error("清理审计日志失败", e);
        }
    }

    /**
     * 填充请求信息
     */
    private void fillRequestInfo(final AuditLog auditLog) {
        try {
            ServletRequestAttributes attributes = 
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                
                // 设置IP地址
                auditLog.setClientIp(getClientIpAddress(request));
                
                // 设置User-Agent
                auditLog.setUserAgent(request.getHeader("User-Agent"));
                
                // 设置请求URI
                auditLog.setRequestUri(request.getRequestURI());
                
                // 设置请求方法
                auditLog.setRequestMethod(request.getMethod());
                
                // 设置会话ID
                if (request.getSession(false) != null) {
                    auditLog.setSessionId(request.getSession().getId());
                }
            }
        } catch (Exception e) {
            log.warn("填充请求信息失败", e);
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(final HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (StringUtils.hasText(ip) && ip.contains(",")) {
            ip = ip.substring(0, ip.indexOf(",")).trim();
        }
        
        return ip;
    }

    /**
     * 构建查询规范
     */
    private Specification<AuditLog> buildSpecification(final AuditLogQueryParams queryParams) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.hasText(queryParams.getUserId())) {
                predicates.add(criteriaBuilder.equal(root.get("userId"), queryParams.getUserId()));
            }

            if (StringUtils.hasText(queryParams.getTenantId())) {
                predicates.add(criteriaBuilder.equal(root.get("tenantId"), queryParams.getTenantId()));
            }

            if (StringUtils.hasText(queryParams.getResourceType())) {
                predicates.add(criteriaBuilder.equal(root.get("resourceType"), queryParams.getResourceType()));
            }

            if (StringUtils.hasText(queryParams.getResourceId())) {
                predicates.add(criteriaBuilder.equal(root.get("resourceId"), queryParams.getResourceId()));
            }

            if (queryParams.getOperationType() != null) {
                predicates.add(criteriaBuilder.equal(root.get("operationType"), queryParams.getOperationType()));
            }

            if (queryParams.getOperationResult() != null) {
                predicates.add(criteriaBuilder.equal(root.get("operationResult"), queryParams.getOperationResult()));
            }

            if (queryParams.getRiskLevel() != null) {
                predicates.add(criteriaBuilder.equal(root.get("riskLevel"), queryParams.getRiskLevel()));
            }

            if (queryParams.getSensitive() != null) {
                predicates.add(criteriaBuilder.equal(root.get("sensitive"), queryParams.getSensitive()));
            }

            if (StringUtils.hasText(queryParams.getBusinessModule())) {
                predicates.add(criteriaBuilder.equal(root.get("businessModule"), queryParams.getBusinessModule()));
            }

            if (StringUtils.hasText(queryParams.getClientIp())) {
                predicates.add(criteriaBuilder.equal(root.get("clientIp"), queryParams.getClientIp()));
            }

            if (StringUtils.hasText(queryParams.getKeyword())) {
                String keyword = "%" + queryParams.getKeyword() + "%";
                Predicate operationDescPredicate = criteriaBuilder.like(root.get("operationDescription"), keyword);
                Predicate userNamePredicate = criteriaBuilder.like(root.get("userName"), keyword);
                Predicate resourceNamePredicate = criteriaBuilder.like(root.get("resourceName"), keyword);
                predicates.add(criteriaBuilder.or(operationDescPredicate, userNamePredicate, resourceNamePredicate));
            }

            if (queryParams.getStartTime() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("operationTime"), queryParams.getStartTime()));
            }

            if (queryParams.getEndTime() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("operationTime"), queryParams.getEndTime()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 审计日志查询参数类
     */
    public static class AuditLogQueryParams {
        private String userId;
        private String tenantId;
        private String resourceType;
        private String resourceId;
        private AuditLog.OperationType operationType;
        private AuditLog.OperationResult operationResult;
        private AuditLog.RiskLevel riskLevel;
        private Boolean sensitive;
        private String businessModule;
        private String clientIp;
        private String keyword;
        private LocalDateTime startTime;
        private LocalDateTime endTime;

        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(final String userId) { this.userId = userId; }

        public String getTenantId() { return tenantId; }
        public void setTenantId(final String tenantId) { this.tenantId = tenantId; }

        public String getResourceType() { return resourceType; }
        public void setResourceType(final String resourceType) { this.resourceType = resourceType; }

        public String getResourceId() { return resourceId; }
        public void setResourceId(final String resourceId) { this.resourceId = resourceId; }

        public AuditLog.OperationType getOperationType() { return operationType; }
        public void setOperationType(final AuditLog.OperationType operationType) { this.operationType = operationType; }

        public AuditLog.OperationResult getOperationResult() { return operationResult; }
        public void setOperationResult(final AuditLog.OperationResult operationResult) { this.operationResult = operationResult; }

        public AuditLog.RiskLevel getRiskLevel() { return riskLevel; }
        public void setRiskLevel(final AuditLog.RiskLevel riskLevel) { this.riskLevel = riskLevel; }

        public Boolean getSensitive() { return sensitive; }
        public void setSensitive(final Boolean sensitive) { this.sensitive = sensitive; }

        public String getBusinessModule() { return businessModule; }
        public void setBusinessModule(final String businessModule) { this.businessModule = businessModule; }

        public String getClientIp() { return clientIp; }
        public void setClientIp(final String clientIp) { this.clientIp = clientIp; }

        public String getKeyword() { return keyword; }
        public void setKeyword(final String keyword) { this.keyword = keyword; }

        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(final LocalDateTime startTime) { this.startTime = startTime; }

        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(final LocalDateTime endTime) { this.endTime = endTime; }
    }
}