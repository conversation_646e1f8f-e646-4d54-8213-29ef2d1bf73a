package com.longge.assessment.service;

import com.longge.assessment.config.BusinessMetrics;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 业务监控服务
 * 定期更新业务指标，监控关键业务数据
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessMonitoringService {

    private final BusinessMetrics businessMetrics;
    
    // 注入各种Repository或Service来获取实际数据
    // private final AssessmentRepository assessmentRepository;
    // private final ElderlyRepository elderlyRepository;
    // private final ScaleRepository scaleRepository;
    // private final UserRepository userRepository;

    /**
     * 定期更新业务指标
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void updateBusinessMetrics() {
        try {
            log.debug("开始更新业务监控指标...");
            
            // 更新总计指标
            updateTotalCounts();
            
            // 更新状态指标
            updateStatusCounts();
            
            // 更新系统指标
            updateSystemMetrics();
            
            log.debug("业务监控指标更新完成");
        } catch (Exception e) {
            log.error("更新业务监控指标失败", e);
            businessMetrics.incrementSystemErrors();
        }
    }

    /**
     * 更新总计指标
     */
    private void updateTotalCounts() {
        try {
            // 模拟数据 - 实际项目中应该从数据库获取
            // long totalElderly = elderlyRepository.count();
            // long totalAssessments = assessmentRepository.count();
            // long totalScales = scaleRepository.count();
            
            // 模拟数据
            long totalElderly = 150L;
            long totalAssessments = 320L;
            long totalScales = 25L;
            
            businessMetrics.setTotalElderly(totalElderly);
            businessMetrics.setTotalAssessments(totalAssessments);
            businessMetrics.setTotalScales(totalScales);
            
        } catch (Exception e) {
            log.error("更新总计指标失败", e);
        }
    }

    /**
     * 更新状态指标
     */
    private void updateStatusCounts() {
        try {
            // 模拟数据 - 实际项目中应该从数据库获取
            // long pendingAssessments = assessmentRepository.countByStatus("PENDING");
            // long completedAssessments = assessmentRepository.countByStatus("COMPLETED");
            // long activeUsers = userRepository.countActiveUsers();
            
            // 模拟数据
            long pendingAssessments = 45L;
            long completedAssessments = 275L;
            long activeUsers = 25L;
            
            businessMetrics.setPendingAssessments(pendingAssessments);
            businessMetrics.setCompletedAssessments(completedAssessments);
            businessMetrics.setActiveUsers(activeUsers);
            
        } catch (Exception e) {
            log.error("更新状态指标失败", e);
        }
    }

    /**
     * 更新系统指标
     */
    private void updateSystemMetrics() {
        try {
            // 获取系统内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            // 计算内存使用率（百分比）
            long memoryUsagePercent = (usedMemory * 100) / maxMemory;
            businessMetrics.setMemoryUsage(memoryUsagePercent);
            
            // CPU使用率需要更复杂的计算，这里简化处理
            // 实际项目中可以使用 OperatingSystemMXBean 或其他工具
            long cpuUsage = getCurrentCpuUsage();
            businessMetrics.setCpuUsage(cpuUsage);
            
        } catch (Exception e) {
            log.error("更新系统指标失败", e);
        }
    }

    /**
     * 获取当前CPU使用率（简化版本）
     */
    private long getCurrentCpuUsage() {
        try {
            // 这是一个简化的CPU使用率计算
            // 实际项目中应该使用更准确的方法
            java.lang.management.OperatingSystemMXBean osBean = 
                java.lang.management.ManagementFactory.getOperatingSystemMXBean();
            
            if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsBean = 
                    (com.sun.management.OperatingSystemMXBean) osBean;
                double cpuUsage = sunOsBean.getProcessCpuLoad() * 100;
                return Math.round(cpuUsage);
            }
            
            // 如果无法获取，返回默认值
            return 0L;
        } catch (Exception e) {
            log.warn("获取CPU使用率失败", e);
            return 0L;
        }
    }

    /**
     * 记录用户登录成功
     */
    public void recordUserLoginSuccess(final String userId) {
        businessMetrics.recordLoginSuccess();
        businessMetrics.incrementActiveUsers();
        log.debug("记录用户登录成功: {}", userId);
    }

    /**
     * 记录用户登录失败
     */
    public void recordUserLoginFailure(final String userId, final String reason) {
        businessMetrics.recordLoginFailure();
        log.debug("记录用户登录失败: {}, 原因: {}", userId, reason);
    }

    /**
     * 记录用户登出
     */
    public void recordUserLogout(final String userId) {
        businessMetrics.decrementActiveUsers();
        log.debug("记录用户登出: {}", userId);
    }

    /**
     * 记录评估创建
     */
    public void recordAssessmentCreated(final String assessmentId) {
        businessMetrics.recordAssessmentCreated();
        log.debug("记录创建评估: {}", assessmentId);
    }

    /**
     * 记录评估完成
     */
    public void recordAssessmentCompleted(final String assessmentId, final long durationMs) {
        businessMetrics.recordAssessmentCompleted();
        
        // 记录评估完成耗时
        businessMetrics.recordTimerWithTags(
            "assessment.completion.duration",
            "评估完成耗时",
            durationMs,
            "assessment.id", assessmentId
        );
        
        log.debug("记录完成评估: {}, 耗时: {}ms", assessmentId, durationMs);
    }

    /**
     * 记录老人注册
     */
    public void recordElderlyRegistration(final String elderlyId) {
        businessMetrics.recordElderlyRegistered();
        log.debug("记录老人注册: {}", elderlyId);
    }

    /**
     * 记录量表使用
     */
    public void recordScaleUsage(final String scaleId) {
        businessMetrics.recordScaleUsed();
        log.debug("记录量表使用: {}", scaleId);
    }

    /**
     * 记录API调用
     */
    public void recordApiCall(final String endpoint, final long responseTimeMs, final boolean success) {
        // 记录API响应时间
        businessMetrics.recordTimerWithTags(
            "api.response.duration",
            "API响应时间",
            responseTimeMs,
            "endpoint", endpoint,
            "success", String.valueOf(success)
        );
        
        // 如果调用失败，记录错误
        if (!success) {
            businessMetrics.recordApiError();
        }
        
        log.debug("记录API调用: {}, 耗时: {}ms, 成功: {}", endpoint, responseTimeMs, success);
    }

    /**
     * 记录PDF处理
     */
    public void recordPdfProcessing(final String fileName, final long processingTimeMs, final boolean success) {
        // 记录PDF处理时间
        businessMetrics.recordTimerWithTags(
            "pdf.processing.duration",
            "PDF处理耗时",
            processingTimeMs,
            "file", fileName,
            "success", String.valueOf(success)
        );
        
        log.debug("记录PDF处理: {}, 耗时: {}ms, 成功: {}", fileName, processingTimeMs, success);
    }

    /**
     * 记录导出操作
     */
    public void recordExportOperation(final String exportType, final long duration, final int recordCount) {
        businessMetrics.recordExportOperation();
        
        // 记录导出详细信息
        businessMetrics.recordTimerWithTags(
            "export.operation.duration",
            "导出操作耗时",
            duration,
            "type", exportType,
            "record.count", String.valueOf(recordCount)
        );
        
        log.debug("记录导出操作: 类型={}, 记录数={}, 耗时={}ms", exportType, recordCount, duration);
    }

    /**
     * 记录导入操作
     */
    public void recordImportOperation(final String importType, final long duration, 
                                    final int successCount, final int failureCount) {
        businessMetrics.recordImportOperation();
        
        // 记录导入详细信息
        businessMetrics.recordTimerWithTags(
            "import.operation.duration",
            "导入操作耗时",
            duration,
            "type", importType,
            "success.count", String.valueOf(successCount),
            "failure.count", String.valueOf(failureCount)
        );
        
        log.debug("记录导入操作: 类型={}, 成功={}, 失败={}, 耗时={}ms", 
                 importType, successCount, failureCount, duration);
    }

    /**
     * 记录数据库操作
     */
    public void recordDatabaseOperation(final String operation, final long durationMs) {
        businessMetrics.recordTimerWithTags(
            "database.operation.duration",
            "数据库操作耗时",
            durationMs,
            "operation", operation
        );
        
        log.debug("记录数据库操作: {}, 耗时: {}ms", operation, durationMs);
    }

    /**
     * 记录系统错误
     */
    public void recordSystemError(final String errorType, final String errorMessage) {
        businessMetrics.incrementSystemErrors();
        
        // 记录错误详细信息
        businessMetrics.recordCounterWithTags(
            "system.error.details",
            "系统错误详情",
            "error.type", errorType,
            "error.message", errorMessage
        );
        
        log.error("记录系统错误: 类型={}, 消息={}", errorType, errorMessage);
    }

    /**
     * 开始计时
     */
    public Timer.Sample startTimer() {
        return businessMetrics.startApiTimer();
    }

    /**
     * 停止计时并记录API响应时间
     */
    public void stopTimer(final Timer.Sample sample, final String endpoint, final boolean success) {
        businessMetrics.stopApiTimer(sample);
        recordApiCall(endpoint, 0, success); // 实际耗时已经在stopApiTimer中记录
    }

    /**
     * 获取业务指标摘要
     */
    public BusinessMetricsSummary getBusinessMetricsSummary() {
        return BusinessMetricsSummary.builder()
                .totalElderly(150L) // 实际项目中从指标或数据库获取
                .totalAssessments(320L)
                .totalScales(25L)
                .pendingAssessments(45L)
                .completedAssessments(275L)
                .activeUsers(25L)
                .systemErrors(businessMetrics.getMeterRegistry()
                        .get("system.error.count").gauge().value())
                .memoryUsage(businessMetrics.getMeterRegistry()
                        .get("system.memory.usage").gauge().value())
                .cpuUsage(businessMetrics.getMeterRegistry()
                        .get("system.cpu.usage").gauge().value())
                .build();
    }

    /**
     * 业务指标摘要类
     */
    public static class BusinessMetricsSummary {
        private long totalElderly;
        private long totalAssessments;
        private long totalScales;
        private long pendingAssessments;
        private long completedAssessments;
        private long activeUsers;
        private double systemErrors;
        private double memoryUsage;
        private double cpuUsage;

        // Builder模式
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private final BusinessMetricsSummary summary = new BusinessMetricsSummary();

            public Builder totalElderly(final long totalElderly) {
                summary.totalElderly = totalElderly;
                return this;
            }

            public Builder totalAssessments(final long totalAssessments) {
                summary.totalAssessments = totalAssessments;
                return this;
            }

            public Builder totalScales(final long totalScales) {
                summary.totalScales = totalScales;
                return this;
            }

            public Builder pendingAssessments(final long pendingAssessments) {
                summary.pendingAssessments = pendingAssessments;
                return this;
            }

            public Builder completedAssessments(final long completedAssessments) {
                summary.completedAssessments = completedAssessments;
                return this;
            }

            public Builder activeUsers(final long activeUsers) {
                summary.activeUsers = activeUsers;
                return this;
            }

            public Builder systemErrors(final double systemErrors) {
                summary.systemErrors = systemErrors;
                return this;
            }

            public Builder memoryUsage(final double memoryUsage) {
                summary.memoryUsage = memoryUsage;
                return this;
            }

            public Builder cpuUsage(final double cpuUsage) {
                summary.cpuUsage = cpuUsage;
                return this;
            }

            public BusinessMetricsSummary build() {
                return summary;
            }
        }

        // Getters
        public long getTotalElderly() { return totalElderly; }
        public long getTotalAssessments() { return totalAssessments; }
        public long getTotalScales() { return totalScales; }
        public long getPendingAssessments() { return pendingAssessments; }
        public long getCompletedAssessments() { return completedAssessments; }
        public long getActiveUsers() { return activeUsers; }
        public double getSystemErrors() { return systemErrors; }
        public double getMemoryUsage() { return memoryUsage; }
        public double getCpuUsage() { return cpuUsage; }
    }
}