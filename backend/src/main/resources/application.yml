spring:
  application:
    name: assessment-platform

  profiles:
    active: local

  config:
    import:
      - optional:classpath:lm-studio-config.yml

  datasource:
    url: *******************************************************
    username: assessment_user
    password: ${DB_PASSWORD:assessment123}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000

  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        show-sql: true

  data:
    redis:
      host: localhost
      port: 6379
      password: ${REDIS_PASSWORD:redis123}
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: -1ms

  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1小时
      cache-null-values: false

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB

server:
  port: 8181
  compression:
    enabled: true
    min-response-size: 1024

# MinIO配置
minio:
  endpoint: ${MINIO_ENDPOINT:http://localhost:9000}
  access-key: ${MINIO_ACCESS_KEY:minioadmin}
  secret-key: ${MINIO_SECRET_KEY:minioadmin}
  bucket-name: assessment-files
  secure: false

# JWT配置
jwt:
  secret: ${JWT_SECRET:Ma4WhNJ/qBcz8b/VzxJN1CfFYkfVIIEys5+u1D8Y8CM=}
  expiration: 86400000 # 24小时
  refresh-expiration: 604800000 # 7天

# 日志配置
logging:
  level:
    root: INFO
    "[com.assessment]": DEBUG
    "[org.springframework.web]": DEBUG
    "[org.springframework.security]": DEBUG
    "[org.hibernate.SQL]": DEBUG
    "[org.hibernate.type.descriptor.sql.BasicBinder]": TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/assessment.log
    max-size: 100MB
    max-history: 30

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  prometheus:
    metrics:
      export:
        enabled: true

# API文档配置
springdoc:
  api-docs:
    enabled: true
    path: /api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha

# 业务配置
assessment:
  # 评估相关配置
  evaluation:
    max-duration: 120 # 最大评估时长（分钟）
    auto-save-interval: 30 # 自动保存间隔（秒）

  # 文件上传配置
  upload:
    allowed-types:
      - image/jpeg
      - image/png
      - image/gif
      - application/pdf
    max-size: 10485760 # 10MB

  # 安全配置
  security:
    password-min-length: 8
    password-require-special: true
    max-login-attempts: 5
    lock-duration: 1800 # 30分钟

  # 评估阈值配置 - 患者安全关键参数
  threshold:
    # 字段映射置信度阈值
    field-mapping-overall-confidence: 0.85
    name-field-confidence: 0.95
    age-field-confidence: 0.90
    rating-field-confidence: 0.88

    # 评估权重配置
    daily-living-ability-weight: 0.3

    # 数值范围配置
    max-age: 120.0
    rating-min-value: 1.0
    rating-max-value: 5.0

# Docling PDF转换服务配置
docling:
  service:
    url: ${DOCLING_SERVICE_URL:http://localhost:8088}
    timeout: ${DOCLING_SERVICE_TIMEOUT:60}
    enabled: ${DOCLING_SERVICE_ENABLED:true}

# AI分析服务配置
ai:
  lmstudio:
    url: ${AI_LMSTUDIO_URL:http://*************:1234}
    model: ${AI_LMSTUDIO_MODEL:deepseek/deepseek-r1-0528-qwen3-8b}
  analysis:
    timeout: ${AI_ANALYSIS_TIMEOUT:600000}
    max-content-length: 100000
    enabled: ${AI_ANALYSIS_ENABLED:true}

# 自研滑动验证码配置
captcha:
  simple:
    # 验证码过期时间（分钟）
    expire-minutes: 5
    # 滑动误差容忍度（像素）
    tolerance: 5
    # 图片尺寸配置
    image:
      width: 310
      height: 155
      piece-width: 47
      piece-height: 47
