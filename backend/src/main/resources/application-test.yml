# 测试环境配置 - 基于生产优化但保持本地运行
# 用于性能测试，不影响开发环境
spring:
  application:
    name: assessment-platform-test

  datasource:
    url: *******************************************************
    username: assessment_user
    password: ${DB_PASSWORD:assessment123}
    hikari:
      # 优化的连接池配置
      minimum-idle: 10
      maximum-pool-size: 30  # 从50降至30
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000
      leak-detection-threshold: 60000
      connection-test-query: SELECT 1

  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        '[format_sql]': false
        '[show_sql]': false
        jdbc:
          '[batch_size]': 25
          '[fetch_size]': 100
        '[order_inserts]': true
        '[order_updates]': true
        cache:
          '[use_second_level_cache]': false
          '[use_query_cache]': false

  data:
    redis:
      host: localhost
      port: 6379
      password: redis123
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 50
          max-idle: 20
          min-idle: 10
          max-wait: 5000ms

  cache:
    type: redis
    redis:
      time-to-live: 3600000
      cache-null-values: false

server:
  port: 8181  # 保持当前端口
  compression:
    enabled: true
    min-response-size: 1024
  tomcat:
    threads:
      max: 200
      min-spare: 25
    max-connections: 10000
    accept-count: 200

# 保持其他配置与local相同
minio:
  endpoint: http://localhost:9000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: assessment-files
  secure: false

jwt:
  secret: ${JWT_SECRET:D0fJov+robFjgi6kSig+WmQkxqaML8T3jES2YdhJJyM=}
  expiration: 86400000
  refresh-expiration: 604800000

logging:
  level:
    root: WARN
    '[com.assessment]': INFO
    '[org.springframework]': WARN
    '[org.hibernate]': WARN

# 启用性能监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always