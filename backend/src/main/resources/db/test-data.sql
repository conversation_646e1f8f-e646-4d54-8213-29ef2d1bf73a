-- 测试数据生成脚本
-- 用于创建多租户系统的测试机构和用户数据
-- 创建日期：2025-06-24

-- 清理现有测试数据（可选）
-- DELETE FROM tenant_user_memberships WHERE tenant_id IN (SELECT id FROM tenants WHERE code LIKE 'TEST_%');
-- DELETE FROM tenant_hierarchies WHERE parent_tenant_id IN (SELECT id FROM tenants WHERE code LIKE 'TEST_%');
-- DELETE FROM tenants WHERE code LIKE 'TEST_%';
-- DELETE FROM platform_users WHERE username LIKE 'test_%';
-- DELETE FROM individual_users WHERE email LIKE '%@example.com' OR email LIKE '%@test.%';

-- ========================================
-- 1. 创建租户（机构）
-- ========================================

-- 省级总部机构
INSERT INTO tenants (id, code, name, industry, hierarchy_level, subscription_plan, is_active, created_at) VALUES
('10000001-0000-0000-0000-000000000001', 'SH_HQ', '上海长护评估管理中心', '政府机构', 1, 'ENTERPRISE', true, CURRENT_TIMESTAMP),
('10000002-0000-0000-0000-000000000001', 'HN_HQ', '海南健康评估总部', '政府机构', 1, 'ENTERPRISE', true, CURRENT_TIMESTAMP),
('10000003-0000-0000-0000-000000000001', 'HB_HQ', '湖北省护理评估中心', '政府机构', 1, 'ENTERPRISE', true, CURRENT_TIMESTAMP);

-- 上海市级机构
INSERT INTO tenants (id, code, name, industry, hierarchy_level, subscription_plan, is_active, created_at) VALUES
('20000001-0000-0000-0000-000000000001', 'SH_PD', '浦东新区评估中心', '政府机构', 2, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('20000002-0000-0000-0000-000000000001', 'SH_XH', '徐汇区康复评估中心', '政府机构', 2, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('20000003-0000-0000-0000-000000000001', 'SH_JA', '静安区养老评估站', '政府机构', 2, 'PROFESSIONAL', true, CURRENT_TIMESTAMP);

-- 海南市级机构
INSERT INTO tenants (id, code, name, industry, hierarchy_level, subscription_plan, is_active, created_at) VALUES
('20000004-0000-0000-0000-000000000001', 'HN_HK', '海口市评估分中心', '政府机构', 2, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('20000005-0000-0000-0000-000000000001', 'HN_SY', '三亚市健康评估站', '政府机构', 2, 'PROFESSIONAL', true, CURRENT_TIMESTAMP);

-- 湖北市级机构
INSERT INTO tenants (id, code, name, industry, hierarchy_level, subscription_plan, is_active, created_at) VALUES
('20000006-0000-0000-0000-000000000001', 'HB_WH', '武汉市评估总站', '政府机构', 2, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('20000007-0000-0000-0000-000000000001', 'HB_YC', '宜昌市评估分中心', '政府机构', 2, 'BASIC', true, CURRENT_TIMESTAMP);

-- 区级机构
INSERT INTO tenants (id, code, name, industry, hierarchy_level, subscription_plan, is_active, created_at) VALUES
('30000001-0000-0000-0000-000000000001', 'SH_PD_LJZ', '陆家嘴街道评估点', '政府机构', 3, 'BASIC', true, CURRENT_TIMESTAMP),
('30000002-0000-0000-0000-000000000001', 'SH_PD_ZJ', '张江镇评估服务站', '政府机构', 3, 'BASIC', true, CURRENT_TIMESTAMP),
('30000003-0000-0000-0000-000000000001', 'SH_XH_XJH', '徐家汇评估服务点', '政府机构', 3, 'BASIC', true, CURRENT_TIMESTAMP);

-- 医疗机构
INSERT INTO tenants (id, code, name, industry, hierarchy_level, subscription_plan, is_active, created_at) VALUES
('40000001-0000-0000-0000-000000000001', 'HOSP_RJ', '上海瑞金医院', '医疗机构', 1, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('40000002-0000-0000-0000-000000000001', 'HOSP_HS', '华山医院康复科', '医疗机构', 1, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('40000003-0000-0000-0000-000000000001', 'HOSP_HNRM', '海南省人民医院', '医疗机构', 1, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('40000004-0000-0000-0000-000000000001', 'HOSP_TJ', '武汉同济医院', '医疗机构', 1, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('40000005-0000-0000-0000-000000000001', 'HOSP_SQWS', '社区卫生服务中心', '医疗机构', 2, 'BASIC', true, CURRENT_TIMESTAMP);

-- 养老机构
INSERT INTO tenants (id, code, name, industry, hierarchy_level, subscription_plan, is_active, created_at) VALUES
('50000001-0000-0000-0000-000000000001', 'CARE_FSK', '上海福寿康养老院', '养老机构', 1, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('50000002-0000-0000-0000-000000000001', 'CARE_CXM', '椿萱茂养老社区', '养老机构', 1, 'ENTERPRISE', true, CURRENT_TIMESTAMP),
('50000003-0000-0000-0000-000000000001', 'CARE_XYH', '海南夕阳红养护院', '养老机构', 2, 'BASIC', true, CURRENT_TIMESTAMP),
('50000004-0000-0000-0000-000000000001', 'CARE_XFZJ', '武汉幸福之家', '养老机构', 2, 'BASIC', true, CURRENT_TIMESTAMP),
('50000005-0000-0000-0000-000000000001', 'CARE_YG', '阳光护理院', '养老机构', 3, 'BASIC', true, CURRENT_TIMESTAMP);

-- 保险公司
INSERT INTO tenants (id, code, name, industry, hierarchy_level, subscription_plan, is_active, created_at) VALUES
('60000001-0000-0000-0000-000000000001', 'INS_ZGRS', '中国人寿保险', '保险公司', 1, 'ENTERPRISE', true, CURRENT_TIMESTAMP),
('60000002-0000-0000-0000-000000000001', 'INS_TPYB', '太平洋保险', '保险公司', 1, 'ENTERPRISE', true, CURRENT_TIMESTAMP),
('60000003-0000-0000-0000-000000000001', 'INS_TKRS', '泰康人寿', '保险公司', 1, 'PROFESSIONAL', true, CURRENT_TIMESTAMP),
('60000004-0000-0000-0000-000000000001', 'INS_PAJK', '平安健康险', '保险公司', 1, 'PROFESSIONAL', true, CURRENT_TIMESTAMP);

-- ========================================
-- 2. 创建租户层级关系
-- ========================================

-- 上海层级
INSERT INTO tenant_hierarchies (parent_tenant_id, child_tenant_id, hierarchy_path, created_at) VALUES
('10000001-0000-0000-0000-000000000001', '20000001-0000-0000-0000-000000000001', '/SH_HQ/SH_PD', CURRENT_TIMESTAMP),
('10000001-0000-0000-0000-000000000001', '20000002-0000-0000-0000-000000000001', '/SH_HQ/SH_XH', CURRENT_TIMESTAMP),
('10000001-0000-0000-0000-000000000001', '20000003-0000-0000-0000-000000000001', '/SH_HQ/SH_JA', CURRENT_TIMESTAMP),
('20000001-0000-0000-0000-000000000001', '30000001-0000-0000-0000-000000000001', '/SH_HQ/SH_PD/SH_PD_LJZ', CURRENT_TIMESTAMP),
('20000001-0000-0000-0000-000000000001', '30000002-0000-0000-0000-000000000001', '/SH_HQ/SH_PD/SH_PD_ZJ', CURRENT_TIMESTAMP),
('20000002-0000-0000-0000-000000000001', '30000003-0000-0000-0000-000000000001', '/SH_HQ/SH_XH/SH_XH_XJH', CURRENT_TIMESTAMP);

-- 海南层级
INSERT INTO tenant_hierarchies (parent_tenant_id, child_tenant_id, hierarchy_path, created_at) VALUES
('10000002-0000-0000-0000-000000000001', '20000004-0000-0000-0000-000000000001', '/HN_HQ/HN_HK', CURRENT_TIMESTAMP),
('10000002-0000-0000-0000-000000000001', '20000005-0000-0000-0000-000000000001', '/HN_HQ/HN_SY', CURRENT_TIMESTAMP);

-- 湖北层级
INSERT INTO tenant_hierarchies (parent_tenant_id, child_tenant_id, hierarchy_path, created_at) VALUES
('10000003-0000-0000-0000-000000000001', '20000006-0000-0000-0000-000000000001', '/HB_HQ/HB_WH', CURRENT_TIMESTAMP),
('10000003-0000-0000-0000-000000000001', '20000007-0000-0000-0000-000000000001', '/HB_HQ/HB_YC', CURRENT_TIMESTAMP);

-- ========================================
-- 3. 创建平台用户（机构用户）
-- ========================================

-- 密码都是 Test@123 的bcrypt hash: $2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO

-- 省级管理员
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('11000001-0000-0000-0000-000000000001', 'sh_admin', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '张', '明华', 'user', true, CURRENT_TIMESTAMP),
('11000002-0000-0000-0000-000000000001', 'hn_admin', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '李', '海南', 'user', true, CURRENT_TIMESTAMP),
('11000003-0000-0000-0000-000000000001', 'hb_admin', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '王', '武汉', 'user', true, CURRENT_TIMESTAMP);

-- 市级用户
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('12000001-0000-0000-0000-000000000001', 'pd_admin', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '陈', '浦东', 'user', true, CURRENT_TIMESTAMP),
('12000002-0000-0000-0000-000000000001', 'pd_assessor', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '赵', '评估', 'user', true, CURRENT_TIMESTAMP),
('12000003-0000-0000-0000-000000000001', 'hk_manager', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '刘', '海口', 'user', true, CURRENT_TIMESTAMP),
('12000004-0000-0000-0000-000000000001', 'wh_reviewer', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '周', '审核', 'user', true, CURRENT_TIMESTAMP);

-- 医疗机构用户
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('13000001-0000-0000-0000-000000000001', 'rj_doctor', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '王', '医生', 'user', true, CURRENT_TIMESTAMP),
('13000002-0000-0000-0000-000000000001', 'rj_nurse', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '李', '护士', 'user', true, CURRENT_TIMESTAMP),
('13000003-0000-0000-0000-000000000001', 'hs_admin', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '张', '院长', 'user', true, CURRENT_TIMESTAMP),
('13000004-0000-0000-0000-000000000001', 'tj_assessor', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '刘', '评估师', 'user', true, CURRENT_TIMESTAMP);

-- 养老机构用户
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('14000001-0000-0000-0000-000000000001', 'fsk_admin', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '孙', '院长', 'user', true, CURRENT_TIMESTAMP),
('14000002-0000-0000-0000-000000000001', 'fsk_nurse', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '钱', '护理', 'user', true, CURRENT_TIMESTAMP),
('14000003-0000-0000-0000-000000000001', 'cxm_manager', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '吴', '经理', 'user', true, CURRENT_TIMESTAMP),
('14000004-0000-0000-0000-000000000001', 'xyh_assessor', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '郑', '评估', 'user', true, CURRENT_TIMESTAMP);

-- 保险公司用户
INSERT INTO platform_users (id, username, email, password_hash, first_name, last_name, platform_role, is_active, created_at) VALUES
('15000001-0000-0000-0000-000000000001', 'zgrs_manager', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '冯', '经理', 'user', true, CURRENT_TIMESTAMP),
('15000002-0000-0000-0000-000000000001', 'zgrs_auditor', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '陈', '审核', 'user', true, CURRENT_TIMESTAMP),
('15000003-0000-0000-0000-000000000001', 'tpy_viewer', '<EMAIL>', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', '杨', '查看', 'user', true, CURRENT_TIMESTAMP);

-- ========================================
-- 4. 创建租户用户关系
-- ========================================

-- 省级管理员
INSERT INTO tenant_user_memberships (tenant_id, user_id, tenant_role, display_name, department, is_active, joined_at) VALUES
('10000001-0000-0000-0000-000000000001', '11000001-0000-0000-0000-000000000001', 'ADMIN', '张明华', '管理部', true, CURRENT_TIMESTAMP),
('10000002-0000-0000-0000-000000000001', '11000002-0000-0000-0000-000000000001', 'ADMIN', '李海南', '管理部', true, CURRENT_TIMESTAMP),
('10000003-0000-0000-0000-000000000001', '11000003-0000-0000-0000-000000000001', 'ADMIN', '王武汉', '管理部', true, CURRENT_TIMESTAMP);

-- 市级用户
INSERT INTO tenant_user_memberships (tenant_id, user_id, tenant_role, display_name, department, is_active, joined_at) VALUES
('20000001-0000-0000-0000-000000000001', '12000001-0000-0000-0000-000000000001', 'ADMIN', '陈浦东', '管理部', true, CURRENT_TIMESTAMP),
('20000001-0000-0000-0000-000000000001', '12000002-0000-0000-0000-000000000001', 'ASSESSOR', '赵评估', '评估部', true, CURRENT_TIMESTAMP),
('20000004-0000-0000-0000-000000000001', '12000003-0000-0000-0000-000000000001', 'ADMIN', '刘海口', '管理部', true, CURRENT_TIMESTAMP),
('20000006-0000-0000-0000-000000000001', '12000004-0000-0000-0000-000000000001', 'REVIEWER', '周审核', '审核部', true, CURRENT_TIMESTAMP);

-- 医疗机构用户
INSERT INTO tenant_user_memberships (tenant_id, user_id, tenant_role, display_name, department, professional_title, is_active, joined_at) VALUES
('40000001-0000-0000-0000-000000000001', '13000001-0000-0000-0000-000000000001', 'ASSESSOR', '王医生', '康复科', '主治医师', true, CURRENT_TIMESTAMP),
('40000001-0000-0000-0000-000000000001', '13000002-0000-0000-0000-000000000001', 'ASSESSOR', '李护士', '护理部', '护师', true, CURRENT_TIMESTAMP),
('40000002-0000-0000-0000-000000000001', '13000003-0000-0000-0000-000000000001', 'ADMIN', '张院长', '院办', '院长', true, CURRENT_TIMESTAMP),
('40000004-0000-0000-0000-000000000001', '13000004-0000-0000-0000-000000000001', 'ASSESSOR', '刘评估师', '评估科', '评估师', true, CURRENT_TIMESTAMP);

-- 养老机构用户
INSERT INTO tenant_user_memberships (tenant_id, user_id, tenant_role, display_name, department, professional_title, is_active, joined_at) VALUES
('50000001-0000-0000-0000-000000000001', '14000001-0000-0000-0000-000000000001', 'ADMIN', '孙院长', '管理部', '院长', true, CURRENT_TIMESTAMP),
('50000001-0000-0000-0000-000000000001', '14000002-0000-0000-0000-000000000001', 'ASSESSOR', '钱护理', '护理部', '护理主管', true, CURRENT_TIMESTAMP),
('50000002-0000-0000-0000-000000000001', '14000003-0000-0000-0000-000000000001', 'ADMIN', '吴经理', '运营部', '运营经理', true, CURRENT_TIMESTAMP),
('50000003-0000-0000-0000-000000000001', '14000004-0000-0000-0000-000000000001', 'ASSESSOR', '郑评估', '评估部', '评估员', true, CURRENT_TIMESTAMP);

-- 保险公司用户
INSERT INTO tenant_user_memberships (tenant_id, user_id, tenant_role, display_name, department, is_active, joined_at) VALUES
('60000001-0000-0000-0000-000000000001', '15000001-0000-0000-0000-000000000001', 'ADMIN', '冯经理', '长护险部', true, CURRENT_TIMESTAMP),
('60000001-0000-0000-0000-000000000001', '15000002-0000-0000-0000-000000000001', 'REVIEWER', '陈审核', '理赔部', true, CURRENT_TIMESTAMP),
('60000002-0000-0000-0000-000000000001', '15000003-0000-0000-0000-000000000001', 'VIEWER', '杨查看', '客服部', true, CURRENT_TIMESTAMP);

-- ========================================
-- 5. 创建个人用户（B2C）
-- ========================================

-- 密码都是 User@123 的bcrypt hash

-- 免费版用户
INSERT INTO individual_users (id, email, real_name, password_hash, status, service_type, monthly_assessment_count, created_at) VALUES
('21000001-0000-0000-0000-000000000001', '<EMAIL>', '张三', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', 'ACTIVE', 'FREE', 0, CURRENT_TIMESTAMP),
('21000002-0000-0000-0000-000000000001', '<EMAIL>', '李四', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', 'ACTIVE', 'FREE', 0, CURRENT_TIMESTAMP),
('21000003-0000-0000-0000-000000000001', '<EMAIL>', '王五', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', 'ACTIVE', 'FREE', 0, CURRENT_TIMESTAMP);

-- 付费版用户
INSERT INTO individual_users (id, email, real_name, password_hash, status, service_type, subscription_expires_at, monthly_assessment_count, created_at) VALUES
('22000001-0000-0000-0000-000000000001', '<EMAIL>', '赵六', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', 'ACTIVE', 'PREMIUM', CURRENT_TIMESTAMP + INTERVAL '30 days', 0, CURRENT_TIMESTAMP),
('22000002-0000-0000-0000-000000000001', '<EMAIL>', '孙七', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', 'ACTIVE', 'PREMIUM', CURRENT_TIMESTAMP + INTERVAL '30 days', 0, CURRENT_TIMESTAMP),
('22000003-0000-0000-0000-000000000001', '<EMAIL>', '周八', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', 'ACTIVE', 'PREMIUM', CURRENT_TIMESTAMP + INTERVAL '30 days', 0, CURRENT_TIMESTAMP);

-- 专业版用户
INSERT INTO individual_users (id, email, real_name, password_hash, status, service_type, subscription_expires_at, monthly_assessment_count, created_at) VALUES
('23000001-0000-0000-0000-000000000001', '<EMAIL>', '吴医生', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', 'ACTIVE', 'PRO', CURRENT_TIMESTAMP + INTERVAL '365 days', 0, CURRENT_TIMESTAMP),
('23000002-0000-0000-0000-000000000001', '<EMAIL>', '郑护士', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', 'ACTIVE', 'PRO', CURRENT_TIMESTAMP + INTERVAL '365 days', 0, CURRENT_TIMESTAMP),
('23000003-0000-0000-0000-000000000001', '<EMAIL>', '冯评估师', '$2a$10$u66NutNVBe0OGiQS.Vjp7OF2zI1UC0pQ5i/CooqaBXE2C/l4o0cnO', 'ACTIVE', 'PRO', CURRENT_TIMESTAMP + INTERVAL '365 days', 0, CURRENT_TIMESTAMP);

-- ========================================
-- 6. 创建测试评估量表
-- ========================================

INSERT INTO assessment_scales (id, name, type, version, description, is_public, created_by, created_at) VALUES
('31000001-0000-0000-0000-000000000001', '老年人能力评估量表', 'ELDERLY_ABILITY', '2.0', '民政部标准老年人能力评估量表', true, 'system', CURRENT_TIMESTAMP),
('31000002-0000-0000-0000-000000000001', '巴氏量表', 'BARTHEL_INDEX', '1.0', '日常生活能力评估', true, 'system', CURRENT_TIMESTAMP),
('*************-0000-0000-000000000001', 'interRAI-HC', 'INTERRAI', '3.0', '国际居家照护评估', true, 'system', CURRENT_TIMESTAMP),
('*************-0000-0000-000000000001', '长护险评估表', 'LTCI', '1.0', '长期护理保险资格评估', true, 'system', CURRENT_TIMESTAMP);

-- ========================================
-- 完成提示
-- ========================================

-- 注意：实际使用时需要：
-- 1. 将密码hash替换为真实的bcrypt hash值
-- 2. 根据实际需要调整UUID
-- 3. 确保外键关系正确
-- 4. 可能需要根据实际表结构调整字段

-- 生成测试密码hash的示例代码（Java）：
-- BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
-- String testHash = encoder.encode("Test@123");
-- String userHash = encoder.encode("User@123");

-- 查询验证：
-- SELECT COUNT(*) FROM tenants; -- 应该有29个机构
-- SELECT COUNT(*) FROM platform_users; -- 应该有18个机构用户
-- SELECT COUNT(*) FROM individual_users; -- 应该有9个个人用户
-- SELECT COUNT(*) FROM tenant_hierarchies; -- 应该有10个层级关系
-- SELECT COUNT(*) FROM tenant_user_memberships; -- 应该有18个用户-机构关系