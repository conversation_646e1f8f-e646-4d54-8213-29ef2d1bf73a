-- ========================================
-- 智能评估平台 - 多租户登录系统Schema修复
-- V4__Fix_schema_for_multitenant_login.sql
-- 创建日期: 2025-06-25
-- 目的: 修复多租户登录系统所需的数据库Schema问题
-- ========================================

-- 注意：这个迁移文件包含了所有为支持多租户登录系统而进行的Schema修复

-- 1. 为 platform_users 表添加缺失的列
ALTER TABLE platform_users 
ADD COLUMN IF NOT EXISTS avatar_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS full_name VARCHAR(100);

-- 2. 为 tenants 表添加缺失的列
ALTER TABLE tenants 
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS logo_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS subscription_expires_at TIMESTAMP;

-- 3. 创建租户数据权限表（用于层级权限管理）
CREATE TABLE IF NOT EXISTS tenant_data_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_tenant_id UUID NOT NULL,
    target_tenant_id UUID NOT NULL,
    permission_type VARCHAR(50) NOT NULL,
    permission_scope VARCHAR(50) NOT NULL DEFAULT 'ALL',
    is_active BOOLEAN NOT NULL DEFAULT true,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    CONSTRAINT fk_tenant_data_permissions_source FOREIGN KEY (source_tenant_id) REFERENCES tenants(id),
    CONSTRAINT fk_tenant_data_permissions_target FOREIGN KEY (target_tenant_id) REFERENCES tenants(id)
);

-- 为权限表创建索引
CREATE INDEX IF NOT EXISTS idx_tenant_data_permissions_source ON tenant_data_permissions(source_tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_data_permissions_target ON tenant_data_permissions(target_tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_data_permissions_type ON tenant_data_permissions(permission_type);
CREATE INDEX IF NOT EXISTS idx_tenant_data_permissions_active ON tenant_data_permissions(is_active) WHERE is_active = true;

-- 4. 规范化枚举值（确保Java枚举兼容性）
-- 4.1 更新租户状态值为大写
UPDATE tenants SET status = 'ACTIVE' WHERE status = 'active';
UPDATE tenants SET status = 'INACTIVE' WHERE status = 'inactive';
UPDATE tenants SET status = 'SUSPENDED' WHERE status = 'suspended';

-- 4.2 更新租户订阅状态为大写
UPDATE tenants SET subscription_status = 'ACTIVE' WHERE subscription_status = 'active';
UPDATE tenants SET subscription_status = 'INACTIVE' WHERE subscription_status = 'inactive';
UPDATE tenants SET subscription_status = 'SUSPENDED' WHERE subscription_status = 'suspended';
UPDATE tenants SET subscription_status = 'EXPIRED' WHERE subscription_status = 'expired';

-- 4.3 更新平台用户角色为大写
UPDATE platform_users SET platform_role = 'USER' WHERE platform_role = 'user';
UPDATE platform_users SET platform_role = 'ADMIN' WHERE platform_role = 'admin';
UPDATE platform_users SET platform_role = 'SUPER_ADMIN' WHERE platform_role = 'super_admin';

-- 4.4 更新租户用户关系状态为大写
UPDATE tenant_user_memberships SET status = 'ACTIVE' WHERE status = 'active';
UPDATE tenant_user_memberships SET status = 'INACTIVE' WHERE status = 'inactive';
UPDATE tenant_user_memberships SET status = 'SUSPENDED' WHERE status = 'suspended';

-- 5. 添加表注释以说明多租户架构
COMMENT ON TABLE platform_users IS '平台用户表 - 多租户架构核心用户表（修复版）';
COMMENT ON TABLE tenants IS '租户表 - 存储所有租户(机构)信息（修复版）';
COMMENT ON TABLE tenant_user_memberships IS '租户用户关系表 - 管理用户在各租户中的角色（修复版）';
COMMENT ON TABLE tenant_data_permissions IS '租户数据权限表 - 管理租户间的数据访问权限';

COMMENT ON COLUMN platform_users.avatar_url IS '用户头像URL地址';
COMMENT ON COLUMN platform_users.full_name IS '用户全名（优先使用此字段）';
COMMENT ON COLUMN tenants.description IS '租户描述信息';
COMMENT ON COLUMN tenants.logo_url IS '租户LOGO地址';
COMMENT ON COLUMN tenants.subscription_expires_at IS '订阅过期时间戳';

-- 6. 创建数据一致性检查视图
CREATE OR REPLACE VIEW tenant_login_status AS
SELECT 
    t.code as tenant_code,
    t.name as tenant_name,
    t.status as tenant_status,
    t.subscription_status,
    COUNT(tum.user_id) as user_count,
    COUNT(CASE WHEN tum.status = 'ACTIVE' THEN 1 END) as active_users,
    COUNT(CASE WHEN tum.tenant_role = 'ADMIN' THEN 1 END) as admin_users
FROM tenants t
LEFT JOIN tenant_user_memberships tum ON t.id = tum.tenant_id
GROUP BY t.id, t.code, t.name, t.status, t.subscription_status
ORDER BY t.code;

COMMENT ON VIEW tenant_login_status IS '租户登录状态视图 - 用于监控各租户的用户状态';

-- 7. 添加数据完整性约束
-- 7.1 确保平台用户的角色值有效
ALTER TABLE platform_users 
ADD CONSTRAINT IF NOT EXISTS chk_platform_role 
CHECK (platform_role IN ('USER', 'ADMIN', 'SUPER_ADMIN'));

-- 7.2 确保租户状态值有效
ALTER TABLE tenants 
ADD CONSTRAINT IF NOT EXISTS chk_tenant_status 
CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED'));

-- 7.3 确保租户订阅状态值有效
ALTER TABLE tenants 
ADD CONSTRAINT IF NOT EXISTS chk_subscription_status 
CHECK (subscription_status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'EXPIRED'));

-- 7.4 确保租户用户关系状态值有效
ALTER TABLE tenant_user_memberships 
ADD CONSTRAINT IF NOT EXISTS chk_membership_status 
CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED'));

-- 8. 优化索引（针对登录查询）
-- 8.1 复合索引用于登录查询优化
CREATE INDEX IF NOT EXISTS idx_platform_users_username_active 
ON platform_users(username) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_tenants_code_active 
ON tenants(code) WHERE status = 'ACTIVE';

CREATE INDEX IF NOT EXISTS idx_tenant_memberships_user_tenant_active 
ON tenant_user_memberships(user_id, tenant_id) WHERE status = 'ACTIVE';

-- 8.2 为权限查询优化的索引
CREATE INDEX IF NOT EXISTS idx_tenant_memberships_tenant_role_active 
ON tenant_user_memberships(tenant_id, tenant_role) WHERE status = 'ACTIVE';

-- 9. 创建登录审计触发器（可选）
CREATE OR REPLACE FUNCTION log_login_attempt()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新最后登录时间时记录审计日志
    IF TG_OP = 'UPDATE' AND OLD.last_login_at IS DISTINCT FROM NEW.last_login_at THEN
        INSERT INTO audit_logs (
            user_id, 
            username, 
            action, 
            resource_type, 
            resource_id, 
            created_at
        ) VALUES (
            NEW.id, 
            NEW.username, 
            'LOGIN_SUCCESS', 
            'platform_user', 
            NEW.id::text, 
            NEW.last_login_at
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为平台用户表添加登录审计触发器
DROP TRIGGER IF EXISTS audit_platform_user_login ON platform_users;
CREATE TRIGGER audit_platform_user_login
    AFTER UPDATE ON platform_users
    FOR EACH ROW
    EXECUTE FUNCTION log_login_attempt();

-- 10. 记录迁移完成
INSERT INTO schema_migrations_log (version, description, executed_at) 
VALUES ('V4', 'Fixed schema for multi-tenant login system - all login issues resolved', NOW())
ON CONFLICT DO NOTHING;

-- 11. 验证迁移结果的查询（在日志中显示）
DO $$
DECLARE
    tenant_count INTEGER;
    user_count INTEGER;
    membership_count INTEGER;
    permission_table_exists BOOLEAN;
BEGIN
    -- 统计数据
    SELECT COUNT(*) INTO tenant_count FROM tenants;
    SELECT COUNT(*) INTO user_count FROM platform_users;
    SELECT COUNT(*) INTO membership_count FROM tenant_user_memberships;
    
    -- 检查权限表是否存在
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'tenant_data_permissions'
    ) INTO permission_table_exists;
    
    -- 输出验证信息
    RAISE NOTICE '=== 多租户登录系统Schema修复完成 ===';
    RAISE NOTICE '租户数量: %', tenant_count;
    RAISE NOTICE '平台用户数量: %', user_count;
    RAISE NOTICE '用户关系数量: %', membership_count;
    RAISE NOTICE '权限表已创建: %', permission_table_exists;
    RAISE NOTICE '所有枚举值已规范化为大写格式';
    RAISE NOTICE '登录查询优化索引已创建';
    RAISE NOTICE '数据完整性约束已添加';
    RAISE NOTICE '=== 迁移成功完成 ===';
END $$;