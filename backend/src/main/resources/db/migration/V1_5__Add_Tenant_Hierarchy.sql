-- 租户层级关系表
-- 支持多级组织架构的租户管理

-- 添加租户层级字段到现有租户表
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS parent_tenant_id UUID;
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS tenant_level INTEGER DEFAULT 1;
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS tenant_path TEXT;
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS organization_type VARCHAR(50) DEFAULT 'ORGANIZATION';

-- 添加外键约束
ALTER TABLE tenants ADD CONSTRAINT fk_tenant_parent 
    FOREIGN KEY (parent_tenant_id) REFERENCES tenants(id);

-- 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_tenants_parent ON tenants(parent_tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenants_level ON tenants(tenant_level);
CREATE INDEX IF NOT EXISTS idx_tenants_path ON tenants(tenant_path);
CREATE INDEX IF NOT EXISTS idx_tenants_org_type ON tenants(organization_type);

-- 租户层级关系表（用于快速查询上下级关系）
CREATE TABLE IF NOT EXISTS tenant_hierarchies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ancestor_tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    descendant_tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    depth INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 确保唯一性
    UNIQUE(ancestor_tenant_id, descendant_tenant_id)
);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_hierarchies_ancestor ON tenant_hierarchies(ancestor_tenant_id);
CREATE INDEX IF NOT EXISTS idx_hierarchies_descendant ON tenant_hierarchies(descendant_tenant_id);
CREATE INDEX IF NOT EXISTS idx_hierarchies_depth ON tenant_hierarchies(depth);

-- 租户数据访问权限表
CREATE TABLE IF NOT EXISTS tenant_data_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    target_tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    permission_type VARCHAR(50) NOT NULL, -- READ, WRITE, ADMIN
    permission_scope VARCHAR(100) NOT NULL, -- ASSESSMENTS, USERS, REPORTS, ALL
    granted_by UUID REFERENCES platform_users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    
    UNIQUE(source_tenant_id, target_tenant_id, permission_type, permission_scope)
);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_data_permissions_source ON tenant_data_permissions(source_tenant_id);
CREATE INDEX IF NOT EXISTS idx_data_permissions_target ON tenant_data_permissions(target_tenant_id);
CREATE INDEX IF NOT EXISTS idx_data_permissions_active ON tenant_data_permissions(is_active);

-- 组织类型枚举说明
COMMENT ON COLUMN tenants.organization_type IS '组织类型: GOVERNMENT(政府机构), HOSPITAL(医院), NURSING_HOME(养老院), COMPANY(企业), OTHER(其他)';
COMMENT ON COLUMN tenants.tenant_level IS '租户层级: 1=省级, 2=市级, 3=区县级, 4=机构级';
COMMENT ON COLUMN tenants.tenant_path IS '租户路径: /省级ID/市级ID/区县级ID/机构ID';

-- 插入示例层级数据
INSERT INTO tenants (id, code, name, organization_type, tenant_level, tenant_path, industry, contact_person, contact_email, contact_phone, subscription_plan, subscription_status, max_users, max_monthly_assessments, status, created_at, updated_at) VALUES
-- 省民政厅
('11111111-1111-1111-1111-111111111111', 'gov_province_civil', '省民政厅', 'GOVERNMENT', 1, '/11111111-1111-1111-1111-111111111111', 'government', '张厅长', '<EMAIL>', '010-12345678', 'ENTERPRISE', 'ACTIVE', 1000, 50000, 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 市民政局
('*************-2222-2222-************', 'gov_city_a_civil', '市民政局A', 'GOVERNMENT', 2, '/11111111-1111-1111-1111-111111111111/*************-2222-2222-************', 'government', '李局长', '<EMAIL>', '020-12345678', 'PREMIUM', 'ACTIVE', 500, 20000, 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('*************-3333-3333-**********33', 'gov_city_b_civil', '市民政局B', 'GOVERNMENT', 2, '/11111111-1111-1111-1111-111111111111/*************-3333-3333-**********33', 'government', '王局长', '<EMAIL>', '021-12345678', 'PREMIUM', 'ACTIVE', 500, 20000, 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 养老机构
('44444444-4444-4444-4444-444444444444', 'nursing_home_a1', '阳光养老院', 'NURSING_HOME', 3, '/11111111-1111-1111-1111-111111111111/*************-2222-2222-************/44444444-4444-4444-4444-444444444444', 'healthcare', '陈院长', '<EMAIL>', '020-87654321', 'STANDARD', 'ACTIVE', 100, 5000, 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('55555555-5555-5555-5555-555555555555', 'nursing_home_a2', '康乐养老院', 'NURSING_HOME', 3, '/11111111-1111-1111-1111-111111111111/*************-2222-2222-************/55555555-5555-5555-5555-555555555555', 'healthcare', '刘院长', '<EMAIL>', '020-87654322', 'STANDARD', 'ACTIVE', 100, 5000, 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('66666666-6666-6666-6666-666666666666', 'nursing_home_b1', '幸福养老院', 'NURSING_HOME', 3, '/11111111-1111-1111-1111-111111111111/*************-3333-3333-**********33/66666666-6666-6666-6666-666666666666', 'healthcare', '赵院长', '<EMAIL>', '021-87654321', 'STANDARD', 'ACTIVE', 100, 5000, 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 更新parent_tenant_id
UPDATE tenants SET parent_tenant_id = '11111111-1111-1111-1111-111111111111' WHERE id IN ('*************-2222-2222-************', '*************-3333-3333-**********33');
UPDATE tenants SET parent_tenant_id = '*************-2222-2222-************' WHERE id IN ('44444444-4444-4444-4444-444444444444', '55555555-5555-5555-5555-555555555555');
UPDATE tenants SET parent_tenant_id = '*************-3333-3333-**********33' WHERE id = '66666666-6666-6666-6666-666666666666';

-- 构建层级关系表
-- 自关联（每个租户都是自己的祖先，深度为0）
INSERT INTO tenant_hierarchies (ancestor_tenant_id, descendant_tenant_id, depth)
SELECT id, id, 0 FROM tenants;

-- 一级关系（直接父子关系，深度为1）
INSERT INTO tenant_hierarchies (ancestor_tenant_id, descendant_tenant_id, depth)
SELECT parent_tenant_id, id, 1 
FROM tenants 
WHERE parent_tenant_id IS NOT NULL;

-- 二级关系（祖孙关系，深度为2）
INSERT INTO tenant_hierarchies (ancestor_tenant_id, descendant_tenant_id, depth)
SELECT h1.ancestor_tenant_id, h2.descendant_tenant_id, 2
FROM tenant_hierarchies h1
JOIN tenant_hierarchies h2 ON h1.descendant_tenant_id = h2.ancestor_tenant_id
WHERE h1.depth = 1 AND h2.depth = 1;

-- 插入默认数据访问权限
-- 省民政厅可以查看所有下级数据
INSERT INTO tenant_data_permissions (source_tenant_id, target_tenant_id, permission_type, permission_scope)
SELECT '11111111-1111-1111-1111-111111111111', descendant_tenant_id, 'READ', 'ALL'
FROM tenant_hierarchies 
WHERE ancestor_tenant_id = '11111111-1111-1111-1111-111111111111' AND depth > 0;

-- 市民政局可以查看下级养老机构数据
INSERT INTO tenant_data_permissions (source_tenant_id, target_tenant_id, permission_type, permission_scope)
SELECT '*************-2222-2222-************', descendant_tenant_id, 'READ', 'ALL'
FROM tenant_hierarchies 
WHERE ancestor_tenant_id = '*************-2222-2222-************' AND depth > 0;

INSERT INTO tenant_data_permissions (source_tenant_id, target_tenant_id, permission_type, permission_scope)
SELECT '*************-3333-3333-**********33', descendant_tenant_id, 'READ', 'ALL'
FROM tenant_hierarchies 
WHERE ancestor_tenant_id = '*************-3333-3333-**********33' AND depth > 0;

-- 创建函数：获取租户的所有子级租户
CREATE OR REPLACE FUNCTION get_tenant_descendants(tenant_uuid UUID, max_depth INTEGER DEFAULT NULL)
RETURNS TABLE(tenant_id UUID, tenant_code TEXT, tenant_name TEXT, depth INTEGER) AS $$
BEGIN
    RETURN QUERY
    SELECT t.id, t.code, t.name, h.depth
    FROM tenant_hierarchies h
    JOIN tenants t ON h.descendant_tenant_id = t.id
    WHERE h.ancestor_tenant_id = tenant_uuid 
      AND h.depth > 0
      AND (max_depth IS NULL OR h.depth <= max_depth)
    ORDER BY h.depth, t.name;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：获取租户的所有上级租户
CREATE OR REPLACE FUNCTION get_tenant_ancestors(tenant_uuid UUID)
RETURNS TABLE(tenant_id UUID, tenant_code TEXT, tenant_name TEXT, depth INTEGER) AS $$
BEGIN
    RETURN QUERY
    SELECT t.id, t.code, t.name, h.depth
    FROM tenant_hierarchies h
    JOIN tenants t ON h.ancestor_tenant_id = t.id
    WHERE h.descendant_tenant_id = tenant_uuid 
      AND h.depth > 0
    ORDER BY h.depth DESC;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：检查租户是否有权限访问目标租户的数据
CREATE OR REPLACE FUNCTION has_tenant_data_permission(
    source_tenant UUID, 
    target_tenant UUID, 
    perm_type TEXT DEFAULT 'READ',
    perm_scope TEXT DEFAULT 'all'
)
RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN := FALSE;
BEGIN
    -- 检查直接权限
    SELECT EXISTS(
        SELECT 1 FROM tenant_data_permissions 
        WHERE source_tenant_id = source_tenant 
          AND target_tenant_id = target_tenant
          AND permission_type = UPPER(perm_type)
          AND (permission_scope = UPPER(perm_scope) OR permission_scope = 'ALL')
          AND is_active = true
          AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
    ) INTO has_permission;
    
    -- 如果没有直接权限，检查是否有层级权限
    IF NOT has_permission THEN
        SELECT EXISTS(
            SELECT 1 FROM tenant_hierarchies h
            JOIN tenant_data_permissions p ON h.descendant_tenant_id = p.target_tenant_id
            WHERE h.ancestor_tenant_id = source_tenant
              AND p.source_tenant_id = source_tenant
              AND h.descendant_tenant_id = target_tenant
              AND p.permission_type = UPPER(perm_type)
              AND (p.permission_scope = UPPER(perm_scope) OR p.permission_scope = 'ALL')
              AND p.is_active = true
              AND (p.expires_at IS NULL OR p.expires_at > CURRENT_TIMESTAMP)
        ) INTO has_permission;
    END IF;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql;