# 生产环境配置文件
# 优化版本: v1.0
# 创建日期: 2025-06-26
# 说明: 针对生产环境的性能优化配置

spring:
  application:
    name: assessment-platform-prod

  # 数据源配置 - 生产环境优化
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/assessment_multitenant
    username: ${DB_USERNAME:assessment_user}
    password: ${DB_PASSWORD}
    hikari:
      # 连接池配置优化
      minimum-idle: 20                    # 最小空闲连接数（已优化）
      maximum-pool-size: 50               # 最大连接数（测试验证的生产配置）
      idle-timeout: 300000                # 5分钟空闲超时
      max-lifetime: 1800000               # 30分钟最大生命周期
      connection-timeout: 30000           # 30秒连接超时
      leak-detection-threshold: 60000     # 60秒连接泄露检测
      # 性能优化参数
      connection-test-query: SELECT 1     # 连接测试查询
      pool-name: "HikariPool-Production"  # 连接池名称
      register-mbeans: true               # 注册JMX监控

  # JPA配置 - 生产环境优化
  jpa:
    hibernate:
      ddl-auto: validate                  # 生产环境只验证不修改
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        '[format_sql]': false             # 生产环境关闭SQL格式化
        '[show_sql]': false               # 生产环境关闭SQL显示
        '[use_sql_comments]': false       # 生产环境关闭SQL注释
        jdbc:
          '[batch_size]': 50              # 批量操作优化（测试验证）
          '[batch_versioned_data]': true
          '[fetch_size]': 200             # 查询优化（测试验证）
        '[order_inserts]': true
        '[order_updates]': true
        # 二级缓存配置
        cache:
          '[use_second_level_cache]': true
          '[use_query_cache]': true
          '[region.factory_class]': org.hibernate.cache.jcache.JCacheRegionFactory
        # 统计信息（生产环境谨慎开启）
        '[generate_statistics]': false

  # Redis配置 - 生产环境优化
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD}
      database: 0
      timeout: 5000ms
      connect-timeout: 5000ms
      # 集群配置（可选）
      # cluster:
      #   nodes: ${REDIS_CLUSTER_NODES:redis1:6379,redis2:6379,redis3:6379}
      #   max-redirects: 3
      lettuce:
        pool:
          max-active: 100                 # 最大活跃连接（测试验证的生产配置）
          max-idle: 50                    # 最大空闲连接
          min-idle: 20                    # 最小空闲连接
          max-wait: 5000ms                # 最大等待时间
        shutdown-timeout: 1000ms          # 关闭超时
        # 集群配置
        cluster:
          refresh:
            period: 60000ms               # 拓扑刷新周期
            adaptive: true                # 自适应拓扑刷新

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600000               # 1小时默认过期
      cache-null-values: false            # 不缓存null值
      key-prefix: "prod:cache:"           # 生产环境缓存前缀
      use-key-prefix: true                # 使用键前缀

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
      # 生产环境使用磁盘存储临时文件
      location: ${TEMP_DIR:/tmp/assessment}

# 服务器配置 - 生产环境优化
server:
  port: ${SERVER_PORT:8080}
  compression:
    enabled: true
    min-response-size: 1024
    mime-types:
      - text/html
      - text/xml
      - text/plain
      - text/css
      - text/javascript
      - application/javascript
      - application/json
      - application/xml
  # Tomcat优化
  tomcat:
    threads:
      max: 300                            # 最大线程数（测试验证的生产配置）
      min-spare: 50                       # 最小空闲线程
    connection-timeout: 20000ms
    max-connections: 20000                # 最大连接数（测试验证）
    accept-count: 500                     # 等待队列长度
    # 访问日志
    accesslog:
      enabled: true
      directory: logs
      pattern: "%h %l %u %t \"%r\" %s %b %D"
      prefix: access_log
      suffix: .log
      rotate: true
  # HTTP/2支持
  http2:
    enabled: true
  # 优雅关闭
  shutdown: graceful

# MinIO配置 - 生产环境
minio:
  endpoint: ${MINIO_ENDPOINT:http://minio:9000}
  access-key: ${MINIO_ACCESS_KEY}
  secret-key: ${MINIO_SECRET_KEY}
  bucket-name: ${MINIO_BUCKET:assessment-files}
  secure: true                            # 生产环境使用HTTPS
  # 连接池配置
  pool:
    max-connections: 50
    connection-timeout: 10000ms

# JWT配置 - 生产环境
jwt:
  secret: ${JWT_SECRET}                   # 必须从环境变量获取
  expiration: 43200000                    # 12小时（缩短token有效期）
  refresh-expiration: 2592000000          # 30天刷新token

# 日志配置 - 生产环境
logging:
  level:
    root: WARN
    '[com.assessment]': INFO
    '[org.springframework]': WARN
    '[org.hibernate]': WARN
    # 性能相关日志
    '[org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor]': ERROR
    '[org.apache.tomcat.util.net.NioEndpoint]': ERROR
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/assessment-prod.log
    max-size: 100MB
    max-history: 30
    total-size-cap: 3GB
  # 异步日志配置
  config: classpath:logback-spring.xml

# Actuator监控配置 - 生产环境
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /management
  endpoint:
    health:
      show-details: when-authorized       # 授权后显示详情
      probes:
        enabled: true                     # 启用健康探针
    shutdown:
      enabled: false                      # 生产环境禁用shutdown端点
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
      environment: production
  # 健康检查配置
  health:
    redis:
      enabled: true
    db:
      enabled: true
    diskspace:
      enabled: true
      threshold: 10GB

# API文档配置 - 生产环境
springdoc:
  api-docs:
    enabled: false                        # 生产环境关闭API文档
  swagger-ui:
    enabled: false                        # 生产环境关闭Swagger UI

# 业务配置 - 生产环境
assessment:
  # 评估相关配置
  evaluation:
    max-duration: 120
    auto-save-interval: 30
    # 并发控制
    max-concurrent-assessments: 1000      # 最大并发评估数
    queue-capacity: 5000                  # 队列容量

  # 文件上传配置
  upload:
    allowed-types:
      - image/jpeg
      - image/png
      - image/gif
      - application/pdf
    max-size: 52428800                    # 50MB
    # 病毒扫描（可选）
    virus-scan:
      enabled: ${VIRUS_SCAN_ENABLED:false}
      service-url: ${VIRUS_SCAN_URL}

  # 安全配置 - 生产环境强化
  security:
    password-min-length: 12               # 提高密码长度要求
    password-require-special: true
    password-require-uppercase: true
    password-require-lowercase: true
    password-require-digit: true
    max-login-attempts: 3                 # 降低最大尝试次数
    lock-duration: 3600                   # 1小时锁定
    # Session配置
    session:
      timeout: 3600                       # 1小时会话超时
      max-concurrent-sessions: 3          # 最大并发会话数
    # CORS配置
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:https://assessment.example.com}
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600

  # 租户系统配置
  tenant:
    # 缓存配置
    cache:
      enabled: true
      preload-size: 1000                  # 预加载前1000个热门租户
      ttl: 86400                          # 24小时过期
    # 搜索优化
    search:
      suggestion-limit: 10
      debounce-time: 300
      min-query-length: 2

  # 限流配置
  rate-limit:
    enabled: true
    # API限流
    api:
      default-limit: 1000                 # 默认每分钟1000次
      login-limit: 20                     # 登录每分钟20次
      upload-limit: 10                    # 上传每分钟10次
    # IP限流
    ip:
      enabled: true
      limit: 5000                         # 每IP每小时5000次
      blacklist-threshold: 10000          # 超过10000次加入黑名单

# Docling服务配置 - 生产环境
docling:
  service:
    url: ${DOCLING_SERVICE_URL:http://docling:8088}
    timeout: 60
    enabled: true
    # 重试配置
    retry:
      max-attempts: 3
      delay: 1000

# AI服务配置 - 生产环境
ai:
  lmstudio:
    url: ${AI_LMSTUDIO_URL}
    model: ${AI_LMSTUDIO_MODEL}
    # 负载均衡（如果有多个AI服务器）
    servers:
      - ${AI_SERVER_1}
      - ${AI_SERVER_2}
  analysis:
    timeout: 300000                       # 5分钟超时
    max-content-length: 100000
    enabled: true
    # 并发控制
    max-concurrent-requests: 10           # 最大并发AI请求

# 验证码池配置 - 生产环境（测试验证的策略）
captcha:
  pool:
    initial-size: 100                     # 启动预生成100个
    critical-threshold: 50                # 临界阈值，≤50个触发补充
    low-threshold: 200                    # 低水位阈值，≤200个触发补充
    critical-replenish: 500               # 临界补充数量
    normal-replenish: 2000                # 正常补充数量
    max-pool-size: 5000                   # 最大池大小
  simple:
    expire-minutes: 3                     # 缩短过期时间
    tolerance: 5
    # 防暴力破解
    max-attempts: 5                       # 最大尝试次数
    block-duration: 300                   # 5分钟封禁

# 生产环境特殊配置
production:
  # 监控告警
  monitoring:
    enabled: true
    alert-email: ${ALERT_EMAIL:<EMAIL>}
    # 告警阈值
    thresholds:
      cpu-usage: 80                       # CPU使用率80%告警
      memory-usage: 85                    # 内存使用率85%告警
      disk-usage: 90                      # 磁盘使用率90%告警
      response-time: 1000                 # 响应时间1秒告警
      error-rate: 5                       # 错误率5%告警

  # 备份配置
  backup:
    enabled: true
    schedule: "0 2 * * *"                 # 每天凌晨2点备份
    retention-days: 30                    # 保留30天

  # 性能优化
  performance:
    # JVM优化建议（启动参数）
    jvm-options: >
      -XX:+UseG1GC
      -XX:MaxGCPauseMillis=200
      -XX:+UseStringDeduplication
      -XX:+OptimizeStringConcat
      -Xms2g
      -Xmx4g
      -XX:MetaspaceSize=256m
      -XX:MaxMetaspaceSize=512m