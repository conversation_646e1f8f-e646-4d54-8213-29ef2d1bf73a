#!/bin/bash

echo "=== 验证码缓存问题调试工具 ==="
echo "时间: $(date)"
echo ""

# 1. 检查后端服务状态
echo "1. 检查后端服务状态..."
if curl -s http://localhost:8181/actuator/health > /dev/null 2>&1; then
    echo "✅ 后端服务正常运行"
else
    echo "❌ 后端服务未运行"
    exit 1
fi

# 2. 检查验证码API响应头
echo ""
echo "2. 检查验证码API响应头..."
echo "--- 响应头信息 ---"
curl -s -I "http://localhost:8181/api/captcha/get" | grep -E "(Cache-Control|Pragma|Expires|Content-Type)"

# 3. 生成多个验证码并比较
echo ""
echo "3. 生成多个验证码并比较Token..."
for i in {1..3}; do
    token=$(curl -s "http://localhost:8181/api/captcha/get" | jq -r '.data.token' 2>/dev/null)
    if [ "$token" != "null" ] && [ "$token" != "" ]; then
        echo "验证码 #$i Token: ${token:0:16}..."
    else
        echo "验证码 #$i: 获取失败"
    fi
    sleep 1
done

# 4. 检查Redis缓存
echo ""
echo "4. 检查Redis缓存状态..."
if docker exec assessment-redis redis-cli -a redis123 ping > /dev/null 2>&1; then
    echo "✅ Redis连接正常"
    cache_count=$(docker exec assessment-redis redis-cli -a redis123 eval "return #redis.call('keys', 'simple_captcha:*')" 0 2>/dev/null)
    echo "当前缓存的验证码数量: $cache_count"
else
    echo "❌ Redis连接失败"
fi

# 5. 检查JVM系统属性
echo ""
echo "5. 检查JVM headless模式..."
headless_status=$(curl -s "http://localhost:8181/actuator/env/java.awt.headless" 2>/dev/null | jq -r '.property.value' 2>/dev/null)
if [ "$headless_status" = "true" ]; then
    echo "✅ JVM headless模式已启用: $headless_status"
else
    echo "⚠️ JVM headless模式状态: $headless_status"
fi

# 6. 生成测试图片并检查Base64长度
echo ""
echo "6. 检查验证码图片数据..."
response=$(curl -s "http://localhost:8181/api/captcha/get")
bg_length=$(echo "$response" | jq -r '.data.originalImageBase64' 2>/dev/null | wc -c)
piece_length=$(echo "$response" | jq -r '.data.jigsawImageBase64' 2>/dev/null | wc -c)

if [ "$bg_length" -gt 1000 ] && [ "$piece_length" -gt 1000 ]; then
    echo "✅ 验证码图片数据正常"
    echo "   背景图片Base64长度: $bg_length"
    echo "   滑块图片Base64长度: $piece_length"
else
    echo "❌ 验证码图片数据异常"
    echo "   背景图片Base64长度: $bg_length"
    echo "   滑块图片Base64长度: $piece_length"
fi

# 7. 保存最新的验证码到文件进行对比
echo ""
echo "7. 保存验证码图片到文件..."
mkdir -p captcha-debug
timestamp=$(date +%s)

# 获取新的验证码
new_response=$(curl -s "http://localhost:8181/api/captcha/get")
echo "$new_response" | jq -r '.data.originalImageBase64' 2>/dev/null | base64 -d > "captcha-debug/bg_${timestamp}.png" 2>/dev/null
echo "$new_response" | jq -r '.data.jigsawImageBase64' 2>/dev/null | base64 -d > "captcha-debug/piece_${timestamp}.png" 2>/dev/null

if [ -f "captcha-debug/bg_${timestamp}.png" ]; then
    echo "✅ 验证码图片已保存到: captcha-debug/"
    echo "   背景图: bg_${timestamp}.png"
    echo "   滑块图: piece_${timestamp}.png"
    echo "   请手动检查图片内容是否为彩色(天蓝色背景，金黄色滑块)"
else
    echo "❌ 保存验证码图片失败"
fi

echo ""
echo "=== 调试完成 ==="
echo "请打开 http://localhost:8181/test-captcha-simple.html 进行前端测试"