#!/bin/bash

echo "开始自动修复 Checkstyle 警告..."

# 1. 修复工具类的 final 声明
echo "修复工具类 final 声明..."
find src/main/java -name "*Utils.java" -exec sed -i '' 's/^public class \([A-Za-z]*Utils\) {/public final class \1 {/' {} \;

# 2. 修复空格问题 - {} 内需要空格
echo "修复空格格式问题..."
find src/main/java -name "*.java" -exec sed -i '' 's/{}/{ }/g' {} \;

# 3. 修复左大括号问题 - 确保 { 前有空格
find src/main/java -name "*.java" -exec sed -i '' 's/){/) {/g' {} \;
find src/main/java -name "*.java" -exec sed -i '' 's/]{/] {/g' {} \;

echo "基础修复完成，正在检查结果..."

# 检查修复效果
./mvnw checkstyle:check -Dcheckstyle.config.location=checkstyle.xml 2>&1 | grep "\[WARN\]" | wc -l