<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滑动验证码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .captcha-container {
            margin: 20px 0;
            text-align: center;
        }
        
        .captcha-image {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
            position: relative;
            display: inline-block;
        }
        
        .captcha-image img {
            display: block;
            max-width: 100%;
        }
        
        .slider-container {
            width: 310px;
            height: 40px;
            background: #f0f0f0;
            border-radius: 20px;
            margin: 20px auto;
            position: relative;
            border: 1px solid #ddd;
        }
        
        .slider-track {
            width: 100%;
            height: 100%;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .slider-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #81C784);
            border-radius: 20px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .slider-button {
            position: absolute;
            top: 0;
            left: 0;
            width: 40px;
            height: 40px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 20px;
            cursor: grab;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            user-select: none;
        }
        
        .slider-button:active {
            cursor: grabbing;
        }
        
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .button:hover {
            background: #0056b3;
        }
        
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔐 滑动验证码功能测试</h1>
        
        <div class="status info">
            <strong>测试说明：</strong>
            本页面用于测试自研滑动验证码功能的前后端集成效果。
        </div>
        
        <div class="captcha-container" id="captchaContainer">
            <h3>第一步：获取验证码</h3>
            <button class="button" onclick="getCaptcha()">生成验证码</button>
            
            <div id="captchaDisplay" style="display: none;">
                <div class="captcha-image">
                    <img id="backgroundImage" alt="验证码背景图" />
                    <img id="pieceImage" alt="验证码滑块" style="position: absolute; top: 0; left: 0;" />
                </div>
                
                <div class="slider-container">
                    <div class="slider-track">
                        <div class="slider-fill" id="sliderFill" style="width: 0;">
                            向右滑动完成验证
                        </div>
                    </div>
                    <div class="slider-button" id="sliderButton">
                        →
                    </div>
                </div>
                
                <button class="button" onclick="verifyCaptcha()">验证滑动结果</button>
                <button class="button" onclick="getCaptcha()">刷新验证码</button>
            </div>
        </div>
        
        <div id="statusDisplay"></div>
        
        <div id="debugInfo">
            <h3>调试信息</h3>
            <div class="code-block" id="debugLog">等待操作...</div>
        </div>
        
        <div class="status info">
            <strong>功能特点：</strong><br>
            ✅ 基于Java AWT自研实现<br>
            ✅ Redis缓存验证数据<br>
            ✅ 5像素容错机制<br>
            ✅ 渐变背景和拼图切割<br>
            ✅ 前后端完整集成
        </div>
    </div>

    <script>
        let captchaData = null;
        let sliderPosition = 0;
        let isDragging = false;
        let startX = 0;
        
        // API基础路径
        const API_BASE = 'http://localhost:8181/api/captcha';
        
        // 添加调试日志
        function addLog(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusDisplay = document.getElementById('statusDisplay');
            statusDisplay.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // 获取验证码
        async function getCaptcha() {
            try {
                addLog('开始获取验证码...');
                showStatus('正在生成验证码...', 'info');
                
                const response = await fetch(`${API_BASE}/get`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                const result = await response.json();
                addLog(`API响应: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success && result.data) {
                    captchaData = result.data;
                    displayCaptcha(result.data);
                    showStatus('验证码生成成功！请滑动滑块完成验证。', 'success');
                    addLog(`验证码token: ${result.data.token}`);
                } else {
                    showStatus(`验证码生成失败: ${result.message}`, 'error');
                    addLog(`生成失败: ${result.message}`);
                }
            } catch (error) {
                showStatus(`网络错误: ${error.message}`, 'error');
                addLog(`网络错误: ${error.message}`);
            }
        }
        
        // 显示验证码
        function displayCaptcha(data) {
            const captchaDisplay = document.getElementById('captchaDisplay');
            const backgroundImage = document.getElementById('backgroundImage');
            const pieceImage = document.getElementById('pieceImage');
            
            backgroundImage.src = `data:image/png;base64,${data.originalImageBase64}`;
            pieceImage.src = `data:image/png;base64,${data.jigsawImageBase64}`;
            
            captchaDisplay.style.display = 'block';
            
            // 重置滑块位置
            resetSlider();
            
            // 绑定滑块事件
            bindSliderEvents();
        }
        
        // 重置滑块
        function resetSlider() {
            sliderPosition = 0;
            updateSliderPosition();
        }
        
        // 更新滑块位置
        function updateSliderPosition() {
            const sliderButton = document.getElementById('sliderButton');
            const sliderFill = document.getElementById('sliderFill');
            const pieceImage = document.getElementById('pieceImage');
            
            sliderButton.style.left = `${sliderPosition}px`;
            sliderFill.style.width = `${sliderPosition}px`;
            pieceImage.style.left = `${sliderPosition}px`;
            
            if (sliderPosition > 0) {
                sliderFill.textContent = '验证中...';
            } else {
                sliderFill.textContent = '向右滑动完成验证';
            }
        }
        
        // 绑定滑块事件
        function bindSliderEvents() {
            const sliderButton = document.getElementById('sliderButton');
            
            sliderButton.onmousedown = startDrag;
            document.onmousemove = drag;
            document.onmouseup = endDrag;
            
            // 触摸事件支持
            sliderButton.ontouchstart = (e) => startDrag(e.touches[0]);
            document.ontouchmove = (e) => drag(e.touches[0]);
            document.ontouchend = endDrag;
        }
        
        // 开始拖拽
        function startDrag(e) {
            isDragging = true;
            startX = e.clientX;
            addLog(`开始拖拽，起始位置: ${startX}`);
        }
        
        // 拖拽中
        function drag(e) {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const maxPosition = 270; // 310 - 40 (滑块宽度)
            
            sliderPosition = Math.max(0, Math.min(deltaX, maxPosition));
            updateSliderPosition();
        }
        
        // 结束拖拽
        function endDrag() {
            if (!isDragging) return;
            
            isDragging = false;
            addLog(`结束拖拽，最终位置: ${sliderPosition}px`);
            
            // 自动验证
            setTimeout(verifyCaptcha, 500);
        }
        
        // 验证滑动结果
        async function verifyCaptcha() {
            if (!captchaData) {
                showStatus('请先获取验证码', 'error');
                return;
            }
            
            try {
                addLog(`开始验证，滑块位置: ${sliderPosition}px`);
                showStatus('正在验证滑动结果...', 'info');
                
                const verifyData = {
                    captchaType: 'blockPuzzle',
                    token: captchaData.token,
                    pointJson: JSON.stringify({ x: Math.round(sliderPosition), y: 5 }),
                    verification: captchaData.secretKey
                };
                
                addLog(`验证数据: ${JSON.stringify(verifyData, null, 2)}`);
                
                const response = await fetch(`${API_BASE}/check`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify(verifyData)
                });
                
                const result = await response.json();
                addLog(`验证响应: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success && result.data && result.data.result) {
                    showStatus('🎉 验证成功！滑动位置正确。', 'success');
                    document.getElementById('sliderFill').textContent = '验证成功';
                    document.getElementById('sliderButton').textContent = '✓';
                    addLog('验证成功！');
                } else {
                    showStatus('❌ 验证失败，请重新尝试。', 'error');
                    resetSlider();
                    addLog(`验证失败: ${result.message || '位置不正确'}`);
                }
            } catch (error) {
                showStatus(`验证错误: ${error.message}`, 'error');
                addLog(`验证错误: ${error.message}`);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('滑动验证码测试页面已加载');
            showStatus('点击"生成验证码"按钮开始测试', 'info');
        });
    </script>
</body>
</html>