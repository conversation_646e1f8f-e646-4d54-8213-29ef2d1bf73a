# 数据库连接问题修复总结

## 问题描述
`dev-start-m4.sh` 脚本在检查数据库状态时一直失败，显示：
```
⏳ 尝试 1/30...
⏳ 尝试 2/30...
...
```

## 根本原因
脚本中使用的容器名称和数据库名称与实际运行的容器不匹配：

### 脚本中的错误配置
- 容器名称: `assessment-postgres-dev`, `assessment-redis-dev`, `assessment-minio-dev`
- 数据库名称: 混合使用 `elderly_assessment` 和 `assessment_multitenant`
- 表名检查: `platform_users`, `tenants`, `assessment_subjects`

### 实际的正确配置
- 容器名称: `assessment-postgres`, `assessment-redis`, `assessment-minio`
- 数据库名称: `assessment_multitenant`
- 实际表名: `users`, `organizations`, `elderly`, `assessment_scales`, `assessments`

## 修复内容

### 1. 容器名称修复
```bash
# 修复前
docker exec assessment-postgres-dev pg_isready...
check_service_health "postgres" "assessment-postgres-dev"

# 修复后  
docker exec assessment-postgres pg_isready...
check_service_health "postgres" "assessment-postgres"
```

### 2. 数据库名称统一
```bash
# 统一使用 assessment_multitenant 数据库
docker exec assessment-postgres pg_isready -U assessment_user -d assessment_multitenant
```

### 3. 表名检查修复
```bash
# 修复前
grep -q "platform_users\|tenants\|assessment_subjects"

# 修复后
grep -q "users\|organizations\|elderly"
```

### 4. 表统计信息修复
```sql
-- 修复前
SELECT count(*) FROM platform_users, tenants, assessment_subjects...

-- 修复后  
SELECT count(*) FROM users, organizations, elderly...
```

## 验证结果

### 数据库连接测试
```bash
$ docker exec assessment-postgres pg_isready -U assessment_user -d assessment_multitenant
/var/run/postgresql:5432 - accepting connections
```

### 表存在性验证
```bash
$ docker exec assessment-postgres psql -U assessment_user -d assessment_multitenant -c "\dt"
                   List of relations
 Schema |        Name        | Type  |      Owner      
--------+--------------------+-------+-----------------
 public | assessment_reports | table | assessment_user
 public | assessment_scales  | table | assessment_user
 public | assessments        | table | assessment_user
 public | audit_logs         | table | assessment_user
 public | elderly            | table | assessment_user
 public | files              | table | assessment_user
 public | organizations      | table | assessment_user
 public | system_configs     | table | assessment_user
 public | users              | table | assessment_user
```

### 数据统计验证
```bash
$ docker exec assessment-postgres psql -U assessment_user -d assessment_multitenant -c "SELECT (SELECT count(*) FROM users) as users_count, (SELECT count(*) FROM organizations) as orgs_count, (SELECT count(*) FROM elderly) as elderly_count;"
 users_count | orgs_count | elderly_count 
-------------+------------+---------------
           1 |          0 |             0
```

## 修复的文件
- `scripts/dev-start-m4.sh` - 数据库连接和表检查逻辑

## 现在的状态
✅ 数据库连接正常
✅ 表检查逻辑正确
✅ 容器名称匹配
✅ 统计信息显示正常

脚本现在应该能够正常通过数据库检查步骤，不再出现连接超时的问题。