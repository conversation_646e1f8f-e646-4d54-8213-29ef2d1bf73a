<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码样式验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .captcha-display {
            border: 2px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .image-container {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        .image-item {
            flex: 1;
        }
        .image-item img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
        }
        .image-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 验证码样式验证测试</h1>
        <p>这个测试页面直接调用后端API，验证验证码样式是否已经更新。</p>
        
        <div class="status" id="status">
            准备测试...
        </div>
        
        <button onclick="testCaptcha()">🔄 获取新验证码</button>
        <button onclick="clearCache()">🗑️ 清除缓存</button>
        
        <div class="captcha-display" id="captchaDisplay">
            <p>点击"获取新验证码"按钮开始测试</p>
        </div>
    </div>

    <div class="test-container">
        <h2>📋 检查要点</h2>
        <ul>
            <li><strong>背景图片</strong>: 应该是彩色渐变背景（天蓝色到浅钢蓝色）</li>
            <li><strong>网格纹理</strong>: 应该有白色网格线</li>
            <li><strong>时间戳水印</strong>: 左下角应该显示时间戳</li>
            <li><strong>滑块样式</strong>: 应该是金黄色到橙色的渐变</li>
            <li><strong>滑块边框</strong>: 应该有红色边框</li>
            <li><strong>缺口效果</strong>: 缺口应该有阴影和立体感</li>
        </ul>
    </div>

    <script>
        let currentCaptchaData = null;

        function updateStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (isError ? 'error' : 'success');
        }

        function clearCache() {
            // 清除浏览器缓存
            if (window.caches) {
                caches.keys().then(function(names) {
                    names.forEach(function(name) {
                        caches.delete(name);
                    });
                });
            }
            
            // 重新加载页面但跳过缓存
            location.reload(true);
        }

        async function testCaptcha() {
            updateStatus('🔄 正在获取验证码...');
            
            try {
                // 添加随机参数防止缓存
                const response = await fetch(`http://localhost:8181/api/captcha/get?t=${Date.now()}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    },
                    cache: 'no-store'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('验证码响应:', data);

                if (data.success && data.data && data.data.originalImageBase64) {
                    currentCaptchaData = data.data;
                    displayCaptcha(data.data);
                    updateStatus('✅ 验证码获取成功！请检查样式是否为彩色版本');
                } else {
                    throw new Error('验证码数据格式错误: ' + JSON.stringify(data));
                }
            } catch (error) {
                console.error('获取验证码失败:', error);
                updateStatus('❌ 获取验证码失败: ' + error.message, true);
            }
        }

        function displayCaptcha(captchaData) {
            const display = document.getElementById('captchaDisplay');
            
            const timestamp = Date.now();
            
            display.innerHTML = `
                <h3>🖼️ 验证码图片</h3>
                <div class="image-container">
                    <div class="image-item">
                        <h4>背景图片</h4>
                        <img src="data:image/png;base64,${captchaData.originalImageBase64}" alt="背景图片" />
                        <div class="image-info">
                            尺寸: 310x155px<br>
                            应该包含: 彩色渐变背景、白色网格、时间戳水印、立体缺口
                        </div>
                    </div>
                    <div class="image-item">
                        <h4>滑块图片</h4>
                        <img src="data:image/png;base64,${captchaData.jigsawImageBase64}" alt="滑块图片" />
                        <div class="image-info">
                            尺寸: 47x155px<br>
                            应该包含: 金黄到橙色渐变、红色边框
                        </div>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <strong>Token:</strong> ${captchaData.token}<br>
                    <strong>Y坐标:</strong> ${captchaData.y}<br>
                    <strong>获取时间:</strong> ${new Date(timestamp).toLocaleTimeString()}
                </div>
            `;
        }

        // 页面加载时自动测试一次
        window.onload = function() {
            setTimeout(testCaptcha, 1000);
        };
    </script>
</body>
</html>