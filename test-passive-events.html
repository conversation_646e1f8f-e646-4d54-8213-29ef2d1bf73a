<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>被动事件监听器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-area {
            width: 300px;
            height: 200px;
            border: 2px solid #ccc;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9f9f9;
            cursor: pointer;
            user-select: none;
        }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .warning {
            color: #ff6b6b;
            font-weight: bold;
        }
        .success {
            color: #51cf66;
            font-weight: bold;
        }
        .info {
            color: #339af0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>被动事件监听器测试</h1>
    <p>这个页面用于测试修复后的被动事件监听器是否正常工作，以及是否消除了控制台警告。</p>
    
    <h2>测试区域</h2>
    <div class="test-area" id="testArea">
        触摸或拖拽这个区域
    </div>
    
    <button onclick="clearLog()">清除日志</button>
    <button onclick="testPassiveSupport()">测试被动支持</button>
    <button onclick="addOptimizedListeners()">添加优化监听器</button>
    <button onclick="addNonOptimizedListeners()">添加未优化监听器</button>
    
    <h2>事件日志</h2>
    <div class="log" id="eventLog"></div>

    <script>
        // 检查浏览器是否支持 passive 事件监听器
        let supportsPassive = false;
        try {
            const opts = Object.defineProperty({}, 'passive', {
                get() {
                    supportsPassive = true;
                    return false;
                }
            });
            window.addEventListener('testPassive', null, opts);
            window.removeEventListener('testPassive', null, opts);
        } catch (e) {
            // 不支持 passive
        }

        const log = document.getElementById('eventLog');
        const testArea = document.getElementById('testArea');

        function logEvent(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            log.innerHTML = '';
        }

        function testPassiveSupport() {
            logEvent(`浏览器支持被动事件监听器: ${supportsPassive}`, supportsPassive ? 'success' : 'warning');
        }

        // 优化的事件监听器添加函数
        function addOptimizedEventListener(element, event, handler, options = {}) {
            const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchend'];
            const nonPassiveEvents = ['touchmove'];
            
            let finalOptions = options;
            
            if (supportsPassive) {
                if (typeof options === 'boolean') {
                    finalOptions = { capture: options };
                } else if (typeof options === 'object') {
                    finalOptions = { ...options };
                } else {
                    finalOptions = {};
                }
                
                if (nonPassiveEvents.includes(event)) {
                    finalOptions.passive = false;
                    logEvent(`添加 ${event} 监听器 (passive: false)`, 'warning');
                } else if (passiveEvents.includes(event)) {
                    if (finalOptions.passive === undefined) {
                        finalOptions.passive = true;
                    }
                    logEvent(`添加 ${event} 监听器 (passive: ${finalOptions.passive})`, 'success');
                }
            } else {
                logEvent(`添加 ${event} 监听器 (不支持passive)`, 'info');
            }
            
            element.addEventListener(event, handler, finalOptions);
            
            return () => {
                element.removeEventListener(event, handler, finalOptions);
                logEvent(`移除 ${event} 监听器`, 'info');
            };
        }

        let optimizedRemovers = [];
        let nonOptimizedRemovers = [];

        function addOptimizedListeners() {
            // 清除之前的监听器
            optimizedRemovers.forEach(remove => remove());
            optimizedRemovers = [];

            logEvent('=== 添加优化的事件监听器 ===', 'success');

            // 添加优化的事件监听器
            optimizedRemovers.push(
                addOptimizedEventListener(testArea, 'touchstart', (e) => {
                    logEvent('触摸开始 (优化版)', 'success');
                }),
                addOptimizedEventListener(testArea, 'touchmove', (e) => {
                    logEvent('触摸移动 (优化版)', 'success');
                    e.preventDefault(); // 阻止滚动
                }),
                addOptimizedEventListener(testArea, 'touchend', (e) => {
                    logEvent('触摸结束 (优化版)', 'success');
                }),
                addOptimizedEventListener(testArea, 'mousedown', (e) => {
                    logEvent('鼠标按下 (优化版)', 'success');
                })
            );
        }

        function addNonOptimizedListeners() {
            // 清除之前的监听器
            nonOptimizedRemovers.forEach(remove => remove());
            nonOptimizedRemovers = [];

            logEvent('=== 添加未优化的事件监听器 ===', 'warning');

            // 添加未优化的事件监听器（会产生警告）
            const touchStartHandler = (e) => {
                logEvent('触摸开始 (未优化版)', 'warning');
            };
            const touchMoveHandler = (e) => {
                logEvent('触摸移动 (未优化版)', 'warning');
                e.preventDefault();
            };
            const touchEndHandler = (e) => {
                logEvent('触摸结束 (未优化版)', 'warning');
            };

            testArea.addEventListener('touchstart', touchStartHandler);
            testArea.addEventListener('touchmove', touchMoveHandler);
            testArea.addEventListener('touchend', touchEndHandler);

            logEvent('添加 touchstart 监听器 (无passive选项)', 'warning');
            logEvent('添加 touchmove 监听器 (无passive选项)', 'warning');
            logEvent('添加 touchend 监听器 (无passive选项)', 'warning');

            nonOptimizedRemovers.push(
                () => testArea.removeEventListener('touchstart', touchStartHandler),
                () => testArea.removeEventListener('touchmove', touchMoveHandler),
                () => testArea.removeEventListener('touchend', touchEndHandler)
            );
        }

        // 初始化
        testPassiveSupport();
        logEvent('页面加载完成，请打开开发者工具查看控制台警告', 'info');
        logEvent('建议：先测试"未优化监听器"查看警告，再测试"优化监听器"对比效果', 'info');
    </script>
</body>
</html>