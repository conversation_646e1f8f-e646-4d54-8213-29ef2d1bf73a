<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文背景图验证码测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f7;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .captcha-box {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        .captcha-container {
            position: relative;
            width: 280px;
            margin: 0 auto 20px;
            background: #f0f0f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .captcha-image {
            width: 100%;
            height: auto;
            display: block;
        }
        .slider-container {
            background: #fff;
            height: 40px;
            border-radius: 20px;
            margin: 10px 0;
            position: relative;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .slider-track {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: #4CAF50;
            border-radius: 20px;
            transition: width 0.3s;
        }
        .slider-handle {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }
        .info {
            text-align: center;
            font-size: 14px;
            color: #666;
            margin-top: 10px;
        }
        .chinese-bg {
            background: #fffef0;
            border: 2px solid #ff6b6b;
        }
        .chinese-bg .info {
            color: #ff6b6b;
            font-weight: bold;
        }
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
        }
        button:hover {
            background: #0051D5;
        }
        .stats {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            text-align: center;
        }
        .loading {
            text-align: center;
            color: #999;
            padding: 40px;
        }
        .error {
            color: #ff3b30;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🀄 中文背景图验证码实验 🀄</h1>
        
        <div class="controls">
            <button onclick="generateSingle()">生成单个验证码</button>
            <button onclick="generateBatch()">批量生成 (10个)</button>
            <button onclick="findChinese()">查找中文背景图</button>
            <button onclick="clearAll()">清空显示</button>
        </div>
        
        <div class="stats" id="stats">
            <p>总生成次数: <span id="totalCount">0</span></p>
            <p>中文背景图出现次数: <span id="chineseCount">0</span></p>
            <p>中文背景图概率: <span id="chineseRate">0%</span></p>
        </div>
        
        <div class="test-grid" id="testGrid">
            <div class="loading">点击按钮开始测试...</div>
        </div>
    </div>

    <script>
        let totalCount = 0;
        let chineseCount = 0;
        
        // 生成单个验证码
        async function generateSingle() {
            const grid = document.getElementById('testGrid');
            if (grid.querySelector('.loading')) {
                grid.innerHTML = '';
            }
            
            try {
                const response = await fetch('http://localhost:8181/api/captcha/get');
                const data = await response.json();
                
                if (data.data && data.data.originalImageBase64) {
                    totalCount++;
                    const box = createCaptchaBox(data.data);
                    grid.insertBefore(box, grid.firstChild);
                    updateStats();
                }
            } catch (error) {
                console.error('生成验证码失败:', error);
                showError('生成验证码失败，请确保后端服务正在运行');
            }
        }
        
        // 批量生成验证码
        async function generateBatch() {
            const grid = document.getElementById('testGrid');
            grid.innerHTML = '<div class="loading">正在批量生成验证码...</div>';
            
            const promises = [];
            for (let i = 0; i < 10; i++) {
                promises.push(fetch('http://localhost:8181/api/captcha/get').then(r => r.json()));
            }
            
            try {
                const results = await Promise.all(promises);
                grid.innerHTML = '';
                
                results.forEach(data => {
                    if (data.data && data.data.originalImageBase64) {
                        totalCount++;
                        const box = createCaptchaBox(data.data);
                        grid.appendChild(box);
                    }
                });
                
                updateStats();
            } catch (error) {
                console.error('批量生成失败:', error);
                showError('批量生成失败，请确保后端服务正在运行');
            }
        }
        
        // 持续查找中文背景图
        async function findChinese() {
            const grid = document.getElementById('testGrid');
            grid.innerHTML = '<div class="loading">正在查找中文背景图...</div>';
            
            let found = false;
            let attempts = 0;
            const maxAttempts = 50;
            
            while (!found && attempts < maxAttempts) {
                try {
                    const response = await fetch('http://localhost:8181/api/captcha/get');
                    const data = await response.json();
                    
                    if (data.data && data.data.originalImageBase64) {
                        totalCount++;
                        attempts++;
                        
                        // 检查是否是中文背景图（通过图片大小或其他特征判断）
                        const imageSize = data.data.originalImageBase64.length;
                        if (imageSize < 120000) { // 中文背景图通常较小
                            found = true;
                            chineseCount++;
                            grid.innerHTML = '';
                            const box = createCaptchaBox(data.data, true);
                            grid.appendChild(box);
                            updateStats();
                            
                            // 继续显示更多样本
                            for (let i = 0; i < 5; i++) {
                                const resp = await fetch('http://localhost:8181/api/captcha/get');
                                const d = await resp.json();
                                if (d.data) {
                                    totalCount++;
                                    const b = createCaptchaBox(d.data);
                                    grid.appendChild(b);
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('查找失败:', error);
                    break;
                }
            }
            
            if (!found) {
                grid.innerHTML = '<div class="error">尝试了' + attempts + '次，未找到中文背景图</div>';
            }
            updateStats();
        }
        
        // 创建验证码显示框
        function createCaptchaBox(captchaData, isChineseBg = false) {
            const box = document.createElement('div');
            box.className = 'captcha-box' + (isChineseBg ? ' chinese-bg' : '');
            
            // 检测是否可能是中文背景
            const imageSize = captchaData.originalImageBase64.length;
            if (imageSize < 120000) {
                box.className += ' chinese-bg';
                chineseCount++;
                isChineseBg = true;
            }
            
            box.innerHTML = `
                <div class="captcha-container">
                    <img class="captcha-image" src="data:image/png;base64,${captchaData.originalImageBase64}" alt="验证码">
                </div>
                <div class="slider-container">
                    <div class="slider-track" style="width: 0"></div>
                    <div class="slider-handle" style="left: 0">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M7 10L13 10M13 10L10 7M13 10L10 13" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="info">
                    ${isChineseBg ? '🀄 中文背景图！' : '普通背景图'}<br>
                    大小: ${Math.round(imageSize / 1024)}KB<br>
                    ID: ${captchaData.captchaId.substring(0, 8)}...
                </div>
            `;
            
            // 添加滑动功能
            const handle = box.querySelector('.slider-handle');
            const track = box.querySelector('.slider-track');
            let isDragging = false;
            
            handle.addEventListener('mousedown', () => isDragging = true);
            document.addEventListener('mouseup', () => isDragging = false);
            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;
                const rect = box.querySelector('.slider-container').getBoundingClientRect();
                let x = e.clientX - rect.left;
                x = Math.max(0, Math.min(x, rect.width - 40));
                handle.style.left = x + 'px';
                track.style.width = (x + 20) + 'px';
            });
            
            return box;
        }
        
        // 更新统计信息
        function updateStats() {
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('chineseCount').textContent = chineseCount;
            const rate = totalCount > 0 ? (chineseCount / totalCount * 100).toFixed(1) : 0;
            document.getElementById('chineseRate').textContent = rate + '%';
        }
        
        // 清空显示
        function clearAll() {
            document.getElementById('testGrid').innerHTML = '<div class="loading">点击按钮开始测试...</div>';
            totalCount = 0;
            chineseCount = 0;
            updateStats();
        }
        
        // 显示错误信息
        function showError(message) {
            document.getElementById('testGrid').innerHTML = `<div class="error">${message}</div>`;
        }
        
        // 页面加载时自动生成一个
        window.onload = () => {
            generateSingle();
        };
    </script>
</body>
</html>