<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拼图滑块自动生成原理演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2d3748;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .process-flow {
            display: grid;
            grid-template-columns: 1fr auto 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
        }
        
        .step {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .step h3 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .step p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
        
        .arrow {
            font-size: 24px;
            color: #667eea;
            font-weight: bold;
        }
        
        .demo-area {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #667eea;
            border-radius: 12px;
            text-align: center;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px;
            border-left: 4px solid #667eea;
            border-radius: 0 8px 8px 0;
            margin: 20px 0;
        }
        
        .grid-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .demo-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .demo-item h4 {
            color: #2d3748;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧩 拼图滑块自动生成原理</h1>
            <p style="color: #666; font-size: 18px;">更换背景图后，拼图滑块如何自动适应？</p>
        </div>
        
        <div class="highlight">
            <h3>🎯 核心原理：拼图滑块是从背景图中<strong>实时提取</strong>的，不是独立的图片文件</h3>
            <p>无论你使用什么背景图，系统都会自动从中切出相应的拼图块，确保完美匹配。</p>
        </div>
        
        <div class="process-flow">
            <div class="step">
                <h3>📸 步骤1</h3>
                <p>随机选择<br>背景图片</p>
                <div style="margin-top: 10px; font-size: 12px; color: #667eea;">
                    bg1.png ~ bg9.png
                </div>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="step">
                <h3>📐 步骤2</h3>
                <p>确定随机位置<br>(x, y)</p>
                <div style="margin-top: 10px; font-size: 12px; color: #667eea;">
                    47×47像素区域
                </div>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="step">
                <h3>✂️ 步骤3</h3>
                <p>提取图像区域<br>subimage()</p>
                <div style="margin-top: 10px; font-size: 12px; color: #667eea;">
                    真实图像内容
                </div>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="step">
                <h3>🎨 步骤4</h3>
                <p>应用拼图形状<br>setClip()</p>
                <div style="margin-top: 10px; font-size: 12px; color: #667eea;">
                    最终拼图块
                </div>
            </div>
        </div>
        
        <div class="demo-area">
            <h3>🧪 实时演示</h3>
            <p>点击按钮查看不同背景图生成的拼图效果</p>
            <button class="demo-button" onclick="generateDemo()">🔄 生成新的拼图示例</button>
            <div id="demo-result" style="margin-top: 20px;"></div>
        </div>
        
        <h3>💻 技术实现代码</h3>
        <div class="code-block">
<span style="color: #81c784;">// 1. 从背景图中提取指定区域</span>
<span style="color: #ffb74d;">BufferedImage puzzleArea = backgroundImage.subimage(x, y, PIECE_WIDTH, PIECE_HEIGHT);</span>

<span style="color: #81c784;">// 2. 创建拼图形状路径</span>
<span style="color: #ffb74d;">java.awt.Shape puzzleShape = createPuzzleShapePath(0, y, PIECE_WIDTH, PIECE_HEIGHT);</span>

<span style="color: #81c784;">// 3. 应用形状遮罩，创建真实的拼图块</span>
<span style="color: #ffb74d;">pieceG.setClip(puzzleShape);
pieceG.drawImage(puzzleArea, 0, y, null);</span>

<span style="color: #81c784;">// 4. 添加精致的边框效果</span>
<span style="color: #ffb74d;">pieceG.setStroke(new BasicStroke(2.0f));
pieceG.setColor(new Color(0, 0, 0, 80)); // 深色阴影
pieceG.draw(puzzleShape);</span>
        </div>
        
        <h3>🎨 不同背景图的适应效果</h3>
        <div class="grid-demo">
            <div class="demo-item">
                <h4>🌆 风景背景</h4>
                <p>从风景图中提取的拼图块会包含天空、建筑、树木等元素，形成自然的拼图效果</p>
            </div>
            <div class="demo-item">
                <h4>🎨 抽象背景</h4>
                <p>从抽象图案中提取的拼图块会包含色彩、纹理等元素，形成艺术的拼图效果</p>
            </div>
            <div class="demo-item">
                <h4>🏢 建筑背景</h4>
                <p>从建筑图中提取的拼图块会包含线条、结构等元素，形成几何的拼图效果</p>
            </div>
        </div>
        
        <div class="highlight">
            <h3>✅ 总结：无需担心拼图滑块问题</h3>
            <ul style="text-align: left; margin: 10px 0; padding-left: 20px;">
                <li><strong>自动适应</strong>：拼图滑块会自动从新背景图中提取</li>
                <li><strong>完美匹配</strong>：提取的内容与背景图缺口位置完全一致</li>
                <li><strong>动态生成</strong>：每次验证码生成都是实时处理</li>
                <li><strong>无需配置</strong>：更换背景图后无需任何额外操作</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <h3>🔗 相关链接</h3>
            <a href="http://localhost:5274/login" style="margin: 0 10px; color: #667eea;">管理后台测试</a>
            <a href="http://localhost:5273/#/pages/login/index" style="margin: 0 10px; color: #667eea;">移动端测试</a>
        </div>
    </div>

    <script>
        let demoCount = 0;
        
        function generateDemo() {
            demoCount++;
            const resultDiv = document.getElementById('demo-result');
            
            // 模拟不同背景图的拼图生成
            const backgrounds = [
                { name: 'bg1.png - 城市风景', color: '#4299e1', description: '从城市建筑中提取拼图块' },
                { name: 'bg2.png - 自然风光', color: '#48bb78', description: '从山水风景中提取拼图块' },
                { name: 'bg3.png - 抽象图案', color: '#ed8936', description: '从抽象纹理中提取拼图块' },
                { name: 'bg4.png - 花卉植物', color: '#e53e3e', description: '从花朵植物中提取拼图块' }
            ];
            
            const currentBg = backgrounds[demoCount % backgrounds.length];
            
            resultDiv.innerHTML = `
                <div style="border: 2px solid ${currentBg.color}; border-radius: 8px; padding: 20px; background: rgba(${hexToRgb(currentBg.color)}, 0.1);">
                    <h4 style="color: ${currentBg.color}; margin-bottom: 10px;">📸 当前演示：${currentBg.name}</h4>
                    <p style="margin-bottom: 15px;">${currentBg.description}</p>
                    <div style="display: flex; justify-content: center; align-items: center; gap: 20px;">
                        <div style="text-align: center;">
                            <div style="width: 200px; height: 100px; background: linear-gradient(45deg, ${currentBg.color}, ${lightenColor(currentBg.color)}); border-radius: 8px; position: relative; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                                背景图 (${currentBg.name})
                                <div style="position: absolute; top: 30px; left: 100px; width: 30px; height: 30px; background: rgba(255,255,255,0.3); border: 2px dashed white; border-radius: 4px;"></div>
                            </div>
                            <p style="font-size: 12px; margin-top: 5px;">原始背景图片</p>
                        </div>
                        <div style="font-size: 24px; color: ${currentBg.color};">✂️</div>
                        <div style="text-align: center;">
                            <div style="width: 40px; height: 40px; background: linear-gradient(45deg, ${currentBg.color}, ${lightenColor(currentBg.color)}); border-radius: 6px; margin: 0 auto; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; border: 2px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.2);">
                                🧩
                            </div>
                            <p style="font-size: 12px; margin-top: 5px;">提取的拼图块</p>
                        </div>
                    </div>
                    <p style="text-align: center; margin-top: 15px; color: ${currentBg.color}; font-weight: 500;">
                        ✨ 拼图块自动包含背景图中相应位置的真实内容
                    </p>
                </div>
            `;
        }
        
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? 
                parseInt(result[1], 16) + ',' + parseInt(result[2], 16) + ',' + parseInt(result[3], 16) : 
                '102, 126, 234';
        }
        
        function lightenColor(color) {
            // 简单的颜色变亮算法
            return color + '88';
        }
        
        // 页面加载时自动演示一次
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => generateDemo(), 500);
        });
    </script>
</body>
</html>