<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台验证码组件测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        iframe { width: 100%; height: 800px; border: 1px solid #ccc; border-radius: 4px; }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .tech-info { background: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 13px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 管理后台验证码组件测试</h1>
        <p>直接在iframe中加载管理后台登录页面，查看验证码组件是否正常显示。</p>
        
        <div class="status info">
            <strong>📋 测试目标:</strong>
            <ul>
                <li>✅ 检查管理后台登录页面是否能正常加载</li>
                <li>✅ 检查SlideCaptcha组件是否正确渲染</li>
                <li>✅ 检查验证码API是否正常调用</li>
                <li>✅ 检查彩色验证码是否显示</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🖥️ 管理后台登录页面 (iframe)</h3>
            <iframe src="http://localhost:5274/login" title="管理后台登录页面"></iframe>
        </div>
        
        <div class="tech-info">
            <h4>🔧 技术分析</h4>
            <p><strong>组件路径:</strong> /frontend/admin/src/components/SlideCaptcha.vue</p>
            <p><strong>共享组件:</strong> /frontend/shared/SlideCaptcha.vue</p>
            <p><strong>API端点:</strong> http://localhost:8181/api/captcha/get</p>
            <p><strong>代理配置:</strong> admin端口5274代理到后端8181</p>
            <p><strong>修复状态:</strong> 
                <span style="color: green;">✅ defineEmits/defineExpose警告已修复</span>, 
                <span style="color: green;">✅ 相对路径导入已恢复</span>, 
                <span style="color: green;">✅ Vite服务器已重启</span>
            </p>
        </div>
        
        <div style="margin: 20px 0; font-size: 12px; color: #666;">
            最后更新: <span id="timestamp"></span>
        </div>
    </div>

    <script>
        // 显示时间戳
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
        
        // 检查iframe加载状态
        const iframe = document.querySelector('iframe');
        iframe.onload = function() {
            console.log('✅ 管理后台iframe加载成功');
        };
        iframe.onerror = function() {
            console.error('❌ 管理后台iframe加载失败');
        };
    </script>
</body>
</html>