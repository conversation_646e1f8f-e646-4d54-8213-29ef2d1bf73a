
# Task Master AI - Claude Code Integration Guide  

## 🌐 语言设置 / Language Settings

### 重要提示：请使用中文进行所有交流

本项目要求：

- ✅ 所有代码注释使用中文
- ✅ 所有文档说明使用中文  
- ✅ 与用户的所有交流使用中文
- ✅ Git commit messages 可使用中英文混合

## 📏 代码质量规范 / Code Quality Standards

### Checkstyle规范遵循

本项目启用严格的Checkstyle检查，必须遵循以下规范：

#### 🔧 构造函数和工具类

- **工具类必须添加私有构造函数**：避免实例化

  ```java
  public class UtilityClass {
      private UtilityClass() {
          // 工具类，隐藏构造函数
      }
  }
  ```

- **使用Lombok的@UtilityClass时不要手动添加构造函数**

#### 📝 参数规范

- **所有方法参数必须声明为final**：

  ```java
  public void method(final String param1, final int param2) {
      // 方法实现
  }
  ```

#### 📐 行长度和格式

- **行长度不超过120字符**
- **操作符换行规则**：

  ```java
  // ✅ 正确：操作符在新行开头
  String result = "长字符串"
                + "另一部分"
                + "第三部分";
  
  // ❌ 错误：操作符在行末
  String result = "长字符串" +
                "另一部分" +
                "第三部分";
  ```

#### 🔗 空格规范

- **大括号空格**：`{ }` 不是 `{}`

  ```java
  // ✅ 正确
  new Exception("message") { }
  
  // ❌ 错误  
  new Exception("message") {}
  ```

#### 🏗️ 方法长度

- **方法不超过50行**，超过时需要拆分

#### 📚 文档规范

- **可继承类的非空方法需要Javadoc**

### 🎯 测试代码特别注意

测试文件同样需要遵循所有Checkstyle规则：

1. **Mock对象创建时使用final参数**
2. **长断言语句需要适当换行**
3. **测试数据工厂方法参数使用final**
4. **避免在测试中使用魔术数字**

### 🚀 开发工作流

#### 编码前检查

```bash
# 运行Checkstyle检查
./mvnw checkstyle:check

# 快速编译验证
./mvnw compile -q
```

#### 修复常见问题的模式

```bash
# 批量查找需要添加final的方法参数
grep -r "public.*(" src/ | grep -v "final"

# 查找过长的行
grep -r ".\{121,\}" src/
```

## 🛠️ Checkstyle警告修复经验总结 / Checkstyle Warning Fix Guide

### 警告类型优先级处理

基于实际修复经验，建议按以下优先级处理Checkstyle警告：

#### 1. **FinalParameters** - 最高优先级 (176个警告)

**问题**: 方法参数未声明为 final

**影响**: 代码不够安全，参数可能被意外修改

**修复模式**:

```java
// ❌ 错误
public void setServer(ServerConfig server) { }
public ModelInfo(String id, Integer priority) { }

// ✅ 正确
public void setServer(final ServerConfig server) { }
public ModelInfo(final String id, final Integer priority) { }
```

**批量修复策略**:

- 配置类 setter 方法：一次性修复所有参数
- 构造函数：包括枚举构造函数
- 业务方法：确保参数不被意外修改

#### 2. **MagicNumber** - 高优先级 (89个警告)

**问题**: 硬编码的魔术数字

**影响**: 代码可读性差，维护困难

**修复模式**:

```java
// ❌ 错误
SseEmitter emitter = new SseEmitter(600000L); // 10分钟超时
if (current <= 5) { }

// ✅ 正确
private static final long SSE_TIMEOUT_10_MINUTES = 600000L;
private static final int MAX_RETRY_THRESHOLD = 5;

SseEmitter emitter = new SseEmitter(SSE_TIMEOUT_10_MINUTES);
if (current <= MAX_RETRY_THRESHOLD) { }
```

**常见数字常量定义**:

- 超时时间：`TIMEOUT_*_MINUTES`、`TIMEOUT_*_SECONDS`
- 阈值：`MAX_*_THRESHOLD`、`MIN_*_THRESHOLD`
- 年龄分组：`AGE_THRESHOLD_*`

#### 3. **WhitespaceAround** - 中优先级 (28个警告)

**问题**: 大括号周围缺少空格

**修复模式**:

```java
// ❌ 错误
public Constructor() {}
new Exception("message") {}

// ✅ 正确
public Constructor() { }
new Exception("message") { }
```

#### 4. **OperatorWrap** - 中优先级 (37个警告)

**问题**: 操作符应该在新行开头而不是行末

**修复模式**:

```java
// ❌ 错误
String result = "part1" +
              "part2" +
              "part3";

// ✅ 正确
String result = "part1"
              + "part2"
              + "part3";
```

#### 5. **LineLength** - 低优先级 (50个警告)

**问题**: 行长度超过120字符

**修复策略**:

- 长字符串分行
- 长方法调用链分行
- 长注释分行

### 🎯 系统性修复策略

#### Phase 1: 配置文件优先

1. **Config 包下的所有文件**
   - `LMStudioConfig.java`
   - `AssessmentProperties.java`
   - `RedisConfig.java`
   - 这些文件修复后影响面广，容易批量处理

#### Phase 2: 实体类修复

1. **Entity 包下的所有文件**
   - 构造函数参数 final 修复
   - 枚举构造函数修复
   - 业务方法参数修复

#### Phase 3: 服务类修复

1. **Service 包下的文件**
   - 重点修复 MagicNumber
   - 方法长度拆分
   - OperatorWrap 修复

### 📊 修复效果跟踪

**修复前**: 454个警告 (初始状态)
**当前状态**: 183个警告 (最新)
**已减少**: 271个警告 (59.7%改善)

#### 主要修复成果

**Phase 1 - 配置文件修复 (完成)**:

- ✅ LMStudioConfig.java: 26个 FinalParameters 警告
- ✅ AssessmentProperties.java: 28个 FinalParameters 警告
- ✅ RedisConfig.java: 4个 FinalParameters 警告
- ✅ DoclingProperties.java: 2个 FinalParameters + 2个 WhitespaceAround 警告

**Phase 2 - 实体类修复 (部分完成)**:

- ✅ Tenant.java: 4个 FinalParameters 警告
- ✅ GlobalScaleRegistry.java: 4个 FinalParameters 警告
- ✅ AssessmentSubject.java: 1个 FinalParameters 警告
- ✅ PlatformUser.java: 2个 FinalParameters 警告
- ✅ TenantAssessmentRecord.java: 3个 FinalParameters 警告
- ✅ BaseEntity.java: 4个 FinalParameters 警告

**Phase 3 - 核心类修复 (大部分完成)**:

- ✅ ApiResponse.java: 6个 FinalParameters + 1个 WhitespaceAround 警告
- ✅ AIAnalysisController.java: 4个 MagicNumber 警告
- ✅ SimpleCaptchaService.java: 8个 MagicNumber 警告 (大部分修复)
- ✅ SystemDashboardController.java: 2个 MagicNumber 警告 (部分修复)

**Phase 4 - PDF处理类修复 (部分完成)**:

- ✅ PDFParserService.java: 8个 FinalParameters 警告
- ✅ TableExtractionService.java: 1个 FinalParameters 警告

**Phase 5 - 实体类和服务类深度修复 (最新完成)**:

- ✅ TenantAssessmentRecord.java: 3个 FinalParameters + 3个 HiddenField 警告
- ✅ TenantUserMembership.java: 2个 FinalParameters 警告  
- ✅ TenantHierarchyService.java: 12个 FinalParameters + 5个 LeftCurly 警告
- ✅ SystemDashboardController.java: 14个 MagicNumber 警告 (大部分修复)
- ✅ SimpleCaptchaService.java: 15个 MagicNumber + 2个 LineLength + 1个 OperatorWrap 警告
- ✅ UserIdentityService.java: 6个 LeftCurly 警告

**Phase 6 - PDF处理和实体类深度修复 (最新完成)**:

- ✅ AssessmentSection.java: 2个 FinalParameters 警告
- ✅ QuestionType.java: 1个 FinalParameters 警告
- ✅ AssessmentTable.java: 3个 FinalParameters 警告
- ✅ TableExtractionService.java: 7个 FinalParameters 警告
- ✅ PDFContent.java: 2个 FinalParameters 警告
- ✅ AssessmentStructure.java: 3个 FinalParameters 警告
- ✅ AssessmentQuestion.java: 8个 FinalParameters 警告
- ✅ IndividualUser.java: 3个 FinalParameters + 1个 MagicNumber 警告
- ✅ CaptchaController.java: 2个 LineLength 警告

**快速修复**:

- ✅ PasswordGenerator.java: 1个 FinalClass 警告

#### 当前警告分布 (183个总计)

- 🔄 49个 LineLength 警告 (已减少2个)
- 🔄 36个 OperatorWrap 警告 (已减少1个)  
- 🔄 31个 FinalParameters 警告 (已减少42个)
- 🔄 29个 MagicNumber 警告 (已减少40个)
- 🔄 24个 DesignForExtension 警告
- 🔄 7个 MethodLength 警告
- ✅ 0个 LeftCurly 警告 (已全部修复)
- ✅ 0个 HiddenField 警告 (已全部修复)
- 🔄 其他小类型警告

### 🔄 持续改进流程

```bash
# 1. 检查当前警告数
./mvnw checkstyle:check 2>&1 | grep "\[WARN\]" | wc -l

# 2. 分析警告类型分布
./mvnw checkstyle:check 2>&1 | grep -o '\[.*\]$' | sed 's/.*\[\([^]]*\)\].*/\1/' | sort | uniq -c | sort -nr

# 3. 针对性修复最多的警告类型
./mvnw checkstyle:check 2>&1 | grep "FinalParameters" | head -20

# 4. 验证修复效果
./mvnw clean compile -DskipTests
```

### ⚠️ 避免的反模式

1. **不要跳过 Checkstyle 检查** - 修复问题而不是忽略
2. **不要批量添加 `@SuppressWarnings`** - 应该修复根本问题
3. **不要为了通过检查而破坏代码逻辑** - 保持代码可读性
4. **不要忽视测试代码的规范** - 测试代码同样需要高质量

### 🎖️ 最佳实践总结

1. **小步迭代**: 每次修复一类警告，验证后再继续
2. **影响面分析**: 优先修复影响文件多的配置类
3. **语义化常量**: 魔术数字提取时使用有意义的常量名
4. **保持一致性**: 同类问题使用统一的修复模式
5. **文档更新**: 及时将修复经验更新到 CLAUDE.md

---

## Essential Commands

### Core Workflow Commands

```bash
# Project Setup
task-master init                                    # Initialize Task Master in current project
task-master parse-prd .taskmaster/docs/prd.txt      # Generate tasks from PRD document
task-master models --setup                        # Configure AI models interactively

# Daily Development Workflow
task-master list                                   # Show all tasks with status
task-master next                                   # Get next available task to work on
task-master show <id>                             # View detailed task information (e.g., task-master show 1.2)
task-master set-status --id=<id> --status=done    # Mark task complete

# Task Management
task-master add-task --prompt="description" --research        # Add new task with AI assistance
task-master expand --id=<id> --research --force              # Break task into subtasks
task-master update-task --id=<id> --prompt="changes"         # Update specific task
task-master update --from=<id> --prompt="changes"            # Update multiple tasks from ID onwards
task-master update-subtask --id=<id> --prompt="notes"        # Add implementation notes to subtask

# Analysis & Planning
task-master analyze-complexity --research          # Analyze task complexity
task-master complexity-report                      # View complexity analysis
task-master expand --all --research               # Expand all eligible tasks

# Dependencies & Organization
task-master add-dependency --id=<id> --depends-on=<id>       # Add task dependency
task-master move --from=<id> --to=<id>                       # Reorganize task hierarchy
task-master validate-dependencies                            # Check for dependency issues
task-master generate                                         # Update task markdown files (usually auto-called)
```

## Key Files & Project Structure

### Core Files

- `.taskmaster/tasks/tasks.json` - Main task data file (auto-managed)
- `.taskmaster/config.json` - AI model configuration (use `task-master models` to modify)
- `.taskmaster/docs/prd.txt` - Product Requirements Document for parsing
- `.taskmaster/tasks/*.txt` - Individual task files (auto-generated from tasks.json)
- `.env` - API keys for CLI usage

### Claude Code Integration Files

- `CLAUDE.md` - Auto-loaded context for Claude Code (this file)
- `.claude/settings.json` - Claude Code tool allowlist and preferences
- `.claude/commands/` - Custom slash commands for repeated workflows
- `.mcp.json` - MCP server configuration (project-specific)

### Directory Structure

project/
├── .taskmaster/
│   ├── tasks/              # Task files directory
│   │   ├── tasks.json      # Main task database
│   │   ├── task-1.md      # Individual task files
│   │   └── task-2.md
│   ├── docs/              # Documentation directory
│   │   ├── prd.txt        # Product requirements
│   ├── reports/           # Analysis reports directory
│   │   └── task-complexity-report.json
│   ├── templates/         # Template files
│   │   └── example_prd.txt  # Example PRD template
│   └── config.json        # AI models & settings
├── .claude/
│   ├── settings.json      # Claude Code configuration
│   └── commands/         # Custom slash commands
├── .env                  # API keys
├── .mcp.json            # MCP configuration
└── CLAUDE.md            # This file - auto-loaded by Claude Code

## MCP Integration

Task Master provides an MCP server that Claude Code can connect to. Configure in `.mcp.json`:

```json
{
  "mcpServers": {
    "task-master-ai": {
      "command": "npx",
      "args": ["-y", "--package=task-master-ai", "task-master-ai"],
      "env": {
        "ANTHROPIC_API_KEY": "your_key_here",
        "PERPLEXITY_API_KEY": "your_key_here",
        "OPENAI_API_KEY": "OPENAI_API_KEY_HERE",
        "GOOGLE_API_KEY": "GOOGLE_API_KEY_HERE",
        "XAI_API_KEY": "XAI_API_KEY_HERE",
        "OPENROUTER_API_KEY": "OPENROUTER_API_KEY_HERE",
        "MISTRAL_API_KEY": "MISTRAL_API_KEY_HERE",
        "AZURE_OPENAI_API_KEY": "AZURE_OPENAI_API_KEY_HERE",
        "OLLAMA_API_KEY": "OLLAMA_API_KEY_HERE"
      }
    }
  }
}
```

### Essential MCP Tools

```javascript
help; // = shows available taskmaster commands
// Project setup
initialize_project; // = task-master init
parse_prd; // = task-master parse-prd

// Daily workflow
get_tasks; // = task-master list
next_task; // = task-master next
get_task; // = task-master show <id>
set_task_status; // = task-master set-status

// Task management
add_task; // = task-master add-task
expand_task; // = task-master expand
update_task; // = task-master update-task
update_subtask; // = task-master update-subtask
update; // = task-master update

// Analysis
analyze_project_complexity; // = task-master analyze-complexity
complexity_report; // = task-master complexity-report
```

## Claude Code Workflow Integration

### Standard Development Workflow

#### 1. Project Initialization

```bash
# Initialize Task Master
task-master init

# Create or obtain PRD, then parse it
task-master parse-prd .taskmaster/docs/prd.txt

# Analyze complexity and expand tasks
task-master analyze-complexity --research
task-master expand --all --research
```

If tasks already exist, another PRD can be parsed (with new information only!) using parse-prd with --append flag. This will add the generated tasks to the existing list of tasks..

#### 2. Daily Development Loop

```bash
# Start each session
task-master next                           # Find next available task
task-master show <id>                     # Review task details

# During implementation, check in code context into the tasks and subtasks
task-master update-subtask --id=<id> --prompt="implementation notes..."

# Complete tasks
task-master set-status --id=<id> --status=done
```

#### 3. Multi-Claude Workflows

For complex projects, use multiple Claude Code sessions:

```bash
# Terminal 1: Main implementation
cd project && claude

# Terminal 2: Testing and validation
cd project-test-worktree && claude

# Terminal 3: Documentation updates
cd project-docs-worktree && claude
```

### Custom Slash Commands

Create `.claude/commands/taskmaster-next.md`:

```markdown
Find the next available Task Master task and show its details.

Steps:

1. Run `task-master next` to get the next task
2. If a task is available, run `task-master show <id>` for full details
3. Provide a summary of what needs to be implemented
4. Suggest the first implementation step
```

Create `.claude/commands/taskmaster-complete.md`:

```markdown
Complete a Task Master task: $ARGUMENTS

Steps:

1. Review the current task with `task-master show $ARGUMENTS`
2. Verify all implementation is complete
3. Run any tests related to this task
4. Mark as complete: `task-master set-status --id=$ARGUMENTS --status=done`
5. Show the next available task with `task-master next`
```

## Tool Allowlist Recommendations

Add to `.claude/settings.json`:

```json
{
  "allowedTools": [
    "Edit",
    "Bash(task-master *)",
    "Bash(git commit:*)",
    "Bash(git add:*)",
    "Bash(npm run *)",
    "mcp__task_master_ai__*"
  ]
}
```

## Configuration & Setup

### API Keys Required

At least **one** of these API keys must be configured:

- `ANTHROPIC_API_KEY` (Claude models) - **Recommended**
- `PERPLEXITY_API_KEY` (Research features) - **Highly recommended**
- `OPENAI_API_KEY` (GPT models)
- `GOOGLE_API_KEY` (Gemini models)
- `MISTRAL_API_KEY` (Mistral models)
- `OPENROUTER_API_KEY` (Multiple models)
- `XAI_API_KEY` (Grok models)

An API key is required for any provider used across any of the 3 roles defined in the `models` command.

### Model Configuration

```bash
# Interactive setup (recommended)
task-master models --setup

# Set specific models
task-master models --set-main claude-3-5-sonnet-20241022
task-master models --set-research perplexity-llama-3.1-sonar-large-128k-online
task-master models --set-fallback gpt-4o-mini
```

## Task Structure & IDs

### Task ID Format

- Main tasks: `1`, `2`, `3`, etc.
- Subtasks: `1.1`, `1.2`, `2.1`, etc.
- Sub-subtasks: `1.1.1`, `1.1.2`, etc.

### Task Status Values

- `pending` - Ready to work on
- `in-progress` - Currently being worked on
- `done` - Completed and verified
- `deferred` - Postponed
- `cancelled` - No longer needed
- `blocked` - Waiting on external factors

### Task Fields

```json
{
  "id": "1.2",
  "title": "Implement user authentication",
  "description": "Set up JWT-based auth system",
  "status": "pending",
  "priority": "high",
  "dependencies": ["1.1"],
  "details": "Use bcrypt for hashing, JWT for tokens...",
  "testStrategy": "Unit tests for auth functions, integration tests for login flow",
  "subtasks": []
}
```

## Claude Code Best Practices with Task Master

### Context Management

- Use `/clear` between different tasks to maintain focus
- This CLAUDE.md file is automatically loaded for context
- Use `task-master show <id>` to pull specific task context when needed

### Iterative Implementation

1. `task-master show <subtask-id>` - Understand requirements
2. Explore codebase and plan implementation
3. `task-master update-subtask --id=<id> --prompt="detailed plan"` - Log plan
4. `task-master set-status --id=<id> --status=in-progress` - Start work
5. Implement code following logged plan
6. `task-master update-subtask --id=<id> --prompt="what worked/didn't work"` - Log progress
7. `task-master set-status --id=<id> --status=done` - Complete task

### Complex Workflows with Checklists

For large migrations or multi-step processes:

1. Create a markdown PRD file describing the new changes: `touch task-migration-checklist.md` (prds can be .txt or .md)
2. Use Taskmaster to parse the new prd with `task-master parse-prd --append` (also available in MCP)
3. Use Taskmaster to expand the newly generated tasks into subtasks. Consdier using `analyze-complexity` with the correct --to and --from IDs (the new ids) to identify the ideal subtask amounts for each task. Then expand them.
4. Work through items systematically, checking them off as completed
5. Use `task-master update-subtask` to log progress on each task/subtask and/or updating/researching them before/during implementation if getting stuck

### Git Integration

Task Master works well with `gh` CLI:

```bash
# Create PR for completed task
gh pr create --title "Complete task 1.2: User authentication" --body "Implements JWT auth system as specified in task 1.2"

# Reference task in commits
git commit -m "feat: implement JWT auth (task 1.2)"
```

### Parallel Development with Git Worktrees

```bash
# Create worktrees for parallel task development
git worktree add ../project-auth feature/auth-system
git worktree add ../project-api feature/api-refactor

# Run Claude Code in each worktree
cd ../project-auth && claude    # Terminal 1: Auth work
cd ../project-api && claude     # Terminal 2: API work
```

## Troubleshooting

### AI Commands Failing

```bash
# Check API keys are configured
cat .env                           # For CLI usage

# Verify model configuration
task-master models

# Test with different model
task-master models --set-fallback gpt-4o-mini
```

### MCP Connection Issues

- Check `.mcp.json` configuration
- Verify Node.js installation
- Use `--mcp-debug` flag when starting Claude Code
- Use CLI as fallback if MCP unavailable

### Task File Sync Issues

```bash
# Regenerate task files from tasks.json
task-master generate

# Fix dependency issues
task-master fix-dependencies
```

DO NOT RE-INITIALIZE. That will not do anything beyond re-adding the same Taskmaster core files.

## Important Notes

### AI-Powered Operations

These commands make AI calls and may take up to a minute:

- `parse_prd` / `task-master parse-prd`
- `analyze_project_complexity` / `task-master analyze-complexity`
- `expand_task` / `task-master expand`
- `expand_all` / `task-master expand --all`
- `add_task` / `task-master add-task`
- `update` / `task-master update`
- `update_task` / `task-master update-task`
- `update_subtask` / `task-master update-subtask`

### File Management

- Never manually edit `tasks.json` - use commands instead
- Never manually edit `.taskmaster/config.json` - use `task-master models`
- Task markdown files in `tasks/` are auto-generated
- Run `task-master generate` after manual changes to tasks.json

### Claude Code Session Management

- Use `/clear` frequently to maintain focused context
- Create custom slash commands for repeated Task Master workflows
- Configure tool allowlist to streamline permissions
- Use headless mode for automation: `claude -p "task-master next"`

### Multi-Task Updates

- Use `update --from=<id>` to update multiple future tasks
- Use `update-task --id=<id>` for single task updates
- Use `update-subtask --id=<id>` for implementation logging

### Research Mode

- Add `--research` flag for research-based AI enhancement
- Requires a research model API key like Perplexity (`PERPLEXITY_API_KEY`) in environment
- Provides more informed task creation and updates
- Recommended for complex technical tasks

---

## 🎨 前端开发规范 / Frontend Development Standards

### Vite配置规范

#### 避免CJS弃用警告

- **使用ES模块导入**替代CommonJS require()：

  ```typescript
  // ✅ 正确：ES模块导入
  import tailwindcss from '@tailwindcss/postcss';
  import autoprefixer from 'autoprefixer';
  
  export default defineConfig({
    css: {
      postcss: {
        plugins: [
          tailwindcss,
          autoprefixer,
        ],
      },
    },
  });
  
  // ❌ 错误：CommonJS require
  css: {
    postcss: {
      plugins: [
        require('@tailwindcss/postcss'),
        require('autoprefixer'),
      ],
    },
  },
  ```

#### Package.json配置

- 确保使用最新版本的依赖
- 正确配置开发脚本

### 前端代码质量

- 遵循TypeScript严格模式
- 使用ESLint和Prettier进行代码格式化
- 组件开发遵循Vue 3 Composition API

---

## 📝 最后提醒

**记住：本项目的所有交流和文档都应使用中文！**

_This guide ensures Claude Code has immediate access to Task Master's essential functionality for agentic development workflows._
