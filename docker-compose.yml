services:
  # PostgreSQL数据库（ARM64兼容）
  postgres:
    image: postgres:15-alpine
    platform: linux/arm64
    container_name: assessment-postgres
    environment:
      POSTGRES_DB: assessment_multitenant
      POSTGRES_USER: assessment_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-assessment123}
      POSTGRES_INITDB_ARGS: "-E UTF8 --locale=en_US.UTF-8"
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U assessment_user -d assessment_multitenant"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    networks:
      - assessment-net

  # Redis缓存（ARM64兼容）
  redis:
    image: redis:7-alpine
    platform: linux/arm64
    container_name: assessment-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - ./data/redis:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    networks:
      - assessment-net

  # MinIO对象存储（ARM64兼容）
  minio:
    image: minio/minio:latest
    platform: linux/arm64
    container_name: assessment-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_PASSWORD:-minioadmin}
    volumes:
      - ./data/minio:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped
    networks:
      - assessment-net

  # Nginx反向代理（开发环境可选，ARM64兼容）
  nginx:
    image: nginx:alpine
    platform: linux/arm64
    container_name: assessment-nginx
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/conf.d:/etc/nginx/conf.d
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - postgres
      - redis
      - minio
    restart: unless-stopped
    networks:
      - assessment-net

networks:
  assessment-net:
    driver: bridge